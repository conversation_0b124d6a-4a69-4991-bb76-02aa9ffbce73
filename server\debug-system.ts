/**
 * Comprehensive Debug and Error Tracking System for EquiGestor AI
 * Complete solution for monitoring Error 500s across all application modules
 */

import fs from 'fs';
import path from 'path';
import { logger } from './simple-logger';

interface ErrorContext {
  requestId: string;
  timestamp: string;
  module: string;
  endpoint: string;
  method: string;
  user_id?: number;
  userAgent?: string;
  ip?: string;
  errorDetails: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  requestData: {
    params?: any;
    query?: any;
    body?: any;
    headers?: any;
  };
}

interface ModuleHealthStatus {
  module: string;
  status: 'healthy' | 'warning' | 'critical';
  errorCount: number;
  lastError?: string;
  uptime: number;
  endpoints: {
    [endpoint: string]: {
      totalRequests: number;
      errorCount: number;
      lastError?: string;
      averageResponseTime: number;
    };
  };
}

class DebugSystem {
  private errors: ErrorContext[] = [];
  private moduleStats: Map<string, ModuleHealthStatus> = new Map();
  private requestMetrics: Map<string, any> = new Map();
  private debugFile: string;
  private maxErrors = 2000;

  constructor() {
    const logDir = path.join(process.cwd(), 'logs');
    this.debugFile = path.join(logDir, 'debug-comprehensive.json');
    this.loadExistingData();
    this.initializeModules();
  }

  /**
   * Records a comprehensive error with full context
   */
  recordError(error: any, req: any, module: string): string {
    const requestId = this.generateRequestId();
    
    const errorContext: ErrorContext = {
      requestId,
      timestamp: new Date().toISOString(),
      module,
      endpoint: `${req.method} ${req.url}`,
      method: req.method,
      user_id: this.extractUserId(req),
      userAgent: req.headers['user-agent']?.substring(0, 200),
      ip: req.ip || req.connection?.remoteAddress,
      errorDetails: {
        name: error.name || 'UnknownError',
        message: error.message || 'No error message provided',
        stack: error.stack,
        code: error.code
      },
      requestData: {
        params: req.params,
        query: req.query,
        body: this.sanitizeRequestBody(req.body),
        headers: this.sanitizeHeaders(req.headers)
      }
    };

    this.errors.push(errorContext);
    this.updateModuleStats(module, req.url, true);
    this.maintainErrorLimit();
    this.saveDebugData();

    // Log critical error patterns
    this.analyzeErrorPattern(errorContext);

    logger.error(`Error 500 recorded in ${module}`, {
      requestId,
      module,
      endpoint: errorContext.endpoint,
      errorType: error.name,
      message: error.message
    });

    return requestId;
  }

  /**
   * Records successful request for health monitoring
   */
  recordSuccess(req: any, module: string, responseTime: number): void {
    this.updateModuleStats(module, req.url, false, responseTime);
  }

  /**
   * Generates comprehensive system health report
   */
  generateHealthReport(): any {
    const moduleHealth = Array.from(this.moduleStats.values());
    const criticalModules = moduleHealth.filter(m => m.status === 'critical');
    const warningModules = moduleHealth.filter(m => m.status === 'warning');
    
    const recentErrors = this.errors
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 100);

    const errorsByModule = this.groupErrorsByModule();
    const criticalPatterns = this.identifyCriticalPatterns();

    return {
      summary: {
        totalErrors: this.errors.length,
        criticalModules: criticalModules.length,
        warningModules: warningModules.length,
        healthyModules: moduleHealth.filter(m => m.status === 'healthy').length,
        systemStatus: this.calculateOverallStatus(),
        reportGenerated: new Date().toISOString()
      },
      moduleHealth,
      criticalIssues: criticalPatterns,
      errorsByModule,
      recentErrors: recentErrors.map(error => ({
        requestId: error.requestId,
        timestamp: error.timestamp,
        module: error.module,
        endpoint: error.endpoint,
        errorType: error.errorDetails.name,
        message: error.errorDetails.message,
        user_id: error.user_id
      })),
      troubleshootingGuide: this.generateTroubleshootingGuide(criticalPatterns)
    };
  }

  /**
   * Gets detailed error analysis for a specific module
   */
  getModuleAnalysis(module: string): any {
    const moduleErrors = this.errors.filter(e => e.module === module);
    const moduleHealth = this.moduleStats.get(module);
    
    if (!moduleHealth) {
      return { error: 'Module not found' };
    }

    const errorsByEndpoint = this.groupErrorsByEndpoint(moduleErrors);
    const commonErrors = this.getCommonErrors(moduleErrors);
    const timePattern = this.analyzeTimePattern(moduleErrors);

    return {
      module,
      health: moduleHealth,
      totalErrors: moduleErrors.length,
      errorsByEndpoint,
      commonErrors,
      timePattern,
      recentErrors: moduleErrors
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 50)
        .map(error => ({
          requestId: error.requestId,
          timestamp: error.timestamp,
          endpoint: error.endpoint,
          errorType: error.errorDetails.name,
          message: error.errorDetails.message,
          stack: error.errorDetails.stack?.split('\n').slice(0, 5).join('\n')
        })),
      recommendations: this.generateModuleRecommendations(module, moduleErrors)
    };
  }

  /**
   * Gets specific error details by request ID
   */
  getErrorDetails(requestId: string): ErrorContext | null {
    return this.errors.find(error => error.requestId === requestId) || null;
  }

  /**
   * Express middleware factory for automatic error tracking
   */
  createErrorMiddleware(module: string) {
    return (error: any, req: any, res: any, next: any) => {
      const requestId = this.recordError(error, req, module);
      
      res.setHeader('X-Debug-Request-ID', requestId);
      res.setHeader('X-Debug-Module', module);
      
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Internal Server Error',
          requestId,
          module,
          timestamp: new Date().toISOString(),
          debugEndpoint: `/debug/errors/details/${requestId}`
        });
      }
    };
  }

  /**
   * Express middleware for request success tracking
   */
  createSuccessMiddleware(module: string) {
    return (req: any, res: any, next: any) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        if (res.statusCode < 400) {
          const responseTime = Date.now() - startTime;
          this.recordSuccess(req, module, responseTime);
        }
      });
      
      next();
    };
  }

  private initializeModules(): void {
    const modules = ['cavalos', 'nutricao', 'morfologia', 'manejos', 'veterinario', 'agenda', 'auth', 'general'];
    
    modules.forEach(module => {
      if (!this.moduleStats.has(module)) {
        this.moduleStats.set(module, {
          module,
          status: 'healthy',
          errorCount: 0,
          uptime: Date.now(),
          endpoints: {}
        });
      }
    });
  }

  private updateModuleStats(module: string, endpoint: string, isError: boolean, responseTime?: number): void {
    let stats = this.moduleStats.get(module);
    
    if (!stats) {
      stats = {
        module,
        status: 'healthy',
        errorCount: 0,
        uptime: Date.now(),
        endpoints: {}
      };
      this.moduleStats.set(module, stats);
    }

    if (isError) {
      stats.errorCount++;
      stats.lastError = new Date().toISOString();
    }

    // Update endpoint stats
    if (!stats.endpoints[endpoint]) {
      stats.endpoints[endpoint] = {
        totalRequests: 0,
        errorCount: 0,
        averageResponseTime: 0
      };
    }

    const endpointStats = stats.endpoints[endpoint];
    endpointStats.totalRequests++;
    
    if (isError) {
      endpointStats.errorCount++;
      endpointStats.lastError = new Date().toISOString();
    }

    if (responseTime) {
      endpointStats.averageResponseTime = 
        (endpointStats.averageResponseTime * (endpointStats.totalRequests - 1) + responseTime) / endpointStats.totalRequests;
    }

    // Update module status
    const errorRate = stats.errorCount / Math.max(1, Object.values(stats.endpoints).reduce((sum, ep) => sum + ep.totalRequests, 0));
    
    if (errorRate > 0.3) {
      stats.status = 'critical';
    } else if (errorRate > 0.1) {
      stats.status = 'warning';
    } else {
      stats.status = 'healthy';
    }
  }

  private identifyCriticalPatterns(): any[] {
    const patterns = [];
    
    // Database connection errors
    const dbErrors = this.errors.filter(e => 
      e.errorDetails.message.includes('database') ||
      e.errorDetails.message.includes('connection') ||
      e.errorDetails.code?.includes('ECONNREFUSED') ||
      e.errorDetails.code?.includes('42703')
    );

    if (dbErrors.length > 5) {
      patterns.push({
        type: 'database_connectivity',
        severity: 'critical',
        count: dbErrors.length,
        description: 'Multiple database connection or schema errors detected',
        recommendation: 'Check database connectivity and verify schema consistency',
        examples: dbErrors.slice(0, 3).map(e => ({
          message: e.errorDetails.message,
          timestamp: e.timestamp,
          module: e.module
        }))
      });
    }

    // Authentication failures
    const authErrors = this.errors.filter(e =>
      e.module === 'auth' ||
      e.errorDetails.message.includes('unauthorized') ||
      e.errorDetails.message.includes('authentication')
    );

    if (authErrors.length > 10) {
      patterns.push({
        type: 'authentication_issues',
        severity: 'high',
        count: authErrors.length,
        description: 'High number of authentication failures',
        recommendation: 'Review authentication middleware and session handling'
      });
    }

    // Module-specific high error rates
    this.moduleStats.forEach((stats, module) => {
      if (stats.status === 'critical') {
        patterns.push({
          type: 'module_degradation',
          severity: 'high',
          module,
          count: stats.errorCount,
          description: `${module} module showing critical error rates`,
          recommendation: `Immediate attention required for ${module} module functionality`
        });
      }
    });

    return patterns;
  }

  private generateTroubleshootingGuide(patterns: any[]): any {
    const guide = {
      immediate_actions: [],
      investigation_steps: [],
      prevention_measures: []
    };

    patterns.forEach(pattern => {
      switch (pattern.type) {
        case 'database_connectivity':
          guide.immediate_actions.push('Verify database connection and credentials');
          guide.investigation_steps.push('Check database logs for connection issues');
          guide.prevention_measures.push('Implement database connection pooling and retry logic');
          break;
        case 'authentication_issues':
          guide.immediate_actions.push('Review recent authentication changes');
          guide.investigation_steps.push('Analyze authentication failure patterns');
          guide.prevention_measures.push('Implement progressive authentication retry limits');
          break;
        case 'module_degradation':
          guide.immediate_actions.push(`Focus on ${pattern.module} module debugging`);
          guide.investigation_steps.push(`Analyze ${pattern.module} module recent changes`);
          guide.prevention_measures.push(`Implement health checks for ${pattern.module} module`);
          break;
      }
    });

    return guide;
  }

  private calculateOverallStatus(): string {
    const criticalCount = Array.from(this.moduleStats.values()).filter(m => m.status === 'critical').length;
    const warningCount = Array.from(this.moduleStats.values()).filter(m => m.status === 'warning').length;
    
    if (criticalCount > 0) return 'critical';
    if (warningCount > 2) return 'warning';
    return 'healthy';
  }

  private groupErrorsByModule(): any {
    const grouped: any = {};
    
    this.errors.forEach(error => {
      if (!grouped[error.module]) {
        grouped[error.module] = {
          count: 0,
          lastError: error.timestamp,
          commonErrors: {}
        };
      }
      
      grouped[error.module].count++;
      
      const errorKey = error.errorDetails.name;
      grouped[error.module].commonErrors[errorKey] = 
        (grouped[error.module].commonErrors[errorKey] || 0) + 1;
        
      if (new Date(error.timestamp) > new Date(grouped[error.module].lastError)) {
        grouped[error.module].lastError = error.timestamp;
      }
    });
    
    return grouped;
  }

  private groupErrorsByEndpoint(errors: ErrorContext[]): any {
    const grouped: any = {};
    
    errors.forEach(error => {
      if (!grouped[error.endpoint]) {
        grouped[error.endpoint] = {
          count: 0,
          lastError: error.timestamp,
          errorTypes: {}
        };
      }
      
      grouped[error.endpoint].count++;
      grouped[error.endpoint].errorTypes[error.errorDetails.name] = 
        (grouped[error.endpoint].errorTypes[error.errorDetails.name] || 0) + 1;
    });
    
    return grouped;
  }

  private getCommonErrors(errors: ErrorContext[]): any[] {
    const errorCounts: any = {};
    
    errors.forEach(error => {
      const key = `${error.errorDetails.name}: ${error.errorDetails.message}`;
      errorCounts[key] = (errorCounts[key] || 0) + 1;
    });
    
    return Object.entries(errorCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([error, count]) => ({ error, count }));
  }

  private analyzeTimePattern(errors: ErrorContext[]): any {
    const hourly: any = {};
    
    errors.forEach(error => {
      const hour = new Date(error.timestamp).getHours();
      hourly[hour] = (hourly[hour] || 0) + 1;
    });
    
    return {
      hourlyDistribution: hourly,
      peakHour: Object.entries(hourly).reduce((max, [hour, count]) => 
        (count as number) > (max.count || 0) ? { hour: parseInt(hour), count } : max, { hour: 0, count: 0 })
    };
  }

  private generateModuleRecommendations(module: string, errors: ErrorContext[]): string[] {
    const recommendations = [];
    
    const commonErrorTypes = this.getCommonErrors(errors);
    const recentErrors = errors.filter(e => 
      new Date(e.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
    );
    
    if (recentErrors.length > 10) {
      recommendations.push(`High error frequency in ${module} module - investigate recent changes`);
    }
    
    if (commonErrorTypes.some(e => e.error.includes('database'))) {
      recommendations.push(`Database-related errors detected in ${module} - verify queries and schema`);
    }
    
    if (commonErrorTypes.some(e => e.error.includes('TypeError'))) {
      recommendations.push(`Type errors detected in ${module} - review data validation and TypeScript types`);
    }
    
    return recommendations;
  }

  private analyzeErrorPattern(error: ErrorContext): void {
    // Check for rapid error sequences
    const recentSimilarErrors = this.errors.filter(e =>
      e.module === error.module &&
      e.errorDetails.name === error.errorDetails.name &&
      new Date(e.timestamp).getTime() > Date.now() - 5 * 60 * 1000 // Last 5 minutes
    );

    if (recentSimilarErrors.length > 5) {
      logger.error('Rapid error sequence detected', {
        module: error.module,
        errorType: error.errorDetails.name,
        count: recentSimilarErrors.length,
        timeframe: '5 minutes'
      });
    }
  }

  private extractUserId(req: any): number | undefined {
    return req.headers['user-id'] ? parseInt(req.headers['user-id']) : undefined;
  }

  private sanitizeRequestBody(body: any): any {
    if (!body) return body;
    
    const sanitized = { ...body };
    delete sanitized.password;
    delete sanitized.token;
    delete sanitized.secret;
    return sanitized;
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    return sanitized;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private loadExistingData(): void {
    try {
      if (fs.existsSync(this.debugFile)) {
        const data = JSON.parse(fs.readFileSync(this.debugFile, 'utf8'));
        this.errors = data.errors || [];
        
        if (data.moduleStats) {
          this.moduleStats = new Map(Object.entries(data.moduleStats));
        }
      }
    } catch (error) {
      logger.warn('Failed to load existing debug data', { error: (error as Error).message });
    }
  }

  private saveDebugData(): void {
    try {
      const data = {
        lastUpdated: new Date().toISOString(),
        errors: this.errors,
        moduleStats: Object.fromEntries(this.moduleStats)
      };
      
      fs.writeFileSync(this.debugFile, JSON.stringify(data, null, 2));
    } catch (error) {
      logger.warn('Failed to save debug data', { error: (error as Error).message });
    }
  }

  private maintainErrorLimit(): void {
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }
  }
}

// Export singleton instance
export const debugSystem = new DebugSystem();

export default debugSystem;