import React, { createContext, useState, useContext, useEffect, ReactNode, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";

// Função de log para informações importantes do contexto de autenticação
const log = (...args: any[]) => {
  console.log('[AuthContext]', ...args);
};

// Função para verificar a validade do token de autenticação
const isTokenValid = (): boolean => {
  try {
    const token = localStorage.getItem('auth_token');
    const expiration = localStorage.getItem('token_expiration');
    
    if (!token || !expiration) return false;
    
    const expirationTime = parseInt(expiration);
    const currentTime = new Date().getTime();
    
    return currentTime < expirationTime;
  } catch (e) {
    console.error('Erro ao verificar validade do token', e);
    return false;
  }
};

// Interface para o usuário
interface User {
  id: number;
  name: string;
  username: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  user_id: number | null; // Adicionando user_id diretamente como propriedade
  signup: (name: string, username: string, email: string, password: string) => Promise<void>;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Criar o contexto com um valor padrão para evitar o erro de undefined
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  user_id: null,
  signup: async () => { throw new Error('AuthContext não inicializado') },
  login: async () => { throw new Error('AuthContext não inicializado') },
  logout: async () => { throw new Error('AuthContext não inicializado') },
});

// Hook para usar o contexto de autenticação
const useAuth = () => {
  log('useAuth called');
  const context = useContext(AuthContext);
  log('useAuth returning context with user:', context.user?.username || 'No user');
  
  // Verificar se estamos usando o contexto dentro do AuthProvider
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  
  return context;
};

// Exportamos o hook separadamente para evitar problemas com o Fast Refresh
export { useAuth };

interface AuthProviderProps {
  children: ReactNode;
}

// Função para verificar se há um usuário no localStorage
function getUserFromStorage(): User | null {
  const userJson = localStorage.getItem('user');
  if (userJson) {
    try {
      return JSON.parse(userJson);
    } catch (e) {
      console.error('Erro ao analisar o usuário do localStorage', e);
      return null;
    }
  }
  return null;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(getUserFromStorage());
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  
  // Sincronizar estado com localStorage após reloads/HMR
  useEffect(() => {
    const storedUser = getUserFromStorage();
    const token = localStorage.getItem('auth_token');
    
    // Se temos um usuário no storage mas não no estado, restaurar
    if (storedUser && !user && token) {
      console.log('🔄 Restaurando usuário após reload:', storedUser.username);
      setUser(storedUser);
    }
  }, []);  // Executar apenas uma vez no mount
  
  log('AuthProvider rendered');
  
  // Computar o user_id a partir do usuário atual
  const user_id = user ? user.id : null;

  // Salvar/remover o usuário do localStorage quando o estado mudar
  useEffect(() => {
    if (user) {
      localStorage.setItem('user', JSON.stringify(user));
    } else {
      localStorage.removeItem('user');
    }
  }, [user]);

  async function signup(name: string, username: string, email: string, password: string) {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, username, email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao criar conta');
      }

      const data = await response.json();
      
      // Salvar usuário e token no localStorage
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Salvar o token de autenticação recebido do servidor e configurar expiração
      if (data.token) {
        localStorage.setItem('auth_token', data.token);
        
        // Configurar um tempo de expiração para o token (24 horas)
        const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000;
        localStorage.setItem('token_expiration', expirationTime.toString());
      }
      
      // Invalidate all queries to refetch data with new authentication
      await queryClient.invalidateQueries();
      
      toast({
        title: "Conta criada",
        description: "Você se registrou com sucesso!",
      });
    } catch (error: any) {
      console.error('Signup failed:', error);
      toast({
        title: "Erro ao criar conta",
        description: error.message || 'Ocorreu um erro ao criar a conta',
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function login(username: string, password: string) {
    setLoading(true);
    try {
      console.log('[AuthContext] Iniciando requisição de login para:', username);
      
      // Realizar uma requisição para a API de login com timeout e tratamento de erros melhorado
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos timeout
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('[AuthContext] Resposta recebida:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url
      });

      const contentType = response.headers.get('content-type');
      if (!response.ok) {
        let errorMessage = 'Erro ao fazer login';
        if (contentType && contentType.includes('application/json')) {
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
            console.log('[AuthContext] Dados de erro:', errorData);
          } catch (e) {
            throw new Error('Server returned non-JSON response');
          }
        } else {
          const errorText = await response.text();
          errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
          console.log('[AuthContext] Texto de erro:', errorText);
        }
        throw new Error(errorMessage);
      }

      let data;
      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (e) {
          throw new Error('Server returned non-JSON response');
        }
      } else {
        throw new Error('Server returned non-JSON response');
      }
      console.log('[AuthContext] Dados de login recebidos:', {
        user: data.user?.username,
        hasToken: !!data.token,
        tokenLength: data.token?.length
      });
      
      // Validar se recebemos os dados necessários
      if (!data.user || !data.token) {
        throw new Error('Resposta inválida do servidor: dados de autenticação ausentes');
      }
      
      // Salvar o usuário e o token no localStorage
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Salvar o token de autenticação recebido do servidor e configurar expiração
      localStorage.setItem('auth_token', data.token);
      
      // Usar a expiração do servidor se disponível, caso contrário configurar 24 horas
      const expirationTime = data.expiration || (new Date().getTime() + 24 * 60 * 60 * 1000);
      localStorage.setItem('token_expiration', expirationTime.toString());
      
      console.log('[AuthContext] Token e dados salvos no localStorage');
      
      // Invalidate all queries to refetch data with new authentication
      await queryClient.invalidateQueries();
      
      // Force refetch de dados críticos após login bem-sucedido
      setTimeout(() => {
        queryClient.refetchQueries();
      }, 100);
      
      toast({
        title: "Login realizado",
        description: "Você entrou com sucesso!",
      });
    } catch (error: any) {
      console.error('[AuthContext] Login failed:', error);
      
      // Melhor tratamento de erros específicos
      let errorMessage = 'Ocorreu um erro ao fazer login';
      
      if (error.name === 'AbortError') {
        errorMessage = 'Tempo limite da requisição esgotado. Verifique sua conexão.';
      } else if (error.message === 'Failed to fetch') {
        errorMessage = 'Não foi possível conectar ao servidor. Verifique sua conexão de rede.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Falha no login",
        description: errorMessage,
        variant: "destructive"
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  async function logout() {
    try {
      // Limpar o estado do usuário e armazenamento local
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('token_expiration');
      
      // Clear all cached queries on logout
      queryClient.clear();
      
      // Fazer uma requisição para o endpoint de logout (opcional)
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (e) {
        // Ignorar erros na requisição de logout - o importante é limpar o estado local
        console.warn('Erro ao fazer logout no servidor:', e);
      }
      
      toast({
        title: "Logout realizado",
        description: "Você saiu do sistema.",
      });
    } catch (error: any) {
      console.error('Erro no logout:', error);
      toast({
        title: "Falha no logout",
        description: error.message || 'Ocorreu um erro ao fazer logout',
        variant: "destructive"
      });
    }
  }

  const contextValue = useMemo(() => ({
    user,
    loading,
    user_id,
    signup,
    login,
    logout
  }), [user, loading, user_id]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}