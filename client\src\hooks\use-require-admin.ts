import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from './use-auth';

/**
 * Hook que verifica se o usuário atual tem status de administrador
 * e redireciona para a página inicial caso não tenha acesso
 */
export function useRequireAdmin() {
  const [, navigate] = useLocation();
  const { user } = useAuth();
  
  useEffect(() => {
    // Se não há usuário ou o usuário não é admin, redireciona
    if (!user || user.role !== 'ADMIN') {
      console.log('Access denied: user is not admin', { user });
      navigate('/');
    }
  }, [user, navigate]);

  // Retorna se o usuário é admin
  const isAdmin = user?.role === 'ADMIN';
  
  return {
    isAdmin,
    user
  };
}