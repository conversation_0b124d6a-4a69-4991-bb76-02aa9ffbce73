import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

// Esquema para o login com nome de usuário em vez de email
const loginSchema = z.object({
  username: z.string().min(3, "Nome de usuário deve ter pelo menos 3 caracteres"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres"),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

// As credenciais agora são validadas via API (/api/auth/login)
// Exemplo: admin / admin123
const Login = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { login, loading } = useAuth();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      console.log("Login: Tentando fazer login com", data.username);
      console.log("Login: Função login disponível:", typeof login);
      
      // First, let's test the direct API call to see if it works
      console.log("Login: Testando chamada direta à API...");
        const directResponse = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: data.username, password: data.password }),
        });

        console.log("Login: Resposta direta da API:", {
          status: directResponse.status,
          statusText: directResponse.statusText,
          ok: directResponse.ok,
          url: directResponse.url
        });

        const contentType = directResponse.headers.get('content-type');
        let directData: any = null;
        if (directResponse.ok) {
          if (contentType && contentType.includes('application/json')) {
            try {
              directData = await directResponse.json();
              console.log("Login: Dados recebidos da API direta:", directData);
            } catch (e) {
              throw new Error('Server returned non-JSON response');
            }
          } else {
            throw new Error('Server returned non-JSON response');
          }
        } else {
          const errorText = await directResponse.text();
          console.log("Login: Erro da API direta:", errorText);
        }

        // API is working perfectly, save data and redirect
        if (directResponse.ok) {
          console.log("Login: API funcionando, processando dados...");
          if (!directData) {
            if (contentType && contentType.includes('application/json')) {
              try {
                directData = await directResponse.json();
              } catch (e) {
                throw new Error('Server returned non-JSON response');
              }
            } else {
              throw new Error('Server returned non-JSON response');
            }
          }

          // Save authentication data to localStorage
          localStorage.setItem('user', JSON.stringify(directData.user));
          localStorage.setItem('auth_token', directData.token);
          localStorage.setItem('token_expiration', directData.expiration.toString());

          console.log("Login: Dados salvos, redirecionando...");

          // Navigate to dashboard immediately
          window.location.href = '/dashboard';
          return;
        }
      
      // Navigate to dashboard after successful login
      navigate("/dashboard");
    } catch (error: any) {
      console.error("Login: Falha no login:", error);
      console.error("Login: Tipo do erro:", typeof error);
      console.error("Login: Erro stringificado:", JSON.stringify(error));
      
      // Try to get more specific error information
      let errorMessage = "Erro desconhecido";
      if (error && error.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && error.toString) {
        errorMessage = error.toString();
      }
      
      toast({
        title: "Falha no login",
        description: `Não foi possível autenticar: ${errorMessage}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 container-mobile">
      <div className="max-w-md w-full space-y-6 sm:space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 sm:h-16 sm:w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-4 sm:mb-6">
            <svg className="h-6 w-6 sm:h-8 sm:w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Bem-vindo de volta</h2>
          <p className="text-sm sm:text-base text-gray-600">Entre na sua conta para continuar</p>
        </div>

        <div className="bg-white/80 backdrop-blur-sm py-6 px-4 sm:py-8 sm:px-6 shadow-xl rounded-2xl border border-white/20">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Nome de usuário</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Digite seu usuário"
                        autoComplete="username"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Senha</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Digite sua senha"
                        autoComplete="current-password"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox checked={field.value || false} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm text-gray-600">Lembrar de mim</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 sm:py-3 rounded-lg transition-all duration-200 shadow-lg btn-mobile"
                disabled={loading}
              >
                {loading ? "Entrando..." : "Entrar"}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
