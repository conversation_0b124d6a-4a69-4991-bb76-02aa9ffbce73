import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'wouter';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/context/AuthContext';
import { format, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Loader2, ChevronLeft, Save, Search } from 'lucide-react';
import { useABCCCImport } from '@/hooks/use-abccc-import';
import { useToast } from '@/hooks/use-toast';
import { useCavalosDropdown, useCavaloDetalhes, useCriarCavalo, useAtualizarCavalo } from '@/hooks/use-cavalo-formulario';
import { usePelagens } from '@/hooks/use-pelagens';
import { useRacas } from '@/hooks/use-racas';
import HorseAvatarWithUpload from '@/components/horse/HorseAvatarWithUpload';
import { insertCavaloSchema } from '@shared/insert-schemas';

// Função auxiliar para verificar se uma data é válida
const isValidDate = (date: any): boolean => {
  if (!date) return false;
  
  // Verificar se é um objeto Date
  if (date instanceof Date) {
    return isValid(date);
  }
  
  // Verificar se é uma string de data válida
  if (typeof date === 'string') {
    try {
      const parsedDate = new Date(date);
      return isValid(parsedDate);
    } catch (error) {
      return false;
    }
  }
  
  return false;
};

// Função que combina validação e formatação segura de datas
const formatDateSafe = (date: any, formatStr: string): string => {
  if (!date) return '';
  
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    return isValidDate(dateObj) ? format(dateObj, formatStr, { locale: ptBR }) : '';
  } catch (error) {
    console.error("Erro ao formatar data:", error);
    return '';
  }
};
;

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { GenealogiaSelector } from '@/components/cavalo/GenealogiaSelector';
import { EntradaGenealogica } from '@/types/genealogy';

// Tipagem para o formulário usando schema Zod derivado
type CavaloFormValues = z.infer<typeof insertCavaloSchema> & {
  // Campos específicos do formulário que não estão no schema base
  observacoes?: string | null;
  pai_nome?: string | null;
  mae_nome?: string | null;
  avo_paterno?: string | null;
  avo_materno?: string | null;
};

// A função isValidDate foi movida para o início do arquivo

/**
 * Página de formulário de cadastro e edição de cavalos
 * Esta página substitui o antigo componente de formulário em modal
 */
export default function CavaloFormPage() {
  const { id } = useParams<{ id: string }>();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const isEditMode = !!id;
  
  // Estado do usuário
  const [user_id, setUserId] = useState<number | null>(null);
  
  // Estados para genealogia
  const [paiGenealogico, setPaiGenealogico] = useState<EntradaGenealogica | null>(null);
  const [maeGenealogica, setMaeGenealogica] = useState<EntradaGenealogica | null>(null);
  
  // Hook para importação de dados da ABCCC
  const { importData, isLoading: isABCCCLoading } = useABCCCImport();
  
  // Função para buscar dados do cavalo na ABCCC pelo número de registro
  const handleBuscarDadosABCCC = async (registro: string | null | undefined) => {
    if (!registro) {
      toast({
        title: "Erro na busca",
        description: "Digite um número de registro válido",
        variant: "destructive"
      });
      return;
    }
    
    try {
      const dadosABCCC = await importData(registro);
      
      if (dadosABCCC) {
        // Formatar a data de nascimento para o formato aceito pelo formulário
        let dataNascimento = null;
        if (dadosABCCC.nascimento) {
          try {
            const dateObj = new Date(dadosABCCC.nascimento.split('/').reverse().join('-'));
            dataNascimento = isValidDate(dateObj) ? format(dateObj, 'yyyy-MM-dd') : null;
          } catch (error) {
            console.error("Erro ao processar data de nascimento:", error);
            dataNascimento = null;
          }
        }
        
        // Mapear dados retornados para os campos do formulário
        form.setValue("name", dadosABCCC.nome);
        form.setValue("breed", "Crioulo"); // Raça padrão para ABCCC é Crioulo
        form.setValue("birth_date", dataNascimento);
        // Mapear sexo com validação de tipo
        const sexoValido = ['Macho', 'Fêmea', 'Macho (Castrado)', 'Garanhão', 'Égua'].includes(dadosABCCC.sexo) 
          ? dadosABCCC.sexo as 'Macho' | 'Fêmea' | 'Macho (Castrado)' | 'Garanhão' | 'Égua'
          : 'Macho';
        form.setValue("sexo", sexoValido);
        form.setValue("numero_registro", dadosABCCC.registro);
        form.setValue("criador", dadosABCCC.criador);
        form.setValue("proprietario", dadosABCCC.proprietario);
        
        // Verificar se existe uma pelagem correspondente
        if (dadosABCCC.pelagem && pelagens) {
          const pelagemEncontrada = pelagens.find(
            (p) => p.nome.toLowerCase() === dadosABCCC.pelagem.toLowerCase()
          );
          
          if (pelagemEncontrada) {
            form.setValue("pelagem_id", pelagemEncontrada.id);
          }
        }
        
        // Adicionar dados de genealogia se disponíveis
        // Configurar dados do pai (genealogia paterna) se disponíveis
        if (dadosABCCC.pai) {
          console.log("Configurando genealogia do pai:", dadosABCCC.pai);
          
          // Criar objeto com informações estendidas do pai
          const paiGenealogico: EntradaGenealogica = {
            tipo: 'externo',
            cavaloNome: dadosABCCC.pai,
            // Campos específicos da interface estendida
            registro: dadosABCCC.genealogia?.pai?.registro || null,
            sexo: 'macho',
            pelagem: dadosABCCC.genealogia?.pai?.pelagem || null
          };
          
          // Atualizar no componente e no formulário
          setPaiGenealogico(paiGenealogico);
          
          // Para o componente de genealogia, precisamos converter para o tipo base
          const paiParaComponente: EntradaGenealogica = {
            tipo: paiGenealogico.tipo,
            cavaloNome: paiGenealogico.cavaloNome,
            cavaloSistemaId: null
          };
          
          handleGenealogiaChange('pai', paiParaComponente);
          
          // Adicionar avô paterno se disponível
          if (dadosABCCC.avoPaternoNome) {
            console.log("Adicionando avô paterno:", dadosABCCC.avoPaternoNome);
            form.setValue("avo_paterno", dadosABCCC.avoPaternoNome);
          }
        }
        
        // Configurar dados da mãe (genealogia materna) se disponíveis
        if (dadosABCCC.mae) {
          console.log("Configurando genealogia da mãe:", dadosABCCC.mae);
          
          // Criar objeto com informações estendidas da mãe
          const maeGenealogica: EntradaGenealogica = {
            tipo: 'externo',
            cavaloNome: dadosABCCC.mae,
            // Campos específicos da interface estendida
            registro: dadosABCCC.genealogia?.mae?.registro || null,
            sexo: 'femea',
            pelagem: dadosABCCC.genealogia?.mae?.pelagem || null
          };
          
          // Atualizar no componente e no formulário
          setMaeGenealogica(maeGenealogica);
          
          // Para o componente de genealogia, precisamos converter para o tipo base
          const maeParaComponente: EntradaGenealogica = {
            tipo: maeGenealogica.tipo,
            cavaloNome: maeGenealogica.cavaloNome,
            cavaloSistemaId: null
          };
          
          handleGenealogiaChange('mae', maeParaComponente);
          
          // Adicionar avó materna se disponível
          if (dadosABCCC.avoMaternaNome) {
            console.log("Adicionando avó materna:", dadosABCCC.avoMaternaNome);
            form.setValue("avo_materno", dadosABCCC.avoMaternaNome);
          }
        }
        
        toast({
          title: "Dados importados com sucesso",
          description: `Informações do cavalo ${dadosABCCC.nome} foram carregadas`,
          variant: "default"
        });
      }
    } catch (error) {
      toast({
        title: "Erro na importação",
        description: error instanceof Error ? error.message : "Erro ao importar dados da ABCCC",
        variant: "destructive"
      });
    }
  };
  
  // Carregar o ID do usuário do contexto de autenticação
  const { user_id: authUserId } = useAuth();
  
  // Atualizar user_id do estado local com o valor do contexto de autenticação
  useEffect(() => {
    if (authUserId) {
      setUserId(authUserId);
    }
  }, [authUserId]);

  // Buscar dados necessários
  const { cavalos, isLoading: isLoadingCavalos } = useCavalosDropdown();
  const { cavalo: cavaloDetails, isLoading: isLoadingCavaloDetails } = useCavaloDetalhes(isEditMode ? id : null);
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();
  const { racas, isLoading: isRacasLoading } = useRacas();
  
  // Mutation hooks para criar/atualizar
  const createCavalo = useCriarCavalo();
  const updateCavalo = useAtualizarCavalo();
  
  // Estado de submissão
  const [submitting, setSubmitting] = useState(false);

  // Formulário
  const form = useForm<CavaloFormValues>({
    resolver: zodResolver(
      z.object({
        name: z.string().min(2, { message: 'Nome deve ter pelo menos 2 caracteres' }),
        breed: z.string().min(2, { message: 'Raça deve ter pelo menos 2 caracteres' }),
        birth_date: z.string().min(1, { message: 'Data de nascimento é obrigatória' }),
        sexo: z.string().min(1, { message: 'Sexo é obrigatório' }),
        cor: z.string().nullable().optional(),
        pelagem_id: z.number().nullable().optional(),
        peso: z.number().nullable().optional(),
        altura: z.number().nullable().optional(),
        status: z.string().nullable().optional(),
        observacoes: z.string().nullable().optional(),
        origem: z.string().nullable().optional(),
        numero_registro: z.string().nullable().optional(),
        criador: z.string().nullable().optional(),
        proprietario: z.string().nullable().optional(),
        inspetor: z.string().nullable().optional(),
        valor_compra: z.number().nullable().optional(),
        data_compra: z.string().nullable().optional(),
        data_entrada: z.string().nullable().optional(),
        data_saida: z.string().nullable().optional(),
        motivo_saida: z.string().nullable().optional(),
        pai_id: z.number().nullable().optional(),
        pai_nome: z.string().nullable().optional(),
        mae_id: z.number().nullable().optional(),
        mae_nome: z.string().nullable().optional(),
        avo_paterno: z.string().nullable().optional(),
        avo_materno: z.string().nullable().optional(),
        user_id: z.number().optional().default(1),
        is_external: z.boolean().nullable().optional(),
      })
    ),
    defaultValues: {
      name: cavaloDetails?.name || '',
      breed: cavaloDetails?.breed || '',
      birth_date: cavaloDetails?.birth_date ? cavaloDetails.birth_date.split('T')[0] : '',
      sexo: cavaloDetails?.sexo || '',
      cor: cavaloDetails?.cor || null,
      pelagem_id: cavaloDetails?.pelagem_id || null,
      peso: cavaloDetails?.peso || null,
      altura: cavaloDetails?.altura || null,
      status: cavaloDetails?.status || 'ativo',
      observacoes: cavaloDetails?.observacoes || null,
      origem: cavaloDetails?.origem || null,
      numero_registro: cavaloDetails?.numero_registro || null,
      criador: cavaloDetails?.criador || null,
      proprietario: cavaloDetails?.proprietario || null,
      inspetor: cavaloDetails?.inspetor || null,
      valor_compra: cavaloDetails?.valor_compra || null,
      data_compra: cavaloDetails?.data_compra || null,
      data_entrada: cavaloDetails?.data_entrada || null,
      data_saida: cavaloDetails?.data_saida || null,
      motivo_saida: cavaloDetails?.motivo_saida || null,
      pai_id: cavaloDetails?.pai_id || null,
      pai_nome: cavaloDetails?.pai_nome || null,
      mae_id: cavaloDetails?.mae_id || null,
      mae_nome: cavaloDetails?.mae_nome || null,
      avo_paterno: cavaloDetails?.avo_paterno || null,
      avo_materno: cavaloDetails?.avo_materno || null,
      user_id: authUserId || 0,
      is_external: cavaloDetails?.is_external || false,
    },
  });
  
  // Atualizar o formulário quando os dados do cavalo estiverem disponíveis
  useEffect(() => {
    if (cavaloDetails && user_id) {
      form.reset({
        name: cavaloDetails.name,
        breed: cavaloDetails.breed,
        birth_date: cavaloDetails.birth_date ? cavaloDetails.birth_date.split('T')[0] : '',
        sexo: cavaloDetails.sexo,
        cor: cavaloDetails.cor,
        pelagem_id: cavaloDetails.pelagem_id,
        peso: cavaloDetails.peso,
        altura: cavaloDetails.altura,
        status: cavaloDetails.status || 'ativo',
        observacoes: cavaloDetails.observacoes,
        origem: cavaloDetails.origem,
        numero_registro: cavaloDetails.numero_registro,
        criador: cavaloDetails.criador,
        proprietario: cavaloDetails.proprietario,
        inspetor: cavaloDetails.inspetor,
        valor_compra: cavaloDetails.valor_compra,
        data_compra: cavaloDetails.data_compra,
        data_entrada: cavaloDetails.data_entrada,
        data_saida: cavaloDetails.data_saida,
        motivo_saida: cavaloDetails.motivo_saida,
        pai_id: cavaloDetails.pai_id,
        pai_nome: cavaloDetails.pai_nome,
        mae_id: cavaloDetails.mae_id,
        mae_nome: cavaloDetails.mae_nome,
        avo_paterno: cavaloDetails.avo_paterno,
        avo_materno: cavaloDetails.avo_materno,
        user_id: user_id,
        is_external: cavaloDetails.is_external,
      });
    }
  }, [cavaloDetails, user_id, form]);

  // Atualizar user_id no formulário quando ele for carregado
  useEffect(() => {
    if (user_id) {
      form.setValue('user_id', user_id);
    }
  }, [user_id, form]);

  // Inicializador para campos de genealogia
  const inicializarGenealogiaDefault = (
    cavaloId: number | null | undefined,
    cavaloNome: string | null | undefined,
    cavalos: any[],
    sexoFiltro: 'macho' | 'femea'
  ): EntradaGenealogica => {
    console.log('[CavaloFormPage] Inicializando genealogia com:', { cavaloId, cavaloNome });
    
    // Caso 1: Temos um ID válido de cavalo
    if (cavaloId && !isNaN(Number(cavaloId))) {
      const id = Number(cavaloId);
      
      // Buscar em todos os cavalos (plantel e externos)
      const cavaloEncontrado = cavalos.find(c => c.id === id);
      
      if (cavaloEncontrado) {
        // Verificar se é um cavalo do sistema ou externo
        if (cavaloEncontrado.isExternal === true) {
          console.log(`[CavaloFormPage] Encontrado cavalo externo com ID ${id}: ${cavaloEncontrado.name}`);
          // É um cavalo externo com ID
          return {
            tipo: 'externo',
            cavaloSistemaId: id.toString(),
            cavaloNome: cavaloEncontrado.name
          };
        } else {
          console.log(`[CavaloFormPage] Encontrado cavalo do plantel com ID ${id}: ${cavaloEncontrado.name}`);
          // É um cavalo do plantel
          return {
            tipo: 'sistema',
            cavaloSistemaId: id.toString(),
            cavaloNome: cavaloEncontrado.name
          };
        }
      } else {
        console.log(`[CavaloFormPage] Cavalo com ID ${id} não encontrado na lista. Verificando nome...`);
        
        // O cavalo não foi encontrado na lista, mas temos um ID
        if (cavaloNome) {
          console.log(`[CavaloFormPage] Usando ID ${id} com nome ${cavaloNome} como cavalo externo`);
          return {
            tipo: 'externo',
            cavaloSistemaId: id.toString(),
            cavaloNome: cavaloNome
          };
        }
      }
    }
    
    // Caso 2: Não temos ID, mas temos um nome
    if (cavaloNome && 
        cavaloNome !== 'nao_informado' && 
        cavaloNome !== '') {
      console.log(`[CavaloFormPage] Usando apenas o nome para cavalo externo: ${cavaloNome}`);
      // É um nome de cavalo externo sem ID
      return {
        tipo: 'externo',
        cavaloSistemaId: null,
        cavaloNome: cavaloNome
      };
    }
    
    // Caso 3: Não temos nenhuma informação (normal no cadastro)
    return {
      tipo: 'nenhum',
      cavaloSistemaId: null,
      cavaloNome: null
    };
  };

  // Preparar dados de genealogia padrão para o formulário
  const paiDefault = inicializarGenealogiaDefault(
    form.getValues().pai_id,
    form.getValues().pai_nome,
    cavalos,
    'macho'
  );
  
  const maeDefault = inicializarGenealogiaDefault(
    form.getValues().mae_id,
    form.getValues().mae_nome,
    cavalos,
    'femea'
  );

  // Handler para alterações na genealogia
  const handleGenealogiaChange = (tipo: 'pai' | 'mae', entrada: EntradaGenealogica) => {
    console.log(`[CavaloFormPage] Alteração de genealogia (${tipo}):`, entrada);
    
    if (tipo === 'pai') {
      // Para o pai
      if (entrada.tipo === 'sistema' && entrada.cavaloSistemaId) {
        form.setValue('pai_id', Number(entrada.cavaloSistemaId));
        form.setValue('pai_nome', entrada.cavaloNome || null);
      } else if (entrada.tipo === 'externo') {
        form.setValue('pai_id', entrada.cavaloSistemaId ? Number(entrada.cavaloSistemaId) : null);
        form.setValue('pai_nome', entrada.cavaloNome || null);
      } else {
        form.setValue('pai_id', null);
        form.setValue('pai_nome', null);
      }
    } else {
      // Para a mãe
      if (entrada.tipo === 'sistema' && entrada.cavaloSistemaId) {
        form.setValue('mae_id', Number(entrada.cavaloSistemaId));
        form.setValue('mae_nome', entrada.cavaloNome || null);
      } else if (entrada.tipo === 'externo') {
        form.setValue('mae_id', entrada.cavaloSistemaId ? Number(entrada.cavaloSistemaId) : null);
        form.setValue('mae_nome', entrada.cavaloNome || null);
      } else {
        form.setValue('mae_id', null);
        form.setValue('mae_nome', null);
      }
    }
  };

  // Função para validar todas as datas do formulário
  const validarDatas = (data: CavaloFormValues): CavaloFormValues => {
    const dadosValidados = { ...data };
    
    // Validar a data de nascimento
    if (data.birth_date && !isValidDate(new Date(data.birth_date))) {
      dadosValidados.birth_date = null;
      console.warn("Data de nascimento inválida foi removida");
    }
    
    // Validar a data de compra
    if (data.data_compra && !isValidDate(new Date(data.data_compra))) {
      dadosValidados.data_compra = null;
      console.warn("Data de compra inválida foi removida");
    }
    
    // Validar a data de entrada
    if (data.data_entrada && !isValidDate(new Date(data.data_entrada))) {
      dadosValidados.data_entrada = null;
      console.warn("Data de entrada inválida foi removida");
    }
    
    // Validar a data de saída
    if (data.data_saida && !isValidDate(new Date(data.data_saida))) {
      dadosValidados.data_saida = null;
      console.warn("Data de saída inválida foi removida");
    }
    
    return dadosValidados;
  };

  // Submit do formulário
  const onSubmit = async (data: CavaloFormValues) => {
    console.log("🚀 Submit iniciado com dados:", data);
    console.log("🔍 authUserId:", authUserId);
    console.log("🔍 isEditMode:", isEditMode);
    console.log("🔍 form.formState.isValid:", form.formState.isValid);
    console.log("🔍 form.formState.errors:", form.formState.errors);
    
    // Usar user_id padrão se não estiver autenticado (temporário para testes)
    const userIdToUse = authUserId || 1;
    
    setSubmitting(true);
    
    try {
      // Validar e limpar datas inválidas
      const dadosValidados = validarDatas(data);
      
      // Garantir que o user_id está presente
      dadosValidados.user_id = userIdToUse!;
      
      if (isEditMode && id) {
        // Modo de edição
        await updateCavalo.mutateAsync({
          id: Number(id),
          dados: dadosValidados
        });
        toast({
          title: "Sucesso!",
          description: "Cavalo atualizado com sucesso."
        });
      } else {
        // Modo de criação
        await createCavalo.mutateAsync(dadosValidados);
        toast({
          title: "Sucesso!",
          description: "Cavalo cadastrado com sucesso."
        });
      }
      
      // Redirecionar para a lista de cavalos
      navigate('/cavalos');
    } catch (error) {
      console.error("Erro ao salvar cavalo:", error);
      toast({
        title: "Erro",
        description: isEditMode 
          ? "Não foi possível atualizar o cavalo. Tente novamente." 
          : "Não foi possível cadastrar o cavalo. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Renderização de loading
  if ((isEditMode && isLoadingCavaloDetails) || isLoadingCavalos || isRacasLoading || isPelagensLoading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <h2 className="mt-4 text-xl font-semibold">Carregando dados...</h2>
          <p className="text-muted-foreground">Aguarde enquanto preparamos tudo.</p>
        </div>
      </div>
    );
  }

  // Se estiver no modo de edição mas o cavalo não for encontrado
  if (isEditMode && !cavaloDetails && !isLoadingCavaloDetails) {
    return (
      <div className="container mx-auto py-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Cavalo não encontrado</CardTitle>
            <CardDescription>
              Não foi possível encontrar o cavalo com o ID {id}.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/cavalos')} variant="outline">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Voltar para a lista de cavalos
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button 
          variant="outline" 
          onClick={() => navigate('/cavalos')} 
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Voltar para a lista
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? `Editar ${cavaloDetails?.name}` : 'Cadastrar Novo Cavalo'}
        </h1>
        <p className="text-muted-foreground mt-1">
          {isEditMode 
            ? 'Atualize as informações do cavalo conforme necessário' 
            : 'Preencha os dados do novo cavalo para adicionar ao seu plantel'}
        </p>
        {!isEditMode && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">📝 Como cadastrar</h3>
            <p className="text-blue-700 text-base">
              <strong>OBRIGATÓRIO:</strong> Nome, Sexo, Data de Nascimento e Raça<br/>
              <strong>OPCIONAL:</strong> Todos os outros campos podem ficar em branco<br/>
              <em>Dica: Preencha só o básico primeiro, depois você pode editar e completar</em>
            </p>
          </div>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="informacoes" className="w-full">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="informacoes">Informações Básicas</TabsTrigger>
              <TabsTrigger value="caracteristicas">Características</TabsTrigger>
              <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
              <TabsTrigger value="outros">Outros Dados</TabsTrigger>
            </TabsList>
            
            {/* Aba de Informações Básicas */}
            <TabsContent value="informacoes" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Dados Gerais</CardTitle>
                  <CardDescription>
                    Informações básicas de identificação do cavalo
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Avatar com upload */}
                  {isEditMode && id && (
                    <div className="flex justify-center mb-6">
                      <HorseAvatarWithUpload
                        horse_id={parseInt(id)}
                        horseName={form.watch("name") || "Cavalo"}
                        currentPhotoUrl={`/api/cavalos/${id}/photo`}
                        size="lg"
                      />
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Nome */}
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome do Cavalo *</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome completo do cavalo (obrigatório)" {...field} />
                          </FormControl>
                          <FormDescription>
                            Campo obrigatório
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Raça */}
                    <FormField
                      control={form.control}
                      name="breed"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Raça *</FormLabel>
                          <Select
                            disabled={isRacasLoading}
                            onValueChange={field.onChange}
                            value={field.value}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione a raça" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {isRacasLoading ? (
                                <SelectItem value="carregando">Carregando raças...</SelectItem>
                              ) : racas && racas.length > 0 ? (
                                racas.map((raca: string) => (
                                  <SelectItem key={raca} value={raca}>
                                    {raca}
                                  </SelectItem>
                                ))
                              ) : (
                                <SelectItem value="sem-racas">Nenhuma raça encontrada</SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Sexo */}
                    <FormField
                      control={form.control}
                      name="sexo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sexo *</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o sexo" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Macho">Macho</SelectItem>
                              <SelectItem value="Fêmea">Fêmea</SelectItem>
                              <SelectItem value="Garanhão">Garanhão</SelectItem>
                              <SelectItem value="Égua">Égua</SelectItem>
                              <SelectItem value="Macho (Castrado)">Castrado</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Campo obrigatório
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Data de Nascimento */}
                    <FormField
                      control={form.control}
                      name="birth_date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Data de Nascimento *</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              name={field.name}
                              onChange={field.onChange}
                              onBlur={field.onBlur}
                              value={field.value instanceof Date 
                                ? field.value.toISOString().split('T')[0] 
                                : field.value || ''}
                              max={new Date().toISOString().split('T')[0]}
                              min="1900-01-01"
                            />
                          </FormControl>
                          <FormDescription>
                            Campo obrigatório
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Registro */}
                    <FormField
                      control={form.control}
                      name="numero_registro"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Número de Registro</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: B405132" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Status */}
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value || "ativo"}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="ativo">Ativo</SelectItem>
                              <SelectItem value="vendido">Vendido</SelectItem>
                              <SelectItem value="falecido">Falecido</SelectItem>
                              <SelectItem value="doado">Doado</SelectItem>
                              <SelectItem value="reprodução">Reprodução</SelectItem>
                              <SelectItem value="competição">Competição</SelectItem>
                              <SelectItem value="tratamento">Tratamento</SelectItem>
                              <SelectItem value="aposentado">Aposentado</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Origem */}
                    <FormField
                      control={form.control}
                      name="origem"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Haras/Fazenda de Origem</FormLabel>
                          <FormControl>
                            <Input placeholder="Origem do cavalo" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Proprietário */}
                    <FormField
                      control={form.control}
                      name="proprietario"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Proprietário</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome do proprietário atual" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Criador */}
                    <FormField
                      control={form.control}
                      name="criador"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Criador</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome do criador original" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Inspetor */}
                    <FormField
                      control={form.control}
                      name="inspetor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Inspetor Técnico</FormLabel>
                          <FormControl>
                            <Input placeholder="Inspetor técnico da ABCCC" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Aba de Características */}
            <TabsContent value="caracteristicas" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Características Físicas</CardTitle>
                  <CardDescription>
                    Descrição física e detalhes do cavalo
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Pelagem */}
                    <FormField
                      control={form.control}
                      name="pelagem_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pelagem</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              // Converter valor para number ou null
                              const pelagem_id = value === "nao_informado" ? null : Number(value);
                              field.onChange(pelagem_id);
                            }}
                            value={field.value ? String(field.value) : "nao_informado"}
                            disabled={isPelagensLoading}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={isPelagensLoading ? "Carregando pelagens..." : "Selecione a pelagem"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="nao_informado">Não informado</SelectItem>
                              
                              {/* Pelagens carregadas do banco de dados */}
                              {pelagens.map((pelagem) => (
                                <SelectItem key={pelagem.id} value={String(pelagem.id)}>
                                  {pelagem.nome}
                                </SelectItem>
                              ))}
                              
                              {/* Se não houver pelagens, exibe uma mensagem */}
                              {!isPelagensLoading && pelagens.length === 0 && (
                                <SelectItem value="outros" disabled>
                                  Nenhuma pelagem cadastrada
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Peso */}
                    <FormField
                      control={form.control}
                      name="peso"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Peso (kg)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number"
                              step="0.1"
                              placeholder="Peso em quilogramas"
                              {...field}
                              onChange={(e) => {
                                const value = e.target.value ? parseFloat(e.target.value) : null;
                                field.onChange(value);
                              }}
                              value={field.value ?? ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Altura */}
                    <FormField
                      control={form.control}
                      name="altura"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Altura (m)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number"
                              step="0.01"
                              placeholder="Altura em metros" 
                              {...field}
                              onChange={(e) => {
                                const value = e.target.value ? parseFloat(e.target.value) : null;
                                field.onChange(value);
                              }}
                              value={field.value ?? ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  {/* Observações */}
                  <FormField
                    control={form.control}
                    name="observacoes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Observações</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Observações adicionais sobre o cavalo" 
                            className="min-h-[100px]"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Aba de Genealogia */}
            <TabsContent value="genealogia" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Genealogia</CardTitle>
                  <CardDescription>
                    Informações sobre os pais e ancestrais do cavalo
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Pai</h3>
                    <GenealogiaSelector
                      value={paiDefault}
                      onChange={(entrada) => handleGenealogiaChange('pai', entrada)}
                      cavalos={cavalos}
                      sexoFiltro="macho"
                      label="Pai"
                      description="Selecione um cavalo do sistema ou informe um cavalo externo"
                      className="border p-4 rounded-md"
                      disabled={isLoadingCavalos}
                    />
                  </div>
                  
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-3">Mãe</h3>
                    <GenealogiaSelector
                      value={maeDefault}
                      onChange={(entrada) => handleGenealogiaChange('mae', entrada)}
                      cavalos={cavalos}
                      sexoFiltro="femea"
                      label="Mãe"
                      description="Selecione uma égua do sistema ou informe uma égua externa"
                      className="border p-4 rounded-md"
                      disabled={isLoadingCavalos}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    {/* Avô Paterno */}
                    <FormField
                      control={form.control}
                      name="avo_paterno"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Avô Paterno</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome do avô paterno" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Avô Materno */}
                    <FormField
                      control={form.control}
                      name="avo_materno"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Avô Materno</FormLabel>
                          <FormControl>
                            <Input placeholder="Nome do avô materno" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Aba de Outros Dados */}
            <TabsContent value="outros" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Dados Financeiros e de Movimentação</CardTitle>
                  <CardDescription>
                    Informações sobre compra, venda e movimentação do cavalo
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Valor de Compra */}
                    <FormField
                      control={form.control}
                      name="valor_compra"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Compra (R$)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number"
                              step="0.01"
                              placeholder="Valor em reais"
                              {...field}
                              onChange={(e) => {
                                const value = e.target.value ? parseFloat(e.target.value) : null;
                                field.onChange(value);
                              }}
                              value={field.value ?? ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Data de Compra */}
                    <FormField
                      control={form.control}
                      name="data_compra"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Data de Compra</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(new Date(field.value), "dd/MM/yyyy", { locale: ptBR })
                                  ) : (
                                    <span>Selecione uma data</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value ? new Date(field.value) : undefined}
                                onSelect={(date) => field.onChange(date ? format(date, "yyyy-MM-dd") : null)}
                                disabled={(date) =>
                                  date > new Date()
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Data de Entrada */}
                    <FormField
                      control={form.control}
                      name="data_entrada"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Data de Entrada</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(new Date(field.value), "dd/MM/yyyy", { locale: ptBR })
                                  ) : (
                                    <span>Selecione uma data</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value ? new Date(field.value) : undefined}
                                onSelect={(date) => field.onChange(date ? format(date, "yyyy-MM-dd") : null)}
                                disabled={(date) =>
                                  date > new Date()
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            Data de entrada no haras/fazenda atual
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Data de Saída */}
                    <FormField
                      control={form.control}
                      name="data_saida"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Data de Saída</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(new Date(field.value), "dd/MM/yyyy", { locale: ptBR })
                                  ) : (
                                    <span>Selecione uma data</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value ? new Date(field.value) : undefined}
                                onSelect={(date) => field.onChange(date ? format(date, "yyyy-MM-dd") : null)}
                                disabled={(date) =>
                                  date > new Date()
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormDescription>
                            Data de saída ou venda, se aplicável
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  {/* Motivo da Saída */}
                  <FormField
                    control={form.control}
                    name="motivo_saida"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Motivo da Saída</FormLabel>
                        <FormControl>
                          <Input placeholder="Motivo da saída ou venda" {...field} value={field.value || ''} />
                        </FormControl>
                        <FormDescription>
                          Razão pela qual o cavalo saiu do plantel, se aplicável
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <div className="flex items-center justify-end space-x-4 pt-4">
            <Button 
              variant="outline" 
              type="button" 
              onClick={() => navigate('/cavalos')}
            >
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={submitting}
              className="min-w-[120px]"
              onClick={(e) => {
                console.log("🔧 Botão clicado! Verificando validação...");
                console.log("🔧 Form válido?", form.formState.isValid);
                console.log("🔧 Form errors:", form.formState.errors);
                console.log("🔧 Form values:", form.getValues());
                // Não prevenir default - deixar o form.handleSubmit fazer seu trabalho
              }}
            >
              {submitting ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditMode ? 'Atualizar' : 'Cadastrar'}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}