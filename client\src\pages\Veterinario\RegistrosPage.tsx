import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { logger, logError } from '@/lib/logger';
import { LayoutWrapper } from '@/components/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/debug-select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, Eye, FileText, AlertCircle } from 'lucide-react';
import { ProcedimentoVet, Cavalo } from '@shared/schema';
import { AuthenticationErrorHandler, isAuthenticationError } from '@/components/auth/AuthenticationErrorHandler';
import { ProcedimentoVetFormModern } from '@/components/veterinario/ProcedimentoVetFormModern';
import { useAuth } from '@/hooks/use-auth';

// Funções utilitárias para formatação
const formatarData = (data: string | Date | null) => {
  if (!data) return 'N/A';
  try {
    const date = typeof data === 'string' ? new Date(data) : data;
    return format(date, 'dd/MM/yyyy', { locale: ptBR });
  } catch {
    return 'N/A';
  }
};

const truncarTexto = (text: string, maxLength: number) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Página de Registros Clínicos Veterinários
 * Exibe listagem de registros clínicos com opções de filtro
 */
export default function RegistrosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCavalo, setFilterCavalo] = useState('todos');
  const [filterTipo, setFilterTipo] = useState('todos');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Mutação para criar novo procedimento
  const addProcedimentoMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/procedimentos-vet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': user?.id?.toString() || '1'
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Erro ao criar procedimento');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
      setIsAddDialogOpen(false);
    },
    onError: (error) => {
      logError('veterinario.registros', 'Erro ao criar procedimento', {}, error as Error);
    }
  });

  // Handler para adicionar novo registro
  const handleAddSubmit = (values: any) => {
    addProcedimentoMutation.mutate({
      ...values,
      user_id: user?.id
    });
  };

  // Buscar dados de procedimentos veterinários
  const { 
    data: procedimentos = [], 
    isLoading: loadingProcedimentos,
    error: procedimentosError
  } = useQuery<ProcedimentoVet[]>({
    queryKey: ['/api/procedimentos-vet'],
    retry: 1,
    // Logging via useEffect abaixo para compatibilidade com React Query v5
  });

  // Buscar dados de cavalos para o filtro
  const { 
    data: cavalos = [], 
    isLoading: loadingCavalos,
    error: cavalosError
  } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
    retry: 1,
    // Logging via useEffect abaixo para compatibilidade com React Query v5
  });
  
  // Log de dados carregados
  useEffect(() => {
    if (Array.isArray(procedimentos) && procedimentos.length > 0) {
      logger.info('veterinario.registros', `Carregados ${procedimentos.length} procedimentos veterinários`);
    }
    if (Array.isArray(cavalos) && cavalos.length > 0) {
      logger.info('veterinario.registros', `Carregados ${cavalos.length} cavalos`);
    }
  }, [procedimentos, cavalos]);

  // Log de erros nas queries
  useEffect(() => {
    if (procedimentosError) {
      logError('veterinario.registros', 'Erro na consulta de procedimentos', {}, procedimentosError as Error);
    }
    if (cavalosError) {
      logError('veterinario.registros', 'Erro na consulta de cavalos', {}, cavalosError as Error);
    }
  }, [procedimentosError, cavalosError]);

  // Filtrar registros clínicos (procedimentos do tipo exame ou consulta)
  const registrosFiltrados = React.useMemo(() => {
    // Verificar se procedimentos é um array válido
    if (!Array.isArray(procedimentos)) {
      logger.warn('veterinario.registros', 'Procedimentos não é um array válido', {
        tipoRecebido: typeof procedimentos,
        valorRecebido: procedimentos
      });
      return [];
    }

    // Primeiro filtro - procedimentos do tipo exame ou consulta
    const registrosTemp = procedimentos.filter((proc: ProcedimentoVet) => {
      if (!proc || !proc.tipo) return false;
      
      const tipoLowerCase = proc.tipo?.toLowerCase() || '';
      return (
        tipoLowerCase.includes('exame') || 
        tipoLowerCase.includes('consult') ||
        tipoLowerCase.includes('check')
      );
    });
    
    // Segundo filtro - critérios de busca e filtros de UI
    return registrosTemp.filter((proc: ProcedimentoVet) => {
      if (!proc) return false;
      
      const matchesSearch = searchTerm === '' || 
        ((proc.descricao?.toLowerCase() || '').includes(searchTerm.toLowerCase())) ||
        ((proc.veterinario?.toLowerCase() || '').includes(searchTerm.toLowerCase()));
      
      const matchesCavalo = filterCavalo === 'todos' || 
                           (proc.horse_id && proc.horse_id.toString() === filterCavalo);
      
      const matchesTipo = filterTipo === 'todos' || 
                         (proc.tipo && proc.tipo?.toLowerCase().includes(filterTipo.toLowerCase()));
      
      return matchesSearch && matchesCavalo && matchesTipo;
    });
  }, [procedimentos, searchTerm, filterCavalo, filterTipo]);

  // Obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    if (!Array.isArray(cavalos)) return 'N/A';
    
    const cavalo = cavalos.find((c: Cavalo) => c && c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };

  // Tipos únicos de procedimentos para o filtro
  const tiposProcedimentos = React.useMemo(() => {
    if (!Array.isArray(procedimentos)) {
      logger.warn('veterinario.registros', 'Procedimentos não é um array válido para tiposProcedimentos', {
        tipoRecebido: typeof procedimentos
      });
      return [];
    }
    
    return Array.from(new Set(
      procedimentos
        .filter((proc: ProcedimentoVet) => {
          if (!proc || !proc.tipo) return false;
          
          const tipoLowerCase = proc.tipo?.toLowerCase() || '';
          return (
            tipoLowerCase.includes('exame') || 
            tipoLowerCase.includes('consult') ||
            tipoLowerCase.includes('check')
          );
        })
        .map((proc: ProcedimentoVet) => proc.tipo)
    )).filter((tipo: string) => tipo && tipo.trim() !== '');
  }, [procedimentos]);

  // Verificar se há erros de autenticação
  if (isAuthenticationError(procedimentosError) || isAuthenticationError(cavalosError)) {
    return (
      <LayoutWrapper pageTitle="Registros Clínicos" showBackButton backUrl="/veterinario">
        <div className="container mx-auto py-10">
          <AuthenticationErrorHandler 
            error={procedimentosError || cavalosError} 
            message="Sua sessão expirou ou você não está autenticado. Para visualizar os registros clínicos, faça login novamente."
          />
        </div>
      </LayoutWrapper>
    );
  }

  return (
    <LayoutWrapper pageTitle="Registros Clínicos" showBackButton backUrl="/veterinario">
      <div className="container mx-auto py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filtros</CardTitle>
            <CardDescription>Filtre os registros clínicos por cavalo, tipo ou termo de busca</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Buscar</label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar em registros..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Cavalo</label>
                <Select 
                  value={filterCavalo} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.registros', `Filtro de cavalo alterado para: ${value}`, {
                      previousValue: filterCavalo,
                      newValue: value
                    });
                    setFilterCavalo(value);
                  }}
                  debugId="filtro-cavalo-registros"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cavalo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos" debugId="todos-cavalo-reg">Todos</SelectItem>
                    {Array.isArray(cavalos) && cavalos.map((cavalo: Cavalo) => (
                      cavalo && cavalo.id && (
                        <SelectItem 
                          key={`cavalo-${cavalo.id}`} 
                          value={cavalo.id.toString()}
                          debugId={`cavalo-reg-${cavalo.id}`}
                        >
                          {cavalo.name || `Cavalo ${cavalo.id}`}
                        </SelectItem>
                      )
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo de Registro</label>
                <Select 
                  value={filterTipo} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.registros', `Filtro de tipo alterado para: ${value}`, {
                      previousValue: filterTipo,
                      newValue: value
                    });
                    setFilterTipo(value);
                  }}
                  debugId="filtro-tipo-registros"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos" debugId="todos-tipo-reg">Todos</SelectItem>
                    {Array.isArray(tiposProcedimentos) && tiposProcedimentos.map((tipo: string) => (
                      <SelectItem 
                        key={`tipo-${tipo}`} 
                        value={tipo || "tipo_nao_especificado"}
                        debugId={`tipo-reg-${tipo.replace(/\s+/g, '-').toLowerCase()}`}
                      >
                        {tipo || "Não especificado"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Registros Clínicos</h2>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Novo Registro
          </Button>
        </div>

        {isAddDialogOpen && (
          <ProcedimentoVetFormModern
            onSubmit={handleAddSubmit}
            isSubmitting={addProcedimentoMutation.isPending}
            cavalos={cavalos || []}
            user_id={user?.id || 0}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        )}

        {loadingProcedimentos ? (
          <div className="text-center py-10">Carregando registros...</div>
        ) : registrosFiltrados.length > 0 ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Cavalo</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Veterinário</TableHead>
                  <TableHead>Resultado</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(registrosFiltrados) && registrosFiltrados.map((registro: ProcedimentoVet) => {
                  if (!registro || !registro.id) return null;
                  
                  return (
                    <TableRow key={`registro-${registro.id}`}>
                      <TableCell>{formatarData(registro.data)}</TableCell>
                      <TableCell>{getCavaloName(registro.horse_id)}</TableCell>
                      <TableCell>{registro.tipo || 'N/A'}</TableCell>
                      <TableCell>{truncarTexto(registro.descricao || '', 50)}</TableCell>
                      <TableCell>{registro.veterinario || 'N/A'}</TableCell>
                      <TableCell>{truncarTexto(registro.resultado || 'N/A', 30)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-muted-foreground">Nenhum registro clínico encontrado.</p>
            <Button variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> Criar Primeiro Registro
            </Button>
          </div>
        )}
      </div>
    </LayoutWrapper>
  );
}

