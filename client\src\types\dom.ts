/**
 * Extensões de tipos DOM para suporte multiplataforma
 * 
 * Este arquivo adiciona tipos para propriedades específicas de navegadores
 * relacionadas a APIs como Fullscreen que têm implementações diferentes
 * de acordo com o navegador.
 */

// Extensão para Document com suporte à API Fullscreen multiplataforma
interface ExtendedDocument extends Document {
  // Propriedades de verificação se fullscreen é habilitado
  webkitFullscreenEnabled?: boolean;
  mozFullScreenEnabled?: boolean;
  msFullscreenEnabled?: boolean;
  
  // Elementos atualmente em fullscreen
  webkitFullscreenElement?: Element | null;
  mozFullScreenElement?: Element | null;
  msFullscreenElement?: Element | null;
  
  // Métodos para sair do fullscreen
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

// Extensão para HTMLElement com suporte à API Fullscreen multiplataforma
interface ExtendedHTMLElement extends HTMLElement {
  // Métodos para entrar em fullscreen
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

export {
  ExtendedDocument,
  ExtendedHTMLElement
};