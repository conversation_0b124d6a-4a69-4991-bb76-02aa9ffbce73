/**
 * Custom API router that bypasses Vite middleware
 * Ensures API routes are handled directly by Express
 */
import express from 'express';
import { addApiRoutes } from './routes.js';

export function createApiRouter(): express.Router {
  const apiRouter = express.Router();
  
  // Add all API routes to this router
  addApiRoutes(apiRouter as any);
  
  return apiRouter;
}

export function setupApiMiddleware(app: express.Express): void {
  // Mount API router with highest priority
  app.use('/api', createApiRouter());
  
  // Catch-all for API routes that don't exist
  app.use('/api/*', (req, res) => {
    res.status(404).json({ 
      error: 'API endpoint not found',
      path: req.originalUrl 
    });
  });
}