import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  UserPlus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  UserCog, 
  User, 
  Shield, 
  Mail, 
  Key,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { LayoutWrapper } from '@/components/Layout';

/**
 * Interface para definir a estrutura de um usuário
 */
interface Usuario {
  id: number;
  nome: string;
  email: string;
  cargo: string;
  perfil: 'admin' | 'gerente' | 'veterinario' | 'tratador' | 'financeiro' | 'visualizador';
  status: 'ativo' | 'inativo' | 'bloqueado';
  ultimoAcesso: string | null;
  dataCadastro: string;
}

/**
 * Componente principal da página de Usuários
 */
export function UsuariosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [perfilFilter, setPerfilFilter] = useState('todos');
  const [statusFilter, setStatusFilter] = useState('todos');
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  
  // Estados para o formulário de novo usuário
  const [novoUsuario, setNovoUsuario] = useState({
    nome: '',
    email: '',
    cargo: '',
    senha: '',
    confirmarSenha: '',
    perfil: 'visualizador',
    status: 'ativo',
    enviarEmail: true
  });

  // Filtrar usuários com base nos filtros aplicados
  const filteredUsuarios = usuarios.filter(usuario => {
    const matchesSearch = 
      usuario.nome.toLowerCase().includes(searchTerm.toLowerCase()) || 
      usuario.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      usuario.cargo.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPerfil = perfilFilter === 'todos' || usuario.perfil === perfilFilter;
    const matchesStatus = statusFilter === 'todos' || usuario.status === statusFilter;
    
    return matchesSearch && matchesPerfil && matchesStatus;
  });

  // Função para obter a cor do badge de status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ativo':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'inativo':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      case 'bloqueado':
        return 'bg-red-100 text-red-800 hover:bg-red-100';
      default:
        return '';
    }
  };

  // Função para obter a cor do badge de perfil
  const getPerfilColor = (perfil: string) => {
    switch (perfil) {
      case 'admin':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
      case 'gerente':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'veterinario':
        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-100';
      case 'tratador':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-100';
      case 'financeiro':
        return 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100';
      case 'visualizador':
        return 'bg-slate-100 text-slate-800 hover:bg-slate-100';
      default:
        return '';
    }
  };

  // Função para formatar a data
  const formatarData = (dataString: string | null) => {
    if (!dataString) return 'Nunca';
    const data = new Date(dataString);
    return data.toLocaleDateString('pt-BR') + ' ' + data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Função para lidar com mudanças no formulário de novo usuário
  const handleNovoUsuarioChange = (campo: string, valor: any) => {
    setNovoUsuario(prev => ({
      ...prev,
      [campo]: valor
    }));
  };

  // Função para adicionar um novo usuário
  const handleAddUser = () => {
    console.log('Novo usuário:', novoUsuario);
    // Aqui seria implementada a lógica para salvar no backend
    
    // Resetar o formulário e fechar o diálogo
    setNovoUsuario({
      nome: '',
      email: '',
      cargo: '',
      senha: '',
      confirmarSenha: '',
      perfil: 'visualizador',
      status: 'ativo',
      enviarEmail: true
    });
    setIsAddUserOpen(false);
    
    // Exibir notificação de sucesso (em uma implementação real)
    alert('Usuário adicionado com sucesso!');
  };

  return (
    <LayoutWrapper pageTitle="Usuários">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Gerenciamento de Usuários</h1>
            <p className="text-muted-foreground">
              Gerencie o acesso de usuários ao sistema
            </p>
          </div>
          
          <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                <span>Adicionar Usuário</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Usuário</DialogTitle>
                <DialogDescription>
                  Preencha as informações para adicionar um novo usuário ao sistema
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome Completo</Label>
                    <Input 
                      id="nome" 
                      placeholder="Nome do usuário" 
                      value={novoUsuario.nome}
                      onChange={(e) => handleNovoUsuarioChange('nome', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">E-mail</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="<EMAIL>" 
                      value={novoUsuario.email}
                      onChange={(e) => handleNovoUsuarioChange('email', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cargo">Cargo</Label>
                    <Input 
                      id="cargo" 
                      placeholder="Cargo na empresa" 
                      value={novoUsuario.cargo}
                      onChange={(e) => handleNovoUsuarioChange('cargo', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="perfil">Perfil de Acesso</Label>
                    <Select 
                      value={novoUsuario.perfil}
                      onValueChange={(value) => handleNovoUsuarioChange('perfil', value)}
                    >
                      <SelectTrigger id="perfil">
                        <SelectValue placeholder="Selecione um perfil" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Administrador</SelectItem>
                        <SelectItem value="gerente">Gerente</SelectItem>
                        <SelectItem value="veterinario">Veterinário</SelectItem>
                        <SelectItem value="tratador">Tratador</SelectItem>
                        <SelectItem value="financeiro">Financeiro</SelectItem>
                        <SelectItem value="visualizador">Visualizador</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="senha">Senha</Label>
                    <Input 
                      id="senha" 
                      type="password" 
                      placeholder="Senha" 
                      value={novoUsuario.senha}
                      onChange={(e) => handleNovoUsuarioChange('senha', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmarSenha">Confirmar Senha</Label>
                    <Input 
                      id="confirmarSenha" 
                      type="password" 
                      placeholder="Confirme a senha" 
                      value={novoUsuario.confirmarSenha}
                      onChange={(e) => handleNovoUsuarioChange('confirmarSenha', e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={novoUsuario.status}
                    onValueChange={(value) => handleNovoUsuarioChange('status', value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Selecione um status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ativo">Ativo</SelectItem>
                      <SelectItem value="inativo">Inativo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 pt-2">
                  <Switch 
                    id="enviarEmail" 
                    checked={novoUsuario.enviarEmail}
                    onCheckedChange={(checked) => handleNovoUsuarioChange('enviarEmail', checked)}
                  />
                  <Label htmlFor="enviarEmail">Enviar e-mail de boas-vindas com instruções de acesso</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddUserOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleAddUser}>
                  Adicionar Usuário
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Card de filtros e busca */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Filtros</CardTitle>
            <CardDescription>
              Busque e filtre usuários por diferentes critérios
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar por nome, e-mail ou cargo..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <Select 
                value={perfilFilter} 
                onValueChange={setPerfilFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por Perfil" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os Perfis</SelectItem>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="gerente">Gerente</SelectItem>
                  <SelectItem value="veterinario">Veterinário</SelectItem>
                  <SelectItem value="tratador">Tratador</SelectItem>
                  <SelectItem value="financeiro">Financeiro</SelectItem>
                  <SelectItem value="visualizador">Visualizador</SelectItem>
                </SelectContent>
              </Select>
              
              <Select 
                value={statusFilter} 
                onValueChange={setStatusFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filtrar por Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os Status</SelectItem>
                  <SelectItem value="ativo">Ativo</SelectItem>
                  <SelectItem value="inativo">Inativo</SelectItem>
                  <SelectItem value="bloqueado">Bloqueado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de usuários */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Usuários do Sistema</CardTitle>
            <CardDescription>
              {filteredUsuarios.length} usuários encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome / E-mail</TableHead>
                    <TableHead>Cargo</TableHead>
                    <TableHead>Perfil</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Último Acesso</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsuarios.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center gap-2">
                          <User className="h-8 w-8 text-muted-foreground/60" />
                          <p className="text-muted-foreground font-medium">Nenhum usuário encontrado</p>
                          <p className="text-sm text-muted-foreground">
                            Tente ajustar os filtros ou adicione um novo usuário
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsuarios.map(usuario => (
                      <TableRow key={usuario.id}>
                        <TableCell>
                          <div className="font-medium">{usuario.nome}</div>
                          <div className="text-sm text-muted-foreground">{usuario.email}</div>
                        </TableCell>
                        <TableCell>{usuario.cargo}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getPerfilColor(usuario.perfil)}>
                            {usuario.perfil === 'admin' && 'Administrador'}
                            {usuario.perfil === 'gerente' && 'Gerente'}
                            {usuario.perfil === 'veterinario' && 'Veterinário'}
                            {usuario.perfil === 'tratador' && 'Tratador'}
                            {usuario.perfil === 'financeiro' && 'Financeiro'}
                            {usuario.perfil === 'visualizador' && 'Visualizador'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {usuario.status === 'ativo' && (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            )}
                            {usuario.status === 'inativo' && (
                              <XCircle className="h-4 w-4 text-gray-500" />
                            )}
                            {usuario.status === 'bloqueado' && (
                              <Shield className="h-4 w-4 text-red-600" />
                            )}
                            <Badge variant="outline" className={getStatusColor(usuario.status)}>
                              {usuario.status === 'ativo' && 'Ativo'}
                              {usuario.status === 'inativo' && 'Inativo'}
                              {usuario.status === 'bloqueado' && 'Bloqueado'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>{formatarData(usuario.ultimoAcesso)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <span className="sr-only">Abrir menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                <span>Editar</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <UserCog className="mr-2 h-4 w-4" />
                                <span>Permissões</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Key className="mr-2 h-4 w-4" />
                                <span>Redefinir Senha</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail className="mr-2 h-4 w-4" />
                                <span>Enviar E-mail</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {usuario.status === 'ativo' ? (
                                <DropdownMenuItem>
                                  <XCircle className="mr-2 h-4 w-4 text-amber-600" />
                                  <span className="text-amber-600">Desativar</span>
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem>
                                  <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                  <span className="text-green-600">Ativar</span>
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem>
                                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                                <span className="text-red-600">Excluir</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}

// Dados de exemplo para usuários (apenas para demonstração)
const usuarios: Usuario[] = [
  {
    id: 1,
    nome: 'Administrador',
    email: '<EMAIL>',
    cargo: 'Administrador do Sistema',
    perfil: 'admin',
    status: 'ativo',
    ultimoAcesso: '2025-03-30T10:30:00',
    dataCadastro: '2025-01-01T00:00:00'
  },
  {
    id: 2,
    nome: 'João Silva',
    email: '<EMAIL>',
    cargo: 'Gerente de Haras',
    perfil: 'gerente',
    status: 'ativo',
    ultimoAcesso: '2025-03-29T15:45:00',
    dataCadastro: '2025-01-15T00:00:00'
  },
  {
    id: 3,
    nome: 'Maria Oliveira',
    email: '<EMAIL>',
    cargo: 'Veterinária Chefe',
    perfil: 'veterinario',
    status: 'ativo',
    ultimoAcesso: '2025-03-30T08:15:00',
    dataCadastro: '2025-01-20T00:00:00'
  },
  {
    id: 4,
    nome: 'Carlos Santos',
    email: '<EMAIL>',
    cargo: 'Tratador Principal',
    perfil: 'tratador',
    status: 'ativo',
    ultimoAcesso: '2025-03-28T17:30:00',
    dataCadastro: '2025-02-05T00:00:00'
  },
  {
    id: 5,
    nome: 'Fernanda Costa',
    email: '<EMAIL>',
    cargo: 'Contadora',
    perfil: 'financeiro',
    status: 'ativo',
    ultimoAcesso: '2025-03-29T09:20:00',
    dataCadastro: '2025-02-10T00:00:00'
  },
  {
    id: 6,
    nome: 'Roberto Almeida',
    email: '<EMAIL>',
    cargo: 'Veterinário',
    perfil: 'veterinario',
    status: 'inativo',
    ultimoAcesso: '2025-02-15T14:10:00',
    dataCadastro: '2025-01-25T00:00:00'
  },
  {
    id: 7,
    nome: 'Amanda Ferreira',
    email: '<EMAIL>',
    cargo: 'Tratadora',
    perfil: 'tratador',
    status: 'bloqueado',
    ultimoAcesso: '2025-01-20T11:45:00',
    dataCadastro: '2025-01-10T00:00:00'
  },
  {
    id: 8,
    nome: 'Pedro Henrique',
    email: '<EMAIL>',
    cargo: 'Estagiário',
    perfil: 'visualizador',
    status: 'ativo',
    ultimoAcesso: '2025-03-30T09:00:00',
    dataCadastro: '2025-03-01T00:00:00'
  }
];

export default UsuariosPage;