import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export interface Pelagem {
  id: number;
  nome: string;
  slug: string;
  descricao?: string;
  fonte?: string;
}

/**
 * Hook para buscar pelagens com autocomplete
 * @param search Termo de busca
 * @param limit Limite de resultados
 * @returns Pelagens encontradas
 */
export function usePelagens(search: string = '', limit: number = 20) {
  const query = useQuery({
    queryKey: ['/api/pelagens', { search, limit }],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (limit) params.append('limit', limit.toString());
      
      const response = await fetch(`/api/pelagens?${params}`, {
        headers: {
          'Content-Type': 'application/json',
          'User-Id': '1'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao buscar pelagens');
      }
      
      const result = await response.json();
      return result.success ? result.data : [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false,
    enabled: true
  });

  return {
    pelagens: Array.isArray(query.data) ? query.data : [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
  };
}

/**
 * Hook para buscar uma pelagem específica por ID
 * @param id ID da pelagem
 * @returns Dados da pelagem
 */
export function usePelagemById(id: number | undefined) {
  const query = useQuery({
    queryKey: ['/api/pelagens', id],
    queryFn: async () => {
      if (!id) return null;
      
      const response = await fetch(`/api/pelagens/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'User-Id': '1'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao buscar pelagem');
      }
      
      const result = await response.json();
      return result.success ? result.data : null;
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    enabled: !!id
  });

  return {
    pelagem: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
  };
}

/**
 * Hook para criar nova pelagem
 */
export function useCreatePelagem() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (nome: string) => {
      const response = await fetch('/api/pelagens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Id': '1'
        },
        body: JSON.stringify({ nome })
      });
      
      if (!response.ok) {
        throw new Error('Erro ao criar pelagem');
      }
      
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Erro ao criar pelagem');
      }
      
      return result.data;
    },
    onSuccess: () => {
      // Invalidar cache de pelagens para atualizar a lista
      queryClient.invalidateQueries({ queryKey: ['/api/pelagens'] });
    }
  });
}