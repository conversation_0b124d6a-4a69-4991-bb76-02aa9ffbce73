import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { TouchModal } from "@/components/ui/touch-modal"

const DialogResponsive = DialogPrimitive.Root

const DialogResponsiveTrigger = DialogPrimitive.Trigger

const DialogResponsivePortal = DialogPrimitive.Portal

const DialogResponsiveClose = DialogPrimitive.Close

const DialogResponsiveOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogResponsiveOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogResponsiveContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => {
  const isMobile = useIsMobile();
  const [open, setOpen] = React.useState(true);

  // Função para fechar o modal com gesto touch
  const handleTouchClose = React.useCallback(() => {
    if (props.onOpenChange) {
      props.onOpenChange(false);
    }
  }, [props.onOpenChange]);

  const content = (
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed z-50 grid w-full gap-4 border bg-background shadow-lg duration-200",
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        isMobile ? [
          // Mobile styles with margin and scrolling
          "inset-0 m-2 h-[calc(100dvh-1rem)] rounded-none p-4 overflow-y-auto overscroll-contain",
          "data-[state=closed]:slide-out-to-bottom-[100%] data-[state=open]:slide-in-from-bottom-[100%]"
        ] : [
          // Desktop styles
          "left-[50%] top-[50%] max-w-lg translate-x-[-50%] translate-y-[-50%] p-6 sm:rounded-lg",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
        ],
        className
      )}
      {...props}
    >
      {isMobile ? (
        <TouchModal onClose={handleTouchClose}>
          <div className="pt-2">
            {children}
          </div>
        </TouchModal>
      ) : (
        children
      )}
      <DialogPrimitive.Close className={cn(
        "absolute opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
        isMobile ? "right-4 top-4 rounded-full p-2" : "right-4 top-4 rounded-sm"
      )}>
        <X className={cn(isMobile ? "h-5 w-5" : "h-4 w-4")} />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  );

  return (
    <DialogResponsivePortal>
      <DialogResponsiveOverlay />
      {content}
    </DialogResponsivePortal>
  )
})
DialogResponsiveContent.displayName = DialogPrimitive.Content.displayName

const DialogResponsiveHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  const isMobile = useIsMobile();

  return (
    <div
      className={cn(
        "flex flex-col space-y-1.5",
        isMobile ? "text-center mb-2" : "text-center sm:text-left",
        className
      )}
      {...props}
    />
  )
}
DialogResponsiveHeader.displayName = "DialogResponsiveHeader"

const DialogResponsiveFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  const isMobile = useIsMobile();

  return (
    <div
      className={cn(
        "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
        isMobile && "gap-3 mt-6",
        className
      )}
      {...props}
    />
  )
}
DialogResponsiveFooter.displayName = "DialogResponsiveFooter"

const DialogResponsiveTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();

  return (
    <DialogPrimitive.Title
      ref={ref}
      className={cn(
        "text-lg font-semibold leading-none tracking-tight",
        isMobile && "text-center",
        className
      )}
      {...props}
    />
  )
})
DialogResponsiveTitle.displayName = DialogPrimitive.Title.displayName

const DialogResponsiveDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogResponsiveDescription.displayName = DialogPrimitive.Description.displayName

export {
  DialogResponsive,
  DialogResponsivePortal,
  DialogResponsiveOverlay,
  DialogResponsiveClose,
  DialogResponsiveTrigger,
  DialogResponsiveContent,
  DialogResponsiveHeader,
  DialogResponsiveFooter,
  DialogResponsiveTitle,
  DialogResponsiveDescription,
}
