/**
 * Rotas de debug para testar funcionalidades do sistema
 */
import express from 'express';
import { getTraceId } from '../middleware/tracing';
import { logger } from '../logger';
import * as Sentry from '../sentry';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const router = express.Router();

// Extrair o DSN do Sentry para gerar URL da dashboard
const SENTRY_DSN = process.env.SENTRY_DSN || '';
let SENTRY_DASHBOARD_URL = '';

// Extrair o ID do projeto e a organização do DSN
if (SENTRY_DSN) {
  try {
    // Formato típico: https://[key]@[org].ingest.sentry.io/[project]
    const dsnMatch = SENTRY_DSN.match(/https:\/\/[^@]+@([^.]+)\.ingest\.sentry\.io\/(\d+)/);
    if (dsnMatch && dsnMatch.length >= 3) {
      const org = dsnMatch[1];
      const project = dsnMatch[2];
      SENTRY_DASHBOARD_URL = `https://sentry.io/organizations/${org}/projects/${project}/`;
      logger.info(`URL da dashboard do Sentry configurada: ${SENTRY_DASHBOARD_URL}`);
    }
  } catch (error) {
    logger.error({
      msg: 'Erro ao extrair informações do DSN do Sentry',
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Rota para acessar o painel de debug
 */
router.get('/', (_req, res) => {
  res.redirect('/debug.html');
});

/**
 * Rota para testar o middleware de tracing
 * Retorna o traceId gerado para a requisição
 */
router.get('/trace', (req, res) => {
  const traceId = getTraceId(req);
  const reqLogger = (req as any).logger || logger;

  reqLogger.info({
    msg: 'Teste de tracing',
    customField: 'valor de teste'
  });

  res.json({
    success: true,
    traceId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Rota para testar diferentes níveis de log
 */
router.get('/log-levels', (req, res) => {
  const reqLogger = (req as any).logger || logger;

  reqLogger.debug({ msg: 'Mensagem de debug' });
  reqLogger.info({ msg: 'Mensagem de info' });
  reqLogger.warn({ msg: 'Mensagem de aviso' });
  reqLogger.error({ msg: 'Mensagem de erro' });

  res.json({
    success: true,
    message: 'Logs gerados com sucesso',
    traceId: getTraceId(req)
  });
});

/**
 * Rota para testar a captura de erros pelo Sentry
 */
router.get('/sentry-test', (_req, _res) => {
  throw new Error('Erro de teste para o Sentry');
});

/**
 * Rota para testar a captura manual de exceções pelo Sentry
 */
router.get('/sentry-capture', (req, res) => {
  try {
    // Simular um erro
    throw new Error('Erro capturado manualmente para o Sentry');
  } catch (error) {
    // Capturar a exceção no Sentry
    Sentry.captureException(error as Error, req);

    res.status(200).json({
      success: true,
      message: 'Erro capturado e enviado para o Sentry',
      traceId: getTraceId(req)
    });
  }
});

/**
 * Rota para testar o desempenho (para uso com clinic)
 */
router.get('/performance', (req, res) => {
  const reqLogger = (req as any).logger || logger;
  const iterations = Number(req.query.iterations) || 1000;

  reqLogger.info({
    msg: 'Teste de desempenho iniciado',
    iterations
  });

  const start = Date.now();

  // Simular carga de CPU
  let result = 0;
  for (let i = 0; i < iterations; i++) {
    for (let j = 0; j < 1000; j++) {
      result += Math.sqrt(j) * Math.random();
    }
  }

  const duration = Date.now() - start;

  reqLogger.info({
    msg: 'Teste de desempenho concluído',
    duration,
    result: result.toFixed(2)
  });

  res.json({
    success: true,
    iterations,
    duration,
    result: result.toFixed(2),
    traceId: getTraceId(req)
  });
});

/**
 * Rota para obter informações sobre a configuração do Sentry
 * Retorna a URL da dashboard e o status da configuração
 */
router.get('/sentry-info', (req, res) => {
  const reqLogger = (req as any).logger || logger;

  reqLogger.info({
    msg: 'Solicitação de informações do Sentry',
    sentryDashboardUrl: SENTRY_DASHBOARD_URL || 'Não configurado'
  });

  res.json({
    success: true,
    sentryConfigured: !!SENTRY_DSN && SENTRY_DSN !== 'https://<EMAIL>/0',
    dashboardUrl: SENTRY_DASHBOARD_URL || null,
    message: SENTRY_DASHBOARD_URL
      ? 'Dashboard do Sentry disponível. Clique no link abaixo para acessar.'
      : 'Dashboard do Sentry não configurada. Verifique a variável de ambiente SENTRY_DSN.',
    traceId: getTraceId(req)
  });
});

/**
 * Rota para redirecionar para a dashboard do Sentry
 */
router.get('/sentry-dashboard', (req, res) => {
  const reqLogger = (req as any).logger || logger;

  if (SENTRY_DASHBOARD_URL) {
    reqLogger.info({
      msg: 'Redirecionando para dashboard do Sentry',
      url: SENTRY_DASHBOARD_URL
    });

    res.redirect(SENTRY_DASHBOARD_URL);
  } else {
    reqLogger.warn({
      msg: 'Tentativa de acesso à dashboard do Sentry sem configuração'
    });

    res.status(404).json({
      success: false,
      message: 'Dashboard do Sentry não configurada. Verifique a variável de ambiente SENTRY_DSN.',
      traceId: getTraceId(req)
    });
  }
});

export default router;
