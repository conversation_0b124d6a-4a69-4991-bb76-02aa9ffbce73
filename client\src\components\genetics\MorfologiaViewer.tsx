import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Loader2, Bar<PERSON>hart3, PlusCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { AvaliacaoMorfologica } from '@/types/genetica';

interface MorfologiaViewerProps {
  isLoading: boolean;
  avaliacoes: AvaliacaoMorfologica[];
  horseName: string;
  getDestaquesMorfologicos: (avaliacao: AvaliacaoMorfologica) => string[];
  onAddClick: () => void;
  onViewChart: (avaliacao: AvaliacaoMorfologica) => void;
}

/**
 * Componente para visualização da lista de avaliações morfológicas
 */
const MorfologiaViewer: React.FC<MorfologiaViewerProps> = ({
  isLoading,
  avaliacoes,
  horseName,
  getDestaquesMorfologicos,
  onAddClick,
  onViewChart,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando avaliações...</span>
      </div>
    );
  }

  // Se não houver avaliações, exibir estado vazio
  if (!avaliacoes || avaliacoes.length === 0) {
    return (
      <div className="text-center py-10 text-muted-foreground">
        <p>Nenhuma avaliação morfológica registrada para {horseName}.</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={onAddClick}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Adicionar Primeira Avaliação
        </Button>
      </div>
    );
  }

  // Se houver avaliações, exibir tabela
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Data</TableHead>
            <TableHead>Avaliador</TableHead>
            <TableHead>Total</TableHead>
            <TableHead>Destaques</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {avaliacoes.map((avaliacao) => {
            // Calcular destaques com a função passada por props
            const destaques = getDestaquesMorfologicos(avaliacao);

            return (
              <TableRow key={avaliacao.id} className="cursor-pointer hover:bg-muted/50" 
                onClick={() => onViewChart(avaliacao)}>
                <TableCell>
                  {avaliacao.dataMedicao ? 
                    format(new Date(avaliacao.dataMedicao), "dd/MM/yyyy", {
                      locale: ptBR,
                    }) : 
                    "Data não informada"
                  }
                </TableCell>
                <TableCell>{avaliacao.avaliador}</TableCell>
                <TableCell>
                  <span className="font-medium">
                    {avaliacao.pontuacaoTotal}/80
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {destaques.length > 0 ? (
                      destaques.map((destaque) => (
                        <Badge key={destaque} variant="outline" className="bg-green-50">
                          {destaque}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-sm">Nenhum destaque</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onViewChart(avaliacao);
                    }}
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    Gráfico
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default MorfologiaViewer;