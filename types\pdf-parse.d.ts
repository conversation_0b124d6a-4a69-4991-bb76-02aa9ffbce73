declare module 'pdf-parse' {
  interface PdfParseOptions {
    pagerender?: (pageData: any) => string;
    max?: number;
    version?: string;
  }

  interface PdfParseResult {
    numpages: number;
    numrender: number;
    info: {
      PDFFormatVersion: string;
      IsAcroFormPresent: boolean;
      IsXFAPresent: boolean;
      [key: string]: any;
    };
    metadata: any;
    text: string;
    version: string;
  }

  function parse(dataBuffer: Buffer, options?: PdfParseOptions): Promise<PdfParseResult>;

  export = parse;
}