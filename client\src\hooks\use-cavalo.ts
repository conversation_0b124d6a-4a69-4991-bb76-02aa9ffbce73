import { useState, useMemo, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import { format, isValid, differenceInYears, differenceInMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { z } from 'zod';

// Definir tipos para dados dos cavalos
export const cavaloSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(2, 'O nome deve ter pelo menos 2 caracteres'),
  breed: z.string().min(1, 'Raça é obrigatória'),
  birth_date: z.string().nullable().optional(),
  sexo: z.string().nullable().optional(),
  cor: z.string().nullable().optional(), // Campo de pelagem/coloração do cavalo
  peso: z.number().nullable().optional(),
  altura: z.number().nullable().optional(),
  status: z.string().nullable().optional(),
  observacoes: z.string().nullable().optional(),
  origem: z.string().nullable().optional(),
  numero_registro: z.string().nullable().optional(),
  pai_id: z.number().nullable().optional(),
  mae_id: z.number().nullable().optional(),
  criador: z.string().nullable().optional(),
  proprietario: z.string().nullable().optional(),
  valor_compra: z.number().nullable().optional(),
  data_compra: z.string().nullable().optional(),
  data_entrada: z.string().nullable().optional(),
  data_saida: z.string().nullable().optional(),
  user_id: z.number(),
  created_at: z.string().nullable().optional(),
});

// Exportar tipo para uso no frontend
export type Cavalo = z.infer<typeof cavaloSchema>;

// Schema para criar/atualizar cavalos (sem id e campos gerados)
export const cavaloInputSchema = cavaloSchema.omit({ id: true, created_at: true });
export type CavaloInput = z.infer<typeof cavaloInputSchema>;

// Interface para os parâmetros de filtro
interface CavalosFiltrosParams {
  raca?: string;
  sexo?: string;
  status?: string;
  searchTerm?: string;
}

/**
 * Hook personalizado para gerenciar operações relacionadas a cavalos
 * 
 * Este hook encapsula a lógica de negócio para buscar, adicionar, atualizar e excluir
 * cavalos, além de fornecer funções utilitárias e estado de filtros.
 * 
 * Utiliza o React Query para gerenciamento avançado de estado e cache.
 * 
 * @returns Objeto com operações CRUD, dados de cavalos, estados de filtros e utilitários
 */
export function useCavalo() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Estados para filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRaca, setFilterRaca] = useState('');
  const [filterSexo, setFilterSexo] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  
  // Consulta para buscar cavalos do usuário atual com React Query (somente do plantel)
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    queryFn: () => apiRequest('/api/cavalos', 'GET'),
    enabled: !!user && !!user.id,
    staleTime: 0, // Sem cache - sempre buscar dados frescos
    gcTime: 0, // Limpar cache imediatamente
    
    // Verificação de dados inválidos retornados pela API
    select: (response: any) => {
      const payload = response?.success ? response.data : response;
      console.log(`Dados recebidos da API cavalos para user_id ${user?.id}:`, payload);
      if (!Array.isArray(payload)) {
        console.error('API retornou dados inválidos para cavalos:', response);
        return [];
      }
      return payload;
    },
    
    // Handle errors in component instead of query options
  });
  
  // Consulta para buscar cavalos inativos
  const cavalosInativosQuery = useQuery({
    queryKey: ['/api/cavalos-inativos', user?.id],
    queryFn: () => apiRequest('/api/cavalos-inativos', 'GET'),
    enabled: !!user && !!user.id,
    staleTime: 0,
    gcTime: 0,
    
    select: (response: any) => {
      const payload = response?.success ? response.data : response;
      console.log(`Dados recebidos da API cavalos inativos para user_id ${user?.id}:`, payload);
      if (!Array.isArray(payload)) {
        console.error('API retornou dados inválidos para cavalos inativos:', response);
        return [];
      }
      return payload;
    },
  });

  // Consulta para buscar todos os cavalos, incluindo externos (para genealogia)
  const todosCavalosQuery = useQuery({
    queryKey: ['/api/cavalos-genealogia', user?.id],
    enabled: !!user && !!user.id,
    staleTime: 0, // Sem cache - sempre buscar dados frescos
    gcTime: 0, // Limpar cache imediatamente
    
    select: (response: any) => {
      const payload = response?.success ? response.data : response;
      console.log(`Dados recebidos da API cavalos-genealogia para user_id ${user?.id}:`, payload);
      if (!Array.isArray(payload)) {
        console.error('API retornou dados inválidos para todos os cavalos:', response);
        return [];
      }
      return payload;
    },
  });
  
  /**
   * Busca um cavalo específico pelo ID
   */
  const useCavaloById = (id?: number) => {
    return useQuery({
      queryKey: ['/api/cavalos', id?.toString()],
      enabled: !!id && !!user,
    });
  };
  
  /**
   * Busca medidas físicas de um cavalo
   */
  const useMedidasFisicas = (horse_id?: number) => {
    return useQuery({
      queryKey: [`/api/medidas-fisicas/horse/${horse_id}`],
      enabled: !!horse_id && !!user,
    });
  };
  
  // Mutation para adicionar um novo cavalo
  const addCavaloMutation = useMutation({
    mutationFn: (cavaloData: CavaloInput) => 
      apiRequest('/api/cavalos', 'POST', cavaloData),
      
    onSuccess: (data) => {
      // Invalidar cache para recarregar dados com user_id específico
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia', user?.id] });
      
      toast({
        title: 'Cavalo adicionado',
        description: `${data.name} foi cadastrado com sucesso.`,
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao adicionar cavalo',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Mutation para atualizar um cavalo existente
  const updateCavaloMutation = useMutation({
    mutationFn: (cavaloData: Partial<Cavalo> & { id: number }) => 
      apiRequest(`/api/cavalos/${cavaloData.id}`, 'PUT', cavaloData),
      
    onSuccess: (data) => {
      // Atualizar cache com user_id específico
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', data.id?.toString()] });
      
      toast({
        title: 'Cavalo atualizado',
        description: `${data.name} foi atualizado com sucesso.`,
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Erro ao atualizar cavalo',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  
  // Mutation para excluir um cavalo (soft delete)
  const deleteCavaloMutation = useMutation({
    mutationFn: (id: number) => 
      apiRequest(`/api/cavalos/${id}`, 'DELETE'),
      
    onSuccess: async (data, variables) => {
      // Invalidar cache com user_id específico
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-inativos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia', user?.id] });
      
      // Remover queries específicas do cavalo
      queryClient.removeQueries({ queryKey: ['/api/cavalos', variables.toString()] });
      
      // Mensagem específica para soft delete
      toast({
        title: 'Cavalo removido do plantel',
        description: 'O cavalo foi inativado mas seus dados históricos foram preservados.',
        variant: 'default',
      });
    },
    
    onError: (error: Error) => {
      console.error('Erro ao remover cavalo:', error);
      toast({
        title: 'Erro ao remover cavalo',
        description: error.message || 'Não foi possível remover o cavalo do plantel.',
        variant: 'destructive',
      });
    }
  });
  
  // Formatar data para exibição amigável ao usuário
  const formatarData = useCallback((dataString: string | null): string => {
    if (!dataString) return '—';
    
    const data = new Date(dataString);
    if (!isValid(data)) return '—';
    
    return format(data, 'dd/MM/yyyy', { locale: ptBR });
  }, []);
  
  // Calcular idade do cavalo baseada na data de nascimento
  const calcularIdade = useCallback((dataString: string | null): string => {
    if (!dataString) return '—';
    
    const dataNascimento = new Date(dataString);
    if (!isValid(dataNascimento)) return '—';
    
    const hoje = new Date();
    const anos = differenceInYears(hoje, dataNascimento);
    
    if (anos < 1) {
      const meses = differenceInMonths(hoje, dataNascimento);
      return `${meses} ${meses === 1 ? 'mês' : 'meses'}`;
    }
    
    return `${anos} ${anos === 1 ? 'ano' : 'anos'}`;
  }, []);
  
  // Lista de raças únicas para filtro
  const racasUnicas = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const racas = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.breed) racas.add(cavalo.breed);
    });
    
    return Array.from(racas).sort();
  }, [cavalosQuery.data]);
  
  // Lista de sexos disponíveis para filtro
  const sexosDisponiveis = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const sexos = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.sexo) sexos.add(cavalo.sexo);
    });
    
    return Array.from(sexos).sort();
  }, [cavalosQuery.data]);
  
  // Lista de status disponíveis para filtro
  const statusDisponiveis = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    const status = new Set<string>();
    cavalosQuery.data.forEach((cavalo: Cavalo) => {
      if (cavalo.status) status.add(cavalo.status);
    });
    
    return Array.from(status).sort();
  }, [cavalosQuery.data]);
  
  // Lista de cavalos filtrada de acordo com os filtros aplicados
  const cavalosFiltrados = useMemo(() => {
    if (!cavalosQuery.data) return [];
    
    return cavalosQuery.data.filter((cavalo: Cavalo) => {
      // Filtro por termo de busca (nome do cavalo)
      const nameMatch = !searchTerm || 
        cavalo.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Filtro por raça
      const breedMatch = !filterRaca || cavalo.breed === filterRaca;
      
      // Filtro por sexo
      const sexoMatch = !filterSexo || cavalo.sexo === filterSexo;
      
      // Filtro por status
      const statusMatch = !filterStatus || cavalo.status === filterStatus;
      
      return nameMatch && breedMatch && sexoMatch && statusMatch;
    });
  }, [cavalosQuery.data, searchTerm, filterRaca, filterSexo, filterStatus]);
  
  // Limpar todos os filtros
  const limparFiltros = useCallback(() => {
    setSearchTerm('');
    setFilterRaca('');
    setFilterSexo('');
    setFilterStatus('');
  }, []);
  
  return {
    // Queries e mutations
    cavalosQuery,
    cavalosInativosQuery,
    todosCavalosQuery, // Nova query que inclui cavalos externos (para genealogia)
    useCavaloById,
    useMedidasFisicas,
    addCavaloMutation,
    updateCavaloMutation,
    deleteCavaloMutation,
    
    // Estados de filtros e setters
    searchTerm,
    setSearchTerm,
    filterRaca,
    setFilterRaca,
    filterSexo,
    setFilterSexo,
    filterStatus,
    setFilterStatus,
    limparFiltros,
    
    // Dados calculados
    racasUnicas,
    sexosDisponiveis,
    statusDisponiveis,
    cavalosFiltrados,
    
    // Utilitários
    formatarData,
    calcularIdade
  };
}