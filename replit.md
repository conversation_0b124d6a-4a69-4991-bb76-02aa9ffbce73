# RS HORSE - Advanced Equine Management Platform

## Overview
RS HORSE é uma plataforma avançada de gestão equina que combina rastreamento financeiro inteligente com autenticação robusta e gerenciamento abrangente da experiência do usuário.

## Recent Changes

### System Cleanup & Optimization (August 4, 2025)
- ✅ Removed unnecessary test files and scripts
- ✅ Cleaned up temporary files and logs  
- ✅ Removed backup directories and migration files
- ✅ Deleted documentation artifacts and build cache
- ✅ Restored essential configuration files
- ✅ Fixed package.json JSON syntax errors
- ✅ Removed 195 ABCCC PDFs (327MB freed)
- ✅ Cleaned Cypress cache (400MB+ freed)
- ✅ Project size reduced from 1.7GB to 897MB
- ✅ System running optimally after cleanup

## Project Architecture

### Core Technologies
- **Frontend**: React with TypeScript, responsive mobile-first design
- **Backend**: Node.js with Express.js API infrastructure
- **Authentication**: Context-based secure login system
- **Database**: PostgreSQL with Drizzle ORM
- **Core Libraries**: Zod Schema Validation, React Query
- **AI Integration**: OpenAI-powered smart assistant
- **Styling**: Tailwind CSS with shadcn/ui components

### Directory Structure
```
.
├── client/              # React frontend application
│   ├── src/            # Source code
│   └── public/         # Static assets
├── server/              # Express.js backend API
├── shared/              # Shared schemas and types
├── types/               # TypeScript type definitions
├── uploads/             # File upload storage
└── node_modules/        # Dependencies
```

### Configuration Files
- `package.json` - Project dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration
- `vite.config.js` - Vite build configuration

## User Preferences
- **Language**: Portuguese (Brazilian)
- **Communication Style**: Clear, direct, professional
- **Documentation**: Keep comprehensive project documentation
- **Code Style**: Modern TypeScript with proper type safety

## Features
- Advanced horse management and tracking
- Financial management and reporting
- Veterinary record keeping
- Genealogy and breeding management
- AI-powered assistant
- Mobile-responsive design
- Secure authentication system

## Development Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run db:push      # Push database schema changes
npm run db:studio    # Open database studio
```

## System Status
- **Status**: ✅ Running properly
- **Port**: 5000 (backend), 5173 (frontend)
- **Database**: PostgreSQL connected
- **Last Updated**: August 4, 2025