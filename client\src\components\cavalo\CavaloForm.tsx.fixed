import React from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format, isValid } from 'date-fns';
import { toast } from '@/hooks/use-toast';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ptBR } from 'date-fns/locale';
import { GenealogiaSelector, TipoEntradaGenealogica, EntradaGenealogica } from './GenealogiaSelector';

// Definição das props
export interface CavaloFormValues {
  name: string;
  breed: string;
  birthDate?: string | null;
  sexo?: string | null;
  cor?: string | null;
  peso?: number | null;
  altura?: number | null;
  status?: string | null;
  observacoes?: string | null;
  origem?: string | null;
  numeroRegistro?: string | null;
  criador?: string | null;
  proprietario?: string | null;
  valorCompra?: number | null;
  dataCompra?: string | null;
  dataEntrada?: string | null;
  dataSaida?: string | null;
  paiId?: number | null;
  paiNome?: string | null;
  maeId?: number | null;
  maeNome?: string | null;
  userId: number;
}

interface CavaloFormProps {
  onSubmit: (values: CavaloFormValues) => void;
  defaultValues?: Partial<CavaloFormValues>;
  isSubmitting?: boolean;
  userId: number;
  cavalos?: any[]; // Lista de cavalos para selecionar pais e mães
}

// Função auxiliar para verificar se o valor é uma data válida
const isValidDate = (value: any): boolean => {
  return value instanceof Date && !isNaN(value.getTime());
};

/**
 * Componente de formulário reutilizável para cadastro e edição de cavalos
 */
export function CavaloForm({
  onSubmit,
  defaultValues,
  isSubmitting = false,
  userId,
  cavalos = []
}: CavaloFormProps) {
  // Inicializar valores padrão para campos de genealogia
  const inicializarGenealogiaDefault = (
    cavaloId: number | null | undefined,
    cavaloNome: string | null | undefined,
    cavalos: any[],
    sexoFiltro: 'Macho' | 'Fêmea'
  ): EntradaGenealogica => {
    console.log('Inicializando genealogia com:', { cavaloId, cavaloNome });
    
    // Se temos um ID de cavalo do sistema
    if (cavaloId && !isNaN(Number(cavaloId))) {
      const id = Number(cavaloId);
      const cavaloEncontrado = cavalos.find(c => c.id === id);
      
      if (cavaloEncontrado) {
        // É um cavalo do sistema
        return {
          tipo: 'sistema',
          cavaloSistemaId: id.toString(),
          cavaloNome: cavaloEncontrado.name
        };
      }
    }
    
    // Se temos um nome de cavalo externo
    if (cavaloNome && 
        cavaloNome !== 'nao_informado' && 
        cavaloNome !== '') {
      // É um nome de cavalo externo
      return {
        tipo: 'externo',
        cavaloSistemaId: "",
        cavaloNome
      };
    }
    
    // Caso contrário, não temos informação
    return {
      tipo: 'nao_informado',
      cavaloSistemaId: '',
      cavaloNome: ''
    };
  };
  
  // Valores padrão para pai e mãe
  const paiDefault = inicializarGenealogiaDefault(
    defaultValues?.paiId, 
    defaultValues?.paiNome,
    cavalos, 
    'Macho'
  );
  
  const maeDefault = inicializarGenealogiaDefault(
    defaultValues?.maeId, 
    defaultValues?.maeNome,
    cavalos, 
    'Fêmea'
  );
  
  // Inicializar formulário com react-hook-form
  const form = useForm({
    resolver: zodResolver(
      z.object({
        name: z.string().min(2, 'O nome deve ter pelo menos 2 caracteres'),
        breed: z.string().min(1, 'Raça é obrigatória'),
        birthDate: z.any().optional(),
        sexo: z.string().optional(),
        cor: z.string().optional(),
        peso: z.any().optional(),
        altura: z.any().optional(),
        status: z.string().optional(),
        observacoes: z.string().optional(),
        origem: z.string().optional(),
        numeroRegistro: z.string().optional(),
        criador: z.string().optional(),
        proprietario: z.string().optional(),
        valorCompra: z.any().optional(),
        dataCompra: z.any().optional(),
        dataEntrada: z.any().optional(),
        dataSaida: z.any().optional(),
        pai: z.any().optional(),
        mae: z.any().optional(),
        userId: z.number()
      })
    ),
    defaultValues: {
      name: defaultValues?.name || '',
      breed: defaultValues?.breed || '',
      birthDate: defaultValues?.birthDate || null,
      sexo: defaultValues?.sexo || 'nao_informado',
      cor: defaultValues?.cor || 'nao_informado',
      peso: defaultValues?.peso || null,
      altura: defaultValues?.altura || null,
      status: defaultValues?.status || 'nao_informado',
      observacoes: defaultValues?.observacoes || '',
      origem: defaultValues?.origem || '',
      numeroRegistro: defaultValues?.numeroRegistro || '',
      
      pai: inicializarGenealogiaDefault(
        defaultValues?.paiId, 
        defaultValues?.paiNome,
        cavalos, 
        'Macho'
      ),
      
      mae: inicializarGenealogiaDefault(
        defaultValues?.maeId, 
        defaultValues?.maeNome,
        cavalos, 
        'Fêmea'
      ),
      
      criador: defaultValues?.criador || '',
      proprietario: defaultValues?.proprietario || '',
      valorCompra: defaultValues?.valorCompra || '',
      dataCompra: defaultValues?.dataCompra,
      dataEntrada: defaultValues?.dataEntrada,
      dataSaida: defaultValues?.dataSaida,
      userId: userId,
    }
  });
  
  // Processar dados antes de enviar para backend
  const handleSubmit = (values: any) => {
    try {
      // Processar valores de genealogia
      let paiId = null;
      let paiNome = null;
      if (values.pai) {
        if (values.pai.tipo === 'sistema' && values.pai.cavaloSistemaId) {
          paiId = parseInt(values.pai.cavaloSistemaId);
        } else if (values.pai.tipo === 'externo' && values.pai.cavaloNome) {
          paiNome = values.pai.cavaloNome;
        }
      }
      
      let maeId = null;
      let maeNome = null;
      if (values.mae) {
        if (values.mae.tipo === 'sistema' && values.mae.cavaloSistemaId) {
          maeId = parseInt(values.mae.cavaloSistemaId);
        } else if (values.mae.tipo === 'externo' && values.mae.cavaloNome) {
          maeNome = values.mae.cavaloNome;
        }
      }
      
      // Converter valores de string para número onde apropriado
      const peso = values.peso ? parseFloat(values.peso) : null;
      const altura = values.altura ? parseFloat(values.altura) : null;
      const valorCompra = values.valorCompra ? parseFloat(values.valorCompra) : null;
      
      // Criar objeto final com valores processados
      const valuesAjustados = {
        ...values,
        peso,
        altura,
        valorCompra,
        paiId,
        paiNome,
        maeId,
        maeNome
      };
      
      // Remover os objetos complexos que não serão enviados ao backend
      delete valuesAjustados.pai;
      delete valuesAjustados.mae;
      
      // Enviar ao backend via callback
      onSubmit(valuesAjustados);
    } catch (error) {
      console.error("Erro ao processar dados do formulário:", error);
      toast({
        title: "Erro ao processar dados",
        description: "Não foi possível processar os dados do formulário. Tente novamente.",
        variant: "destructive"
      });
      return;
    }
  };
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Nome */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome *</FormLabel>
                <FormControl>
                  <Input placeholder="Nome do cavalo" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Raça */}
          <FormField
            control={form.control}
            name="breed"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Raça *</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a raça" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Crioulo">Crioulo</SelectItem>
                    <SelectItem value="Quarto de Milha">Quarto de Milha</SelectItem>
                    <SelectItem value="Mangalarga">Mangalarga</SelectItem>
                    <SelectItem value="Mangalarga Marchador">Mangalarga Marchador</SelectItem>
                    <SelectItem value="Árabe">Árabe</SelectItem>
                    <SelectItem value="Paint Horse">Paint Horse</SelectItem>
                    <SelectItem value="Appaloosa">Appaloosa</SelectItem>
                    <SelectItem value="Campolina">Campolina</SelectItem>
                    <SelectItem value="Lusitano">Lusitano</SelectItem>
                    <SelectItem value="Pônei">Pônei</SelectItem>
                    <SelectItem value="Puro Sangue Inglês">Puro Sangue Inglês</SelectItem>
                    <SelectItem value="Outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Data Nascimento */}
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data de Nascimento</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {isValidDate(field.value) ? (
                          format(field.value, "dd/MM/yyyy", { locale: ptBR })
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={isValidDate(field.value) ? field.value : undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1970-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Sexo */}
          <FormField
            control={form.control}
            name="sexo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sexo</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || "nao_informado"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o sexo" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="nao_informado">Não informado</SelectItem>
                    <SelectItem value="Macho">Macho</SelectItem>
                    <SelectItem value="Fêmea">Fêmea</SelectItem>
                    <SelectItem value="Garanhão">Garanhão</SelectItem>
                    <SelectItem value="Égua">Égua</SelectItem>
                    <SelectItem value="Castrado">Castrado</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Cor/Pelagem */}
          <FormField
            control={form.control}
            name="cor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Pelagem</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || "nao_informado"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a pelagem" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="nao_informado">Não informado</SelectItem>
                    <SelectItem value="Alazão">Alazão</SelectItem>
                    <SelectItem value="Baio">Baio</SelectItem>
                    <SelectItem value="Branco">Branco</SelectItem>
                    <SelectItem value="Castanho">Castanho</SelectItem>
                    <SelectItem value="Preto">Preto</SelectItem>
                    <SelectItem value="Tordilho">Tordilho</SelectItem>
                    <SelectItem value="Zaino">Zaino</SelectItem>
                    <SelectItem value="Palomino">Palomino</SelectItem>
                    <SelectItem value="Rosilho">Rosilho</SelectItem>
                    <SelectItem value="Pampa">Pampa</SelectItem>
                    <SelectItem value="Lobuno">Lobuno</SelectItem>
                    <SelectItem value="Gateado">Gateado</SelectItem>
                    <SelectItem value="Ruão">Ruão</SelectItem>
                    <SelectItem value="Malhado">Malhado</SelectItem>
                    <SelectItem value="Outros">Outros</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Peso */}
          <FormField
            control={form.control}
            name="peso"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Peso (kg)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.1"
                    placeholder="Peso em kg"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Altura */}
          <FormField
            control={form.control}
            name="altura"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Altura (m)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Altura em metros"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Status */}
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || "nao_informado"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="nao_informado">Não informado</SelectItem>
                    <SelectItem value="Ativo">Ativo</SelectItem>
                    <SelectItem value="Vendido">Vendido</SelectItem>
                    <SelectItem value="Falecido">Falecido</SelectItem>
                    <SelectItem value="Doado">Doado</SelectItem>
                    <SelectItem value="Reprodução">Reprodução</SelectItem>
                    <SelectItem value="Competição">Competição</SelectItem>
                    <SelectItem value="Tratamento">Tratamento</SelectItem>
                    <SelectItem value="Aposentado">Aposentado</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Origem */}
          <FormField
            control={form.control}
            name="origem"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Haras/Fazenda de Origem</FormLabel>
                <FormControl>
                  <Input placeholder="Origem do cavalo" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Criador */}
          <FormField
            control={form.control}
            name="criador"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Criador</FormLabel>
                <FormControl>
                  <Input placeholder="Nome do criador" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Proprietário */}
          <FormField
            control={form.control}
            name="proprietario"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Proprietário</FormLabel>
                <FormControl>
                  <Input placeholder="Nome do proprietário" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Número de Registro */}
          <FormField
            control={form.control}
            name="numeroRegistro"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Número de Registro</FormLabel>
                <FormControl>
                  <Input placeholder="Número de registro (ex: B123456)" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Valor de Compra */}
          <FormField
            control={form.control}
            name="valorCompra"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valor de Compra (R$)</FormLabel>
                <FormControl>
                  <Input 
                    type="number"
                    step="0.01"
                    placeholder="Valor em reais"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Data de Compra */}
          <FormField
            control={form.control}
            name="dataCompra"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data de Compra</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {isValidDate(field.value) ? (
                          format(field.value, "dd/MM/yyyy", { locale: ptBR })
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={isValidDate(field.value) ? field.value : undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1970-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Data de Entrada */}
          <FormField
            control={form.control}
            name="dataEntrada"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data de Entrada</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {isValidDate(field.value) ? (
                          format(field.value, "dd/MM/yyyy", { locale: ptBR })
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={isValidDate(field.value) ? field.value : undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1970-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Data de Saída */}
          <FormField
            control={form.control}
            name="dataSaida"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data de Saída</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {isValidDate(field.value) ? (
                          format(field.value, "dd/MM/yyyy", { locale: ptBR })
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={isValidDate(field.value) ? field.value : undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1970-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="space-y-3">
          <h3 className="text-lg font-medium">Genealogia</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Pai */}
            <FormField
              control={form.control}
              name="pai"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pai</FormLabel>
                  <FormControl>
                    <GenealogiaSelector
                      tipoEntrada={TipoEntradaGenealogica.Pai}
                      valorInicial={field.value || paiDefault}
                      cavalos={cavalos?.filter(c => c.sexo === 'Macho' || c.sexo === 'Garanhão')}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Mãe */}
            <FormField
              control={form.control}
              name="mae"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mãe</FormLabel>
                  <FormControl>
                    <GenealogiaSelector
                      tipoEntrada={TipoEntradaGenealogica.Mae}
                      valorInicial={field.value || maeDefault}
                      cavalos={cavalos?.filter(c => c.sexo === 'Fêmea' || c.sexo === 'Égua')}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        
        {/* Observações */}
        <FormField
          control={form.control}
          name="observacoes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Observações</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Observações adicionais"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button 
          type="submit" 
          className="w-full mt-4"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Salvando...
            </>
          ) : (
            "Salvar"
          )}
        </Button>
      </form>
    </Form>
  );
}