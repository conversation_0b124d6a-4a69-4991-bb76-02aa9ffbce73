/**
 * Módulo de monitoramento de desempenho para o EquiGestor
 * 
 * Este módulo fornece ferramentas para rastrear o desempenho de operações
 * críticas no sistema, como consultas ao banco de dados, chamadas a APIs
 * externas e processamento de solicitações.
 */

import { getModuleLogger } from './logger';

// Logger dedicado para métricas de desempenho
const perfLogger = getModuleLogger('performance');

/**
 * Classe cronômetro para medir tempo de execução de operações
 */
export class PerformanceTimer {
  private startTime: number;
  private name: string;
  private metadata: Record<string, any>;
  
  /**
   * Cria um novo cronômetro de desempenho
   * @param name Nome da operação sendo medida
   * @param metadata Metadados adicionais sobre a operação
   */
  constructor(name: string, metadata: Record<string, any> = {}) {
    this.startTime = Date.now();
    this.name = name;
    this.metadata = metadata;
    
    // Registra o início da operação
    perfLogger.debug(`Iniciando operação: ${name}`, metadata);
  }
  
  /**
   * Finaliza o cronômetro e registra a duração no log
   * @param additionalMeta Metadados adicionais para incluir no registro final
   * @returns Duração da operação em milissegundos
   */
  end(additionalMeta: Record<string, any> = {}): number {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    perfLogger.info(`Operação concluída: ${this.name}`, {
      duration: `${duration}ms`,
      ...this.metadata,
      ...additionalMeta
    });
    
    return duration;
  }
}

/**
 * Decorator para medir o desempenho de métodos em classes
 * (necessita de TypeScript experimental decorators)
 * @param target Classe alvo
 * @param propertyKey Nome do método
 * @param descriptor Descritor do método
 */
export function measurePerformance(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;
  
  descriptor.value = async function(...args: any[]) {
    const timer = new PerformanceTimer(`${target.constructor.name}.${propertyKey}`, {
      className: target.constructor.name,
      method: propertyKey,
      args: args.map(arg => 
        typeof arg === 'object' ? 
          (arg === null ? 'null' : 'object') : 
          String(arg)
      ).join(', ')
    });
    
    try {
      const result = await originalMethod.apply(this, args);
      timer.end({ success: true });
      return result;
    } catch (error) {
      timer.end({ 
        success: false, 
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  };
  
  return descriptor;
}

/**
 * Medidor de utilização de memória
 * @param label Identificador para a medição
 */
export function logMemoryUsage(label: string) {
  const memoryUsage = process.memoryUsage();
  
  perfLogger.info(`Uso de memória [${label}]`, {
    rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
    heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
    external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
  });
}

/**
 * Função para envolver uma função com monitoramento de desempenho
 * @param fn Função a ser monitorada
 * @param name Nome da operação (opcional, usa o nome da função por padrão)
 */
export function trackPerformance<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  name?: string
): T {
  const functionName = name || fn.name || 'anonymous';
  
  const wrappedFunction = async (...args: any[]): Promise<any> => {
    const timer = new PerformanceTimer(functionName, {
      args: args.map(arg => 
        typeof arg === 'object' ? 
          (arg === null ? 'null' : 'object') : 
          String(arg)
      ).join(', ')
    });
    
    try {
      const result = await fn(...args);
      timer.end({ success: true });
      return result;
    } catch (error) {
      timer.end({ 
        success: false, 
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  };
  
  return wrappedFunction as T;
}

/**
 * Registra métricas periódicas sobre o sistema
 * Útil para monitoramento de longo prazo e identificação de vazamentos de memória
 */
export function startPerformanceMonitoring(intervalMinutes: number = 30) {
  // Registra métricas iniciais
  logMemoryUsage('Inicial');
  
  // Configura o intervalo de monitoramento
  const interval = intervalMinutes * 60 * 1000;
  
  setInterval(() => {
    logMemoryUsage('Periódico');
    
    // Registra informações sobre conexões e recursos do sistema
    perfLogger.info('Métricas do sistema', {
      uptime: `${Math.floor(process.uptime() / 3600)}h ${Math.floor(process.uptime() % 3600 / 60)}m`,
      cpuUsage: JSON.stringify(process.cpuUsage()),
      date: new Date().toISOString()
    });
  }, interval);
  
  perfLogger.info(`Monitoramento de desempenho iniciado (intervalo: ${intervalMinutes}min)`);
}

export default {
  PerformanceTimer,
  logMemoryUsage,
  trackPerformance,
  startPerformanceMonitoring
};