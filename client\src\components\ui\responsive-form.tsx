import * as React from "react"
import { cn } from "@/lib/utils"

const ResponsiveFormGrid = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "grid gap-4",
      // Mobile: single column
      "grid-cols-1",
      // Tablet: two columns
      "sm:grid-cols-2",
      // Desktop: three columns for better space usage
      "lg:grid-cols-3",
      className
    )}
    {...props}
  />
)
ResponsiveFormGrid.displayName = "ResponsiveFormGrid"

const ResponsiveFormRow = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "grid gap-4",
      // Mobile: single column
      "grid-cols-1",
      // Tablet and up: two columns
      "sm:grid-cols-2",
      className
    )}
    {...props}
  />
)
ResponsiveFormRow.displayName = "ResponsiveFormRow"

const ResponsiveFormSection = ({
  className,
  title,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  title?: string
}) => (
  <div
    className={cn(
      "space-y-4",
      className
    )}
    {...props}
  >
    {title && (
      <div className="border-b border-border/10 pb-2">
        <h3 className="text-sm font-medium text-foreground">{title}</h3>
      </div>
    )}
    {children}
  </div>
)
ResponsiveFormSection.displayName = "ResponsiveFormSection"

const ResponsiveFormFullWidth = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "col-span-full space-y-4",
      className
    )}
    {...props}
  />
)
ResponsiveFormFullWidth.displayName = "ResponsiveFormFullWidth"

export {
  ResponsiveFormGrid,
  ResponsiveFormRow,
  ResponsiveFormSection,
  ResponsiveFormFullWidth,
}