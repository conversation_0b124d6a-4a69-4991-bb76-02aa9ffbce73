import * as React from "react";
import { isValidSelectValue } from "@/lib/select-utils";

/**
 * SelectItemValidator
 * 
 * Componente que valida em tempo de desenvolvimento se um SelectItem tem um valor válido.
 * Este componente não renderiza nada em produção, mas emite avisos no console em desenvolvimento.
 */
interface SelectItemValidatorProps {
  value: any;
  componentName?: string;
  location?: string;
}

export function SelectItemValidator({
  value,
  componentName = "SelectItem",
  location = "unknown"
}: SelectItemValidatorProps) {
  // Só executar em desenvolvimento
  React.useEffect(() => {
    if (process.env.NODE_ENV !== "development") return;
    
    // Verificar se o valor é válido
    if (!isValidSelectValue(value)) {
      console.error(
        `[SelectValidator] Valor inválido detectado em ${componentName}: "${value}"`,
        `\nLocalização: ${location}`,
        `\nTipo: ${typeof value}`,
        `\nEste erro ocorre porque o componente SelectItem não pode ter um valor vazio.`,
        `\nSolução: Use o componente SafeSelectItem em vez de SelectItem, ou certifique-se de que o valor não é vazio.`,
        `\nExemplo: <SafeSelectItem originalValue="">Nenhum item</SafeSelectItem>`
      );
    }
  }, [value, componentName, location]);
  
  // Não renderizar nada
  return null;
}

/**
 * SelectValidator
 * 
 * Componente que verifica todos os SelectItem em um Select e valida seus valores.
 * Este componente deve ser usado em desenvolvimento para detectar problemas com SelectItem.
 */
interface SelectValidatorProps {
  children: React.ReactNode;
  location?: string;
}

export function SelectValidator({ children, location = "unknown" }: SelectValidatorProps) {
  // Só executar em desenvolvimento
  React.useEffect(() => {
    if (process.env.NODE_ENV !== "development") return;
    
    // Função recursiva para verificar todos os SelectItem
    const validateSelectItems = (node: React.ReactNode, path: string = ""): void => {
      // Se não for um elemento React, retornar
      if (!React.isValidElement(node)) return;
      
      // Se for um SelectItem, verificar seu valor
      if (node.type && (node.type as any).displayName === "SelectItem") {
        const value = node.props.value;
        if (!isValidSelectValue(value)) {
          console.error(
            `[SelectValidator] Valor inválido detectado em SelectItem: "${value}"`,
            `\nLocalização: ${location}${path ? ` > ${path}` : ""}`,
            `\nTipo: ${typeof value}`,
            `\nEste erro ocorre porque o componente SelectItem não pode ter um valor vazio.`,
            `\nSolução: Use o componente SafeSelectItem em vez de SelectItem, ou certifique-se de que o valor não é vazio.`,
            `\nExemplo: <SafeSelectItem originalValue="">Nenhum item</SafeSelectItem>`
          );
        }
      }
      
      // Se tiver filhos, verificar recursivamente
      if (node.props && node.props.children) {
        const childPath = path ? `${path} > ${node.type.displayName || node.type}` : (node.type.displayName || node.type);
        React.Children.forEach(node.props.children, child => {
          validateSelectItems(child, childPath);
        });
      }
    };
    
    // Verificar todos os filhos
    React.Children.forEach(children, child => {
      validateSelectItems(child);
    });
  }, [children, location]);
  
  // Renderizar os filhos normalmente
  return <>{children}</>;
}

/**
 * withSelectValidation
 * 
 * HOC que adiciona validação de SelectItem a um componente.
 * @param Component Componente a ser envolvido
 * @param options Opções de validação
 * @returns Componente com validação de SelectItem
 */
export function withSelectValidation<P extends object>(
  Component: React.ComponentType<P>,
  options: { location?: string } = {}
): React.FC<P> {
  const WithSelectValidation: React.FC<P> = (props) => {
    return (
      <SelectValidator location={options.location}>
        <Component {...props} />
      </SelectValidator>
    );
  };
  
  WithSelectValidation.displayName = `WithSelectValidation(${Component.displayName || Component.name || "Component"})`;
  
  return WithSelectValidation;
}
