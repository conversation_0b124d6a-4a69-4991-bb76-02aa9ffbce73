/**
 * Hook para gerenciar raças de cavalos brasileiras
 * Inclui as principais raças utilizadas no Brasil
 */

export const RACAS_BRASILEIRAS = [
  '<PERSON><PERSON><PERSON>',
  'Mangalar<PERSON> March<PERSON>',
  'Mangalar<PERSON>',
  '<PERSON>uart<PERSON>',
  'Paint Horse',
  '<PERSON><PERSON>loosa',
  '<PERSON><PERSON><PERSON>',
  'Puro Sangue Inglês',
  'Hanoveriano',
  'Oldenburg',
  'Holsteiner',
  'Lusitano',
  'Andaluz',
  '<PERSON><PERSON>',
  'Brasileiro de Hipismo',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'Friesian',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>on',
  'Shire',
  'Belgian',
  'Thoroughbred',
  'Standardbred',
  'Tennessee Walker',
  'Missouri Fox Trotter',
  'Icelandic Horse',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Conne<PERSON>',
  'Welsh Pony',
  'Shetland',
  'Mustang',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>fin<PERSON> (SRD)',
  '<PERSON>st<PERSON><PERSON><PERSON>',
  'Outras'
] as const;

export type RacaCavalo = typeof RACAS_BRASILEIRAS[number];

export function useRacas() {
  return {
    racas: RACAS_BRASILEIRAS,
    isLoading: false,
    error: null
  };
}