import { z } from "zod";

// Manual schema definitions to avoid TypeScript conflicts with snake_case
export const insertCavaloSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  breed: z.string().optional(),
  birth_date: z.string().optional(),
  sexo: z.enum(['<PERSON><PERSON>', '<PERSON>ê<PERSON><PERSON>', '<PERSON><PERSON> (Castrado)', 'Garanhão', 'Égua']).optional(),
  cor: z.string().optional(),
  pelagem_id: z.number().optional(),
  status: z.enum(['ativo', 'inativo', 'vendido', 'falecido', 'nao_informado', 'competicao', 'reproducao']).default('ativo'),
  user_id: z.number(),
  notes: z.string().optional(),
  peso: z.number().positive().optional(),
  altura: z.number().positive().optional(),
  data_entrada: z.string().optional(),
  data_saida: z.string().optional(),
  motivo_saida: z.string().optional(),
  numero_registro: z.string().optional(),
  origem: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  valor_compra: z.number().positive().optional(),
  data_compra: z.string().optional(),
  inspetor: z.string().optional(),
  is_external: z.boolean().default(false),
  pai_id: z.number().nullable().optional(),
  mae_id: z.number().nullable().optional(),
});

export const updateCavaloSchema = insertCavaloSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertGenealogiaSchema = z.object({
  horse_id: z.number(),
  pai: z.string().optional(),
  mae: z.string().optional(),
  avo_paterno_id: z.number().optional(),
  avo_paterno: z.string().optional(),
  avo_paterna_id: z.number().optional(),
  avo_paterna: z.string().optional(),
  avo_materno_id: z.number().optional(),
  avo_materno: z.string().optional(),
  avo_materna_id: z.number().optional(),
  avo_materna: z.string().optional(),
  bisavo_paterno_paterno: z.string().optional(),
  bisavo_paterno_paterno_id: z.number().optional(),
  bisavo_paterna_paterno: z.string().optional(),
  bisavo_paterna_paterno_id: z.number().optional(),
  bisavo_materno_paterno: z.string().optional(),
  bisavo_materno_paterno_id: z.number().optional(),
  bisavo_materna_paterno: z.string().optional(),
  bisavo_materna_paterno_id: z.number().optional(),
});

export const updateGenealogiaSchema = insertGenealogiaSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertManejoSchema = z.object({
  cavalo_id: z.number(),
  tipo: z.string(),
  descricao: z.string(),
  data_execucao: z.string(),
  user_id: z.number(),
  observacoes: z.string().optional(),
  custo: z.string().optional(),
  responsavel: z.string().optional(),
  status: z.string().default('pendente'),
});

export const updateManejoSchema = insertManejoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertNutricaoSchema = z.object({
  cavalo_id: z.number(),
  tipo_alimento: z.string(),
  quantidade: z.number(),
  unidade: z.string(),
  frequencia: z.string(),
  horario_alimentacao: z.string().optional(),
  observacoes: z.string().optional(),
  custo_mensal: z.number().optional(),
  data_inicio: z.string(),
  data_fim: z.string().optional(),
  status: z.string().default('ativo'),
  user_id: z.number(),
});

export const updateNutricaoSchema = insertNutricaoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Tasks schema
export const insertTaskSchema = z.object({
  title: z.string(),
  due_date: z.string().optional(),
  completed: z.boolean().default(false),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const updateTaskSchema = insertTaskSchema.partial().extend({
  id: z.number().int().positive(),
});

// Events schema
export const insertEventoSchema = z.object({
  titulo: z.string(),
  descricao: z.string().optional(),
  data: z.string(),
  hora_inicio: z.string().optional(),
  hora_fim: z.string().optional(),
  tipo: z.string(),
  status: z.string().default('ativo'),
  prioridade: z.string().default('media'),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const updateEventoSchema = insertEventoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Veterinary procedures schema
export const insertProcedimentoVetSchema = z.object({
  tipo: z.string(),
  descricao: z.string(),
  data: z.string(),
  veterinario: z.string().optional(),
  crmv: z.string().optional(),
  medicamentos: z.string().optional(),
  dosagem: z.string().optional(),
  resultado: z.string().optional(),
  recomendacoes: z.string().optional(),
  horse_id: z.number(),
  user_id: z.number(),
});

export const updateProcedimentoVetSchema = insertProcedimentoVetSchema.partial().extend({
  id: z.number().int().positive(),
});

// Reproduction schema
export const insertReproducaoSchema = z.object({
  horse_id: z.number(),
  padreiro_id: z.number().optional(),
  data_cobertura: z.string(),
  tipo_cobertura: z.string(),
  estado: z.string().default('planejado'),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateReproducaoSchema = insertReproducaoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Morphological measurements schema
export const insertMedidasMorfologicasSchema = z.object({
  cavalo_id: z.number(),
  user_id: z.number(),
});

export const updateMedidasMorfologicasSchema = insertMedidasMorfologicasSchema.partial().extend({
  id: z.number().int().positive(),
});

// Morphology files schema
export const insertMorfologiaArquivoSchema = z.object({
  morfologia_id: z.number(),
  user_id: z.number(),
});

export const updateMorfologiaArquivoSchema = insertMorfologiaArquivoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Performance history schema
export const insertDesempenhoHistoricoSchema = z.object({
  horse_id: z.number(),
  data: z.string(),
  atividade: z.string(),
  tempo: z.string().optional(),
  distancia: z.number().optional(),
  velocidade: z.number().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateDesempenhoHistoricoSchema = insertDesempenhoHistoricoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Breeding suggestions schema
export const insertSugestoesCruzamentoSchema = z.object({
  horse_id: z.number(),
  padreiro_sugerido: z.string(),
  motivo: z.string().optional(),
  probabilidade_cor_pelagem: z.string().optional(),
  caracteristicas_esperadas: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateSugestoesCruzamentoSchema = insertSugestoesCruzamentoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Coat colors schema
export const insertPelagemSchema = z.object({
  nome: z.string(),
  descricao: z.string().optional(),
  slug: z.string().optional(),
  fonte: z.string().optional(),
  user_id: z.number().optional(),
});

export const updatePelagemSchema = insertPelagemSchema.partial().extend({
  id: z.number().int().positive(),
});

// Feed templates schema
export const insertFeedTemplateSchema = z.object({
  nome: z.string(),
  descricao: z.string().optional(),
  categoria: z.string(),
  ingredientes: z.string().optional(),
  valor_nutricional: z.string().optional(),
  user_id: z.number(),
});

export const updateFeedTemplateSchema = insertFeedTemplateSchema.partial().extend({
  id: z.number().int().positive(),
});

// Feed plan items schema
export const insertFeedPlanItemSchema = z.object({
  template_id: z.number(),
  horse_id: z.number(),
  quantidade: z.number(),
  frequencia: z.string(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateFeedPlanItemSchema = insertFeedPlanItemSchema.partial().extend({
  id: z.number().int().positive(),
});

// Stock batches schema
export const insertStockBatchSchema = z.object({
  produto: z.string(),
  lote: z.string(),
  quantidade: z.number(),
  data_validade: z.string().optional(),
  data_entrada: z.string(),
  preco_unitario: z.number().optional(),
  fornecedor: z.string().optional(),
  user_id: z.number(),
});

export const updateStockBatchSchema = insertStockBatchSchema.partial().extend({
  id: z.number().int().positive(),
});

// Stock alerts schema
export const insertStockAlertSchema = z.object({
  produto: z.string(),
  nivel_minimo: z.number(),
  nivel_atual: z.number(),
  status: z.string().default('ativo'),
  data_alerta: z.string(),
  user_id: z.number(),
});

export const updateStockAlertSchema = insertStockAlertSchema.partial().extend({
  id: z.number().int().positive(),
});

// Users schema
export const insertUserSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  username: z.string(),
  email: z.string().email().optional(),
  password: z.string(),
});

export const updateUserSchema = insertUserSchema.partial().extend({
  id: z.number().int().positive(),
});

// Files schema
export const insertArquivoSchema = z.object({
  filename: z.string(),
  filepath: z.string(),
  mimetype: z.string(),
  size: z.number(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const updateArquivoSchema = insertArquivoSchema.partial().extend({
  id: z.number().int().positive(),
});

// Type exports
export type InsertCavaloType = z.infer<typeof insertCavaloSchema>;
export type UpdateCavaloType = z.infer<typeof updateCavaloSchema>;
export type InsertGenealogiaType = z.infer<typeof insertGenealogiaSchema>;
export type UpdateGenealogiaType = z.infer<typeof updateGenealogiaSchema>;
export type InsertManejoType = z.infer<typeof insertManejoSchema>;
export type UpdateManejoType = z.infer<typeof updateManejoSchema>;
export type InsertNutricaoType = z.infer<typeof insertNutricaoSchema>;
export type UpdateNutricaoType = z.infer<typeof updateNutricaoSchema>;
export type InsertTaskType = z.infer<typeof insertTaskSchema>;
export type UpdateTaskType = z.infer<typeof updateTaskSchema>;
export type InsertEventoType = z.infer<typeof insertEventoSchema>;
export type UpdateEventoType = z.infer<typeof updateEventoSchema>;
export type InsertProcedimentoVetType = z.infer<typeof insertProcedimentoVetSchema>;
export type UpdateProcedimentoVetType = z.infer<typeof updateProcedimentoVetSchema>;
export type InsertReproducaoType = z.infer<typeof insertReproducaoSchema>;
export type UpdateReproducaoType = z.infer<typeof updateReproducaoSchema>;
export type InsertMedidasMorfologicasType = z.infer<typeof insertMedidasMorfologicasSchema>;
export type UpdateMedidasMorfologicasType = z.infer<typeof updateMedidasMorfologicasSchema>;
export type InsertMorfologiaArquivoType = z.infer<typeof insertMorfologiaArquivoSchema>;
export type UpdateMorfologiaArquivoType = z.infer<typeof updateMorfologiaArquivoSchema>;
export type InsertDesempenhoHistoricoType = z.infer<typeof insertDesempenhoHistoricoSchema>;
export type UpdateDesempenhoHistoricoType = z.infer<typeof updateDesempenhoHistoricoSchema>;
export type InsertSugestoesCruzamentoType = z.infer<typeof insertSugestoesCruzamentoSchema>;
export type UpdateSugestoesCruzamentoType = z.infer<typeof updateSugestoesCruzamentoSchema>;
export type InsertPelagemType = z.infer<typeof insertPelagemSchema>;
export type UpdatePelagemType = z.infer<typeof updatePelagemSchema>;
export type InsertFeedTemplateType = z.infer<typeof insertFeedTemplateSchema>;
export type UpdateFeedTemplateType = z.infer<typeof updateFeedTemplateSchema>;
export type InsertFeedPlanItemType = z.infer<typeof insertFeedPlanItemSchema>;
export type UpdateFeedPlanItemType = z.infer<typeof updateFeedPlanItemSchema>;
export type InsertStockBatchType = z.infer<typeof insertStockBatchSchema>;
export type UpdateStockBatchType = z.infer<typeof updateStockBatchSchema>;
export type InsertStockAlertType = z.infer<typeof insertStockAlertSchema>;
export type UpdateStockAlertType = z.infer<typeof updateStockAlertSchema>;
export type InsertUserType = z.infer<typeof insertUserSchema>;
export type UpdateUserType = z.infer<typeof updateUserSchema>;
export type InsertArquivoType = z.infer<typeof insertArquivoSchema>;
export type UpdateArquivoType = z.infer<typeof updateArquivoSchema>;