import type { Express, Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { pool } from "./db";
import { storage } from "./storage";
import { comparePassword, formatAuthResponse, isValidToken } from "./security";
import { getModuleLogger } from "./logger";

const logger = getModuleLogger("auth");

const JWT_SECRET = process.env.JWT_SECRET || "default-jwt-secret";

export interface AuthenticatedRequest extends Request {
  user?: { id: number; roles?: string[] };
}

/**
 * Middleware de autenticação para proteger rotas
 */
export const authenticateUser = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log(`❌ Acesso negado - token não fornecido para ${req.method} ${req.path}`);
      return res.status(401).json({ message: "Acesso não autorizado. Token não fornecido." });
    }

    const token = authHeader.slice(7).trim();
    let payload: any;

    try {
      payload = jwt.verify(token, JWT_SECRET);
    } catch (tokenError) {
      console.log("Token inválido:", tokenError);
      return res.status(401).json({ message: "Token de autenticação inválido" });
    }

    const user_id = parseInt(payload.user_id ?? payload.id ?? payload.userId);

    if (!user_id || isNaN(user_id)) {
      logger.debug(`❌ Acesso negado - user_id não fornecido para ${req.method} ${req.path}`);
      return res.status(401).json({
        message: "Acesso não autorizado. ID de usuário inválido no token.",
      });
    }

    logger.debug(`🔐 Auth check - Method: ${req.method}, Path: ${req.path}, User ID: ${user_id}`);

    req.body = req.body || {};
    req.body.user_id = user_id;
    (req as any).user_id = user_id;
    req.user = {
      id: user_id,
      roles: payload.roles || (payload.role ? [payload.role] : undefined),
    };
    (req as any).user = req.user;

    const apiKey = req.headers["x-api-key"] as string;
    if (apiKey && !isValidToken(apiKey)) {
      logger.warn("Token inválido recebido", { user_id });
      return res.status(401).json({ message: "Token de autenticação inválido" });
    }

    next();
  } catch (error) {
    logger.error("Erro na autenticação", { error });
    res.status(500).json({ message: "Erro interno do servidor" });
  }
};

/**
 * Configura as rotas de autenticação na aplicação Express
 */
export function setupAuth(app: Express) {
  app.post("/api/auth/signup", async (req, res) => {
    try {
      const { name, username, email, password } = req.body;
      logger.info("Signup attempt", { username, email });

      if (!name || !username || !email || !password) {
        return res.status(400).json({
          message: "Todos os campos são obrigatórios",
        });
      }

      if (password.length < 6) {
        return res.status(400).json({
          message: "Senha deve ter pelo menos 6 caracteres",
        });
      }

      try {
        const existingUser = await pool.query(
          "SELECT username FROM users WHERE username = $1",
          [username]
        );

        if (existingUser.rows.length > 0) {
          return res.status(400).json({
            message: "Nome de usuário já está em uso",
          });
        }
      } catch (e) {
        logger.warn("Erro ao verificar usuário existente, prosseguindo");
      }

      const bcrypt = await import("bcrypt");
      const hashedPassword = await bcrypt.hash(password, 10);

      const result = await pool.query(
        "INSERT INTO users (name, username, email, password_hash, role) VALUES ($1, $2, $3, $4, $5) RETURNING id, username, email, name, password_hash",
        [name, username, email, hashedPassword, "USER"]
      );

      const newUser = result.rows[0];
      logger.info("User created successfully", {
        id: newUser.id,
        username: newUser.username,
      });

      res.status(201).json({
        message: "Usuário criado com sucesso",
        user: {
          id: newUser.id,
          name: newUser.name,
          username: newUser.username,
          email: newUser.email,
          accessLevel: "user",
          isActive: true,
        },
        token: "auth-token-" + Date.now(), // Substitua por jwt real se necessário
      });
    } catch (error) {
      logger.error("Erro no signup", { error });
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : String(error),
      });
    }
  });

  app.post("/api/auth/logout", authenticateUser, (req, res) => {
    logger.info(`Logout realizado para o usuário ID: ${req.body.user_id}`);
    res.json({
      message: "Logout realizado com sucesso",
      timestamp: new Date().toISOString(),
    });
  });
}
