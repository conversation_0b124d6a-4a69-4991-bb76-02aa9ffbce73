import * as openai from 'openai';

// Implementação específica para a versão 4.98.0 da OpenAI
const apiKey = process.env.OPENAI_API_KEY as string;
if (!apiKey) {
  console.error('OPENAI_API_KEY não está definido no ambiente');
}

// Cria uma instância do cliente OpenAI
// o modelo mais recente da OpenAI é "gpt-4o" que foi lançado em 13 de maio de 2024
const client = new openai.default({
  apiKey
});

export default client;