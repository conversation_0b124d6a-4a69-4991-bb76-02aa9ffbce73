import { apiRequest } from './queryClient';
import { toast } from '@/hooks/use-toast';

/**
 * Opções para tentativas de requisição
 */
interface RetryOptions {
  /** Número máximo de tentativas */
  maxRetries?: number;
  /** Atraso inicial entre tentativas (ms) */
  initialDelay?: number;
  /** Multiplicador de atraso para backoff exponencial */
  backoffMultiplier?: number;
  /** Função para decidir se deve tentar novamente com base no erro */
  shouldRetry?: (error: any) => boolean;
  /** Função chamada antes de cada nova tentativa */
  onRetry?: (attempt: number, error: any) => void;
}

/**
 * Função para fazer requisições à API com retry manual
 *
 * Esta função tenta a requisição várias vezes em caso de falha,
 * com um atraso crescente entre as tentativas (backoff exponencial).
 *
 * @param method Método HTTP (GET, POST, PATCH, DELETE)
 * @param path Caminho da API
 * @param data Dados a serem enviados
 * @param options Opções para fetch
 * @param retryOptions Opções para tentativas
 * @returns Resposta da API
 */
export async function apiRequestWithRetry<T = any>(
  method: string,
  path: string,
  data?: any,
  options: RequestInit = {},
  retryOptions: RetryOptions = {}
): Promise<T> {
  // Configurar opções de retry com valores padrão
  const {
    maxRetries = 3,
    initialDelay = 1000,
    backoffMultiplier = 1.5,
    shouldRetry = defaultShouldRetry,
    onRetry = defaultOnRetry
  } = retryOptions;

  let lastError: any = null;

  // Tentar a requisição várias vezes
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Fazer a requisição
      return await apiRequest<T>(path, method, data, options);
    } catch (error) {
      lastError = error;

      // Verificar se deve tentar novamente
      if (attempt < maxRetries - 1 && shouldRetry(error)) {
        // Calcular atraso com backoff exponencial
        const delay = initialDelay * Math.pow(backoffMultiplier, attempt);

        // Notificar sobre a nova tentativa
        onRetry(attempt + 1, error);

        // Aguardar antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // Se não deve tentar novamente ou é a última tentativa, propagar o erro
      throw error;
    }
  }

  // Este ponto só é alcançado se todas as tentativas falharem
  throw lastError || new Error('Falha após várias tentativas');
}

/**
 * Função padrão para decidir se deve tentar novamente com base no erro
 */
function defaultShouldRetry(error: any): boolean {
  // Tentar novamente para erros de rede
  if (error instanceof TypeError &&
      (error.message === 'Failed to fetch' ||
       error.message.includes('NetworkError'))) {
    return true;
  }

  // Tentar novamente para erros de servidor (500+)
  if (error instanceof Error &&
      (error.message.includes('500') ||
       error.message.includes('502') ||
       error.message.includes('503') ||
       error.message.includes('504'))) {
    return true;
  }

  // Tentar novamente para erros de HTML em vez de JSON
  if (error instanceof Error &&
      error.message.includes('Erro no servidor')) {
    return true;
  }

  // Não tentar novamente para outros erros
  return false;
}

/**
 * Função padrão chamada antes de cada nova tentativa
 */
function defaultOnRetry(attempt: number, error: any): void {
  console.log(`Tentativa ${attempt} falhou, tentando novamente...`, error);

  // Mostrar toast apenas na primeira tentativa
  if (attempt === 1) {
    toast({
      title: "Problemas de conexão",
      description: "Tentando reconectar ao servidor...",
      variant: "default"
    });
  }
}
