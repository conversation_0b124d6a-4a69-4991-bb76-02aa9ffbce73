import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { cavaloFormSchema, converterFormParaBanco } from "@shared/schema";
import { useAuth } from "@/context/AuthContext";
import { usePelagens } from "@/hooks/use-pelagens";

interface CavaloFormSimplesProps {
  onSuccess?: (cavalo: any) => void;
  onCancel?: () => void;
  isModal?: boolean;
}

export function CavaloFormSimples({ onSuccess, onCancel, isModal = false }: CavaloFormSimplesProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();

  const form = useForm({
    resolver: zodResolver(cavaloFormSchema),
    defaultValues: {
      nome: "",
      breed: "",
      birth_date: "",
      peso: null,
      altura: null,
      sexo: "",
      pelagem: "",
      status: "ativo",
      data_entrada: "",
      data_saida: "",
      motivo_saida: "",
      pai_nome: "",
      mae_nome: "",
      numero_registro: "",
      origem: "",
      criador: "",
      proprietario: "",
      inspetor: "",
      valor_compra: null,
      data_compra: "",
      observacoes: "",
    },
  });

  const handleSubmit = async (data: any) => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // Usar a mesma conversão do sistema PDF funcionante
      const dadosParaBanco = converterFormParaBanco(data, user.id);

      const response = await fetch("/api/cavalos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "user-id": user.id.toString(),
        },
        body: JSON.stringify(dadosParaBanco),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao cadastrar cavalo");
      }

      const cavalo = await response.json();

      toast({
        title: "Sucesso!",
        description: "Cavalo cadastrado com sucesso!",
        variant: "default",
      });

      if (onSuccess) {
        onSuccess(cavalo);
      }

      if (!isModal) {
        form.reset();
      }
    } catch (error: any) {
      console.error("Erro ao cadastrar cavalo:", error);
      toast({
        title: "Erro",
        description: error.message || "Erro inesperado ao cadastrar cavalo",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Cadastro de Cavalo</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Nome - único campo obrigatório */}
            <FormField
              control={form.control}
              name="nome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome *</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome do cavalo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Raça */}
            <FormField
              control={form.control}
              name="breed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Raça</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Crioulo" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data de Nascimento */}
            <FormField
              control={form.control}
              name="birth_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Nascimento</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sexo */}
            <FormField
              control={form.control}
              name="sexo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sexo</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ""}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o sexo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="nao_informado">Não informado</SelectItem>
                      <SelectItem value="macho">Macho</SelectItem>
                      <SelectItem value="femea">Fêmea</SelectItem>
                      <SelectItem value="Castrado">Castrado</SelectItem>
                      <SelectItem value="garanhao">Garanhão</SelectItem>
                      <SelectItem value="femea">Égua</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Pelagem */}
            <FormField
              control={form.control}
              name="pelagem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pelagem</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isPelagensLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isPelagensLoading
                              ? "Carregando pelagens..."
                              : "Selecione a pelagem"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="nao_informado">
                        Não informado
                      </SelectItem>
                      {pelagens.map((pelagem) => (
                        <SelectItem key={pelagem.id} value={pelagem.nome}>
                          {pelagem.nome}
                        </SelectItem>
                      ))}
                      {!isPelagensLoading && pelagens.length === 0 && (
                        <SelectItem value="outros" disabled>
                          Nenhuma pelagem cadastrada
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Número de Registro */}
            <FormField
              control={form.control}
              name="numero_registro"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Número de Registro</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: ABCCC 12345" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Peso e Altura */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="peso"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Peso (kg)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Ex: 450" 
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="altura"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Altura (m)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="Ex: 1.45" 
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Genealogia */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pai_nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Pai</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do pai" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mae_nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome da Mãe</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome da mãe" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Criador e Proprietário */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="criador"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Criador</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do criador" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="proprietario"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proprietário</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do proprietário" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Observações */}
            <FormField
              control={form.control}
              name="observacoes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Observações sobre o cavalo..." {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Botões */}
            <div className="flex justify-end space-x-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancelar
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? "Cadastrando..." : "Cadastrar Cavalo"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}