/**
 * Componente de visualização de logs para depuração
 * Fornece uma interface para visualizar, filtrar e gerenciar logs
 */

import React, { useState, useEffect } from 'react';
import { logger, LogLevel } from '@/lib/logger';
import { 
  AlertCircle, 
  Clock, 
  Download, 
  Filter, 
  Info, 
  LayoutList, 
  Trash2, 
  X, 
  AlertTriangle,
  Bug,
  Terminal,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Tipo para entradas de log
interface LogEntry {
  timestamp: number;
  level: LogLevel;
  module: string;
  message: string;
  data?: any;
  context?: string;
  error?: Error;
}

// Props do componente LogViewer
interface LogViewerProps {
  open: boolean;
  onClose: () => void;
  initialLevel?: LogLevel;
  initialModule?: string;
}

export function LogViewer({ 
  open, 
  onClose,
  initialLevel = LogLevel.INFO,
  initialModule = 'all'
}: LogViewerProps) {
  // Estado dos logs
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  
  // Estados de filtro
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | 'all'>(initialLevel);
  const [selectedModule, setSelectedModule] = useState<string>(initialModule);
  
  // Estado do modal de detalhes
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  
  // Lista de módulos disponíveis
  const [availableModules, setAvailableModules] = useState<string[]>(['all']);
  
  // Carregar logs ao abrir o componente
  useEffect(() => {
    if (open) {
      refreshLogs();
    }
  }, [open]);
  
  // Atualizar logs filtrados quando mudanças nos filtros ou logs
  useEffect(() => {
    applyFilters();
  }, [logs, searchTerm, selectedLevel, selectedModule]);

  // Função para atualizar a lista de logs
  const refreshLogs = () => {
    const allLogs = logger.getAllLogs();
    setLogs(allLogs);
    
    // Extrair módulos únicos
    const modules = ['all', ...new Set(allLogs.map(log => log.module))];
    setAvailableModules(modules);
  };
  
  // Função para aplicar filtros aos logs
  const applyFilters = () => {
    let filtered = [...logs];
    
    // Filtrar por nível
    if (selectedLevel !== 'all') {
      const levels = Object.values(LogLevel);
      const selectedIndex = levels.indexOf(selectedLevel as LogLevel);
      filtered = filtered.filter(log => 
        levels.indexOf(log.level) <= selectedIndex
      );
    }
    
    // Filtrar por módulo
    if (selectedModule !== 'all') {
      filtered = filtered.filter(log => log.module === selectedModule);
    }
    
    // Filtrar por termo de busca
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(term) || 
        log.module.toLowerCase().includes(term) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(term))
      );
    }
    
    setFilteredLogs(filtered);
  };
  
  // Função para limpar todos os logs
  const clearAllLogs = () => {
    logger.clearLogs();
    refreshLogs();
  };
  
  // Função para exportar logs
  const exportLogs = () => {
    const jsonStr = logger.exportLogs();
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(jsonStr)}`;
    
    const link = document.createElement('a');
    link.href = dataUri;
    link.download = `equigestor_logs_${new Date().toISOString()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Função para abrir detalhes de um log
  const openLogDetails = (log: LogEntry) => {
    setSelectedLog(log);
    setDetailsOpen(true);
  };
  
  // Função para formatar data
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // Função para renderizar ícone do nível de log
  const renderLevelIcon = (level: LogLevel) => {
    switch (level) {
      case LogLevel.ERROR:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case LogLevel.WARN:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case LogLevel.INFO:
        return <Info className="h-4 w-4 text-blue-500" />;
      case LogLevel.DEBUG:
        return <Bug className="h-4 w-4 text-gray-500" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };
  
  // Função para renderizar badge do nível de log
  const renderLevelBadge = (level: LogLevel) => {
    let variant: "default" | "secondary" | "destructive" | "outline" = "default";
    
    switch (level) {
      case LogLevel.ERROR:
        variant = "destructive";
        break;
      case LogLevel.WARN:
        variant = "outline";
        break;
      case LogLevel.INFO:
        variant = "default";
        break;
      case LogLevel.DEBUG:
        variant = "secondary";
        break;
    }
    
    return (
      <Badge variant={variant} className="ml-2">
        {level.toUpperCase()}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Terminal className="h-5 w-5 mr-2" />
            Sistema de Logs EquiGestor
          </DialogTitle>
          <DialogDescription>
            Visualize e analise logs para depuração do sistema
          </DialogDescription>
        </DialogHeader>
        
        {/* Barra de ferramentas */}
        <div className="flex flex-col sm:flex-row gap-2 my-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar logs..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex flex-row gap-2">
            <Select
              value={selectedLevel}
              onValueChange={(value) => setSelectedLevel(value as LogLevel | 'all')}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Nível" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value={LogLevel.ERROR}>{LogLevel.ERROR}</SelectItem>
                <SelectItem value={LogLevel.WARN}>{LogLevel.WARN}</SelectItem>
                <SelectItem value={LogLevel.INFO}>{LogLevel.INFO}</SelectItem>
                <SelectItem value={LogLevel.DEBUG}>{LogLevel.DEBUG}</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={selectedModule}
              onValueChange={setSelectedModule}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Módulo" />
              </SelectTrigger>
              <SelectContent>
                {availableModules.map(module => (
                  <SelectItem key={module} value={module}>
                    {module}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="icon"
              onClick={refreshLogs}
              title="Atualizar logs"
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Tabela de logs */}
        <ScrollArea className="flex-1 border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">Nível</TableHead>
                <TableHead className="w-[180px]">Timestamp</TableHead>
                <TableHead className="w-[120px]">Módulo</TableHead>
                <TableHead>Mensagem</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.length > 0 ? (
                filteredLogs.map((log, index) => (
                  <TableRow 
                    key={`${log.timestamp}_${index}`}
                    className="cursor-pointer hover:bg-secondary/30"
                    onClick={() => openLogDetails(log)}
                  >
                    <TableCell>{renderLevelIcon(log.level)}</TableCell>
                    <TableCell className="font-mono text-xs">
                      {formatDate(log.timestamp)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {log.module}
                      </Badge>
                    </TableCell>
                    <TableCell className="truncate max-w-[300px]">
                      {log.message}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                    Nenhum log encontrado
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </ScrollArea>
        
        {/* Barra de ações */}
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-muted-foreground">
            {filteredLogs.length} log(s) exibido(s) / {logs.length} total
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
              className="flex items-center"
            >
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={clearAllLogs}
              className="flex items-center"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Limpar
            </Button>
          </div>
        </div>
      </DialogContent>
      
      {/* Diálogo de detalhes do log */}
      {selectedLog && (
        <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                {renderLevelIcon(selectedLog.level)}
                <span className="ml-2">Detalhes do Log</span>
                {renderLevelBadge(selectedLog.level)}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-semibold">Timestamp</h4>
                <p className="font-mono text-sm">{formatDate(selectedLog.timestamp)}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold">Módulo</h4>
                <p>{selectedLog.module}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold">Mensagem</h4>
                <p>{selectedLog.message}</p>
              </div>
              
              {selectedLog.context && (
                <div>
                  <h4 className="text-sm font-semibold">Contexto</h4>
                  <p>{selectedLog.context}</p>
                </div>
              )}
              
              {selectedLog.data && (
                <div>
                  <h4 className="text-sm font-semibold">Dados</h4>
                  <pre className="bg-secondary/30 p-2 rounded-md text-xs overflow-auto max-h-[200px]">
                    {JSON.stringify(selectedLog.data, null, 2)}
                  </pre>
                </div>
              )}
              
              {selectedLog.error && (
                <div>
                  <h4 className="text-sm font-semibold">Erro</h4>
                  <div className="bg-red-50 border border-red-200 p-2 rounded-md text-xs">
                    <p className="font-semibold">{selectedLog.error.name}: {selectedLog.error.message}</p>
                    {selectedLog.error.stack && (
                      <pre className="mt-2 overflow-auto max-h-[200px]">
                        {selectedLog.error.stack}
                      </pre>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex justify-end mt-4">
              <Button
                onClick={() => setDetailsOpen(false)}
                className="flex items-center"
              >
                <X className="mr-2 h-4 w-4" />
                Fechar
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Dialog>
  );
}

export default LogViewer;