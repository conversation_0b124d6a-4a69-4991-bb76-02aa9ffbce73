/**
 * Configuração e integração do Sentry para rastreamento de erros
 */
import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { Request, Response, NextFunction, Express } from 'express';
import { getTraceId } from './middleware/tracing';
import { logger } from './logger';

// DSN fornecido para o Sentry
const SENTRY_DSN = process.env.SENTRY_DSN || 'https://<EMAIL>/0'; // Usar variável de ambiente ou DSN padrão

/**
 * Inicializa o Sentry com as configurações apropriadas
 */
export const initSentry = () => {
  try {
    if (!SENTRY_DSN || SENTRY_DSN === 'https://<EMAIL>/0') {
      logger.warn('Sentry DSN não configurado ou inválido. Rastreamento de erros desativado.');
      return false;
    }

    Sentry.init({
      dsn: SENTRY_DSN,
      integrations: [
        // Habilita o HTTP para rastrear requisições HTTP
        new Sentry.Integrations.Http({ tracing: true }),
        // Habilita o Express para rastrear middlewares e rotas
        new Sentry.Integrations.Express({ app: undefined as any }),
        // Habilita o profiling para análise de desempenho
        nodeProfilingIntegration(),
      ],
      // Configuração de amostragem de transações
      tracesSampleRate: 1.0, // Captura 100% das transações em desenvolvimento
      // Configuração de amostragem de profiling
      profileSessionSampleRate: 1.0, // Captura 100% dos profiles em desenvolvimento
      // Configuração do ciclo de vida do profiling
      profileLifecycle: 'trace',
      // Ambiente atual
      environment: process.env.NODE_ENV || 'development',
      // Ativa o modo de debug em desenvolvimento
      debug: process.env.NODE_ENV !== 'production',
      // Versão da aplicação
      release: process.env.npm_package_version || '1.0.0',
    });

    logger.info('Sentry inicializado com sucesso');
    return true;
  } catch (error) {
    logger.error({
      msg: 'Erro ao inicializar Sentry',
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
};

/**
 * Configura os middlewares do Sentry para uma aplicação Express
 */
export const setupSentryMiddlewares = (app: Express) => {
  // RequestHandler cria um middleware que inicializa o Sentry para cada requisição
  app.use(Sentry.Handlers.requestHandler({
    // Adiciona informações da requisição ao escopo do Sentry
    user: ['id', 'username', 'email'],
    request: ['headers', 'method', 'url', 'query_string'],
  }));

  // TracingHandler cria um middleware que adiciona rastreamento de performance
  app.use(Sentry.Handlers.tracingHandler());
};

/**
 * Middleware para capturar exceções e enviar para o Sentry
 * Deve ser adicionado após todas as rotas e antes do errorHandler
 */
export const sentryErrorMiddleware = (app: Express) => {
  app.use(Sentry.Handlers.errorHandler({
    shouldHandleError(error) {
      // Envia para o Sentry apenas erros com status >= 400
      const status = error.status || error.statusCode || 500;
      return status >= 400;
    },
  }));
};

/**
 * Middleware para adicionar contexto do usuário ao Sentry
 */
export const sentryUserContextMiddleware = (req: Request, _res: Response, next: NextFunction) => {
  try {
    const user_id = req.body?.user_id || req.headers['user-id'];
    const traceId = getTraceId(req);

    if (user_id) {
      // Adiciona informações do usuário ao escopo atual do Sentry
      Sentry.configureScope((scope) => {
        scope.setUser({ id: String(user_id) });
        scope.setTag('traceId', traceId);
        scope.setTag('route', `${req.method} ${req.path}`);
      });
    }
  } catch (error) {
    logger.error({
      msg: 'Erro ao configurar contexto do Sentry',
      error: error instanceof Error ? error.message : String(error)
    });
  }

  next();
};

/**
 * Captura uma exceção e envia para o Sentry
 */
export const captureException = (error: Error, req?: Request) => {
  try {
    if (req) {
      // Captura com contexto da requisição
      Sentry.withScope((scope) => {
        const traceId = getTraceId(req);
        scope.setTag('traceId', traceId);
        scope.setTag('route', `${req.method} ${req.path}`);

        if (req.body?.user_id) {
          scope.setUser({ id: String(req.body.user_id) });
        }

        Sentry.captureException(error);
      });
    } else {
      // Captura sem contexto adicional
      Sentry.captureException(error);
    }
  } catch (sentryError) {
    logger.error({
      msg: 'Erro ao enviar exceção para o Sentry',
      error: sentryError instanceof Error ? sentryError.message : String(sentryError),
      originalError: error.message
    });
  }
};

export default {
  initSentry,
  setupSentryMiddlewares,
  sentryErrorMiddleware,
  sentryUserContextMiddleware,
  captureException
};
