import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "@shared/schema";

// Direct PostgreSQL connection for local server deployment
let pool: Pool | null = null;
let db: any = null;

export async function connectToDatabase() {
  if (db) {
    return { success: true, db, pool };
  }

  try {
    const connectionString = process.env.DATABASE_URL;
    
    if (!connectionString) {
      throw new Error('DATABASE_URL not configured');
    }

    console.log('🔌 Connecting to PostgreSQL database...');
    
    // Create PostgreSQL pool
    pool = new Pool({
      connectionString,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Test connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();

    // Create Drizzle instance
    db = drizzle(pool, { schema });

    console.log('✅ Database connected successfully');
    return { success: true, db, pool };

  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return { success: false, error: error.message };
  }
}

export function getDB() {
  if (!db) {
    throw new Error('Database not initialized');
  }
  return db;
}

export function getPool() {
  if (!pool) {
    throw new Error('Database pool not initialized');
  }
  return pool;
}

export function isConnected() {
  return db !== null && pool !== null;
}