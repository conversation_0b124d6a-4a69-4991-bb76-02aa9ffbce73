/**
 * Middleware para geração e gerenciamento de traceID
 * Gera um ID único para cada requisição para facilitar o rastreamento
 */
import { Request, Response, NextFunction } from 'express';
import { randomUUID } from 'crypto';
import { logger } from '../logger';

// Chave para armazenar o traceId no objeto de requisição
export const TRACE_ID_KEY = 'x-trace-id';

/**
 * Middleware que gera um traceID único para cada requisição
 * Adiciona o traceID aos headers da resposta e ao objeto de requisição
 */
export const traceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Verifica se já existe um traceID (pode ter sido gerado por um gateway ou proxy)
    const existingTraceId = req.headers[TRACE_ID_KEY];
    
    // Usa o traceID existente ou gera um novo
    const traceId = existingTraceId ? String(existingTraceId) : randomUUID();
    
    // Adiciona o traceID ao objeto de requisição para uso em outros middlewares
    req.headers[TRACE_ID_KEY] = traceId;
    
    // Adiciona o traceID aos headers da resposta para debugging
    res.setHeader(TRACE_ID_KEY, traceId);
    
    // Cria um logger específico para esta requisição
    const reqLogger = logger.child({ 
      traceId,
      method: req.method,
      path: req.path,
      user_id: req.body?.user_id || 'anonymous'
    });
    
    // Adiciona o logger ao objeto de requisição para uso em outros middlewares
    (req as any).logger = reqLogger;
    
    // Log de início da requisição
    reqLogger.debug({
      msg: 'Requisição iniciada',
      query: req.query,
      params: req.params,
      // Não logamos o body completo para evitar dados sensíveis
      hasBody: !!req.body
    });
    
    // Captura o momento de finalização da requisição
    res.on('finish', () => {
      reqLogger.debug({
        msg: 'Requisição finalizada',
        statusCode: res.statusCode,
        responseTime: Date.now() - (req as any).startTime
      });
    });
    
    // Armazena o tempo de início para cálculo de duração
    (req as any).startTime = Date.now();
    
    next();
  } catch (error) {
    // Em caso de erro no middleware, não bloqueia a requisição
    logger.error({
      msg: 'Erro no middleware de tracing',
      error: error instanceof Error ? error.message : String(error)
    });
    next();
  }
};

/**
 * Obtém o traceID da requisição atual
 */
export const getTraceId = (req: Request): string => {
  return String(req.headers[TRACE_ID_KEY] || 'unknown');
};

/**
 * Obtém o logger específico da requisição
 * Se não existir, cria um novo com o traceID
 */
export const getRequestLogger = (req: Request) => {
  if ((req as any).logger) {
    return (req as any).logger;
  }
  
  const traceId = getTraceId(req);
  return logger.child({ traceId });
};

export default traceMiddleware;
