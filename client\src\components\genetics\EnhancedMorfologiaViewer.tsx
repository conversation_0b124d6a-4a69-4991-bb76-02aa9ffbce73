import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Loader2, 
  BarChart3, 
  PlusCircle, 
  Eye, 
  TrendingUp,
  Target,
  Award,
  Activity
} from "lucide-react";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface MorfologiaData {
  id: number;
  cavaloId: number;
  cavaloNome: string;
  dataAvaliacao: string;
  avaliador: string;
  pontuacaoGeral: number;
  cabeca: number;
  pescoco: number;
  corpo: number;
  membros: number;
  aprumos: number;
  movimentacao: number;
  observacoes?: string;
}

interface EnhancedMorfologiaViewerProps {
  onAddNew?: () => void;
}

export function EnhancedMorfologiaViewer({ onAddNew }: EnhancedMorfologiaViewerProps) {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'individual' | 'comparison'>('individual');

  // Buscar cavalos
  const { data: cavalos, isLoading: isLoadingCavalos } = useQuery<any[]>({
    queryKey: ['/api/cavalos'],
  });

  // Buscar dados de morfologia
  const { data: morfologiaData, isLoading: isLoadingMorfologia } = useQuery<MorfologiaData[]>({
    queryKey: ['/api/morfologia'],
    enabled: true,
  });

  const selectedHorse = cavalos?.find(cavalo => cavalo.id === selectedHorseId);
  const horseMorfologia = morfologiaData?.filter(m => m.cavaloId === selectedHorseId) || [];

  // Dados para gráfico radar
  const getRadarData = (avaliacao: MorfologiaData) => [
    { categoria: 'Cabeça', valor: avaliacao.cabeca, fullMark: 10 },
    { categoria: 'Pescoço', valor: avaliacao.pescoco, fullMark: 10 },
    { categoria: 'Corpo', valor: avaliacao.corpo, fullMark: 10 },
    { categoria: 'Membros', valor: avaliacao.membros, fullMark: 10 },
    { categoria: 'Aprumos', valor: avaliacao.aprumos, fullMark: 10 },
    { categoria: 'Movimentação', valor: avaliacao.movimentacao, fullMark: 10 },
  ];

  // Estatísticas dos cavalos
  const getHorseStats = () => {
    if (!morfologiaData || morfologiaData.length === 0) return null;
    
    const totalAvaliacoes = morfologiaData.length;
    const mediaGeral = morfologiaData.reduce((acc, curr) => acc + curr.pontuacaoGeral, 0) / totalAvaliacoes;
    const melhorPontuacao = Math.max(...morfologiaData.map(m => m.pontuacaoGeral));
    
    return {
      totalAvaliacoes,
      mediaGeral: mediaGeral.toFixed(1),
      melhorPontuacao,
      cavalosAvaliados: new Set(morfologiaData.map(m => m.cavaloId)).size
    };
  };

  const stats = getHorseStats();

  const ScoreCard = ({ title, score, maxScore = 10, icon: Icon }: {
    title: string;
    score: number;
    maxScore?: number;
    icon: any;
  }) => (
    <Card className="transition-all hover:shadow-md">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium">{title}</span>
          </div>
          <Badge variant={score >= 8 ? 'default' : score >= 6 ? 'secondary' : 'outline'}>
            {score.toFixed(1)}/{maxScore}
          </Badge>
        </div>
        <Progress value={(score / maxScore) * 100} className="h-2" />
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Cabeçalho com estatísticas gerais */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Activity className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.totalAvaliacoes}</div>
              <div className="text-sm text-gray-500">Avaliações</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.mediaGeral}</div>
              <div className="text-sm text-gray-500">Média Geral</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Award className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.melhorPontuacao}</div>
              <div className="text-sm text-gray-500">Melhor Score</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Eye className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.cavalosAvaliados}</div>
              <div className="text-sm text-gray-500">Cavalos Avaliados</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Seleção de cavalo */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Análise Morfológica</CardTitle>
              <CardDescription>
                Visualize e compare avaliações morfológicas detalhadas
              </CardDescription>
            </div>
            <Button onClick={onAddNew} className="flex items-center gap-2">
              <PlusCircle className="h-4 w-4" />
              Nova Avaliação
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Select 
                value={selectedHorseId?.toString() || ""} 
                onValueChange={(value) => setSelectedHorseId(Number(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um cavalo..." />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingCavalos ? (
                    <SelectItem value="loading" disabled>
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Carregando...
                      </div>
                    </SelectItem>
                  ) : (
                    cavalos?.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name} {cavalo.breed ? `(${cavalo.breed})` : ''}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'individual' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('individual')}
              >
                Individual
              </Button>
              <Button
                variant={viewMode === 'comparison' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('comparison')}
              >
                Comparação
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Conteúdo principal */}
      {selectedHorseId && selectedHorse && (
        <Tabs defaultValue="scores" className="w-full">
          <TabsList>
            <TabsTrigger value="scores">Pontuações</TabsTrigger>
            <TabsTrigger value="radar">Análise Radar</TabsTrigger>
            <TabsTrigger value="evolution">Evolução</TabsTrigger>
          </TabsList>

          <TabsContent value="scores" className="space-y-4">
            {isLoadingMorfologia ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : horseMorfologia.length > 0 ? (
              <div className="space-y-6">
                {horseMorfologia.map((avaliacao, index) => (
                  <Card key={avaliacao.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">
                            Avaliação #{index + 1}
                          </CardTitle>
                          <CardDescription>
                            {new Date(avaliacao.dataAvaliacao).toLocaleDateString()} - {avaliacao.avaliador}
                          </CardDescription>
                        </div>
                        <Badge 
                          variant={avaliacao.pontuacaoGeral >= 8 ? 'default' : 
                                 avaliacao.pontuacaoGeral >= 6 ? 'secondary' : 'outline'}
                          className="text-lg px-3 py-1"
                        >
                          {avaliacao.pontuacaoGeral.toFixed(1)}/10
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <ScoreCard title="Cabeça" score={avaliacao.cabeca} icon={Eye} />
                        <ScoreCard title="Pescoço" score={avaliacao.pescoco} icon={Activity} />
                        <ScoreCard title="Corpo" score={avaliacao.corpo} icon={Target} />
                        <ScoreCard title="Membros" score={avaliacao.membros} icon={TrendingUp} />
                        <ScoreCard title="Aprumos" score={avaliacao.aprumos} icon={Award} />
                        <ScoreCard title="Movimentação" score={avaliacao.movimentacao} icon={BarChart3} />
                      </div>
                      
                      {avaliacao.observacoes && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium mb-1">Observações:</h4>
                          <p className="text-sm text-gray-600">{avaliacao.observacoes}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nenhuma avaliação encontrada</h3>
                  <p className="text-gray-500 mb-4">
                    Este cavalo ainda não possui avaliações morfológicas registradas.
                  </p>
                  <Button onClick={onAddNew}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Criar primeira avaliação
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="radar" className="space-y-4">
            {horseMorfologia.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Análise Radar - Última Avaliação</CardTitle>
                  <CardDescription>
                    Visualização multidimensional das características morfológicas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={getRadarData(horseMorfologia[horseMorfologia.length - 1])}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="categoria" />
                        <PolarRadiusAxis angle={90} domain={[0, 10]} />
                        <Radar
                          name="Pontuação"
                          dataKey="valor"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.3}
                          strokeWidth={2}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sem dados para análise</h3>
                  <p className="text-gray-500">
                    Adicione avaliações morfológicas para visualizar o gráfico radar.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="evolution" className="space-y-4">
            {horseMorfologia.length > 1 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Evolução das Pontuações</CardTitle>
                  <CardDescription>
                    Acompanhe o progresso morfológico ao longo do tempo
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={horseMorfologia.map((av, index) => ({
                        avaliacao: `#${index + 1}`,
                        pontuacao: av.pontuacaoGeral,
                        data: new Date(av.dataAvaliacao).toLocaleDateString()
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="avaliacao" />
                        <YAxis domain={[0, 10]} />
                        <Tooltip />
                        <Bar dataKey="pontuacao" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Histórico insuficiente</h3>
                  <p className="text-gray-500">
                    São necessárias pelo menos 2 avaliações para mostrar a evolução.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}