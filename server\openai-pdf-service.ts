/**
 * Serviço de Processamento de PDF usando OpenAI 
 * 
 * Este módulo utiliza a API OpenAI para extrair informações de 
 * certificados de registro genealógico da ABCCC.
 */

import fs from 'fs';
import { openai } from './openai-wrapper.js';
import { GenealogiaInfo, CavaloInfo } from './abccc-pdf-parser';
import { processarPdfABCCC } from './abccc-pdf-parser';
// Importação dinâmica para evitar carregamento inicial
const loadPdfParser = async () => {
  try {
    return await import('pdf-parse');
  } catch (error) {
    console.error('Erro ao carregar pdf-parse:', error);
    throw error;
  }
};

// Função que processa o PDF usando a OpenAI
export async function processarPdfComOpenAI(filePath: string): Promise<{
  dados: GenealogiaInfo,
  log: string
}> {
  try {
    console.log('Processando PDF com OpenAI:', filePath);
    
    // Primeiro extraímos o texto do PDF
    const dataBuffer = fs.readFileSync(filePath);
    
    // Carregar o parser de PDF dinamicamente para evitar problemas de inicialização
    const pdfParseModule = await loadPdfParser();
    const pdfParse = pdfParseModule.default;
    
    const resultado = await pdfParse(dataBuffer);
    const textoPdf = resultado.text;
    
    // Verificar se temos um texto razoável para analisar
    if (!textoPdf || textoPdf.length < 100) {
      throw new Error('O PDF extraído contém texto insuficiente para análise (menos de 100 caracteres)');
    }
    
    // Log para ajudar no diagnóstico
    console.log(`Tamanho do texto extraído do PDF: ${textoPdf.length} caracteres`);
    // Salvamos o primeiro trecho para debug
    fs.writeFileSync('texto_extraido_debug.txt', textoPdf.substring(0, 1000));
    
    // Logs
    const logs: { nivel: string, mensagem: string, timestamp: string }[] = [];
    const adicionarLog = (nivel: string, mensagem: string) => {
      logs.push({
        nivel,
        mensagem,
        timestamp: new Date().toISOString()
      });
    };
    
    adicionarLog('info', 'Iniciando processamento com OpenAI');
    
    // Solicitar à OpenAI que extraia as informações do cavalo
    // Vamos pegar uma porção maior do texto para processamento
    const textoCompleto = textoPdf;
    const tamanhoMaximo = Math.min(textoCompleto.length, 8000); // Aumentamos o limite para 8000 caracteres
    
    const promptCavalo = `
    Você é um assistente especializado em extrair informações precisas de registros genealógicos de cavalos Crioulos da ABCCC (Associação Brasileira de Criadores de Cavalos Crioulos).
    
    Sua tarefa é analisar METICULOSAMENTE o texto abaixo que foi extraído de um certificado PDF oficial da ABCCC e extrair TODAS as informações do cavalo principal e sua genealogia, SEM DEIXAR NENHUM CAMPO SEM PREENCHER que esteja no documento.
    
    Texto do certificado:
    """
    ${textoCompleto.substring(0, tamanhoMaximo)}
    """
    
    INSTRUÇÕES CRÍTICAS E OBRIGATÓRIAS:
    1. EXAMINE MINUCIOSAMENTE CADA LINHA do texto para não perder NENHUMA informação.
    2. Você DEVE extrair TODOS os dados disponíveis, mesmo que estejam em formatos não padronizados ou espalhados pelo texto.
    3. É ABSOLUTAMENTE ESSENCIAL que você identifique o nome, registro, sexo, data de nascimento, pelagem, criador, proprietário e inspetor técnico.
    4. NUNCA deixe um campo vazio se a informação existir no texto - isto é CRÍTICO para o funcionamento do sistema.
    5. Quando um campo estiver explicitamente marcado como "Não informado" no documento, VOCÊ DEVE incluir esse texto exatamente assim.
    6. TODOS os campos não encontrados ou vazios que não têm valor explícito "Não informado" devem ser deixados como null no JSON.
    
    PADRÕES ESPECÍFICOS A PROCURAR:
    - NOMES DE CAVALOS: Geralmente em MAIÚSCULAS e podem ter "DA/DO" no nome (ex: "HONESTA 305 DA VENDRAMIN")
    - REGISTRO: Formato "BXXXXXX" (ex: B388719) - geralmente após "SBB:", "Registro:" ou "RP:"
    - SEXO: Procure por "MACHO", "FÊMEA", "FEMEA", "ÉGUA", "GARANHÃO", ou "Não informado"
    - PELAGEM: Palavras como "TORDILHA", "BAIA", "ALAZÃ", "ROSILHA", "GATEADA", "PRETA", "CASTANHA", ou "Não informada"
    - INSPETOR TÉCNICO: Geralmente um nome completo com formato "NOME SOBRENOME" (ex: "MARCOS GOMES ANTUNES")
    - DATAS: Padrões como DD/MM/AAAA ou DD-MM-AAAA, ou "Não informado"
    
    INSTRUÇÕES ESPECIAIS PARA CAMPOS "NÃO INFORMADO":
    - Se o texto contiver explicitamente "Sexo: Não informado", você DEVE incluir o valor "Não informado" (não null).
    - Se o texto contiver explicitamente "Pelagem: Não informada", você DEVE incluir o valor "Não informada" (não null).
    - Se o texto contiver explicitamente "Data de Nascimento: Não informado", você DEVE incluir o valor "Não informado" (não null).
    - Se o texto contiver explicitamente "Criador: Não informado", você DEVE incluir o valor "Não informado" (não null).
    - Se o texto contiver explicitamente "Proprietário: Não informado", você DEVE incluir o valor "Não informado" (não null).
    
    ESTRATÉGIAS AVANÇADAS DE EXTRAÇÃO:
    - Use reconhecimento contextual: se "MARCOS GOMES ANTUNES" aparece próximo a palavras como "inspetor", "técnico", "assinatura", então é o inspetor técnico.
    - Procure por padrões específicos da ABCCC como "INSPETOR TÉCNICO:", "CRIADOR:", "PROPRIETÁRIO:"
    - Examine o texto para encontrar campos que possam estar separados por quebras de linha ou formatação irregular
    - Não confunda campos: Um nome de cavalo não deve ser confundido com nome de criador ou proprietário
    
    CASOS ESPECIAIS:
    - Se encontrar "Não informado" ou equivalente no texto original para algum campo, use EXATAMENTE esse valor (não null)
    - Preste atenção especial aos valores que aparecem como "ID no Sistema" ou "Status Previsto" que são metadados importantes
    - ALERTA: Um erro comum é ignorar campos "Não informado" - você DEVE incluí-los quando explicitamente mencionados
    
    FORMATO DE RESPOSTA:
    {
      "cavalo_principal": {
        "nome": "NOME DO CAVALO PRINCIPAL",
        "registro": "NÚMERO SBB (ex: B388719)",
        "rp": "NÚMERO RP (se existir)",
        "sexo": "MACHO ou FÊMEA ou Não informado",
        "nascimento": "DATA DE NASCIMENTO (DD/MM/AAAA) ou Não informado",
        "pelagem": "COR DO PELO ou Não informada",
        "proprietario": "NOME DO PROPRIETÁRIO ou Não informado",
        "criador": "NOME DO CRIADOR ou Não informado",
        "inspetor_tecnico": "NOME DO INSPETOR TÉCNICO",
        "status": "STATUS DO CAVALO (ex: Atualização)",
        "id_sistema": "ID NO SISTEMA (ex: 99)"
      },
      "pai": {
        "nome": "NOME DO PAI",
        "registro": "NÚMERO SBB DO PAI"
      },
      "mae": {
        "nome": "NOME DA MÃE",
        "registro": "NÚMERO SBB DA MÃE"
      },
      "avo_paterno": {
        "nome": "NOME DO PAI DO PAI",
        "registro": "NÚMERO SBB"
      },
      "ava_paterna": {
        "nome": "NOME DA MÃE DO PAI",
        "registro": "NÚMERO SBB"
      },
      "avo_materno": {
        "nome": "NOME DO PAI DA MÃE",
        "registro": "NÚMERO SBB"
      },
      "ava_materna": {
        "nome": "NOME DA MÃE DA MÃE",
        "registro": "NÚMERO SBB"
      }
    }
    
    EXEMPLO DE ANÁLISE (este é um exemplo CRÍTICO, siga-o à risca):
    
    Para o texto:
    "Cavalo Principal
    Detalhes do cavalo principal identificado no documento.
    Nome: HONESTA 305 DA VENDRAMIN
    Registro: B388719
    Status Previsto: Atualização
    ID no Sistema: 99
    Sexo: Não informado
    Pelagem: Não informada
    Data de Nascimento: Não informado
    Criador: Não informado
    Proprietário: Não informado
    Inspetor Técnico: MARCOS GOMES ANTUNES"
    
    A resposta correta é EXATAMENTE:
    {
      "cavalo_principal": {
        "nome": "HONESTA 305 DA VENDRAMIN",
        "registro": "B388719",
        "sexo": "Não informado",
        "nascimento": "Não informado",
        "pelagem": "Não informada",
        "proprietario": "Não informado",
        "criador": "Não informado",
        "inspetor_tecnico": "MARCOS GOMES ANTUNES",
        "status": "Atualização",
        "id_sistema": "99"
      }
    }
    
    OBSERVAÇÃO FINAL OBRIGATÓRIA: 
    Neste exemplo, note que os valores "Não informado" e "Não informada" ESTÃO PRESENTES no JSON, e não como null ou omitidos!
    É ABSOLUTAMENTE VITAL que você preserve os valores exatos de "Não informado" / "Não informada" quando eles aparecerem no texto.
    
    Formate a resposta como um JSON válido, sem comentários adicionais.
    `;
    
    // Chamada à API da OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-4o", // o modelo mais recente da OpenAI é "gpt-4o" que foi lançado em 13 de maio de 2024
      messages: [
        { role: "system", content: "Você é um assistente especializado na extração de dados estruturados de documentos." },
        { role: "user", content: promptCavalo }
      ],
      max_tokens: 1000,
      response_format: { type: "json_object" }
    });
    
    // Verificar se temos uma resposta válida
    if (!completion || !completion.choices || !completion.choices[0] || !completion.choices[0].message || !completion.choices[0].message.content) {
      adicionarLog('error', `Resposta inválida da OpenAI: ${JSON.stringify(completion)}`);
      throw new Error('A API OpenAI retornou uma resposta inválida');
    }
    
    // Log do conteúdo bruto para debug
    adicionarLog('debug', 'Conteúdo bruto da resposta: ' + completion.choices[0].message.content);
    
    let dadosExtraidos;
    try {
      dadosExtraidos = JSON.parse(completion.choices[0].message.content);
      adicionarLog('info', 'Recebido resposta da OpenAI e parseado com sucesso');
    } catch (parseError) {
      adicionarLog('error', `Erro ao parsear JSON: ${parseError.message}`);
      adicionarLog('error', `Conteúdo que falhou: ${completion.choices[0].message.content}`);
      throw new Error(`Falha ao processar resposta da OpenAI: ${parseError.message}`);
    }
    
    // Verificar se dadosExtraidos é um objeto válido
    if (!dadosExtraidos || typeof dadosExtraidos !== 'object') {
      adicionarLog('error', `Dados extraídos inválidos: ${JSON.stringify(dadosExtraidos)}`);
      throw new Error('A resposta da OpenAI não contém um objeto JSON válido');
    }
    
    // Log da resposta para debug
    adicionarLog('debug', 'Dados extraídos: ' + JSON.stringify(dadosExtraidos, null, 2));
    
    // Verificar se temos os dados do cavalo principal
    // A propriedade pode estar como 'cavalo_principal' ou 'cavalo'
    let dadosCavaloPrincipal = dadosExtraidos.cavalo_principal || dadosExtraidos.cavalo;
    
    // Validação extensiva dos dados extraídos para melhorar a precisão
    if (!dadosCavaloPrincipal) {
      adicionarLog('error', 'Dados do cavalo principal não encontrados na resposta');
      throw new Error('Dados do cavalo principal não encontrados na resposta');
    }
    
    // Log detalhado para diagnóstico
    adicionarLog('info', `Dados do cavalo principal extraídos: ${JSON.stringify(dadosCavaloPrincipal, null, 2)}`);
    
    // Verificar campos críticos
    if (!dadosCavaloPrincipal.nome) {
      adicionarLog('error', 'Nome do cavalo não encontrado nos dados extraídos');
      throw new Error('Nome do cavalo não encontrado nos dados extraídos');
    }
    
    // Normalizar dados para garantir consistência e capturar todos os campos do exemplo
    // Aqui está a principal mudança: preservar valores "Não informado" em vez de convertê-los para null
    // Isso garante que sejam exibidos corretamente na pré-visualização
    dadosCavaloPrincipal = {
      ...dadosCavaloPrincipal,
      nome: dadosCavaloPrincipal.nome?.trim(),
      registro: dadosCavaloPrincipal.registro || dadosCavaloPrincipal.sbb || null,
      // Preservar valor "Não informado" para sexo
      sexo: dadosCavaloPrincipal.sexo?.toUpperCase()?.includes('FEM') ? 'FÊMEA' : 
            dadosCavaloPrincipal.sexo?.toUpperCase()?.includes('FÊM') ? 'FÊMEA' : 
            dadosCavaloPrincipal.sexo,
      // Preservar valores "Não informado" para outros campos
      proprietario: dadosCavaloPrincipal.proprietario,
      criador: dadosCavaloPrincipal.criador,
      inspetor_tecnico: dadosCavaloPrincipal.inspetor_tecnico || dadosCavaloPrincipal.inspetor,
      pelagem: dadosCavaloPrincipal.pelagem || dadosCavaloPrincipal.cor,
      nascimento: dadosCavaloPrincipal.nascimento || dadosCavaloPrincipal.data_nascimento,
      // Campos adicionais que podem estar presentes
      status: dadosCavaloPrincipal.status,
      id_sistema: dadosCavaloPrincipal.id_sistema
    };
    
    if (!dadosCavaloPrincipal || !dadosCavaloPrincipal.nome) {
      adicionarLog('error', 'Dados do cavalo principal não encontrados na resposta da OpenAI');
      adicionarLog('error', 'Estrutura da resposta recebida: ' + JSON.stringify(Object.keys(dadosExtraidos)));
      
      // Verificar se é possível criar um objeto compatível com base em outros campos
      if (dadosExtraidos.nome || (dadosExtraidos.cavalo_nome || dadosExtraidos.nome_cavalo)) {
        adicionarLog('info', 'Tentando reconstruir objeto com base em campos de nível raiz');
        const nomeCavalo = dadosExtraidos.nome || dadosExtraidos.cavalo_nome || dadosExtraidos.nome_cavalo;
        dadosExtraidos.cavalo_principal = {
          nome: nomeCavalo,
          registro: dadosExtraidos.registro || dadosExtraidos.numero_registro,
          rp: dadosExtraidos.rp || dadosExtraidos.registro_particular,
          sexo: dadosExtraidos.sexo,
          nascimento: dadosExtraidos.nascimento || dadosExtraidos.data_nascimento,
          pelagem: dadosExtraidos.pelagem || dadosExtraidos.cor,
          proprietario: dadosExtraidos.proprietario,
          criador: dadosExtraidos.criador,
          inspetor_tecnico: dadosExtraidos.inspetor_tecnico || dadosExtraidos.inspetor
        };
        
        // Tentar novamente com o objeto reconstruído
        dadosCavaloPrincipal = dadosExtraidos.cavalo_principal;
        
        if (dadosCavaloPrincipal && dadosCavaloPrincipal.nome) {
          adicionarLog('info', `Objeto reconstruído com sucesso: ${dadosCavaloPrincipal.nome}`);
          // Continuaremos com a execução normal do código
        } else {
          throw new Error('Falha ao extrair dados do cavalo principal: Tentativa de reconstrução falhou');
        }
      }
      
      throw new Error('Falha ao extrair dados do cavalo principal: O formato da resposta da API não contém as informações necessárias');
    }
    
    // Processamento avançado dos dados para melhorar a extração dos familiares
    
    // Extrair e normalizar dados do PAI
    let dadosPai = dadosExtraidos.pai || {};
    // Verificar se temos informações completas do pai
    if (dadosPai && typeof dadosPai === 'object' && !dadosPai.registro && dadosPai.nome) {
      // Tentar extrair o registro do nome
      const regexRegistro = /[B][0-9]{5,6}/gi;
      const matches = dadosPai.nome.match(regexRegistro);
      if (matches && matches.length > 0) {
        dadosPai.registro = matches[0];
        dadosPai.nome = dadosPai.nome.replace(regexRegistro, '').trim();
      }
    }
    
    // Extrair e normalizar dados da MÃE
    let dadosMae = dadosExtraidos.mae || {};
    // Verificar se temos informações completas da mãe
    if (dadosMae && typeof dadosMae === 'object' && !dadosMae.registro && dadosMae.nome) {
      // Tentar extrair o registro do nome
      const regexRegistro = /[B][0-9]{5,6}/gi;
      const matches = dadosMae.nome.match(regexRegistro);
      if (matches && matches.length > 0) {
        dadosMae.registro = matches[0];
        dadosMae.nome = dadosMae.nome.replace(regexRegistro, '').trim();
      }
    }
    
    // Extrair e normalizar dados dos AVÓS
    let dadosAvoPai = dadosExtraidos.avo_paterno || {};
    let dadosAvaMae = dadosExtraidos.ava_paterna || {};
    let dadosAvoPai2 = dadosExtraidos.avo_materno || {};
    let dadosAvaMae2 = dadosExtraidos.ava_materna || {};
    
    // Extrair registro dos avós se estiver no nome
    [dadosAvoPai, dadosAvaMae, dadosAvoPai2, dadosAvaMae2].forEach(dadosAvo => {
      if (dadosAvo && typeof dadosAvo === 'object' && !dadosAvo.registro && dadosAvo.nome) {
        const regexRegistro = /[B][0-9]{5,6}/gi;
        const matches = dadosAvo.nome.match(regexRegistro);
        if (matches && matches.length > 0) {
          dadosAvo.registro = matches[0];
          dadosAvo.nome = dadosAvo.nome.replace(regexRegistro, '').trim();
        }
      }
    });

    // Log de depuração para todos os dados processados
    console.log('Dados normalizados para conversão:');
    console.log(`Cavalo principal: ${dadosCavaloPrincipal?.nome} (${dadosCavaloPrincipal?.registro || 'sem registro'})`);
    console.log(`Pai: ${dadosPai?.nome} (${dadosPai?.registro || 'sem registro'})`);
    console.log(`Mãe: ${dadosMae?.nome} (${dadosMae?.registro || 'sem registro'})`);
    
    // Verificar se temos os campos status e id_sistema que são específicos do exemplo
    if (dadosCavaloPrincipal.status || dadosCavaloPrincipal.id_sistema) {
      console.log(`Dados adicionais encontrados - Status: ${dadosCavaloPrincipal.status || 'não fornecido'}, ID: ${dadosCavaloPrincipal.id_sistema || 'não fornecido'}`);
    }
    
    // Converter para o formato GenealogiaInfo com os dados normalizados
    const genealogia: GenealogiaInfo = {
      cavalo: converterParaCavaloInfo(dadosCavaloPrincipal || {}),
      pai: converterParaCavaloInfo(dadosPai || {}),
      mae: converterParaCavaloInfo(dadosMae || {}),
      avoPai: converterParaCavaloInfo(dadosAvoPai || {}),
      avaMae: converterParaCavaloInfo(dadosAvaMae || {}),
      avoPai2: converterParaCavaloInfo(dadosAvoPai2 || {}),
      avaMae2: converterParaCavaloInfo(dadosAvaMae2 || {})
    };
    
    // Validação adicional para garantir que os dados principais estejam presentes
    adicionarLog('info', `Dados do cavalo principal extraídos: ${genealogia.cavalo.nome} (${genealogia.cavalo.registro || 'sem registro'})`);
    if (genealogia.pai && genealogia.pai.nome) {
      adicionarLog('info', `Pai: ${genealogia.pai.nome} (${genealogia.pai.registro || 'sem registro'})`);
    }
    if (genealogia.mae && genealogia.mae.nome) {
      adicionarLog('info', `Mãe: ${genealogia.mae.nome} (${genealogia.mae.registro || 'sem registro'})`);
    }
    
    adicionarLog('info', 'Dados convertidos com sucesso');
    
    // Crie um log completo das operações
    const logTexto = logs.map(entry => 
      `[${entry.timestamp}] [${entry.nivel.toUpperCase()}] ${entry.mensagem}`
    ).join('\n');
    
    return {
      dados: genealogia,
      log: logTexto
    };
  } catch (error) {
    console.error('Erro ao processar PDF com OpenAI:', error);
    
    // Se o erro for undefined, trate de maneira explícita
    if (!error) {
      throw new Error('Falha na conexão com a API OpenAI');
    }
    
    // Se o erro for um objeto de erro ou uma string, encaminhe-o
    if (error instanceof Error) {
      throw new Error(`Falha na extração de dados com OpenAI: ${error.message}`);
    } else {
      throw new Error(`Falha na extração de dados com OpenAI: ${String(error)}`);
    }
    
    // Código abaixo fica inacessível devido aos throws acima, mas deixamos
    // como referência para implementação futura de fallback
    /*
    // Em caso de erro, tente usar o parser tradicional
    console.log('Tentando usar o parser tradicional...');
    const resultadoTradicional = await processarPdfABCCC(filePath);
    
    return {
      dados: resultadoTradicional.dados,
      log: resultadoTradicional.logs.map(log => 
        `[${log.timestamp}] [${log.nivel.toUpperCase()}] ${log.mensagem}`
      ).join('\n')
    };
    */
  }
}

// Função auxiliar para converter os dados extraídos para CavaloInfo com melhoria na detecção de padrões e normalização
function converterParaCavaloInfo(dados: any): CavaloInfo {
  // Se não tiver dados, retorna um objeto com o nome obrigatório
  if (!dados || Object.keys(dados).length === 0) {
    return { nome: '' };
  }
  
  // Extrair o nome com tratamento especial
  let nome = dados.nome || '';
  
  // Extrair registro com vários formatos possíveis
  let registro = '';
  if (dados.registro) {
    registro = dados.registro;
  } else if (dados.numero_registro) {
    registro = dados.numero_registro;
  } else if (dados.sbb) {
    registro = dados.sbb;
  } else if (dados.number) {
    registro = dados.number;
  } else if (typeof nome === 'string' && nome) {
    // Tenta extrair o número do registro do próprio nome se tiver um padrão como "NOME B12345"
    const regexRegistro = /[B][0-9]{5,6}/gi;
    const matches = nome.match(regexRegistro);
    if (matches && matches.length > 0) {
      registro = matches[0];
      // Remove o registro do nome para evitar duplicação
      nome = nome.replace(regexRegistro, '').trim();
    }
  }
  
  // Limpar o registro (remover prefixos comuns e espaços)
  if (registro) {
    // Remove prefixos como "SBB:", "Registro:", etc.
    registro = registro.toString().trim()
      .replace(/^SBB[:\s]+/i, '')
      .replace(/^REGISTRO[:\s]+/i, '')
      .replace(/^REG[:\s]+/i, '')
      .replace(/[^a-zA-Z0-9]/g, ''); // Remove caracteres especiais
  }
  
  // Normalização de sexo
  let sexo = '';
  if (dados.sexo) {
    const sexoUpper = dados.sexo.toString().toUpperCase();
    if (sexoUpper.includes('FÊMEA') || sexoUpper.includes('FEMEA') || sexoUpper.includes('FEM')) {
      sexo = 'Fêmea';
    } else if (sexoUpper.includes('MACHO')) {
      sexo = 'Macho';
    } else if (sexoUpper.includes('GARANHÃO') || sexoUpper.includes('GARANHAO')) {
      sexo = 'Garanhão';
    } else if (sexoUpper.includes('ÉGUA') || sexoUpper.includes('EGUA')) {
      sexo = 'Égua';
    } else if (sexoUpper.includes('CASTRADO')) {
      sexo = 'Macho (Castrado)';
    } else {
      sexo = dados.sexo.trim();
    }
  }
  
  // Normalização da data de nascimento
  const nascimento = (dados.data_nascimento || dados.nascimento || dados.dataNascimento || '').toString().trim();
  
  // Normalização da pelagem
  const pelagem = (dados.pelagem || dados.cor || '').toString().trim();
  
  // Normalização de outros campos
  const criador = (dados.criador || '').toString().trim();
  const proprietario = (dados.proprietario || '').toString().trim();
  const inspetor = (dados.inspetor_tecnico || dados.inspetor || '').toString().trim();
  const origem = (dados.origem || dados.haras || '').toString().trim();
  
  // Montagem do objeto final com todos os campos normalizados
  const info: CavaloInfo = {
    nome: nome.trim()
  };
  
  // Adicionar apenas campos com valores
  if (registro) info.registro = registro;
  if (dados.rp) info.rp = dados.rp.toString().trim();
  if (sexo) info.sexo = sexo;
  if (nascimento) info.nascimento = nascimento;
  if (pelagem) info.pelagem = pelagem;
  if (criador) info.criador = criador;
  if (proprietario) info.proprietario = proprietario;
  if (inspetor) info.inspetor = inspetor;
  if (origem) info.origem = origem;
  
  // Campos adicionais específicos
  if (dados.status) info.status = dados.status.toString().trim();
  if (dados.id_sistema) info.id_sistema = dados.id_sistema.toString().trim();
  
  // Log para depuração
  console.log(`Convertido para CavaloInfo: ${nome} (registro: ${registro || 'não identificado'})`);
  
  return info;
}