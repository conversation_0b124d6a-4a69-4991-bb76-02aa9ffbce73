// Improved authentication middleware
import { Request, Response, NextFunction } from 'express';
import { AuthenticationError, AuthorizationError } from '../core/errors';
import { UserService } from '../services/user.service';
import { getDatabaseService } from '../core/database';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'default-jwt-secret';

export interface AuthenticatedRequest extends Request {
  user?: any;
}

export const createAuthMiddleware = () => {
  const db = getDatabaseService();
  const userService = new UserService(db);

  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      let userId: any;

      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.slice(7).trim();
        try {
          const payload: any = jwt.verify(token, JWT_SECRET);
          userId = payload.user_id ?? payload.id ?? payload.userId;
        } catch (tokenError) {
          console.error('JWT decode error:', tokenError);
        }
      }

      if (!userId) {
        userId =
          req.query.user_id ||
          req.body.user_id ||
          req.headers['user-id'] ||
          req.headers['x-user-id'];
      }

      if (!userId) {
        console.log('🔐 Auth check - No user ID provided');
        throw new AuthenticationError('Authentication required');
      }

      const userResult = await userService.findById(Number(userId));
      if (!userResult.success) {
        console.log(`🔐 Auth check - User not found: ${userId}`);
        throw new AuthenticationError('Invalid user');
      }

      req.user = userResult.data;
      console.log(`🔐 Auth check - Method: ${req.method}, Path: ${req.path}, User ID: ${req.user.id}`);
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      
      if (error instanceof AuthenticationError) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: error.message
          }
        });
      }

      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Authentication failed'
        }
      });
    }
  };
};

export const requireAdmin = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      error: {
        code: 'AUTHORIZATION_ERROR',
        message: 'Admin access required'
      }
    });
  }
  next();
};

export const requireOwnership = (resourceUserIdField: string = 'user_id') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const resourceUserId = req.body[resourceUserIdField] || req.params[resourceUserIdField];
    
    if (!req.user || (req.user.id !== Number(resourceUserId) && req.user.role !== 'ADMIN')) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'AUTHORIZATION_ERROR',
          message: 'Access denied to this resource'
        }
      });
    }
    next();
  };
};

// Legacy authentication function for backward compatibility
export const authenticateUser = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authMiddleware = createAuthMiddleware();
  return authMiddleware(req, res, next);
};