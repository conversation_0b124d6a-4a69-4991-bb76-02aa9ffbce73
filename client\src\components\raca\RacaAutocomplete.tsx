/**
 * Componente de Autocomplete para Raças de Cavalos
 * Implementa busca e seleção das principais raças brasileiras
 */

import React, { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useRacas } from '@/hooks/use-racas';

interface RacaAutocompleteProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function RacaAutocomplete({
  value,
  onValueChange,
  placeholder = "Selecione uma raça...",
  disabled = false
}: RacaAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  
  const { racas } = useRacas();

  // Filtrar raças baseado na busca
  const racasFiltradas = racas.filter(raca =>
    raca.toLowerCase().includes(search.toLowerCase())
  );

  const handleSelectRaca = (raca: string) => {
    onValueChange(raca);
    setOpen(false);
    setSearch('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {value || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput
            placeholder="Buscar raça..."
            value={search}
            onValueChange={setSearch}
          />
          <CommandEmpty>
            <div className="p-4 text-center">
              <p className="text-sm text-muted-foreground">
                Nenhuma raça encontrada para "{search}"
              </p>
            </div>
          </CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {racasFiltradas.map((raca) => (
              <CommandItem
                key={raca}
                onSelect={() => handleSelectRaca(raca)}
                className="cursor-pointer"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value === raca ? "opacity-100" : "opacity-0"
                  )}
                />
                {raca}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}