import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Eye, Plus, Activity, TrendingUp, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGeneticsContext } from '@/contexts/GeneticsContext';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

// Schema de validação
const desempenhoSchema = z.object({
  data: z.string().min(1, 'Data é obrigatória'),
  atividade: z.string().min(1, 'Atividade é obrigatória'),
  tempo: z.string().optional(),
  distancia: z.number().min(0).optional(),
  velocidade: z.number().min(0).optional(),
  observacoes: z.string().optional(),
});

type DesempenhoFormData = z.infer<typeof desempenhoSchema>;

interface DesempenhoRecord {
  id: number;
  horse_id: number;
  data: string;
  atividade: string;
  tempo?: string;
  distancia?: number;
  velocidade?: number;
  observacoes?: string;
  created_at: string;
}

export default function DesempenhoPageModern() {
  const { selectedHorse } = useGeneticsContext();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [viewingRecord, setViewingRecord] = useState<DesempenhoRecord | null>(null);
  const [selectedAtividade, setSelectedAtividade] = useState('');

  const form = useForm<DesempenhoFormData>({
    resolver: zodResolver(desempenhoSchema),
    defaultValues: {
      data: new Date().toISOString().split('T')[0],
      atividade: '',
      observacoes: '',
    },
  });

  // Query para buscar dados de desempenho
  const { data: desempenhoData = [], refetch: refetchDesempenho, isLoading } = useQuery<DesempenhoRecord[]>({
    queryKey: [`/api/desempenho/horse/${selectedHorse?.id}`],
    enabled: !!selectedHorse?.id,
    staleTime: 0,
    gcTime: 0,
  });

  // Mutation para criar registro
  const createMutation = useMutation({
    mutationFn: async (data: DesempenhoFormData) => {
      const response = await fetch('/api/desempenho', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          horse_id: selectedHorse?.id,
        }),
      });
      if (!response.ok) throw new Error('Falha ao criar registro');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Sucesso",
        description: "Registro de desempenho cadastrado com sucesso!",
      });
      setIsCreateDialogOpen(false);
      form.reset();
      // Recarregar dados
      setTimeout(() => {
        refetchDesempenho();
      }, 100);
    },
    onError: (error) => {
      console.error('Erro ao criar desempenho:', error);
      toast({
        title: "Erro",
        description: "Falha ao cadastrar registro de desempenho.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (data: DesempenhoFormData) => {
    if (!selectedHorse) {
      toast({
        title: "Erro",
        description: "Selecione um cavalo primeiro.",
        variant: "destructive",
      });
      return;
    }
    createMutation.mutate(data);
  };

  const handleViewRecord = (record: DesempenhoRecord) => {
    setViewingRecord(record);
  };

  if (!selectedHorse) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Activity className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground">
            Selecione um cavalo para visualizar dados de desempenho
          </h3>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando dados de desempenho...</p>
        </div>
      </div>
    );
  }

  // Estatísticas resumidas
  const totalRegistros = desempenhoData?.length || 0;
  const ultimoTreino = desempenhoData?.length > 0 ? 
    desempenhoData.sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())[0] : null;
  
  // Filtrar por atividade para estatísticas
  const atividadesUnicas = desempenhoData?.length > 0 ? 
    [...new Set(desempenhoData.map(d => d.atividade))].length : 0;
    
  // Atividades dos últimos 30 dias
  const trintaDiasAtras = new Date();
  trintaDiasAtras.setDate(trintaDiasAtras.getDate() - 30);
  const atividadesRecentes = desempenhoData?.filter(d => 
    new Date(d.data) >= trintaDiasAtras
  ) || [];

  return (
    <div className="space-y-6">
      {/* Header com informações do cavalo */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Desempenho</h2>
          <p className="text-muted-foreground">
            Histórico de desempenho de {selectedHorse.name}
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Novo Registro
        </Button>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total de Registros
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRegistros}</div>
            <p className="text-xs text-muted-foreground">
              registros de desempenho
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Último Treino
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {ultimoTreino ? format(new Date(ultimoTreino.data), 'dd/MM/yyyy', { locale: pt }) : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              data do último treino
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Atividades
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{atividadesUnicas}</div>
            <p className="text-xs text-muted-foreground">
              tipos diferentes
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Últimos 30 dias
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{atividadesRecentes.length}</div>
            <p className="text-xs text-muted-foreground">
              atividades recentes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Registros */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Desempenho</CardTitle>
          <CardDescription>
            Registros de desempenho organizados por data
          </CardDescription>
        </CardHeader>
        <CardContent>
          {totalRegistros === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                Nenhum registro encontrado
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Adicione o primeiro registro de desempenho deste cavalo
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Primeiro Registro
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {desempenhoData.map((record) => (
                <div
                  key={record.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Badge variant="outline">
                        {format(new Date(record.data), 'dd/MM/yyyy', { locale: pt })}
                      </Badge>
                      <Badge variant="secondary">
                        {record.atividade}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-muted-foreground">
                      {record.tempo && (
                        <span>Tempo: {record.tempo}</span>
                      )}
                      {record.distancia && (
                        <span>Distância: {record.distancia}m</span>
                      )}
                      {record.velocidade && (
                        <span>Velocidade: {record.velocidade} km/h</span>
                      )}
                      {record.observacoes && (
                        <span>Com observações</span>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewRecord(record)}
                    className="gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    Ver Detalhes
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog para Novo Registro */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Novo Registro de Desempenho</DialogTitle>
            <DialogDescription>
              Registrar nova atividade de desempenho para {selectedHorse.name}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data da Atividade</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="atividade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Atividade</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a atividade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Trote">Trote</SelectItem>
                          <SelectItem value="Galope">Galope</SelectItem>
                          <SelectItem value="Corrida">Corrida</SelectItem>
                          <SelectItem value="Salto">Salto</SelectItem>
                          <SelectItem value="Adestramento">Adestramento</SelectItem>
                          <SelectItem value="Trabalho de Solo">Trabalho de Solo</SelectItem>
                          <SelectItem value="Rédea">Rédea</SelectItem>
                          <SelectItem value="Apartação">Apartação</SelectItem>
                          <SelectItem value="Enduro">Enduro</SelectItem>
                          <SelectItem value="CCE">CCE (Concurso Completo)</SelectItem>
                          <SelectItem value="Outro">Outro</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="tempo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tempo (HH:MM:SS)</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="00:05:30"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="distancia"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Distância (metros)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          step="1"
                          placeholder="1000"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="velocidade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Velocidade (km/h)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          step="0.1"
                          placeholder="25.5"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="observacoes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Observações sobre o desempenho, condições do treino, comportamento do animal..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createMutation.isPending}>
                  {createMutation.isPending ? 'Salvando...' : 'Salvar Registro'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Dialog para Visualizar Registro */}
      <Dialog open={!!viewingRecord} onOpenChange={() => setViewingRecord(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalhes do Registro de Desempenho</DialogTitle>
            <DialogDescription>
              {viewingRecord && format(new Date(viewingRecord.data), 'dd/MM/yyyy', { locale: pt })}
            </DialogDescription>
          </DialogHeader>

          {viewingRecord && (
            <div className="space-y-6">
              {/* Informações Gerais */}
              <div className="space-y-2">
                <h4 className="font-medium">Informações da Atividade</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Data:</span> {format(new Date(viewingRecord.data), 'dd/MM/yyyy', { locale: pt })}
                  </div>
                  <div>
                    <span className="font-medium">Atividade:</span> {viewingRecord.atividade}
                  </div>
                </div>
              </div>

              {/* Métricas de Performance */}
              <div className="space-y-2">
                <h4 className="font-medium">Métricas de Performance</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  {viewingRecord.tempo && (
                    <div className="bg-muted p-3 rounded-lg text-center">
                      <div className="font-medium text-lg">{viewingRecord.tempo}</div>
                      <div className="text-muted-foreground">Tempo</div>
                    </div>
                  )}
                  {viewingRecord.distancia && (
                    <div className="bg-muted p-3 rounded-lg text-center">
                      <div className="font-medium text-lg">{viewingRecord.distancia}m</div>
                      <div className="text-muted-foreground">Distância</div>
                    </div>
                  )}
                  {viewingRecord.velocidade && (
                    <div className="bg-muted p-3 rounded-lg text-center">
                      <div className="font-medium text-lg">{viewingRecord.velocidade} km/h</div>
                      <div className="text-muted-foreground">Velocidade</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Observações */}
              {viewingRecord.observacoes && (
                <div className="space-y-2">
                  <h4 className="font-medium">Observações</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">
                    {viewingRecord.observacoes}
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setViewingRecord(null)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}