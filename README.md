# RS HORSE - Advanced Equine Management Platform

RS HORSE is an advanced equine management platform that combines intelligent financial tracking with robust authentication and comprehensive user experience management.

## Key Technologies

- **Frontend**: React with TypeScript, responsive mobile-first design
- **Backend**: Node.js with robust API infrastructure  
- **Authentication**: Context-based secure login system
- **Database**: PostgreSQL for detailed horse and financial records
- **Core Libraries**: Drizzle ORM, Zod Schema Validation, React Query
- **AI Integration**: OpenAI-powered smart assistant
- **Deployment**: Google Cloud Run with enhanced error handling

## Project Structure

```
.
├── client/              # React frontend application
├── server/              # Express.js backend API
├── shared/              # Shared schemas and types
├── types/               # TypeScript type definitions
├── uploads/             # File upload storage
└── node_modules/        # Dependencies
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Database operations
npm run db:push
npm run db:studio
```

## Features

- Advanced horse management and tracking
- Financial management and reporting
- Veterinary record keeping  
- Genealogy and breeding management
- AI-powered assistant
- Mobile-responsive design
- Secure authentication system

## Recently Cleaned

The project has been optimized by removing unnecessary files:
- Test files and temporary scripts
- Log files and debug outputs
- Backup files and migrations
- Documentation artifacts
- Build artifacts and cache files

This cleanup significantly reduced the project size while maintaining all core functionality.