import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import GenealogiaSelector, { EntradaGenealogica } from "@/components/cavalo/GenealogiaSelector";
import { usePelagens } from "@/hooks/use-pelagens";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft } from "lucide-react";
import HorseAvatarWithUpload from "@/components/horse/HorseAvatarWithUpload";

// Zod Schema (igual ao InsertCavalo, padronizado!)
const horseSchema = z.object({
  name: z.string().min(2, "Nome deve ter no mínimo 2 caracteres"),
  breed: z.string().min(1, "Selecione uma raça"),
  birth_date: z.string().min(1, "Data de nascimento é obrigatória"),
  peso: z.string().optional(),
  altura: z.string().optional(),
  sexo: z.string().optional(),
  cor: z.string().optional(),
  pelagem_id: z.number().nullable().optional(),
  status: z.string().optional(),
  data_entrada: z.string().optional(),
  data_saida: z.string().optional(),
  numero_registro: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  inspetor: z.string().optional(),
  origem: z.string().optional(),
  observacoes: z.string().optional(),
  motivo_saida: z.string().optional(),
  
  // Novos campos para o modelo de genealogia dual
  pai_id: z.number().nullable().optional(),
  pai_nome: z.string().nullable().optional(),
  mae_id: z.number().nullable().optional(),
  mae_nome: z.string().nullable().optional(),
  
  // Campos para o componente GenealogiaSelector
  pai: z.object({
    tipo: z.enum(['sistema', 'externo', 'nenhum']),
    cavaloSistemaId: z.string().nullable().optional(),
    cavaloNome: z.string().nullable().optional()
  }).optional(),
  
  mae: z.object({
    tipo: z.enum(['sistema', 'externo', 'nenhum']),
    cavaloSistemaId: z.string().nullable().optional(),
    cavaloNome: z.string().nullable().optional()
  }).optional(),
  
  // Campos adicionais de genealogia completa
  avoPaterno: z.string().optional(),
  avoPaterna: z.string().optional(), // Nova - avó paterna
  avoMaterno: z.string().optional(),
  avoMaterna: z.string().optional(), // Nova - avó materna
  
  // Outros campos
  notes: z.string().optional(),
});

type HorseFormData = z.infer<typeof horseSchema>;

const EditHorsePage = () => {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });

  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();

  // ID do cavalo na URL: /cavalo/123/editar
  const location = useLocation()[0];
  const horse_id = location.split("/")[2];

  // Buscar cavalo pelo ID
  const {
    data: horse,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/cavalos/${horse_id}`],
    enabled: !!horse_id && !!user,
    queryFn: async () => {
      try {
        console.log(`=== BUSCANDO CAVALO ${horse_id} ===`);
        const result = await apiRequest<Cavalo>(`/api/cavalos/${horse_id}`, "GET");
        console.log("DADOS RECEBIDOS DO SERVIDOR:", result);
        return result;
      } catch (error) {
        console.error("ERRO AO BUSCAR CAVALO:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados do cavalo",
          variant: "destructive",
        });
        return null;
      }
    },
  });
  
  // Buscar todos os cavalos (incluindo externos) para seleção de pai/mãe - com cache otimizado
  const { 
    data: todosCavalos = [], 
    isLoading: isLoadingCavalos 
  } = useQuery({
    queryKey: ['/api/cavalos-genealogia'],
    enabled: !!user && !!horse, // Só buscar após carregar o cavalo
    staleTime: 10 * 60 * 1000, // Cache por 10 minutos
    gcTime: 30 * 60 * 1000, // Manter em cache por 30 minutos
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>("/api/cavalos-genealogia", "GET");
      } catch (error) {
        console.error("Erro ao carregar lista de cavalos para genealogia:", error);
        return [];
      }
    },
  });

  // Formulário controlado
  const form = useForm<HorseFormData>({
    resolver: zodResolver(horseSchema),
    defaultValues: {
      name: "",
      breed: "",
      birth_date: "",
      peso: "",
      altura: "",
      sexo: "",
      status: "ativo",
      data_entrada: "",
      data_saida: "",
      motivo_saida: "",
      // Usando pai_id e mae_id em vez de pai/mae para compatibilidade com CavaloForm
      pai_id: null,
      pai_nome: "",
      mae_id: null,
      mae_nome: "",
      // Campos para o componente GenealogiaSelector
      pai: {
        tipo: 'nenhum',
        cavaloSistemaId: null,
        cavaloNome: null
      },
      mae: {
        tipo: 'nenhum',
        cavaloSistemaId: null,
        cavaloNome: null
      },
      avoPaterno: "",
      avoMaterno: "",
      notes: "",
      cor: "",
    },
  });

  // Preencher formulário com dados do cavalo
  useEffect(() => {
    if (horse) {
      console.log('=== CARREGANDO DADOS DO CAVALO ===');
      console.log('Dados recebidos:', horse);
      
      // Converter data se necessário
      let formattedBirthDate = "";
      if (horse.birth_date) {
        if (horse.birth_date.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          const parts = horse.birth_date.split('/');
          formattedBirthDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
        } else {
          formattedBirthDate = horse.birth_date;
        }
      }

      // Preparar dados de genealogia usando type assertion
      const horseData = horse as any;
      const paiData = horseData.pai_id ? {
        tipo: "sistema" as const,
        cavaloSistemaId: horseData.pai_id.toString(),
        cavaloNome: horseData.pai_nome || null
      } : horseData.pai_nome ? {
        tipo: "externo" as const,
        cavaloSistemaId: null,
        cavaloNome: horseData.pai_nome
      } : {
        tipo: "nenhum" as const,
        cavaloSistemaId: null,
        cavaloNome: null
      };

      const maeData = horseData.mae_id ? {
        tipo: "sistema" as const,
        cavaloSistemaId: horseData.mae_id.toString(),
        cavaloNome: horseData.mae_nome || null
      } : horseData.mae_nome ? {
        tipo: "externo" as const,
        cavaloSistemaId: null,
        cavaloNome: horseData.mae_nome
      } : {
        tipo: "nenhum" as const,
        cavaloSistemaId: null,
        cavaloNome: null
      };

      // Preencher formulário
      const formData = {
        name: horse.name || "",
        breed: horse.breed || "",
        birth_date: formattedBirthDate,
        peso: horse.peso ? horse.peso.toString() : "",
        altura: horse.altura ? horse.altura.toString() : "",
        sexo: horse.sexo || "",
        cor: horse.cor || "",
        pelagem_id: horse.pelagem_id || null,
        status: horse.status || "ativo",
        data_entrada: horse.data_entrada || "",
        data_saida: horse.data_saida || "",
        motivo_saida: horse.motivo_saida || "",
        numero_registro: horse.numero_registro || "",
        criador: horse.criador || "",
        proprietario: horse.proprietario || "",
        inspetor: horse.inspetor || "",
        origem: horse.origem || "",
        observacoes: horse.notes || "",
        pai_id: horse.pai_id || null,
        pai_nome: horseData.pai_nome || "",
        mae_id: horseData.mae_id || null,
        mae_nome: horseData.mae_nome || "",
        pai: paiData,
        mae: maeData,
        avo_paterno: horseData.avo_paterno || "",
        avoPaterna: "",
        avo_materno: horseData.avo_materno || "",
        avoMaterna: "",
        notes: horse.notes || "",
      };

      console.log('Dados para o formulário:', formData);
      form.reset(formData);
      console.log('=== FORMULÁRIO PREENCHIDO ===');
    }
  }, [horse, form]);

  // Payload pronto para não perder dados!
  const onSubmit = async (data: HorseFormData) => {
    if (!user) return;
    setLoading(true);
    
    // Logs detalhados para diagnóstico
    console.log('************ INICIANDO SUBMISSÃO DO FORMULÁRIO ************');
    console.log('DADOS DO FORM:', data);
    console.log('CAVALO ORIGINAL:', horse);
    
    // Diagnóstico detalhado da genealogia
    console.log('DIAGNÓSTICO GENEALOGIA:', {
      form_pai_id: data.pai_id,
      form_mae_id: data.mae_id,
      form_pai: data.pai,
      form_mae: data.mae,
      db_pai: (horse as any)?.pai_nome || (horse as any)?.pai_id,
      db_mae: (horse as any)?.mae_nome || (horse as any)?.mae_id,
      pai_id_type: typeof data.pai_id,
      mae_id_type: typeof data.mae_id
    });

    try {
      // Extrair e processar dados de genealogia
      // Usar dados do pai e mãe inseridos pelo GenealogiaSelector
      const infoPai = data.pai || { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };
      const infoMae = data.mae || { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };
      
      // Determinar os valores finais para pai_id/pai_nome e mae_id/mae_nome
      const pai_id_final = infoPai.tipo === 'sistema' && infoPai.cavaloSistemaId 
        ? Number(infoPai.cavaloSistemaId) 
        : null;
      
      const pai_nome_final = infoPai.tipo === 'externo' && infoPai.cavaloNome 
        ? infoPai.cavaloNome 
        : null;
        
      const mae_id_final = infoMae.tipo === 'sistema' && infoMae.cavaloSistemaId 
        ? Number(infoMae.cavaloSistemaId) 
        : null;
      
      const mae_nome_final = infoMae.tipo === 'externo' && infoMae.cavaloNome 
        ? infoMae.cavaloNome 
        : null;
      
      // Montar o payload para atualização
      const updatePayload = {
        name: data.name,
        breed: data.breed,
        birth_date: data.birth_date,
        peso: data.peso ? parseFloat(data.peso) : null,
        altura: data.altura ? parseFloat(data.altura) : null,
        sexo: data.sexo || null,
        cor: data.cor || null,
        status: data.status || "ativo",
        data_entrada: data.data_entrada || null,
        data_saida: data.data_saida || null,
        motivo_saida: data.motivo_saida || null,
        
        // Novo modelo dual para genealogia
        pai_id: pai_id_final,
        pai_nome: pai_nome_final,
        mae_id: mae_id_final,
        mae_nome: mae_nome_final,
        
        // Removendo os campos pai/mae que são estruturas complexas para evitar erro na API
        pai: undefined,
        mae: undefined,
        
        // Log detalhado das informações de genealogia para depuração
        _debug_genealogia: JSON.stringify({
          pai_completo: data.pai,
          mae_completo: data.mae,
          pai_id_final: pai_id_final,
          pai_nome_final: pai_nome_final,
          mae_id_final: mae_id_final,
          mae_nome_final: mae_nome_final
        }),
        
        avoPaterno: data.avoPaterno || null,
        avoMaterno: data.avoMaterno || null,
        notes: data.notes || null,
      };
      // Logs de depuração para entender o fluxo e conversão de valores
      console.log('************ DEBUG - EDIÇÃO DE CAVALO ************');
      console.log('FORMULÁRIO SUBMETIDO (data):', data);
      console.log('MAPEAMENTO GENEALOGIA:', {
        infoPai: infoPai,
        infoMae: infoMae,
        pai_id_final: pai_id_final,
        pai_nome_final: pai_nome_final,
        mae_id_final: mae_id_final,
        mae_nome_final: mae_nome_final
      });
      console.log('PAYLOAD DE ATUALIZAÇÃO:', updatePayload);
      console.log('************ FIM DEBUG ************');
      
      // Opcional: remover campos vazios do payload
      Object.keys(updatePayload).forEach((key) => {
        if (updatePayload[key as keyof typeof updatePayload] === "") {
          (updatePayload as any)[key] = null;
        }
      });

      await apiRequest(`/api/cavalos/${horse_id}`, "PUT", updatePayload);

      // Mostrar toast incluindo mensagem específica sobre pais
      if (data.pai_id || data.mae_id) {
        toast({
          title: "Cavalo atualizado",
          description: `${data.name} foi atualizado com sucesso! Informações de genealogia salvas.`,
        });
      } else {
        toast({
          title: "Cavalo atualizado",
          description: `${data.name} foi atualizado com sucesso!`,
        });
      }
      
      // Atualizar o cache de cavalos para mostrar dados atualizados
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horse_id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      
      // Pequeno atraso para garantir que as atualizações sejam processadas
      setTimeout(() => {
        navigate(`/cavalo/${horse_id}`);
      }, 300);
    } catch (error: any) {
      toast({
        title: "Falha na atualização",
        description: error.message || "Não foi possível atualizar o cavalo",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/cavalo/${horse_id}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div>
              <h3 className="text-red-800 font-medium">Erro ao carregar</h3>
              <p className="text-red-700 mt-1">
                Não foi possível carregar os dados do cavalo. Por favor, tente
                novamente.
              </p>
              <Button
                variant="outline"
                onClick={() => navigate("/")}
                className="mt-4"
              >
                Voltar para o Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <button
          onClick={handleCancel}
          className="inline-flex items-center mb-4 text-gray-500 hover:text-gray-700"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para detalhes do cavalo
        </button>
        <h1 className="text-2xl font-bold text-gray-900">
          Editar Cavalo: {horse.name}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Atualize as informações do cavalo.
        </p>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Avatar com upload */}
              <div className="flex justify-center mb-6">
                <HorseAvatarWithUpload
                  horse_id={parseInt(horse_id)}
                  horseName={horse.name || "Cavalo"}
                  currentPhotoUrl={`/api/cavalos/${horse_id}/photo`}
                  size="lg"
                />
              </div>

              {/* Campos obrigatórios */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Nome *</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do cavalo" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="breed"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Raça *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a raça" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="arabian">Árabe</SelectItem>
                          <SelectItem value="quarter">
                            Quarto de Milha
                          </SelectItem>
                          <SelectItem value="thoroughbred">
                            Puro-Sangue
                          </SelectItem>
                          <SelectItem value="andalusian">Andaluz</SelectItem>
                          <SelectItem value="friesian">Frisão</SelectItem>
                          <SelectItem value="appaloosa">Appaloosa</SelectItem>
                          <SelectItem value="mangalarga">Mangalarga</SelectItem>
                          <SelectItem value="crioulo">Crioulo</SelectItem>
                          <SelectItem value="other">Outra</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birth_date"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Data de Nascimento *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Abas com campos opcionais */}
              <div className="mt-8">
                <Tabs defaultValue="fisico" className="w-full">
                  <TabsList className="mb-4 grid grid-cols-4 w-full">
                    <TabsTrigger value="fisico">
                      Características Físicas
                    </TabsTrigger>
                    <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
                    <TabsTrigger value="status">Status</TabsTrigger>
                    <TabsTrigger value="observacoes">Observações</TabsTrigger>
                  </TabsList>

                  {/* Características Físicas */}
                  <TabsContent value="fisico">
                    <Card>
                      <CardHeader>
                        <CardTitle>Características Físicas</CardTitle>
                        <CardDescription>
                          Informações sobre o porte e características físicas do
                          cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="peso"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Peso (kg)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    placeholder="Ex: 450.5"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="altura"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Altura (m)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="Ex: 1.68"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="sexo"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Sexo</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o sexo" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="macho">Macho</SelectItem>
                                    <SelectItem value="femea">Fêmea</SelectItem>
                                    <SelectItem value="macho_castrado">
                                      Macho (Castrado)
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          {/* Pelagem */}
                          <FormField
                            control={form.control}
                            name="cor"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Pelagem</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                  disabled={isPelagensLoading}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue
                                        placeholder={
                                          isPelagensLoading
                                            ? "Carregando pelagens..."
                                            : "Selecione a pelagem"
                                        }
                                      />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="nao_informado">
                                      Não informado
                                    </SelectItem>
                                    {pelagens.map((pelagem) => (
                                      <SelectItem key={pelagem.id} value={pelagem.nome}>
                                        {pelagem.nome}
                                      </SelectItem>
                                    ))}
                                    {!isPelagensLoading && pelagens.length === 0 && (
                                      <SelectItem value="nenhuma" disabled>
                                        Nenhuma pelagem cadastrada
                                      </SelectItem>
                                    )}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Genealogia */}
                  <TabsContent value="genealogia">
                    <Card>
                      <CardHeader>
                        <CardTitle>Genealogia</CardTitle>
                        <CardDescription>
                          Informações sobre a linhagem e pedigree do cavalo
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          {/* Seleção de Pai usando o novo componente GenealogiaSelector */}
                          <FormField
                            control={form.control}
                            name="pai"
                            render={({ field }) => (
                              <div className="sm:col-span-3">
                                <GenealogiaSelector
                                  label="Pai"
                                  description="Selecione um cavalo do sistema ou informe o nome de um cavalo externo"
                                  cavalos={todosCavalos}
                                  sexoFiltro="macho"
                                  value={field.value || { tipo: 'nenhum' as const, cavaloSistemaId: null, cavaloNome: null } as EntradaGenealogica}
                                  onChange={(val) => {
                                    field.onChange(val);
                                    // Atualizar os campos auxiliares pai_id e pai_nome
                                    if (val.tipo === 'sistema') {
                                      // Para cavalos do plantel, usamos o ID
                                      form.setValue('pai_id', val.cavaloSistemaId ? Number(val.cavaloSistemaId) : null);
                                      form.setValue('pai_nome', null);
                                    } else if (val.tipo === 'externo') {
                                      // Para cavalos externos, agora buscamos/criamos um ID
                                      // mas também mantemos o nome para exibição
                                      if (val.cavaloSistemaId) {
                                        // Se já tiver um ID (de um externo cadastrado)
                                        form.setValue('pai_id', Number(val.cavaloSistemaId));
                                        form.setValue('pai_nome', val.cavaloNome); // Mantém nome para referência
                                      } else {
                                        // Aqui o ID será gerado ao salvar o formulário
                                        form.setValue('pai_id', null);
                                        form.setValue('pai_nome', val.cavaloNome);
                                      }
                                    } else {
                                      // Nenhum cavalo selecionado
                                      form.setValue('pai_id', null);
                                      form.setValue('pai_nome', null);
                                    }
                                  }}
                                  error={form.formState.errors.pai?.message}
                                />
                              </div>
                            )}
                          />
                          
                          {/* Seleção de Mãe usando o novo componente GenealogiaSelector */}
                          <FormField
                            control={form.control}
                            name="mae"
                            render={({ field }) => (
                              <div className="sm:col-span-3">
                                <GenealogiaSelector
                                  label="Mãe"
                                  description="Selecione uma égua do sistema ou informe o nome de uma égua externa"
                                  cavalos={todosCavalos}
                                  sexoFiltro="femea"
                                  value={field.value || { tipo: 'nenhum' as const, cavaloSistemaId: null, cavaloNome: null } as EntradaGenealogica}
                                  onChange={(val) => {
                                    field.onChange(val);
                                    // Atualizar os campos auxiliares mae_id e mae_nome
                                    if (val.tipo === 'sistema') {
                                      // Para cavalos do plantel, usamos o ID
                                      form.setValue('mae_id', val.cavaloSistemaId ? Number(val.cavaloSistemaId) : null);
                                      form.setValue('mae_nome', null);
                                    } else if (val.tipo === 'externo') {
                                      // Para cavalos externos, agora buscamos/criamos um ID
                                      // mas também mantemos o nome para exibição
                                      if (val.cavaloSistemaId) {
                                        // Se já tiver um ID (de um externo cadastrado)
                                        form.setValue('mae_id', Number(val.cavaloSistemaId));
                                        form.setValue('mae_nome', val.cavaloNome); // Mantém nome para referência
                                      } else {
                                        // Aqui o ID será gerado ao salvar o formulário
                                        form.setValue('mae_id', null);
                                        form.setValue('mae_nome', val.cavaloNome);
                                      }
                                    } else {
                                      // Nenhum cavalo selecionado
                                      form.setValue('mae_id', null);
                                      form.setValue('mae_nome', null);
                                    }
                                  }}
                                  error={form.formState.errors.mae?.message}
                                />
                              </div>
                            )}
                          />
                          <div className="mt-4 mb-1">
                            <h3 className="text-md font-semibold">Avós Paternos</h3>
                          </div>
                          <FormField
                            control={form.control}
                            name="avoPaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Paterno</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do avô paterno"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="avoPaterna"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avó Paterna</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome da avó paterna"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="mt-4 mb-1">
                            <h3 className="text-md font-semibold">Avós Maternos</h3>
                          </div>
                          <FormField
                            control={form.control}
                            name="avoMaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Materno</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome do avô materno"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="avoMaterna"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avó Materna</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Nome da avó materna"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Status */}
                  <TabsContent value="status">
                    <Card>
                      <CardHeader>
                        <CardTitle>Status</CardTitle>
                        <CardDescription>
                          Situação atual do cavalo no plantel (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="ativo">Ativo</SelectItem>
                                    <SelectItem value="vendido">
                                      Vendido
                                    </SelectItem>
                                    <SelectItem value="falecido">
                                      Falecido
                                    </SelectItem>
                                    <SelectItem value="doado">Doado</SelectItem>
                                    <SelectItem value="emprestado">
                                      Emprestado
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="data_entrada"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Entrada</FormLabel>
                                <FormControl>
                                  <Input type="date" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="data_saida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Saída</FormLabel>
                                <FormControl>
                                  <Input type="date" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="motivo_saida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-6">
                                <FormLabel>Motivo da Saída</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Explique o motivo da saída, se aplicável"
                                    rows={2}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Observações */}
                  <TabsContent value="observacoes">
                    <Card>
                      <CardHeader>
                        <CardTitle>Observações</CardTitle>
                        <CardDescription>
                          Informações adicionais, histórico ou particularidades
                          (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações Gerais</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Informações adicionais sobre o cavalo, como histórico médico, comportamento, necessidades especiais, etc."
                                  rows={5}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">* Campos obrigatórios</p>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      className="mr-3"
                    >
                      Cancelar
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? "Salvando..." : "Salvar Alterações"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default EditHorsePage;
