import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { createError } from '../error-handler';

/**
 * Configurações gerais para limitação de requisições
 */
const defaultOptions = {
  // Cabeçalhos padrão
  standardHeaders: true,
  // Não usar cabeçalhos legados
  legacyHeaders: false,
  // Mensagem de erro personalizada para excesso de requisições
  handler: (req: Request, res: Response, next: NextFunction, options: any) => {
    next(createError.rateLimit(
      `Muitas requisições. Por favor, tente novamente após ${Math.ceil(options.windowMs / 60000)} minutos.`
    ));
  }
};

/**
 * Limitador de taxa para rotas de autenticação
 * Mais restritivo para proteger contra ataques de força bruta
 */
export const authLimiter = rateLimit({
  // Período de 15 minutos
  windowMs: 15 * 60 * 1000,
  // Limite de 10 requisições por IP dentro do período
  max: 10,
  // Mensagem de erro
  message: {
    error: {
      type: "RATE_LIMIT_ERROR",
      message: "Muitas tentativas de login. Por favor, aguarde 15 minutos antes de tentar novamente.",
      details: "Por questões de segurança, após múltiplas tentativas de login, seu acesso foi temporariamente limitado."
    }
  },
  // Chave para identificar requisições
  keyGenerator: (req) => {
    // Usar IP + nome de usuário (quando disponível) para uma chave mais específica
    const username = req.body?.username || 'anonymous';
    return `${req.ip}-${username}`;
  },
  ...defaultOptions
});

/**
 * Limitador de taxa para rotas sensíveis que modificam dados
 * Protege contra abuso de API
 */
export const mutationLimiter = rateLimit({
  // Período de 1 minuto
  windowMs: 1 * 60 * 1000,
  // Limite de 100 requisições por IP dentro do período (aumentado para desenvolvimento)
  max: 100,
  ...defaultOptions
});

/**
 * Limitador de taxa para rotas de API gerais
 * Menos restritivo, mas ainda protege contra abuso
 */
export const apiLimiter = rateLimit({
  // Período de 1 minuto
  windowMs: 1 * 60 * 1000,
  // Limite de 200 requisições por IP dentro do período (aumentado para desenvolvimento)
  max: 200,
  ...defaultOptions
});

/**
 * Limitador de taxa para rotas ABCCC
 * Proteção especial para chamadas de API externas
 */
export const abcccLimiter = rateLimit({
  // Período de 5 minutos
  windowMs: 5 * 60 * 1000,
  // Limite de 15 requisições por IP dentro do período
  max: 15,
  message: {
    error: {
      type: "RATE_LIMIT_ERROR",
      message: "Número de consultas ABCCC excedido. Por favor, aguarde alguns minutos.",
      details: "O limite de consultas ao serviço ABCCC foi atingido. Esta medida visa proteger o acesso aos serviços externos."
    }
  },
  ...defaultOptions
});