import { pgTable, text, serial, integer, boolean, timestamp, real, time, date, pgEnum } from "drizzle-orm/pg-core";

// Definir tipos ENUM
export const horseStatusEnum = pgEnum('horse_status', ['ativo', 'inativo', 'vendido', 'falecido']);
export const horseSexoEnum = pgEnum('horse_sexo', ['macho', 'femea', 'macho_castrado']);
export const tipoCoberturaEnum = pgEnum('tipo_cobertura', ['monta_natural', 'inseminacao', 'transferencia_embriao']);
export const estadoReproducaoEnum = pgEnum('estado_reproducao', ['pre_cobertura', 'em_cobertura', 'prenhez_confirmada', 'parto_realizado', 'abortado', 'nao_prenhe']);
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Schema ultra-simples para formulários - apenas nome obrigatório como no PDF
export const cavaloFormSchema = z.object({
  // Único campo obrigatório
  nome: z.string().min(1, 'Nome é obrigatório'),
  
  // Todos os outros campos são opcionais
  breed: z.string().optional().nullable(),
  birthDate: z.string().optional().nullable(),
  peso: z.coerce.number().optional().nullable(),
  altura: z.coerce.number().optional().nullable(),
  sexo: z.string().optional().nullable(),
  pelagem: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  dataEntrada: z.string().optional().nullable(),
  dataSaida: z.string().optional().nullable(),
  motivoSaida: z.string().optional().nullable(),
  paiNome: z.string().optional().nullable(),
  maeNome: z.string().optional().nullable(),
  numeroRegistro: z.string().optional().nullable(),
  origem: z.string().optional().nullable(),
  criador: z.string().optional().nullable(),
  proprietario: z.string().optional().nullable(),
  inspetor: z.string().optional().nullable(),
  valorCompra: z.coerce.number().optional().nullable(),
  dataCompra: z.string().optional().nullable(),
  observacoes: z.string().optional().nullable(),
});

// Função para converter dados do formulário para formato do banco
export function converterFormParaBanco(dadosForm: any, user_id: number) {
  return {
    name: dadosForm.nome || "Cavalo sem nome", // nome -> name
    breed: dadosForm.breed || "Sem raça definida",
    birthDate: dadosForm.birthDate || new Date().toISOString().split('T')[0],
    peso: dadosForm.peso || null,
    altura: dadosForm.altura || null,
    sexo: dadosForm.sexo || null,
    cor: dadosForm.pelagem || null, // pelagem -> cor
    status: dadosForm.status || "ativo",
    dataEntrada: dadosForm.dataEntrada || null,
    dataSaida: dadosForm.dataSaida || null,
    motivoSaida: dadosForm.motivoSaida || null,
    paiNome: dadosForm.paiNome || null,
    maeNome: dadosForm.maeNome || null,
    numeroRegistro: dadosForm.numeroRegistro || null,
    origem: dadosForm.origem || null,
    criador: dadosForm.criador || null,
    proprietario: dadosForm.proprietario || null,
    inspetor: dadosForm.inspetor || null,
    valorCompra: dadosForm.valorCompra || null,
    dataCompra: dadosForm.dataCompra || null,
    notes: dadosForm.observacoes || null, // observacoes -> notes
    user_id: user_id,
    isExternal: false
  };
}

// Função para converter dados do banco para formato do formulário
export function converterBancoParaForm(dadosBanco: any) {
  return {
    nome: dadosBanco.name, // name -> nome
    breed: dadosBanco.breed,
    birthDate: dadosBanco.birthDate,
    peso: dadosBanco.peso,
    altura: dadosBanco.altura,
    sexo: dadosBanco.sexo,
    pelagem: dadosBanco.cor, // cor -> pelagem
    status: dadosBanco.status,
    dataEntrada: dadosBanco.dataEntrada,
    dataSaida: dadosBanco.dataSaida,
    motivoSaida: dadosBanco.motivoSaida,
    paiNome: dadosBanco.paiNome,
    maeNome: dadosBanco.maeNome,
    numeroRegistro: dadosBanco.numeroRegistro,
    origem: dadosBanco.origem,
    criador: dadosBanco.criador,
    proprietario: dadosBanco.proprietario,
    inspetor: dadosBanco.inspetor,
    valorCompra: dadosBanco.valorCompra,
    dataCompra: dadosBanco.dataCompra,
    observacoes: dadosBanco.notes, // notes -> observacoes
  };
}

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  firebaseUid: text("firebase_uid").unique(),
  accessLevel: text("access_level").default("user"), // "admin", "user", "readonly"
  isActive: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow(),
});

// Horses table (cavalos in Portuguese)
export const cavalos = pgTable("cavalos", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  breed: text("breed").notNull(),
  birthDate: date("birthDate").notNull(),
  
  // Novos campos físicos
  peso: real("peso"),
  altura: real("altura"),
  sexo: horseSexoEnum("sexo"),
  cor: text("cor"), // Pelagem do cavalo (texto)
  pelagemId: integer("pelagemId").references(() => pelagens.id), // Referência à tabela de pelagens
  
  // Novos campos de status
  status: horseStatusEnum("status").default("ativo"), // ativo, vendido, falecido
  dataEntrada: date("dataEntrada"),
  dataSaida: date("dataSaida"),
  motivoSaida: text("motivoSaida"),
  
  // Genealogia - sistema dual com IDs e nomes
  // Para o pai
  paiId: integer("paiId").references(() => cavalos.id),
  paiNome: text("paiNome"),
  
  // Para a mãe
  maeId: integer("maeId").references(() => cavalos.id),
  maeNome: text("maeNome"),
  
  // Avós (nomes e IDs se forem do sistema)
  avoPaterno: text("avoPaterno"),
  avoPaternoId: integer("avoPaternoId").references(() => cavalos.id),
  avoMaterno: text("avoMaterno"),
  avoMaternoId: integer("avoMaternoId").references(() => cavalos.id),
  
  // Campos de rastreamento e propriedade
  numeroRegistro: text("numeroRegistro"),
  origem: text("origem"),
  criador: text("criador"),
  proprietario: text("proprietario"),
  inspetor: text("inspetor"),  // Inspetor técnico da ABCCC
  valorCompra: real("valorCompra"),
  dataCompra: date("dataCompra"),
  
  // Campos existentes
  notes: text("notes"),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
  
  // Flag para cavalos externos (apenas da genealogia, não fazem parte do plantel)
  isExternal: boolean("isExternal").default(false),
});

// Nova tabela para arquivos
export const arquivos = pgTable("arquivos", {
  id: serial("id").primaryKey(),
  fileName: text("file_name").notNull(),
  filePath: text("file_path").notNull(), // Caminho no sistema de arquivos
  fileType: text("file_type").notNull(), // image, video, pdf
  description: text("description"),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: 'cascade' }),
  user_id: integer("user_id").notNull().references(() => users.id),
  uploadDate: timestamp("upload_date").defaultNow(),
  isProfilePhoto: boolean("is_profile_photo").default(false), // Foto principal do cavalo
  fileSize: integer("file_size"), // Tamanho do arquivo em bytes
  mimeType: text("mime_type"), // Tipo MIME do arquivo
});

// Manejos (horse care tasks) table
export const manejos = pgTable("manejos", {
  id: serial("id").primaryKey(),
  tipo: text("tipo").notNull(), // Type of care (e.g., vaccination, deworming, etc.)
  data: date("data"), // Date of the care task
  observacoes: text("observacoes"), // Notes/observations
  
  // Novos campos para alertas e status
  dataVencimento: date("dataVencimento"), // Data para alertas de vencimento
  status: text("status").default("pendente"), // pendente, concluído, atrasado
  prioridade: text("prioridade").default("normal"), // baixa, normal, alta
  
  horse_id: integer("horse_id").references(() => cavalos.id),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Tasks table for future functionality
export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  dueDate: timestamp("due_date").notNull(),
  completed: boolean("completed").default(false),
  horse_id: integer("horse_id").references(() => cavalos.id),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Eventos de agenda (Schedule Events) table
export const eventos = pgTable("eventos", {
  id: serial("id").primaryKey(),
  titulo: text("titulo").notNull(),
  descricao: text("descricao"),
  data: date("data").notNull(), // Data do evento (YYYY-MM-DD)
  horaInicio: text("hora_inicio").notNull(), // Hora de início (HH:MM)
  horaFim: text("hora_fim").notNull(), // Hora de término (HH:MM)
  tipo: text("tipo").notNull(), // Tipo de evento: 'veterinario', 'ferrageamento', 'treinamento', 'outro'
  status: text("status").default("pendente"), // pendente, concluido, cancelado
  prioridade: text("prioridade").default("media"), // baixa, media, alta
  manejoId: integer("manejo_id").references(() => manejos.id), // Referência opcional a um manejo
  horse_id: integer("horse_id").references(() => cavalos.id), // Opcional - se o evento for para um cavalo específico
  responsavel: text("responsavel"), // Nome da pessoa responsável (veterinário, etc)
  telefoneResponsavel: text("telefone_responsavel"), // Telefone de contato
  local: text("local"), // Local do evento
  observacoes: text("observacoes"), // Observações adicionais
  recorrente: boolean("recorrente").default(false), // Se o evento é recorrente
  padraoRecorrencia: text("padrao_recorrencia"), // diário, semanal, mensal, etc.
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Procedimentos Veterinários table
export const procedimentosVet = pgTable("procedimentos_vet", {
  id: serial("id").primaryKey(),
  tipo: text("tipo").notNull(), // Tipo: 'vacina', 'exame', 'tratamento', 'cirurgia', etc.
  descricao: text("descricao").notNull(), // Descrição detalhada do procedimento
  data: date("data"), // Data do procedimento (YYYY-MM-DD)
  veterinario: text("veterinario"), // Nome do veterinário responsável
  crmv: text("crmv"), // Registro do veterinário
  medicamentos: text("medicamentos"), // Medicamentos utilizados
  dosagem: text("dosagem"), // Dosagem dos medicamentos
  resultado: text("resultado"), // Resultado do procedimento/exame
  recomendacoes: text("recomendacoes"), // Recomendações pós-procedimento
  dataProximoProcedimento: date("data_proximo"), // Data recomendada para próximo procedimento
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: 'cascade' }),
  manejoId: integer("manejo_id").references(() => manejos.id), // Caso esteja vinculado a um manejo
  eventoId: integer("evento_id").references(() => eventos.id), // Caso esteja vinculado a um evento
  arquivoIds: text("arquivo_ids"), // IDs dos arquivos relacionados, como "1,2,3"
  status: text("status").default("realizado"), // realizado, agendado, cancelado
  custo: real("custo"), // Custo do procedimento
  formaPagamento: text("forma_pagamento"), // Forma de pagamento
  observacoes: text("observacoes"), // Observações adicionais
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela de pelagens para cavalos crioulos
export const pelagens = pgTable("pelagens", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull().unique(), // Nome da pelagem (ex: "Tordilho", "Alazão", etc.)
  descricao: text("descricao"),          // Descrição da pelagem
  fonte: text("fonte").default("manual"), // Fonte da informação: 'manual', 'abccc', 'scraper'
  user_id: integer("user_id").references(() => users.id), // Usuário que adicionou (opcional)
  created_at: timestamp("created_at").defaultNow(),
});


// Controle Reprodutivo table
export const reproducao = pgTable("reproducao", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: 'cascade' }), // Égua
  padreiroId: integer("padreiro_id").references(() => cavalos.id), // Garanhão
  dataCobertura: date("data_cobertura"), // Data da cobertura
  tipoCobertura: tipoCoberturaEnum("tipo_cobertura").notNull(), // monta_natural, inseminacao, transferencia_embriao
  estado: estadoReproducaoEnum("estado").notNull(), // pre_cobertura, em_cobertura, prenhez_confirmada, parto_realizado, abortado, nao_prenhe
  dataPrevistaInseminacao: date("data_prevista_inseminacao"), // Data prevista para inseminação
  dataPrevistaEmbriao: date("data_prevista_embriao"), // Data prevista para transferência de embrião
  dataPrevistaParto: date("data_prevista_parto"), // Data prevista para o parto
  dataDiagnosticoGestacao: date("data_diagnostico_gestacao"), // Data do diagnóstico de gestação
  resultadoDiagnostico: text("resultado_diagnostico"), // Resultado do diagnóstico (texto livre)
  observacoes: text("observacoes"), // Observações gerais
  procedimentoVetId: integer("procedimento_vet_id").references(() => procedimentosVet.id), // Procedimento veterinário associado
  eventoId: integer("evento_id").references(() => eventos.id), // Evento associado
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Type definitions for database operations
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;

// Simple validation schemas
export const insertUserSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  username: z.string().min(1),
  password: z.string().min(6),
  email: z.string().email().optional(),
});

export const insertCavaloSchema = z.object({
  name: z.string().min(1),
  breed: z.string().optional(),
  birthDate: z.string().optional(),
  sexo: z.string().optional(),
  cor: z.string().optional(),
  status: z.string().optional(),
  user_id: z.number(),
});

export const insertManejoSchema = z.object({
  tipo: z.string().min(1),
  data: z.string(),
  observacoes: z.string().optional(),
  horse_id: z.number(),
  user_id: z.number(),
});

export const insertTaskSchema = z.object({
  title: z.string().min(1),
  dueDate: z.string().optional(),
  completed: z.boolean().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const insertArquivoSchema = z.object({
  fileName: z.string().min(1),
  filePath: z.string().min(1),
  fileType: z.string().optional(),
  description: z.string().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const insertEventoSchema = z.object({
  titulo: z.string().min(1),
  descricao: z.string().optional(),
  data: z.string(),
  horaInicio: z.string().optional(),
  horaFim: z.string().optional(),
  tipo: z.string().optional(),
  status: z.string().optional(),
  prioridade: z.string().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const insertProcedimentoVetSchema = z.object({
  tipo: z.string().min(1),
  descricao: z.string().optional(),
  data: z.string(),
  veterinario: z.string().optional(),
  crmv: z.string().optional(),
  medicamentos: z.string().optional(),
  dosagem: z.string().optional(),
  resultado: z.string().optional(),
  recomendacoes: z.string().optional(),
  horse_id: z.number(),
  user_id: z.number(),
});

export const insertReproducaoSchema = z.object({
  horse_id: z.number(),
  padreiroId: z.number().optional(),
  dataCobertura: z.string().optional(),
  tipoCobertura: z.string().optional(),
  estado: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

// Duplicate removed - using the one defined later in file

// Duplicate schemas removed - using the ones defined later in file
export type Cavalo = typeof cavalos.$inferSelect;
export type InsertCavalo = typeof cavalos.$inferInsert;
export type Manejo = typeof manejos.$inferSelect;
export type InsertManejo = typeof manejos.$inferInsert;
export type Task = typeof tasks.$inferSelect;
export type InsertTask = typeof tasks.$inferInsert;
export type Arquivo = typeof arquivos.$inferSelect;
export type InsertArquivo = typeof arquivos.$inferInsert;
export type Pelagem = typeof pelagens.$inferSelect;
export type InsertPelagem = typeof pelagens.$inferInsert;
export type Evento = typeof eventos.$inferSelect;
export type InsertEvento = typeof eventos.$inferInsert;
export type ProcedimentoVet = typeof procedimentosVet.$inferSelect;
export type InsertProcedimentoVet = typeof procedimentosVet.$inferInsert;
export type Reproducao = typeof reproducao.$inferSelect;
export type InsertReproducao = typeof reproducao.$inferInsert;

// Tabela de medidas físicas
export const medidasFisicas = pgTable("medidas_fisicas", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  data: date("data").notNull(),
  peso: real("peso"),
  altura: real("altura"),
  condicaoCorporal: integer("condicao_corporal"),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela de consultas médicas veterinárias
export const consultasMedicas = pgTable("consultas_medicas", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  veterinarioNome: text("veterinario_nome").notNull(),
  veterinarioContato: text("veterinario_contato"),
  crmv: text("crmv"),
  dataConsulta: date("data_consulta").notNull(),
  horaConsulta: time("hora_consulta"),
  motivoConsulta: text("motivo_consulta").notNull(),
  anamnese: text("anamnese"), // Histórico clínico relatado
  exameFisico: text("exame_fisico"), // Resultados do exame físico
  sinaisVitais: text("sinais_vitais"), // Temperatura, FC, FR, etc.
  diagnostico: text("diagnostico"),
  diagnosticoDiferencial: text("diagnostico_diferencial"),
  tratamento: text("tratamento"),
  prescricao: text("prescricao"), // Medicamentos prescritos
  examesComplementares: text("exames_complementares"), // Exames solicitados
  proximaConsulta: date("proxima_consulta"),
  observacoes: text("observacoes"),
  custo: real("custo"),
  status: text("status").default("Concluída"), // Agendada, Em andamento, Concluída, Cancelada
  urgencia: text("urgencia").default("Normal"), // Baixa, Normal, Alta, Crítica
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela de medicamentos e prescrições
export const medicamentos = pgTable("medicamentos", {
  id: serial("id").primaryKey(),
  consultaId: integer("consulta_id").references(() => consultasMedicas.id, { onDelete: "cascade" }),
  nome: text("nome").notNull(),
  principioAtivo: text("principio_ativo"),
  concentracao: text("concentracao"),
  forma: text("forma"), // Comprimido, injetável, pomada, etc.
  dosagem: text("dosagem").notNull(),
  frequencia: text("frequencia").notNull(),
  duracao: text("duracao").notNull(),
  viaAdministracao: text("via_administracao"), // Oral, IM, IV, tópica, etc.
  instrucoes: text("instrucoes"),
  observacoes: text("observacoes"),
  dataInicio: date("data_inicio"),
  dataFim: date("data_fim"),
  ativo: boolean("ativo").default(true),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela de exames clínicos
export const examesClinicosVet = pgTable("exames_clinicos_vet", {
  id: serial("id").primaryKey(),
  consultaId: integer("consulta_id").references(() => consultasMedicas.id, { onDelete: "cascade" }),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  tipoExame: text("tipo_exame").notNull(), // Hemograma, bioquímico, radiografia, etc.
  dataExame: date("data_exame").notNull(),
  laboratorio: text("laboratorio"),
  resultados: text("resultados"),
  valoresReferencia: text("valores_referencia"),
  interpretacao: text("interpretacao"),
  arquivoAnexo: text("arquivo_anexo"), // Path para arquivo do exame
  observacoes: text("observacoes"),
  custo: real("custo"),
  status: text("status").default("Pendente"), // Pendente, Em andamento, Concluído
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Schemas de validação para consultas médicas
export const insertConsultaMedicaSchema = createInsertSchema(consultasMedicas).pick({
  horse_id: true,
  veterinarioNome: true,
  veterinarioContato: true,
  crmv: true,
  dataConsulta: true,
  horaConsulta: true,
  motivoConsulta: true,
  anamnese: true,
  exameFisico: true,
  sinaisVitais: true,
  diagnostico: true,
  diagnosticoDiferencial: true,
  tratamento: true,
  prescricao: true,
  examesComplementares: true,
  proximaConsulta: true,
  observacoes: true,
  custo: true,
  status: true,
  urgencia: true,
  user_id: true,
});

export const insertMedicamentoSchema = createInsertSchema(medicamentos).pick({
  consultaId: true,
  nome: true,
  principioAtivo: true,
  concentracao: true,
  forma: true,
  dosagem: true,
  frequencia: true,
  duracao: true,
  viaAdministracao: true,
  instrucoes: true,
  observacoes: true,
  dataInicio: true,
  dataFim: true,
  ativo: true,
  user_id: true,
});

export const insertExameClinicoVetSchema = createInsertSchema(examesClinicosVet).pick({
  consultaId: true,
  horse_id: true,
  tipoExame: true,
  dataExame: true,
  laboratorio: true,
  resultados: true,
  valoresReferencia: true,
  interpretacao: true,
  arquivoAnexo: true,
  observacoes: true,
  custo: true,
  status: true,
  user_id: true,
});

export const insertMedidaFisicaSchema = createInsertSchema(medidasFisicas).pick({
  horse_id: true,
  data: true,
  peso: true,
  altura: true,
  condicaoCorporal: true,
  observacoes: true,
  user_id: true
});

// Tipos TypeScript para as novas funcionalidades veterinárias
export type InsertConsultaMedica = z.infer<typeof insertConsultaMedicaSchema>;
export type ConsultaMedica = typeof consultasMedicas.$inferSelect;

export type InsertMedicamento = z.infer<typeof insertMedicamentoSchema>;
export type Medicamento = typeof medicamentos.$inferSelect;

export type InsertExameClinicoVet = z.infer<typeof insertExameClinicoVetSchema>;
export type ExameClinicoVet = typeof examesClinicosVet.$inferSelect;

export type InsertMedidaFisica = z.infer<typeof insertMedidaFisicaSchema>;
export type MedidaFisica = typeof medidasFisicas.$inferSelect;

// Tabela de Nutrição e Alimentação - alinhada com estrutura real do banco
export const nutricao = pgTable("nutricao", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  data: date("data").notNull(),
  tipo_alimentacao: text("tipo_alimentacao").notNull(), // Concentrado, volumoso, suplemento
  nome_alimento: text("nome_alimento").notNull(), // Nome comercial ou tipo específico
  quantidade: real("quantidade").notNull(), // Em kg ou unidades
  unidade_medida: text("unidade_medida").notNull(), // kg, g, unidade, etc.
  frequencia_diaria: integer("frequencia_diaria").notNull(), // Quantas vezes ao dia
  horarios: text("horarios"), // Ex: "07:00,12:00,18:00"
  observacoes: text("observacoes"),
  custo_unitario: real("custo_unitario"), // Custo por unidade
  custo_mensal: real("custo_mensal"), // Custo mensal estimado
  fornecedor: text("fornecedor"), // Nome do fornecedor
  recomendacao: text("recomendacao"), // Recomendação técnica
  status: text("status").default("ativo"), // ativo, inativo, temporário
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

export const insertNutricaoSchema = createInsertSchema(nutricao).pick({
  horse_id: true,
  data: true,
  tipo_alimentacao: true,
  nome_alimento: true,
  quantidade: true,
  unidade_medida: true,
  frequencia_diaria: true,
  horarios: true,
  observacoes: true,
  custo_unitario: true,
  custo_mensal: true,
  fornecedor: true,
  recomendacao: true,
  status: true,
  user_id: true
});

export type InsertNutricao = z.infer<typeof insertNutricaoSchema>;
export type Nutricao = typeof nutricao.$inferSelect;

// Schema simplificado para formulários de nutrição - seguindo padrão dos cavalos
export const nutricaoFormSchema = z.object({
  // Campos obrigatórios
  horse_id: z.number({ required_error: "Selecione um animal" }),
  data: z.string().min(1, "Selecione uma data"),
  tipoAlimentacao: z.string().min(1, "Selecione o tipo de alimentação"),
  nomeAlimento: z.string().min(1, "Informe o nome do alimento"),
  quantidade: z.coerce.number().min(0.01, "Quantidade deve ser maior que zero"),
  unidadeMedida: z.string().min(1, "Selecione a unidade de medida"),
  frequenciaDiaria: z.coerce.number().min(1, "Frequência deve ser no mínimo 1"),
  
  // Campos opcionais
  horarios: z.string().optional().nullable(),
  custoUnitario: z.coerce.number().min(0, "Custo deve ser um valor positivo").optional().nullable(),
  observacoes: z.string().optional().nullable(),
  recomendacao: z.string().optional().nullable(),
  status: z.string().default("ativo"),
  fornecedor: z.string().optional().nullable(),
});

// Função para converter dados do formulário de nutrição para formato do banco
export function converterNutricaoFormParaBanco(dadosForm: any, user_id: number) {
  return {
    horse_id: dadosForm.horse_id,
    data: dadosForm.data,
    tipoAlimentacao: dadosForm.tipoAlimentacao,
    nomeAlimento: dadosForm.nomeAlimento,
    quantidade: dadosForm.quantidade,
    unidadeMedida: dadosForm.unidadeMedida,
    frequenciaDiaria: dadosForm.frequenciaDiaria,
    horarios: dadosForm.horarios || null,
    custoUnitario: dadosForm.custoUnitario || null,
    custoMensal: dadosForm.custoUnitario ? 
      (dadosForm.custoUnitario * dadosForm.quantidade * dadosForm.frequenciaDiaria * 30) : null,
    observacoes: dadosForm.observacoes || null,
    recomendacao: dadosForm.recomendacao || null,
    status: dadosForm.status || "ativo",
    fornecedor: dadosForm.fornecedor || null,
    user_id: user_id,
  };
}

// Função para converter dados do banco para formato do formulário de nutrição
export function converterNutricaoBancoParaForm(dadosBanco: any) {
  return {
    horse_id: dadosBanco.horse_id,
    data: dadosBanco.data,
    tipoAlimentacao: dadosBanco.tipoAlimentacao,
    nomeAlimento: dadosBanco.nomeAlimento,
    quantidade: dadosBanco.quantidade,
    unidadeMedida: dadosBanco.unidadeMedida,
    frequenciaDiaria: dadosBanco.frequenciaDiaria,
    horarios: dadosBanco.horarios,
    custoUnitario: dadosBanco.custoUnitario,
    observacoes: dadosBanco.observacoes,
    recomendacao: dadosBanco.recomendacao,
    status: dadosBanco.status,
    fornecedor: dadosBanco.fornecedor,
  };
}

// === NUTRITION LOGISTICS MODULE ===

// Templates de Dieta (Feed Templates)
export const feedTemplates = pgTable("feed_templates", {
  id: serial("id").primaryKey(),
  category: text("category").notNull(), // "manutenção", "atleta_leve", "gestacao", "lactacao", "potro"
  forageMnPctMs: real("forage_min_pct_ms").notNull(), // % mínimo de matéria seca do volumoso
  concentrateMaxKgPer100kg: real("concentrate_max_kg_per_100kg").notNull(), // kg máximo concentrado por 100kg peso vivo
  notes: text("notes"), // Observações sobre o template
  createdBy: integer("created_by").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Plano de Alimentação Diário (Daily Feed Plan)
export const feedPlanItems = pgTable("feed_plan_items", {
  id: serial("id").primaryKey(),
  animalId: integer("animal_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  templateId: integer("template_id").notNull().references(() => feedTemplates.id),
  forageKg: real("forage_kg").notNull(), // Quantidade de volumoso em kg
  concentrateKg: real("concentrate_kg").notNull(), // Quantidade de concentrado em kg
  date: date("date").notNull(), // Data do plano
  status: text("status").default("pending"), // "pending", "done", "leftover"
  leftoverPct: real("leftover_pct"), // Percentual de sobra (quando status = "leftover")
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Estoque & Lotes de Compras (Stock Batches)
export const stockBatches = pgTable("stock_batches", {
  id: serial("id").primaryKey(),
  item: text("item").notNull(), // Nome do item (ex: "Feno de Tifton", "Ração Concentrada")
  quantityKg: real("quantity_kg").notNull(), // Quantidade em kg
  unitCost: real("unit_cost").notNull(), // Custo por kg
  expiry: date("expiry"), // Data de validade
  supplier: text("supplier"), // Fornecedor
  batchNumber: text("batch_number"), // Número do lote
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Alertas de Estoque (Stock Alerts)
export const stockAlerts = pgTable("stock_alerts", {
  id: serial("id").primaryKey(),
  item: text("item").notNull(), // Nome do item
  alertType: text("alert_type").notNull(), // "low_stock", "expiring", "expired"
  message: text("message").notNull(), // Mensagem do alerta
  daysRemaining: integer("days_remaining"), // Dias restantes (para estoque ou validade)
  priority: text("priority").default("medium"), // "low", "medium", "high"
  resolved: boolean("resolved").default(false), // Se o alerta foi resolvido
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Schemas de inserção para as novas tabelas
export const insertFeedTemplateSchema = createInsertSchema(feedTemplates).pick({
  category: true,
  forageMnPctMs: true,
  concentrateMaxKgPer100kg: true,
  notes: true,
  createdBy: true,
});

export const insertFeedPlanItemSchema = createInsertSchema(feedPlanItems).pick({
  animalId: true,
  templateId: true,
  forageKg: true,
  concentrateKg: true,
  date: true,
  status: true,
  leftoverPct: true,
  user_id: true,
});

export const insertStockBatchSchema = createInsertSchema(stockBatches).pick({
  item: true,
  quantityKg: true,
  unitCost: true,
  expiry: true,
  supplier: true,
  batchNumber: true,
  user_id: true,
});

export const insertStockAlertSchema = createInsertSchema(stockAlerts).pick({
  item: true,
  alertType: true,
  message: true,
  daysRemaining: true,
  priority: true,
  resolved: true,
  user_id: true,
});

// Tipos TypeScript para as novas tabelas
export type InsertFeedTemplate = z.infer<typeof insertFeedTemplateSchema>;
export type FeedTemplate = typeof feedTemplates.$inferSelect;

export type InsertFeedPlanItem = z.infer<typeof insertFeedPlanItemSchema>;
export type FeedPlanItem = typeof feedPlanItems.$inferSelect;

export type InsertStockBatch = z.infer<typeof insertStockBatchSchema>;
export type StockBatch = typeof stockBatches.$inferSelect;

export type InsertStockAlert = z.infer<typeof insertStockAlertSchema>;
export type StockAlert = typeof stockAlerts.$inferSelect;

// Tabela para armazenar medidas morfológicas detalhadas
export const morfologia = pgTable("morfologia", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  dataMedicao: date("data_medicao").notNull(),
  
  // Medidas de corpo
  alturaCernelha: real("altura_cernelha"), // Altura na cernelha (cm)
  alturaDorso: real("altura_dorso"), // Altura do dorso (cm)
  alturaGarupa: real("altura_garupa"), // Altura da garupa (cm)
  comprimentoCorpo: real("comprimento_corpo"), // Comprimento do corpo (cm)
  comprimentoPescoco: real("comprimento_pescoco"), // Comprimento do pescoço (cm)
  larguraPeito: real("largura_peito"), // Largura do peito (cm)
  perimetroToracico: real("perimetro_toracico"), // Perímetro torácico (cm)
  perimetroPescoco: real("perimetro_pescoco"), // Perímetro do pescoço (cm)
  perimetroCanela: real("perimetro_canela"), // Perímetro da canela (cm)
  
  // Pontuações morfológicas (1-10)
  pontuacaoCabeca: integer("pontuacao_cabeca"), // Pontuação da cabeça
  pontuacaoPescoco: integer("pontuacao_pescoco"), // Pontuação do pescoço
  pontuacaoEspalda: integer("pontuacao_espalda"), // Pontuação da espádua
  pontuacaoDorso: integer("pontuacao_dorso"), // Pontuação do dorso
  pontuacaoGarupa: integer("pontuacao_garupa"), // Pontuação da garupa
  pontuacaoMembros: integer("pontuacao_membros"), // Pontuação dos membros
  pontuacaoAprumos: integer("pontuacao_aprumos"), // Pontuação dos aprumos
  pontuacaoAndamento: integer("pontuacao_andamento"), // Pontuação do andamento
  pontuacaoHarmonia: integer("pontuacao_harmonia"), // Pontuação da harmonia geral
  
  // Pontuação geral (média ou total calculado)
  pontuacaoTotal: real("pontuacao_total"),
  
  // Outros campos
  observacoes: text("observacoes"),
  responsavelMedicao: text("responsavel_medicao"),
  arquivoIds: text("arquivo_ids"), // IDs de arquivos relacionados (fotos, vídeos)
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela para rastrear histórico de desempenho
export const desempenhoHistorico = pgTable("desempenho_historico", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  data: date("data").notNull(),
  tipoEvento: text("tipo_evento").notNull(), // Competição, exposição, treinamento, etc.
  nomeEvento: text("nome_evento").notNull(),
  categoria: text("categoria"), // Categoria de participação
  resultado: text("resultado"), // Colocação, pontuação, etc.
  observacoes: text("observacoes"),
  premiacao: text("premiacao"), // Prêmios ou reconhecimentos recebidos
  desempenhoDetalhes: text("desempenho_detalhes"), // Detalhes específicos do desempenho
  arquivoIds: text("arquivo_ids"), // IDs de arquivos relacionados (fotos, certificados)
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela para armazenar genealogia explícita - estrutura real do banco de dados
export const genealogia = pgTable("genealogia", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  
  // Ancestrais diretos
  pai: text("pai"),
  mae: text("mae"),
  
  // Avós paternos
  avoPaternoId: integer("avo_paterno_id").references(() => cavalos.id),
  avoPaterno: text("avo_paterno"),
  avoPaternaId: integer("avo_paterna_id").references(() => cavalos.id),
  avoPaterna: text("avo_paterna"),
  
  // Avós maternos
  avoMaternoId: integer("avo_materno_id").references(() => cavalos.id),
  avoMaterno: text("avo_materno"),
  avoMaternaId: integer("avo_materna_id").references(() => cavalos.id),
  avoMaterna: text("avo_materna"),
  
  // Bisavós paternos (lado do pai)
  bisavoPaternoPaterno: text("bisavo_paterno_paterno"),
  bisavoPaternoPaterno_id: integer("bisavo_paterno_paterno_id").references(() => cavalos.id),
  bisavoPaternaPaterno: text("bisavo_paterna_paterno"),
  bisavoPaternaPaterno_id: integer("bisavo_paterna_paterno_id").references(() => cavalos.id),
  
  // Bisavós maternos do lado paterno
  bisavoMaternoPaterno: text("bisavo_materno_paterno"),
  bisavoMaternoPaterno_id: integer("bisavo_materno_paterno_id").references(() => cavalos.id),
  bisavoMaternaPaterno: text("bisavo_materna_paterno"),
  bisavoMaternaPaterno_id: integer("bisavo_materna_paterno_id").references(() => cavalos.id),
  
  // Bisavós paternos do lado materno
  bisavoPaternoMaterno: text("bisavo_paterno_materno"),
  bisavoPaternoMaterno_id: integer("bisavo_paterno_materno_id").references(() => cavalos.id),
  bisavoPaternaMaterno: text("bisavo_paterna_materno"),
  bisavoPaternaMaterno_id: integer("bisavo_paterna_materno_id").references(() => cavalos.id),
  
  // Bisavós maternos (lado da mãe)
  bisavoMaternoMaterno: text("bisavo_materno_materno"),
  bisavoMaternoMaterno_id: integer("bisavo_materno_materno_id").references(() => cavalos.id),
  bisavoMaternaMaterno: text("bisavo_materna_materno"), 
  bisavoMaternaMaterno_id: integer("bisavo_materna_materno_id").references(() => cavalos.id),
  
  // Coeficiente de consanguinidade e observações
  coeficienteConsanguinidade: real("coeficiente_consanguinidade"),
  observacoes: text("observacoes"),
  
  // Metadados
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela para sugestões de cruzamento
export const sugestoesCruzamento = pgTable("sugestoes_cruzamento", {
  id: serial("id").primaryKey(),
  horseIdBase: integer("horse_id_base").notNull().references(() => cavalos.id, { onDelete: "cascade" }), // Cavalo base para sugestão
  horseIdSugerido: integer("horse_id_sugerido").notNull().references(() => cavalos.id), // Cavalo sugerido para cruzamento
  dataSugestao: date("data_sugestao").notNull(),
  
  // Pontuações e compatibilidade
  objetivo: text("objetivo").notNull(), // Objetivo do cruzamento: morfologia, desempenho, temperamento, etc.
  pontuacaoCompatibilidade: real("pontuacao_compatibilidade").notNull(), // 0 a 100
  
  // Análises
  analiseConsanguinidade: text("analise_consanguinidade"), // Texto com análise de consanguinidade
  analiseDesempenho: text("analise_desempenho"), // Análise de complementaridade de desempenho
  analiseMorfologia: text("analise_morfologia"), // Análise de complementaridade morfológica
  
  // Previsões
  prevPotencialMorfologico: real("prev_potencial_morfologico"), // 0 a 10
  prevPotencialDesempenho: real("prev_potencial_desempenho"), // 0 a 10
  prevPotencialTemperamento: real("prev_potencial_temperamento"), // 0 a 10
  
  // Outros campos
  justificativa: text("justificativa"), // Justificativa para a sugestão
  recomendacoes: text("recomendacoes"), // Recomendações adicionais
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Schema de validação para genealogia
export const insertGenealogiaSchema = createInsertSchema(genealogia).omit({
  id: true,
  created_at: true,
});

// Schemas para validação Zod
export const insertMorfologiaSchema = createInsertSchema(morfologia).pick({
  horse_id: true,
  dataMedicao: true,
  alturaCernelha: true,
  alturaDorso: true,
  alturaGarupa: true,
  comprimentoCorpo: true,
  comprimentoPescoco: true,
  larguraPeito: true,
  perimetroToracico: true,
  perimetroPescoco: true,
  perimetroCanela: true,
  pontuacaoCabeca: true,
  pontuacaoPescoco: true,
  pontuacaoEspalda: true,
  pontuacaoDorso: true,
  pontuacaoGarupa: true,
  pontuacaoMembros: true,
  pontuacaoAprumos: true,
  pontuacaoAndamento: true,
  pontuacaoHarmonia: true,
  pontuacaoTotal: true,
  observacoes: true,
  responsavelMedicao: true,
  arquivoIds: true,
  user_id: true
});

export const insertDesempenhoHistoricoSchema = createInsertSchema(desempenhoHistorico).pick({
  horse_id: true,
  data: true,
  tipoEvento: true,
  nomeEvento: true,
  categoria: true,
  resultado: true,
  observacoes: true,
  premiacao: true,
  desempenhoDetalhes: true,
  arquivoIds: true,
  user_id: true
});



export const insertSugestoesCruzamentoSchema = createInsertSchema(sugestoesCruzamento).pick({
  horseIdBase: true,
  horseIdSugerido: true,
  dataSugestao: true,
  objetivo: true,
  pontuacaoCompatibilidade: true,
  analiseConsanguinidade: true,
  analiseDesempenho: true,
  analiseMorfologia: true,
  prevPotencialMorfologico: true,
  prevPotencialDesempenho: true,
  prevPotencialTemperamento: true,
  justificativa: true,
  recomendacoes: true,
  user_id: true
});

// Definições de tipo
export type InsertMorfologia = z.infer<typeof insertMorfologiaSchema>;
export type Morfologia = typeof morfologia.$inferSelect;

// Schema simplificado para formulários de morfologia - seguindo padrão dos cavalos
export const morfologiaFormSchema = z.object({
  // Campos obrigatórios
  horse_id: z.number({ required_error: "Selecione um animal" }),
  dataMedicao: z.date({ required_error: "Selecione a data da medição" }),
  responsavelMedicao: z.string().min(3, "Nome do responsável deve ter pelo menos 3 caracteres"),
  
  // Medidas físicas opcionais
  alturaCernelha: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  alturaDorso: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  alturaGarupa: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  comprimentoCorpo: z.coerce.number().min(0, "Comprimento deve ser positivo").optional().nullable(),
  comprimentoPescoco: z.coerce.number().min(0, "Comprimento deve ser positivo").optional().nullable(),
  larguraPeito: z.coerce.number().min(0, "Largura deve ser positiva").optional().nullable(),
  perimetroToracico: z.coerce.number().min(0, "Perímetro deve ser positivo").optional().nullable(),
  perimetroPescoco: z.coerce.number().min(0, "Perímetro deve ser positivo").optional().nullable(),
  perimetroCanela: z.coerce.number().min(0, "Perímetro deve ser positivo").optional().nullable(),
  
  // Pontuações morfológicas (1 a 10)
  pontuacaoCabeca: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoPescoco: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoEspalda: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoDorso: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoGarupa: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoMembros: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoAprumos: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoAndamento: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  pontuacaoHarmonia: z.coerce.number().min(1, "Mínimo 1").max(10, "Máximo 10").default(5),
  
  // Campos opcionais
  observacoes: z.string().optional().nullable(),
  arquivoIds: z.string().optional().nullable(),
});

// Função para converter dados do formulário de morfologia para formato do banco
export function converterMorfologiaFormParaBanco(dadosForm: any, user_id: number) {
  // Calcular pontuação total
  const pontuacaoTotal = 
    dadosForm.pontuacaoCabeca +
    dadosForm.pontuacaoPescoco +
    dadosForm.pontuacaoEspalda +
    dadosForm.pontuacaoDorso +
    dadosForm.pontuacaoGarupa +
    dadosForm.pontuacaoMembros +
    dadosForm.pontuacaoAprumos +
    dadosForm.pontuacaoAndamento +
    dadosForm.pontuacaoHarmonia;

  return {
    horse_id: dadosForm.horse_id,
    dataMedicao: dadosForm.dataMedicao instanceof Date 
      ? dadosForm.dataMedicao.toISOString().split('T')[0] 
      : dadosForm.dataMedicao,
    responsavelMedicao: dadosForm.responsavelMedicao,
    alturaCernelha: dadosForm.alturaCernelha || null,
    alturaDorso: dadosForm.alturaDorso || null,
    alturaGarupa: dadosForm.alturaGarupa || null,
    comprimentoCorpo: dadosForm.comprimentoCorpo || null,
    comprimentoPescoco: dadosForm.comprimentoPescoco || null,
    larguraPeito: dadosForm.larguraPeito || null,
    perimetroToracico: dadosForm.perimetroToracico || null,
    perimetroPescoco: dadosForm.perimetroPescoco || null,
    perimetroCanela: dadosForm.perimetroCanela || null,
    pontuacaoCabeca: dadosForm.pontuacaoCabeca,
    pontuacaoPescoco: dadosForm.pontuacaoPescoco,
    pontuacaoEspalda: dadosForm.pontuacaoEspalda,
    pontuacaoDorso: dadosForm.pontuacaoDorso,
    pontuacaoGarupa: dadosForm.pontuacaoGarupa,
    pontuacaoMembros: dadosForm.pontuacaoMembros,
    pontuacaoAprumos: dadosForm.pontuacaoAprumos,
    pontuacaoAndamento: dadosForm.pontuacaoAndamento,
    pontuacaoHarmonia: dadosForm.pontuacaoHarmonia,
    pontuacaoTotal: pontuacaoTotal,
    observacoes: dadosForm.observacoes || null,
    arquivoIds: dadosForm.arquivoIds || null,
    user_id: user_id,
  };
}

// Função para converter dados do banco para formato do formulário de morfologia
export function converterMorfologiaBancoParaForm(dadosBanco: any) {
  return {
    horse_id: dadosBanco.horse_id,
    dataMedicao: dadosBanco.dataMedicao ? new Date(dadosBanco.dataMedicao) : new Date(),
    responsavelMedicao: dadosBanco.responsavelMedicao || "",
    alturaCernelha: dadosBanco.alturaCernelha,
    alturaDorso: dadosBanco.alturaDorso,
    alturaGarupa: dadosBanco.alturaGarupa,
    comprimentoCorpo: dadosBanco.comprimentoCorpo,
    comprimentoPescoco: dadosBanco.comprimentoPescoco,
    larguraPeito: dadosBanco.larguraPeito,
    perimetroToracico: dadosBanco.perimetroToracico,
    perimetroPescoco: dadosBanco.perimetroPescoco,
    perimetroCanela: dadosBanco.perimetroCanela,
    pontuacaoCabeca: dadosBanco.pontuacaoCabeca || 5,
    pontuacaoPescoco: dadosBanco.pontuacaoPescoco || 5,
    pontuacaoEspalda: dadosBanco.pontuacaoEspalda || 5,
    pontuacaoDorso: dadosBanco.pontuacaoDorso || 5,
    pontuacaoGarupa: dadosBanco.pontuacaoGarupa || 5,
    pontuacaoMembros: dadosBanco.pontuacaoMembros || 5,
    pontuacaoAprumos: dadosBanco.pontuacaoAprumos || 5,
    pontuacaoAndamento: dadosBanco.pontuacaoAndamento || 5,
    pontuacaoHarmonia: dadosBanco.pontuacaoHarmonia || 5,
    observacoes: dadosBanco.observacoes,
    arquivoIds: dadosBanco.arquivoIds,
  };
}

export type InsertDesempenhoHistorico = z.infer<typeof insertDesempenhoHistoricoSchema>;
export type DesempenhoHistorico = typeof desempenhoHistorico.$inferSelect;

export type InsertGenealogia = z.infer<typeof insertGenealogiaSchema>;
export type Genealogia = typeof genealogia.$inferSelect;

export type InsertSugestoesCruzamento = z.infer<typeof insertSugestoesCruzamentoSchema>;
export type SugestoesCruzamento = typeof sugestoesCruzamento.$inferSelect;

// Schema para upload de arquivos/fotos (apenas se não existir)
export const horsePhotoUploadSchema = z.object({
  fileName: z.string().min(1, "Nome do arquivo é obrigatório"),
  fileType: z.enum(["image", "video", "pdf"], {
    required_error: "Tipo de arquivo deve ser image, video ou pdf"
  }),
  description: z.string().optional(),
  isProfilePhoto: z.boolean().default(false),
  fileSize: z.number().positive("Tamanho do arquivo deve ser positivo").optional(),
  mimeType: z.string().optional(),
});
