/**
 * Sistema de Insights Inteligentes
 * Analisa dados existentes e fornece insights valiosos sem alterar lógica
 */

import React, { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Calendar, 
  Heart, 
  DollarSign,
  Lightbulb
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { parseISO, differenceInDays, startOfMonth, endOfMonth } from 'date-fns';

interface InsightItem {
  id: string;
  type: 'performance' | 'health' | 'financial' | 'planning' | 'alert';
  title: string;
  description: string;
  value?: string | number;
  trend?: 'up' | 'down' | 'stable';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  category: string;
}

interface SmartInsightsProps {
  className?: string;
  limit?: number;
}

export function SmartInsights({ className = '', limit = 6 }: SmartInsightsProps) {
  const { user } = useAuth();

  const { data: cavalos } = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const { data: manejos } = useQuery({
    queryKey: ['/api/manejos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const { data: nutricao } = useQuery({
    queryKey: ['/api/nutricao', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const { data: veterinario } = useQuery({
    queryKey: ['/api/veterinario', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const insights = useMemo(() => {
    if (!cavalos || !Array.isArray(cavalos)) return [];

    const generatedInsights: InsightItem[] = [];
    const today = new Date();

    // Análise de Plantel
    const cavalosAtivos = cavalos.filter(c => c.status === 'ativo');
    const cavalosJovens = cavalos.filter(c => {
      if (!c.birth_date) return false;
      const idade = differenceInDays(today, parseISO(c.birth_date)) / 365;
      return idade < 4 && idade > 0;
    });

    if (cavalosJovens.length > 0) {
      generatedInsights.push({
        id: 'jovens-potencial',
        type: 'planning',
        title: 'Potros em Desenvolvimento',
        description: `${cavalosJovens.length} cavalos jovens com alto potencial para treinamento`,
        value: cavalosJovens.length,
        priority: 'medium',
        actionable: true,
        category: 'Plantel'
      });
    }

    // Análise de Manejos
    if (Array.isArray(manejos)) {
      const manejosRecentes = manejos.filter(m => {
        if (!m.data_execucao) return false;
        const dataExecucao = parseISO(m.data_execucao);
        return differenceInDays(today, dataExecucao) <= 30;
      });

      const eficienciaManejo = manejosRecentes.length / cavalosAtivos.length;
      
      if (eficienciaManejo > 1.5) {
        generatedInsights.push({
          id: 'alta-atividade-manejo',
          type: 'performance',
          title: 'Alta Atividade de Manejo',
          description: `Excelente engajamento - ${manejosRecentes.length} manejos realizados este mês`,
          trend: 'up',
          priority: 'low',
          actionable: false,
          category: 'Gestão'
        });
      } else if (eficienciaManejo < 0.5) {
        generatedInsights.push({
          id: 'baixa-atividade-manejo',
          type: 'alert',
          title: 'Oportunidade de Melhoria',
          description: 'Considere aumentar a frequência de manejos para otimizar resultados',
          trend: 'down',
          priority: 'medium',
          actionable: true,
          category: 'Gestão'
        });
      }
    }

    // Análise Veterinária
    if (Array.isArray(veterinario) && veterinario.length > 0) {
      const proximasConsultas = veterinario.filter(v => {
        if (!v.proxima_consulta) return false;
        const proxConsulta = parseISO(v.proxima_consulta);
        return differenceInDays(proxConsulta, today) <= 14 && differenceInDays(proxConsulta, today) >= 0;
      });

      if (proximasConsultas.length > 0) {
        generatedInsights.push({
          id: 'consultas-programadas',
          type: 'health',
          title: 'Cuidados Veterinários Programados',
          description: `${proximasConsultas.length} consulta(s) veterinária(s) nas próximas 2 semanas`,
          value: proximasConsultas.length,
          priority: 'high',
          actionable: true,
          category: 'Saúde'
        });
      }
    }

    // Análise Nutricional
    if (Array.isArray(nutricao) && nutricao.length > 0) {
      const cavalosComNutricao = new Set(nutricao.map(n => n.cavalo_id)).size;
      const coberturaNutricional = (cavalosComNutricao / cavalosAtivos.length) * 100;

      if (coberturaNutricional >= 80) {
        generatedInsights.push({
          id: 'boa-cobertura-nutricional',
          type: 'performance',
          title: 'Excelente Cobertura Nutricional',
          description: `${Math.round(coberturaNutricional)}% dos cavalos com plano nutricional ativo`,
          value: `${Math.round(coberturaNutricional)}%`,
          trend: 'up',
          priority: 'low',
          actionable: false,
          category: 'Nutrição'
        });
      }
    }

    return generatedInsights
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .slice(0, limit);

  }, [cavalos, manejos, nutricao, veterinario, limit]);

  const getIcon = (type: InsightItem['type']) => {
    switch (type) {
      case 'performance': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'health': return <Heart className="h-4 w-4 text-red-600" />;
      case 'financial': return <DollarSign className="h-4 w-4 text-blue-600" />;
      case 'planning': return <Calendar className="h-4 w-4 text-purple-600" />;
      case 'alert': return <Target className="h-4 w-4 text-orange-600" />;
      default: return <Lightbulb className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendIcon = (trend?: InsightItem['trend']) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down': return <TrendingDown className="h-3 w-3 text-red-600" />;
      default: return null;
    }
  };

  const getPriorityBadge = (priority: InsightItem['priority']) => {
    switch (priority) {
      case 'high': return <Badge variant="destructive" className="text-xs">Alta</Badge>;
      case 'medium': return <Badge variant="secondary" className="text-xs">Média</Badge>;
      case 'low': return <Badge variant="outline" className="text-xs">Baixa</Badge>;
    }
  };

  if (insights.length === 0) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Lightbulb className="h-5 w-5" />
          Insights Inteligentes
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className="flex items-start gap-3 p-3 rounded-lg border bg-card/30 hover:bg-card/60 transition-colors"
          >
            <div className="flex-shrink-0 mt-0.5">
              {getIcon(insight.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm text-foreground">
                      {insight.title}
                    </h4>
                    {insight.trend && getTrendIcon(insight.trend)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-0.5">
                    {insight.description}
                  </p>
                  {insight.value && (
                    <p className="text-sm font-medium text-foreground mt-1">
                      {insight.value}
                    </p>
                  )}
                </div>
                
                <div className="flex flex-col items-end gap-1 flex-shrink-0">
                  {getPriorityBadge(insight.priority)}
                  <Badge variant="outline" className="text-xs">
                    {insight.category}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {insights.some(i => i.actionable) && (
          <div className="pt-2 border-t border-border">
            <p className="text-xs text-muted-foreground">
              Insights marcados como acionáveis podem orientar suas próximas decisões
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SmartInsights;