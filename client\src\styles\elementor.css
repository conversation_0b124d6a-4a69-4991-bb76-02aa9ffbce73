/* Elementor specific styles */

/* Page header */
.elementor .elementor-element .page-header{
	visibility:hidden;
	display:none;
}

#hero-slider .splide__track{
	border-bottom-right-radius:30px;
	border-bottom-left-radius:30px;
	border-top-right-radius:30px;
	border-top-left-radius:30px;
}

.elementor .header .row{
	display: none;
}

/* Image */
.navbar .d-flex img{
	visibility:hidden;
}

.navbar .d-flex a{
	visibility:hidden;
	padding-top:5px;
	padding-bottom:5px;
}

/* Cabealho inicial com fundo transparente */
.header nav{
	background-color:rgba(255,255,255,0);
	transform:translatex(0px) translatey(0px);
	box-shadow:none;
	padding-bottom:31px;
}

.navbar-nav .menu-item a{
	border-style:none;
}

/* Column 3/12 */
.navbar .d-flex{
	top: 2px;
	z-index: 91;
	visibility: visible;
	background-color: transparent;
	background-image: url("https://tecnomor.com.br/novo2025/wp-content/uploads/2024/12/logo-1.png");
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	transform: translateX(0px) translateY(0px);
	min-width: 111px;
	height: 42px;
	min-height: 50px;
	transition: background-image 0.3s ease, background-color 0.3s ease;
}

.navbar.scrolled .d-flex{
	background-image: url("https://tecnomor.com.br/novo2025/wp-content/themes/tecnomor/assets/img/logo.webp");
	background-color: white;
}

/* Column 3/12 */
.elementor .elementor-element .e-con-inner .elementor-widget-shortcode .elementor-widget-container .elementor-shortcode .header .row .navbar .container .d-flex{
	width:106px !important;
}

/* Item */
.e-lazyloaded .elementor-nav-menu--main .menu-item:nth-child(6) .elementor-item{
	color:#ffffff;
	border-top-left-radius:18px;
	border-top-right-radius:18px;
	border-bottom-left-radius:18px;
	border-bottom-right-radius:18px;
	padding-left:8px;
	padding-right:8px;
	padding-top:2px;
	padding-bottom:2px;
	font-size:12px;
}

/* Item */
.elementor-location-header .e-lazyloaded .e-con-inner .elementor-nav-menu--dropdown-tablet .elementor-widget-container .elementor-nav-menu--main .elementor-nav-menu .menu-item .elementor-item{
	line-height:1em !important;
}

.elementor-location-header .elementor-widget-image .elementor-widget-container{
	padding-top:3px !important;
	margin-top:-1px !important;
}

.elementor .elementor-element .elementor-widget-media-carousel{
	top:-16px;
}

/* Paragraph */
.elementor-posts--thumbnail-left .elementor-grid-item p{
	text-align:justify;
	padding-right:18px;
}

/* Link */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__text .elementor-post__read-more-wrapper a{
	text-align:right;
	background-color:#1e8549;
	background-clip:padding-box;
	padding-left:8px;
	padding-right:8px;
	padding-top:5px;
	padding-bottom:24px;
	color:#ffffff;
	border-top-left-radius:10px;
	border-top-right-radius:10px;
	border-bottom-left-radius:10px;
	border-bottom-right-radius:10px;
	display:inline-block;
	height:24px;
	position:relative;
	top:-3px;
}

/* Division */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__read-more-wrapper{
	text-align:right;
	padding-right:2px;
	padding-top:0px;
	display:block;
	float:right;
	clear:none;
	margin-bottom:34px;
	margin-right:55px;
}

/* Link */
.elementor-posts--thumbnail-left .elementor-post__title a{
	font-size:15px;
	font-weight:600;
}

/* Heading */
.e-con-inner .elementor-element .elementor-widget-heading .elementor-widget-container .elementor-heading-title{
	font-size:60px;
}

/* Link */
.elementkit-tab-nav .elementkit-nav-item a{
	text-align:center !important;
}

.elementkit-tab-wraper .active h2{
	font-size:35px !important;
	color:#154a30 !important;
	left:0px !important;
}

/* Paragraph */
.elementor .elementor-element .e-con-inner .elementor-element .elementor-posts--thumbnail-left .elementor-widget-container .elementor-posts-container .elementor-grid-item .elementor-post__text .elementor-post__excerpt p{
	width:92% !important;
}

/* Division */
.elementor-posts--thumbnail-left .elementor-grid-item .elementor-post__excerpt{
	margin-bottom:9px !important;
}

/* Media queries for responsive designs */
@media (max-width: 1199px) {
	/* Swiper wrapper */
	.elementor-swiper .elementor-main-swiper .swiper-wrapper{
		max-height:505px;
	}
	
	.elementor .swiper-wrapper .swiper-slide a{
		height:566px !important;
	}
	
	/* Link */
	.elementor .elementor-element .elementor-widget-media-carousel .elementor-widget-container .elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide a{
		display:inline-block !important;
	}
	
	/* Link */
	.elementor-widget-container .elementor-swiper .elementor-main-swiper .swiper-wrapper .swiper-slide a{
		display:inline-block !important;
	}
}

@media (max-width: 640px) {
	/* Span Tag */
	p > span > span{
		font-size:20px !important;
	}
	
	/* Span Tag */
	.elementor p strong span{
		font-size:20px !important;
	}
	
	.elementor > .elementor-element > .e-con-inner > .elementor-widget-text-editor p{
		line-height:20px;
		font-size:15px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-slide .elementor-testimonial__content{
		position:relative;
		top:-24px;
	}
	
	/* Division */
	.elementor-main-swiper .swiper-wrapper .swiper-slide .elementor-testimonial .elementor-testimonial__footer{
		top:-122px !important;
	}
	
	/* Post Division */
	.elementor-posts--thumbnail-left .elementor-grid-item{
		transform:translatex(0px) translatey(0px);
	}
	
	/* Paragraph */
	.e-con-inner .elementor-element .elementor-element .qq .elementor-widget-container p{
		font-size:14px;
		line-height:1.6em;
	}
	
	/* Division */
	.elementor .e-n-accordion-item:nth-child(6) .elementor-button-wrapper{
		position:relative;
		top:-8px;
		bottom:-37px;
		margin-bottom:31px;
	}
	
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		font-size:25px;
		line-height:1em;
	}
}

@media (max-width: 575px) {
	/* Paragraph */
	.elementor > .elementor-element > .e-con-inner > .elementor-element > .elementor-widget-heading p.elementor-heading-title{
		padding-top:27px;
		padding-bottom:41px;
	}
	
	/* Paragraph */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .ttt .elementor-widget-container .elementor-heading-title{
		font-size:20px;
		width:284px;
	}
	
	/* Element */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner > .elementor-element{
		padding-bottom:75px;
	}
	
	/* Division */
	.e-n-accordion-item .elementor-element .elementor-element .e-con-inner .elementor-element .qq .elementor-widget-container{
		font-size:14px;
	}
}