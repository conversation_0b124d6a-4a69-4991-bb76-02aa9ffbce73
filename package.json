{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "npx esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --minify", "start": "node start-production.js", "check": "tsc", "db:push": "drizzle-kit push", "check-env": "tsx check-env.ts", "migrate": "node server/migrations/run_migration.mjs", "deploy": "npm run build && node start-production.js", "build:fast": "node build-production-fast.cjs", "deploy:fix": "node build-production-fast.cjs && node start-production.js", "deploy:final": "node deploy-final-fix.cjs"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@cypress/code-coverage": "^3.14.4", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@tanstack/react-query": "^5.60.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/d3": "^7.4.3", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bottleneck": "^2.19.5", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "csrf-csrf": "^4.0.2", "csurf": "^1.2.2", "cypress": "^14.4.1", "d3": "^7.9.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "firebase": "^11.5.0", "form-data": "^4.0.2", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "idb": "^8.0.2", "input-otp": "^1.2.4", "jest": "^29.7.0", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nyc": "^17.1.0", "openai": "^4.98.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "prom-client": "^15.1.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.13.0", "rest-express": "file:", "supertest": "^7.1.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.0", "vitest": "^3.1.4", "winston": "^3.17.0", "wouter": "^3.3.5", "ws": "^8.18.1", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "^24.0.1", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.18.1", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite": "^6.3.5"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}