import * as React from "react";
import { cn } from "@/lib/utils";

/**
 * CardResponsive
 * 
 * Componente de card com responsividade aprimorada para dispositivos móveis.
 * Versão adaptada do componente Card do shadcn/ui para responder melhor a diferentes tamanhos de tela.
 */

const CardResponsive = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm md:p-6 p-4 transition-all",
      "hover:shadow-md",
      "dark:shadow-slate-900/10",
      "max-w-full overflow-hidden",
      className
    )}
    {...props}
  />
));
CardResponsive.displayName = "CardResponsive";

const CardResponsiveHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 md:pb-4 pb-3", className)}
    {...props}
  />
));
CardResponsiveHeader.displayName = "CardResponsiveHeader";

const CardResponsiveTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-semibold leading-none tracking-tight md:text-2xl",
      className
    )}
    {...props}
  />
));
CardResponsiveTitle.displayName = "CardResponsiveTitle";

const CardResponsiveDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardResponsiveDescription.displayName = "CardResponsiveDescription";

const CardResponsiveContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div 
    ref={ref} 
    className={cn("md:pt-2 pt-1", className)} 
    {...props} 
  />
));
CardResponsiveContent.displayName = "CardResponsiveContent";

const CardResponsiveFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center md:pt-4 pt-3 flex-wrap gap-2",
      className
    )}
    {...props}
  />
));
CardResponsiveFooter.displayName = "CardResponsiveFooter";

export {
  CardResponsive,
  CardResponsiveHeader,
  CardResponsiveFooter,
  CardResponsiveTitle,
  CardResponsiveDescription,
  CardResponsiveContent,
};