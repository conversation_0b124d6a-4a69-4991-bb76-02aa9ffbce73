import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';

interface GenealogiaDados {
  genealogia: {
    id: number;
    horse_id: number;
    pai?: string;
    mae?: string;
    avo_paterno?: string;
    avo_paterna?: string;
    avo_materno?: string;
    avo_materna?: string;
    bisavo_paterno_paterno?: string;
    bisavo_paterna_paterno?: string;
    bisavo_materno_paterno?: string;
    bisavo_materna_paterno?: string;
    bisavo_paterno_materno?: string;
    bisavo_paterna_materno?: string;
    bisavo_materno_materno?: string;
    bisavo_materna_materno?: string;
  };
  cavaloId: number;
}

export default function EditarGenealogiaArvore({ cavaloId: propCavaloId }: { cavaloId?: string }) {
  const params = useParams<{ cavaloId: string }>();
  const cavaloId = propCavaloId || params.cavaloId;
  
  const [, navigate] = useLocation();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Estados para cada campo genealógico
  const [pai, setPai] = useState('');
  const [mae, setMae] = useState('');
  const [avoPaterno, setAvoPaterno] = useState('');
  const [avoPaterna, setAvoPaterna] = useState('');
  const [avoMaterno, setAvoMaterno] = useState('');
  const [avoMaterna, setAvoMaterna] = useState('');
  const [bisavoPaternoPaterno, setBisavoPaternoPaterno] = useState('');
  const [bisavoPaternaPaterno, setBisavoPaternaPaterno] = useState('');
  const [bisavoMaternoPaterno, setBisavoMaternoPaterno] = useState('');
  const [bisavoMaternaPaterno, setBisavoMaternaPaterno] = useState('');
  const [bisavoPaternoMaterno, setBisavoPaternoMaterno] = useState('');
  const [bisavoPaternaMaterno, setBisavoPaternaMaterno] = useState('');
  const [bisavoMaternoMaterno, setBisavoMaternoMaterno] = useState('');
  const [bisavoMaternaMaterno, setBisavoMaternaMaterno] = useState('');

  useEffect(() => {
    if (cavaloId) {
      carregarDados();
    }
  }, [cavaloId]);

  const carregarDados = async () => {
    try {
      setLoading(true);
      
      // Carregar dados da genealogia
      const genealogia = await apiRequest<GenealogiaDados>(`/api/cavalos/${cavaloId}/genealogia`, 'GET');
      const gen = genealogia.genealogia;
      
      console.log('🔥 Dados genealogia carregados:', gen);
      
      // Preencher campos diretamente dos dados da genealogia
      const paiNome = gen.pai || '';
      const maeNome = gen.mae || '';
      const avoPaternoNome = gen.avo_paterno || '';
      const avoPaternaNome = gen.avo_paterna || '';
      const avoMaternoNome = gen.avo_materno || '';
      const avoMaternaNome = gen.avo_materna || '';
      
      console.log('🔥 Preenchendo campos:', {
        paiNome,
        maeNome,
        avoPaternoNome,
        avoPaternaNome,
        avoMaternoNome,
        avoMaternaNome
      });
      
      // Usar setTimeout para garantir que o estado seja atualizado após o render
      setTimeout(() => {
        setPai(paiNome);
        setMae(maeNome);
        setAvoPaterno(avoPaternoNome);
        setAvoPaterna(avoPaternaNome);
        setAvoMaterno(avoMaternoNome);
        setAvoMaterna(avoMaternaNome);
        
        // Bisavós
        setBisavoPaternoPaterno(gen.bisavo_paterno_paterno || '');
        setBisavoPaternaPaterno(gen.bisavo_paterna_paterno || '');
        setBisavoMaternoPaterno(gen.bisavo_materno_paterno || '');
        setBisavoMaternaPaterno(gen.bisavo_materna_paterno || '');
        setBisavoPaternoMaterno(gen.bisavo_paterno_materno || '');
        setBisavoPaternaMaterno(gen.bisavo_paterna_materno || '');
        setBisavoMaternoMaterno(gen.bisavo_materno_materno || '');
        setBisavoMaternaMaterno(gen.bisavo_materna_materno || '');
        
        console.log('✅ Estado atualizado com sucesso!');
      }, 100);
      
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados da genealogia",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSalvar = async () => {
    try {
      setSaving(true);
      
      const dadosGenealogia = {
        horse_id: parseInt(cavaloId),
        pai,
        mae,
        avo_paterno: avoPaterno,
        avo_paterna: avoPaterna,
        avo_materno: avoMaterno,
        avo_materna: avoMaterna,
        bisavo_paterno_paterno: bisavoPaternoPaterno,
        bisavo_paterna_paterno: bisavoPaternaPaterno,
        bisavo_materno_paterno: bisavoMaternoPaterno,
        bisavo_materna_paterno: bisavoMaternaPaterno,
        bisavo_paterno_materno: bisavoPaternoMaterno,
        bisavo_paterna_materno: bisavoPaternaMaterno,
        bisavo_materno_materno: bisavoMaternoMaterno,
        bisavo_materna_materno: bisavoMaternaMaterno,
      };
      
      await apiRequest(`/api/genealogia/${cavaloId}`, 'PUT', dadosGenealogia);
      
      toast({
        title: "Sucesso",
        description: "Genealogia atualizada com sucesso!",
      });
      
      navigate(`/cavalo/${cavaloId}`);
      
    } catch (error) {
      console.error('Erro ao salvar:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a genealogia",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(`/cavalo/${cavaloId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <h1 className="text-2xl font-bold">Editar Árvore Genealógica</h1>
      </div>

      {/* Árvore Genealógica Visual */}
      <div className="space-y-8">
        
        {/* Nível 1 - Pais */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Pais</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="text-lg font-semibold">Pai</Label>
                <Input
                  value={pai}
                  onChange={(e) => setPai(e.target.value)}
                  placeholder="Nome do pai"
                  className="mt-2"
                />
              </div>
              <div>
                <Label className="text-lg font-semibold">Mãe</Label>
                <Input
                  value={mae}
                  onChange={(e) => setMae(e.target.value)}
                  placeholder="Nome da mãe"
                  className="mt-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Nível 2 - Avós */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Avós</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Avós Paternos */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-center text-blue-600">Lado Paterno</h3>
                <div>
                  <Label>Avô Paterno</Label>
                  <Input
                    value={avoPaterno}
                    onChange={(e) => setAvoPaterno(e.target.value)}
                    placeholder="Nome do avô paterno"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label>Avó Paterna</Label>
                  <Input
                    value={avoPaterna}
                    onChange={(e) => setAvoPaterna(e.target.value)}
                    placeholder="Nome da avó paterna"
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Avós Maternos */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-center text-pink-600">Lado Materno</h3>
                <div>
                  <Label>Avô Materno</Label>
                  <Input
                    value={avoMaterno}
                    onChange={(e) => setAvoMaterno(e.target.value)}
                    placeholder="Nome do avô materno"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label>Avó Materna</Label>
                  <Input
                    value={avoMaterna}
                    onChange={(e) => setAvoMaterna(e.target.value)}
                    placeholder="Nome da avó materna"
                    className="mt-1"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Nível 3 - Bisavós */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Bisavós</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Bisavós Paternos */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-center text-blue-600">Lado Paterno</h3>
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <Label className="text-sm">Bisavô Paterno Paterno</Label>
                    <Input
                      value={bisavoPaternoPaterno}
                      onChange={(e) => setBisavoPaternoPaterno(e.target.value)}
                      placeholder="Nome do bisavô paterno paterno"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavó Paterna Paterna</Label>
                    <Input
                      value={bisavoPaternaPaterno}
                      onChange={(e) => setBisavoPaternaPaterno(e.target.value)}
                      placeholder="Nome da bisavó paterna paterna"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavô Materno Paterno</Label>
                    <Input
                      value={bisavoMaternoPaterno}
                      onChange={(e) => setBisavoMaternoPaterno(e.target.value)}
                      placeholder="Nome do bisavô materno paterno"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavó Materna Paterna</Label>
                    <Input
                      value={bisavoMaternaPaterno}
                      onChange={(e) => setBisavoMaternaPaterno(e.target.value)}
                      placeholder="Nome da bisavó materna paterna"
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>

              {/* Bisavós Maternos */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-center text-pink-600">Lado Materno</h3>
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <Label className="text-sm">Bisavô Paterno Materno</Label>
                    <Input
                      value={bisavoPaternoMaterno}
                      onChange={(e) => setBisavoPaternoMaterno(e.target.value)}
                      placeholder="Nome do bisavô paterno materno"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavó Paterna Materna</Label>
                    <Input
                      value={bisavoPaternaMaterno}
                      onChange={(e) => setBisavoPaternaMaterno(e.target.value)}
                      placeholder="Nome da bisavó paterna materna"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavô Materno Materno</Label>
                    <Input
                      value={bisavoMaternoMaterno}
                      onChange={(e) => setBisavoMaternoMaterno(e.target.value)}
                      placeholder="Nome do bisavô materno materno"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">Bisavó Materna Materna</Label>
                    <Input
                      value={bisavoMaternaMaterno}
                      onChange={(e) => setBisavoMaternaMaterno(e.target.value)}
                      placeholder="Nome da bisavó materna materna"
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Botão de Salvar */}
      <div className="mt-8 flex justify-center">
        <Button onClick={handleSalvar} disabled={saving} size="lg">
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Salvando...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Salvar Genealogia
            </>
          )}
        </Button>
      </div>
    </div>
  );
}