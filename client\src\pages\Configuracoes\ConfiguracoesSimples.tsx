import { useState } from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Settings, 
  Palette, 
  Bell, 
  Users,
  Save,
  CloudUpload
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

export function ConfiguracoesPage() {
  const { user } = useAuth();
  const isAdmin = user?.id === 1;

  const [hasChanges, setHasChanges] = useState(false);

  const saveConfigurations = () => {
    console.log('<PERSON><PERSON><PERSON> configurações...');
    setHasChanges(false);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
        <p className="text-muted-foreground">
          Gerencie as configurações da fazenda e do sistema
        </p>
      </div>

      <Tabs defaultValue="geral" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="geral" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Geral</span>
          </TabsTrigger>
          <TabsTrigger value="aparencia" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span className="hidden sm:inline">Aparência</span>
          </TabsTrigger>
          <TabsTrigger value="usuarios" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Perfil</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Geral */}
        <TabsContent value="geral">
          <Card>
            <CardHeader>
              <CardTitle>Informações da Fazenda</CardTitle>
              <CardDescription>
                Configure as informações básicas da fazenda/haras
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="nome-fazenda">Nome da Fazenda</Label>
                    <Input
                      id="nome-fazenda"
                      defaultValue="Fazenda Santa Maria"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="razao-social">Razão Social</Label>
                    <Input
                      id="razao-social"
                      defaultValue="Fazenda Santa Maria LTDA"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="cnpj">CNPJ</Label>
                    <Input
                      id="cnpj"
                      defaultValue="12.345.678/0001-90"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="inscricao">Inscrição Estadual</Label>
                    <Input
                      id="inscricao"
                      defaultValue="123456789"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveConfigurations} disabled={!hasChanges}>
                <Save className="mr-2 h-4 w-4" />
                Salvar Alterações
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Tab Aparência */}
        <TabsContent value="aparencia">
          <Card>
            <CardHeader>
              <CardTitle>Aparência e Interface</CardTitle>
              <CardDescription>
                Personalize a aparência do sistema conforme sua preferência
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label>Tema do Sistema</Label>
                  <Select defaultValue="light" onValueChange={() => setHasChanges(true)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Claro</SelectItem>
                      <SelectItem value="dark">Escuro</SelectItem>
                      <SelectItem value="system">Seguir sistema</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Formato de Data</Label>
                  <Select defaultValue="DD/MM/YYYY" onValueChange={() => setHasChanges(true)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/AAAA</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/AAAA</SelectItem>
                      <SelectItem value="YYYY-MM-DD">AAAA-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveConfigurations} disabled={!hasChanges}>
                <Save className="mr-2 h-4 w-4" />
                Salvar Alterações
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Tab Usuários - Perfil */}
        <TabsContent value="usuarios">
          <Card>
            <CardHeader>
              <CardTitle>Meu Perfil</CardTitle>
              <CardDescription>
                Gerencie suas informações pessoais e configurações de conta
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center">
                      <Users className="h-10 w-10 text-muted-foreground" />
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm">
                        <CloudUpload className="mr-2 h-4 w-4" />
                        Alterar Foto
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        JPG, PNG ou GIF. Máximo 2MB.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="nome-completo">Nome Completo</Label>
                    <Input 
                      id="nome-completo"
                      placeholder="Seu nome completo"
                      className="mt-1"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email-usuario">E-mail</Label>
                    <Input 
                      id="email-usuario"
                      type="email"
                      placeholder="<EMAIL>"
                      className="mt-1"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Alterar Senha</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="senha-atual">Senha Atual</Label>
                    <Input 
                      id="senha-atual"
                      type="password"
                      placeholder="Digite sua senha atual"
                      className="mt-1"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="nova-senha">Nova Senha</Label>
                    <Input 
                      id="nova-senha"
                      type="password"
                      placeholder="Digite a nova senha"
                      className="mt-1"
                      onChange={() => setHasChanges(true)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveConfigurations} disabled={!hasChanges}>
                <Save className="mr-2 h-4 w-4" />
                Salvar Alterações
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}