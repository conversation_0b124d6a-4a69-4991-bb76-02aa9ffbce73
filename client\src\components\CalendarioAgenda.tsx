import { useState } from 'react';
import { DayPicker } from 'react-day-picker';
import { ptBR } from 'date-fns/locale';
import { format, isToday, isSameMonth, isSameDay } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Evento } from '../../../shared/schema';

// Estilos customizados importados automaticamente do arquivo CSS
import 'react-day-picker/dist/style.css';

interface CalendarioAgendaProps {
  eventos: Evento[];
  onSelectDate: (date: Date) => void;
  selectedDate: Date;
  onCreateEvent?: (date: Date) => void;
}

export function CalendarioAgenda({ 
  eventos, 
  onSelectDate, 
  selectedDate,
  onCreateEvent
}: CalendarioAgendaProps) {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  
  // Criar um mapa para contar quantos eventos por dia
  const eventCountByDate = eventos.reduce((acc, evento) => {
    const dataEvento = new Date(evento.data);
    const dataKey = format(dataEvento, 'yyyy-MM-dd');
    
    if (!acc[dataKey]) {
      acc[dataKey] = {
        count: 0,
        tipos: new Set<string>()
      };
    }
    
    acc[dataKey].count += 1;
    acc[dataKey].tipos.add(evento.tipo || 'evento');
    
    return acc;
  }, {} as Record<string, { count: number, tipos: Set<string> }>);
  
  // Modificador para dias com eventos
  const hasEventModifier = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return !!eventCountByDate[dateKey];
  };
  
  // Função para renderizar um dia personalizado no calendário
  const renderDay = (day: Date) => {
    const dateKey = format(day, 'yyyy-MM-dd');
    const eventInfo = eventCountByDate[dateKey];
    
    return (
      <div className="relative h-full w-full p-1">
        <div 
          className={`h-full w-full flex flex-col items-center justify-center rounded-md
            ${isToday(day) ? 'bg-blue-50 font-bold text-blue-700' : ''}
            ${isSameDay(day, selectedDate) ? 'bg-blue-100 text-blue-800' : ''}
          `}
        >
          <span className="text-sm">{format(day, 'd')}</span>
          
          {eventInfo && (
            <div className="mt-1 flex flex-wrap justify-center">
              {eventInfo.count > 0 && (
                <span className="inline-flex items-center justify-center w-4 h-4 text-[9px] font-medium rounded-full bg-blue-600 text-white">
                  {eventInfo.count}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Obter eventos do dia selecionado
  const eventosNoDiaSelecionado = eventos.filter(evento => {
    const dataEvento = new Date(evento.data);
    return isSameDay(dataEvento, selectedDate);
  });
  
  // Função para renderizar uma cor de acordo com o tipo de evento
  const getTipoEventoColor = (tipo: string) => {
    const colors: Record<string, string> = {
      'veterinario': 'bg-blue-100 text-blue-800',
      'ferrador': 'bg-amber-100 text-amber-800',
      'treinamento': 'bg-green-100 text-green-800',
      'competicao': 'bg-purple-100 text-purple-800',
      'reprodutivo': 'bg-pink-100 text-pink-800',
      'outro': 'bg-gray-100 text-gray-800'
    };
    
    return colors[tipo.toLowerCase()] || colors['outro'];
  };
  
  // Agrupamento de eventos por tipo para resumo no topo
  const eventosPorTipo = eventos
    .filter(evento => isSameMonth(new Date(evento.data), currentMonth))
    .reduce((acc, evento) => {
      const tipo = evento.tipo || 'outro';
      if (!acc[tipo]) {
        acc[tipo] = 0;
      }
      acc[tipo]++;
      return acc;
    }, {} as Record<string, number>);
  
  return (
    <div className="space-y-4">
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 py-3 px-4 border-b">
            <div className="flex flex-wrap gap-2 mb-2">
              {Object.entries(eventosPorTipo).map(([tipo, count]) => (
                <Badge key={tipo} variant="outline" className={`${getTipoEventoColor(tipo)}`}>
                  {tipo.charAt(0).toUpperCase() + tipo.slice(1)}: {count}
                </Badge>
              ))}
            </div>
            
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-blue-900">
                {format(currentMonth, 'MMMM yyyy', { locale: ptBR })}
              </h3>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs px-2 border-blue-200 text-blue-700 hover:bg-blue-100"
                onClick={() => onCreateEvent && onCreateEvent(selectedDate)}
              >
                Novo Evento
              </Button>
            </div>
          </div>
          
          <DayPicker
            mode="single"
            selected={selectedDate}
            onSelect={(date) => date && onSelectDate(date)}
            locale={ptBR}
            month={currentMonth}
            onMonthChange={setCurrentMonth}
            modifiers={{ hasEvent: hasEventModifier }}
            modifiersStyles={{
              hasEvent: { 
                fontWeight: 'bold'
              }
            }}
            className="p-3"
            styles={{
              caption: { color: '#1e40af' },
              day: { margin: 0, padding: 0, height: '38px', width: '38px' }
            }}
            components={{
              Day: ({ date }) => renderDay(date)
            }}
          />
        </CardContent>
      </Card>
      
      <div className="mt-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium text-gray-900">
            {eventosNoDiaSelecionado.length ? (
              <span>
                {eventosNoDiaSelecionado.length} 
                {eventosNoDiaSelecionado.length === 1 ? ' evento' : ' eventos'} em{' '}
                {format(selectedDate, 'dd/MM/yyyy')}
              </span>
            ) : (
              <span>Nenhum evento em {format(selectedDate, 'dd/MM/yyyy')}</span>
            )}
          </h3>
        </div>
        
        {eventosNoDiaSelecionado.length > 0 ? (
          <div className="space-y-2">
            {eventosNoDiaSelecionado.map((evento) => (
              <Card key={evento.id} className="overflow-hidden hover:shadow-md transition-shadow duration-200">
                <CardContent className="p-3">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <Badge variant="outline" className={getTipoEventoColor(evento.tipo || 'outro')}>
                          {(evento.tipo || 'outro').charAt(0).toUpperCase() + (evento.tipo || 'outro').slice(1)}
                        </Badge>
                        <h4 className="ml-2 font-medium">{evento.titulo}</h4>
                      </div>
                      
                      {evento.hora_inicio && (
                        <p className="text-sm text-gray-500 mt-1">
                          Horário: {evento.hora_inicio}
                          {evento.hora_fim && ` até ${evento.hora_fim}`}
                        </p>
                      )}
                      
                      {evento.descricao && (
                        <p className="text-sm text-gray-600 mt-2 line-clamp-2">{evento.descricao}</p>
                      )}
                    </div>
                    
                    <Button variant="outline" size="sm" className="shrink-0">
                      Detalhes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="bg-gray-50 border-dashed">
            <CardContent className="p-6 text-center">
              <p className="text-gray-500">Nenhum evento agendado para esta data.</p>
              {onCreateEvent && (
                <Button 
                  variant="outline" 
                  className="mt-2"
                  onClick={() => onCreateEvent(selectedDate)}
                >
                  Adicionar Evento
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default CalendarioAgenda;