import { pgTable, text, serial, integer, timestamp, real, date, pgEnum, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { horses } from './cavalos';
import { 
  tipoCoberturaEnum,
  estadoReproducaoEnum,
  observacoesSchema,
  optionalDateSchema,
  currencySchema,
  UserContext 
} from './core';

// ============================================================================
// ENUMS
// ============================================================================

export const statusCobertura = pgEnum('status_cobertura', [
  'planejada', 'em_andamento', 'confirmada', 'falhada', 'cancelada'
]);

export const resultadoGestacao = pgEnum('resultado_gestacao', [
  'prenhez_confirmada', 'nao_prenhe', 'perda_embrionaria', 'aborto', 'parto_normal', 'parto_assistido'
]);

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const coberturas = pgTable("coberturas", {
  id: serial("id").primaryKey(),
  eguaId: integer("egua_id").notNull(),
  garanhaoId: integer("garanhao_id").notNull(),
  user_id: integer("user_id").notNull(),
  
  // Dados da cobertura
  dataCobertura: date("data_cobertura").notNull(),
  tipoCobertura: tipoCoberturaEnum("tipo_cobertura").notNull(),
  status: statusCobertura("status").default('planejada'),
  
  // Detalhes técnicos
  veterinario: text("veterinario"),
  localCobertura: text("local_cobertura"),
  horaCobertura: text("hora_cobertura"),
  
  // Monitoramento da égua
  cicloEstral: text("ciclo_estral"), // "proestro", "estro", "metaestro", "diestro"
  dataUltimoCio: date("data_ultimo_cio"),
  condicaoReprodutiva: text("condicao_reprodutiva"),
  
  // Dados do garanhão
  qualidadeSemen: text("qualidade_semen"),
  concentracaoEspermatica: real("concentracao_espermatica"), // milhões/ml
  motilidade: real("motilidade"), // %
  
  // Custos
  valorCobertura: real("valor_cobertura"),
  taxasVeterinarias: real("taxas_veterinarias"),
  
  // Seguimento
  dataConfirmacao: date("data_confirmacao"),
  resultadoConfirmacao: text("resultado_confirmacao"),
  
  observacoes: text("observacoes"),
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const gestacoes = pgTable("gestacoes", {
  id: serial("id").primaryKey(),
  coberturaId: integer("cobertura_id").notNull(),
  eguaId: integer("egua_id").notNull(),
  
  // Dados da gestação
  dataConfirmacao: date("data_confirmacao").notNull(),
  dataPrevisaoParto: date("data_previsao_parto"),
  idadeGestacional: integer("idade_gestacional"), // dias
  
  // Monitoramento
  estadoGestacao: estadoReproducaoEnum("estado_gestacao").default('prenhez_confirmada'),
  veterinarioAcompanhamento: text("veterinario_acompanhamento"),
  
  // Exames realizados
  ultrassom30Dias: boolean("ultrassom_30_dias").default(false),
  ultrassom60Dias: boolean("ultrassom_60_dias").default(false),
  ultrassom90Dias: boolean("ultrassom_90_dias").default(false),
  
  // Observações durante gestação
  condicaoFisica: text("condicao_fisica"),
  alteracoesComportamentais: text("alteracoes_comportamentais"),
  suplementacao: text("suplementacao"),
  exercicios: text("exercicios"),
  
  // Resultado
  dataParto: date("data_parto"),
  resultadoParto: resultadoGestacao("resultado_parto"),
  duracaoGestacao: integer("duracao_gestacao"), // dias
  
  // Dados do potro (se nasceu)
  potroId: integer("potro_id"), // FK para horses
  pesoNascimento: real("peso_nascimento"),
  sexoPotro: text("sexo_potro"),
  condicaoNascimento: text("condicao_nascimento"),
  
  // Dados da égua pós-parto
  condicaoEgua: text("condicao_egua"),
  expulsaoPlacenta: text("expulsao_placenta"), // "normal", "retida", "manual"
  recuperacaoParto: text("recuperacao_parto"),
  
  observacoes: text("observacoes"),
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const ciclosReproductivos = pgTable("ciclos_reprodutivos", {
  id: serial("id").primaryKey(),
  eguaId: integer("egua_id").notNull(),
  
  // Dados do ciclo
  anoReproductivo: integer("ano_reprodutivo").notNull(),
  inicioTemporadaReproductiva: date("inicio_temporada_reproductiva"),
  fimTemporadaReproductiva: date("fim_temporada_reproductiva"),
  
  // Registros de cio
  datasCio: date("datas_cio").array(),
  duracaoMediaCio: integer("duracao_media_cio"), // horas
  intervaloCios: integer("intervalo_cios"), // dias
  
  // Eficiência reprodutiva
  numeroTentativas: integer("numero_tentativas").default(0),
  numeroCoberturasEfetivas: integer("numero_coberturas_efetivas").default(0),
  prenhezes: integer("prenhezes").default(0),
  partos: integer("partos").default(0),
  
  // Tratamentos hormonais
  sincronizacaoUsada: text("sincronizacao_usada"),
  hormoniosUtilizados: text("hormonios_utilizados").array(),
  protocoloSeguido: text("protocolo_seguido"),
  
  // Status atual
  statusReproductivo: text("status_reproductivo"), // "ativa", "gestante", "vazia", "aposentada"
  proximoAcasalamento: date("proximo_acasalamento"),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const coberturasRelations = relations(coberturas, ({ one }) => ({
  egua: one(horses, {
    fields: [coberturas.eguaId],
    references: [horses.id],
    relationName: "egua_cobertura"
  }),
  garanhao: one(horses, {
    fields: [coberturas.garanhaoId],
    references: [horses.id],
    relationName: "garanhao_cobertura"
  }),
}));

export const gestacoesRelations = relations(gestacoes, ({ one }) => ({
  cobertura: one(coberturas, {
    fields: [gestacoes.coberturaId],
    references: [coberturas.id],
  }),
  egua: one(horses, {
    fields: [gestacoes.eguaId],
    references: [horses.id],
    relationName: "egua_gestacao"
  }),
  potro: one(horses, {
    fields: [gestacoes.potroId],
    references: [horses.id],
    relationName: "potro_gestacao"
  }),
}));

export const ciclosReproductivosRelations = relations(ciclosReproductivos, ({ one }) => ({
  egua: one(horses, {
    fields: [ciclosReproductivos.eguaId],
    references: [horses.id],
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const coberturaSchema = z.object({
  id: z.number().int().positive().optional(),
  eguaId: z.number().int().positive(),
  garanhaoId: z.number().int().positive(),
  user_id: z.number().int().positive(),
  
  dataCobertura: z.string().datetime().or(z.date()),
  tipoCobertura: z.enum(['monta_natural', 'inseminacao', 'transferencia_embriao']),
  status: z.enum(['planejada', 'em_andamento', 'confirmada', 'falhada', 'cancelada']).default('planejada'),
  
  veterinario: z.string().max(100).optional().nullable(),
  localCobertura: z.string().max(100).optional().nullable(),
  horaCobertura: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional().nullable(),
  
  cicloEstral: z.enum(['proestro', 'estro', 'metaestro', 'diestro']).optional().nullable(),
  dataUltimoCio: optionalDateSchema,
  condicaoReprodutiva: z.string().max(200).optional().nullable(),
  
  qualidadeSemen: z.string().max(100).optional().nullable(),
  concentracaoEspermatica: z.number().positive().max(1000).optional().nullable(), // milhões/ml
  motilidade: z.number().min(0).max(100).optional().nullable(), // %
  
  valorCobertura: currencySchema.optional().nullable(),
  taxasVeterinarias: currencySchema.optional().nullable(),
  
  dataConfirmacao: optionalDateSchema,
  resultadoConfirmacao: z.string().max(200).optional().nullable(),
  
  observacoes: observacoesSchema,
  arquivoIds: z.array(z.string()).optional().nullable(),
}).refine((data) => {
  // Business rule: égua e garanhão devem ser diferentes
  return data.eguaId !== data.garanhaoId;
}, {
  message: "Égua e garanhão devem ser animais diferentes",
  path: ["garanhaoId"]
}).refine((data) => {
  // Business rule: data de confirmação deve ser posterior à cobertura
  if (data.dataConfirmacao && data.dataCobertura) {
    const confirmacao = new Date(data.dataConfirmacao);
    const cobertura = new Date(data.dataCobertura);
    return confirmacao >= cobertura;
  }
  return true;
}, {
  message: "Data de confirmação deve ser posterior à cobertura",
  path: ["dataConfirmacao"]
});

export const gestacaoSchema = z.object({
  id: z.number().int().positive().optional(),
  coberturaId: z.number().int().positive(),
  eguaId: z.number().int().positive(),
  
  dataConfirmacao: z.string().datetime().or(z.date()),
  dataPrevisaoParto: optionalDateSchema,
  idadeGestacional: z.number().int().min(0).max(400).optional().nullable(),
  
  estadoGestacao: z.enum([
    'pre_cobertura', 'em_cobertura', 'prenhez_confirmada',
    'parto_realizado', 'abortado', 'nao_prenhe'
  ]).default('prenhez_confirmada'),
  veterinarioAcompanhamento: z.string().max(100).optional().nullable(),
  
  ultrassom30Dias: z.boolean().default(false),
  ultrassom60Dias: z.boolean().default(false),
  ultrassom90Dias: z.boolean().default(false),
  
  condicaoFisica: z.string().max(200).optional().nullable(),
  alteracoesComportamentais: z.string().max(300).optional().nullable(),
  suplementacao: z.string().max(300).optional().nullable(),
  exercicios: z.string().max(200).optional().nullable(),
  
  dataParto: optionalDateSchema,
  resultadoParto: z.enum([
    'prenhez_confirmada', 'nao_prenhe', 'perda_embrionaria',
    'aborto', 'parto_normal', 'parto_assistido'
  ]).optional().nullable(),
  duracaoGestacao: z.number().int().min(300).max(400).optional().nullable(), // dias
  
  potroId: z.number().int().positive().optional().nullable(),
  pesoNascimento: z.number().positive().max(100).optional().nullable(), // kg
  sexoPotro: z.enum(['macho', 'femea']).optional().nullable(),
  condicaoNascimento: z.string().max(200).optional().nullable(),
  
  condicaoEgua: z.string().max(200).optional().nullable(),
  expulsaoPlacenta: z.enum(['normal', 'retida', 'manual']).optional().nullable(),
  recuperacaoParto: z.string().max(200).optional().nullable(),
  
  observacoes: observacoesSchema,
  arquivoIds: z.array(z.string()).optional().nullable(),
}).refine((data) => {
  // Business rule: data do parto deve ser posterior à confirmação
  if (data.dataParto && data.dataConfirmacao) {
    const parto = new Date(data.dataParto);
    const confirmacao = new Date(data.dataConfirmacao);
    return parto >= confirmacao;
  }
  return true;
}, {
  message: "Data do parto deve ser posterior à confirmação da prenhez",
  path: ["dataParto"]
}).refine((data) => {
  // Business rule: duração da gestação deve estar dentro do normal (320-365 dias)
  if (data.duracaoGestacao) {
    return data.duracaoGestacao >= 320 && data.duracaoGestacao <= 365;
  }
  return true;
}, {
  message: "Duração da gestação deve estar entre 320-365 dias",
  path: ["duracaoGestacao"]
});

export const cicloReprodutivoSchema = z.object({
  id: z.number().int().positive().optional(),
  eguaId: z.number().int().positive(),
  
  anoReproductivo: z.number().int().min(2000).max(2050),
  inicioTemporadaReproductiva: optionalDateSchema,
  fimTemporadaReproductiva: optionalDateSchema,
  
  datasCio: z.array(z.string().datetime()).optional().nullable(),
  duracaoMediaCio: z.number().int().min(12).max(168).optional().nullable(), // 12h - 7 dias
  intervaloCios: z.number().int().min(18).max(24).optional().nullable(), // 18-24 dias
  
  numeroTentativas: z.number().int().nonnegative().default(0),
  numeroCoberturasEfetivas: z.number().int().nonnegative().default(0),
  prenhezes: z.number().int().nonnegative().default(0),
  partos: z.number().int().nonnegative().default(0),
  
  sincronizacaoUsada: z.string().max(100).optional().nullable(),
  hormoniosUtilizados: z.array(z.string()).optional().nullable(),
  protocoloSeguido: z.string().max(200).optional().nullable(),
  
  statusReproductivo: z.enum(['ativa', 'gestante', 'vazia', 'aposentada']).optional().nullable(),
  proximoAcasalamento: optionalDateSchema,
  
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: fim da temporada deve ser posterior ao início
  if (data.fimTemporadaReproductiva && data.inicioTemporadaReproductiva) {
    const fim = new Date(data.fimTemporadaReproductiva);
    const inicio = new Date(data.inicioTemporadaReproductiva);
    return fim >= inicio;
  }
  return true;
}, {
  message: "Fim da temporada deve ser posterior ao início",
  path: ["fimTemporadaReproductiva"]
}).refine((data) => {
  // Business rule: número de coberturas efetivas não pode ser maior que tentativas
  return data.numeroCoberturasEfetivas <= data.numeroTentativas;
}, {
  message: "Coberturas efetivas não podem ser maiores que tentativas",
  path: ["numeroCoberturasEfetivas"]
});

// Insert schemas
export const insertCoberturaSchema = createInsertSchema(coberturas).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertGestacaoSchema = createInsertSchema(gestacoes).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertCicloReprodutivoSchema = createInsertSchema(ciclosReproductivos).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type Cobertura = typeof coberturas.$inferSelect;
export type NewCobertura = typeof coberturas.$inferInsert;
export type CoberturaData = z.infer<typeof coberturaSchema>;

export type Gestacao = typeof gestacoes.$inferSelect;
export type NewGestacao = typeof gestacoes.$inferInsert;
export type GestacaoData = z.infer<typeof gestacaoSchema>;

export type CicloReprodutivo = typeof ciclosReproductivos.$inferSelect;
export type NewCicloReprodutivo = typeof ciclosReproductivos.$inferInsert;
export type CicloReprodutivoData = z.infer<typeof cicloReprodutivoSchema>;

// ============================================================================
// BUSINESS LOGIC
// ============================================================================

/**
 * Calculates expected delivery date based on breeding date
 */
export function calcularDataPrevisaoParto(dataCobertura: string): string {
  const cobertura = new Date(dataCobertura);
  // Average gestation period for horses: 342 days (11 months, 1 week)
  cobertura.setDate(cobertura.getDate() + 342);
  return cobertura.toISOString().split('T')[0];
}

/**
 * Determines breeding season based on hemisphere and date
 */
export function determinarTemporadaReproductiva(
  data: string, 
  hemisferio: 'norte' | 'sul' = 'sul'
): { inicio: string; fim: string } {
  const ano = new Date(data).getFullYear();
  
  if (hemisferio === 'sul') {
    // Southern hemisphere: September to March
    return {
      inicio: `${ano}-09-01`,
      fim: `${ano + 1}-03-31`
    };
  } else {
    // Northern hemisphere: March to September
    return {
      inicio: `${ano}-03-01`,
      fim: `${ano}-09-30`
    };
  }
}

/**
 * Calculates reproductive efficiency
 */
export function calcularEficienciaReprodutiva(ciclo: CicloReprodutivoData): {
  taxaConcepção: number;
  taxaParto: number;
  eficienciaGeral: number;
} {
  const tentativas = ciclo.numeroTentativas || 0;
  const prenhezes = ciclo.prenhezes || 0;
  const partos = ciclo.partos || 0;
  
  const taxaConcepção = tentativas > 0 ? (prenhezes / tentativas) * 100 : 0;
  const taxaParto = prenhezes > 0 ? (partos / prenhezes) * 100 : 0;
  const eficienciaGeral = tentativas > 0 ? (partos / tentativas) * 100 : 0;
  
  return {
    taxaConcepção: Math.round(taxaConcepção * 100) / 100,
    taxaParto: Math.round(taxaParto * 100) / 100,
    eficienciaGeral: Math.round(eficienciaGeral * 100) / 100
  };
}

/**
 * Validates optimal breeding conditions
 */
export function validarCondicoesCobertura(
  egua: { birthDate?: string; status?: string },
  garanhao: { birthDate?: string; status?: string },
  dataCobertura: string
): string[] {
  const warnings: string[] = [];
  const cobertura = new Date(dataCobertura);
  
  // Mare age validation
  if (egua.birthDate) {
    const idadeEgua = calcularIdadeEmAnos(egua.birthDate, dataCobertura);
    if (idadeEgua < 3) {
      warnings.push("Égua muito jovem para reprodução (mínimo 3 anos)");
    } else if (idadeEgua > 18) {
      warnings.push("Égua em idade avançada para reprodução (máximo recomendado 18 anos)");
    }
  }
  
  // Stallion age validation
  if (garanhao.birthDate) {
    const idadeGaranhao = calcularIdadeEmAnos(garanhao.birthDate, dataCobertura);
    if (idadeGaranhao < 2) {
      warnings.push("Garanhão muito jovem para reprodução (mínimo 2 anos)");
    } else if (idadeGaranhao > 20) {
      warnings.push("Garanhão em idade avançada para reprodução");
    }
  }
  
  // Breeding season validation (for southern hemisphere)
  const mes = cobertura.getMonth() + 1;
  const temporadaIdeal = mes >= 9 || mes <= 3;
  if (!temporadaIdeal) {
    warnings.push("Cobertura fora da temporada reprodutiva ideal (setembro a março)");
  }
  
  return warnings;
}

/**
 * Monitors pregnancy progression milestones
 */
export function verificarMarcosPrenhez(gestacao: GestacaoData): {
  proximoExame: string | null;
  alertas: string[];
} {
  const alertas: string[] = [];
  let proximoExame: string | null = null;
  
  if (!gestacao.dataConfirmacao) {
    return { proximoExame, alertas };
  }
  
  const confirmacao = new Date(gestacao.dataConfirmacao);
  const hoje = new Date();
  const diasGestacao = Math.floor((hoje.getTime() - confirmacao.getTime()) / (1000 * 60 * 60 * 24));
  
  // 30-day ultrasound
  if (diasGestacao >= 25 && !gestacao.ultrassom30Dias) {
    alertas.push("Ultrassom de 30 dias pendente");
    if (!proximoExame) {
      const data30Dias = new Date(confirmacao);
      data30Dias.setDate(data30Dias.getDate() + 30);
      proximoExame = data30Dias.toISOString().split('T')[0];
    }
  }
  
  // 60-day ultrasound
  if (diasGestacao >= 55 && !gestacao.ultrassom60Dias) {
    alertas.push("Ultrassom de 60 dias pendente");
    if (!proximoExame) {
      const data60Dias = new Date(confirmacao);
      data60Dias.setDate(data60Dias.getDate() + 60);
      proximoExame = data60Dias.toISOString().split('T')[0];
    }
  }
  
  // 90-day ultrasound
  if (diasGestacao >= 85 && !gestacao.ultrassom90Dias) {
    alertas.push("Ultrassom de 90 dias pendente");
    if (!proximoExame) {
      const data90Dias = new Date(confirmacao);
      data90Dias.setDate(data90Dias.getDate() + 90);
      proximoExame = data90Dias.toISOString().split('T')[0];
    }
  }
  
  // Pre-delivery preparation (330+ days)
  if (diasGestacao >= 330) {
    alertas.push("Início do período de preparação para o parto");
  }
  
  return { proximoExame, alertas };
}

function calcularIdadeEmAnos(birthDate: string, referenceDate: string): number {
  const birth = new Date(birthDate);
  const reference = new Date(referenceDate);
  return (reference.getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
}