// Service Worker para EquiGestor AI

// Nome do cache
const CACHE_NAME = 'equigestor-cache-v1';

// Arquivos estáticos para cache offline
const urlsToCache = [
  '/',
  '/index.html',
  '/main.js',
  '/styles.css',
  '/assets/eq-icon.png'
];

// Instalar o service worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Cache aberto');
        return cache.addAll(urlsToCache);
      })
  );
});

// Ativar o service worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.filter((cacheName) => {
          return cacheName !== CACHE_NAME;
        }).map((cacheName) => {
          return caches.delete(cacheName);
        })
      );
    })
  );
});

// Estratégia de cache: network-first com fallback para cache
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignorar requisições que não são GET ou que não são HTTP/HTTPS
  if (request.method !== 'GET') {
    return;
  }

  if (url.protocol !== 'http:' && url.protocol !== 'https:') {
    return;
  }

  // Ignorar requisições para API do OpenAI (não cacheável)
  if (url.hostname === 'api.openai.com') {
    return;
  }

  event.respondWith(
    fetch(request)
      .then((response) => {
        // Verificar se a resposta é válida
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response;
        }

        // Clonar a resposta (resposta é um stream e só pode ser consumido uma vez)
        const responseToCache = response.clone();

        // Abrir o cache e armazenar resposta apenas para mesma origem
        if (url.origin === self.location.origin) {
          caches.open(CACHE_NAME)
            .then((cache) => {
              cache.put(request, responseToCache);
            });
        }

        return response;
      })
      .catch(() => {
        // Se falhar, tentar obter do cache
        return caches.match(request);
      })
  );
});

// Suporte a Background Sync
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-messages') {
    event.waitUntil(syncMessages());
  }
});

// Função para sincronizar mensagens armazenadas
async function syncMessages() {
  // Esta função seria responsável por pegar mensagens armazenadas
  // no IndexedDB e enviá-las ao servidor
  console.log('Sincronizando mensagens pendentes...');
  
  // A implementação real seria feita via IndexedDB, mas isso
  // é tratado diretamente no hook useAssistente
}