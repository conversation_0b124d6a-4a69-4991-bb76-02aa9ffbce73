/**
 * Utilitários para trabalhar com componentes Select de forma segura
 */

/**
 * Verifica se um valor é válido para SelectItem
 * Um valor válido deve ser uma string não vazia
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for válido, falso caso contrário
 */
export function isValidSelectValue(value: any): boolean {
  // Verificações de segurança para valores de SelectItem
  if (value === undefined || value === null) {
    return false;
  }
  
  // Converter para string para garantir que estamos trabalhando com strings
  const stringValue = String(value);
  
  // Verificar se é uma string vazia ou apenas espaços
  if (stringValue.trim() === '') {
    return false;
  }
  
  return true;
}

/**
 * Converte um valor potencialmente inválido para um valor seguro para SelectItem
 * @param value Valor original
 * @param fallbackValue Valor de fallback se o original for inválido
 * @param prefix Prefixo para o valor (útil para identificar o contexto)
 * @returns Um valor seguro para usar em SelectItem
 */
export function getSafeSelectValue(
  value: any, 
  fallbackValue: string = 'nao_informado',
  prefix: string = ''
): string {
  // Se o valor já for válido, retorná-lo
  if (isValidSelectValue(value)) {
    return String(value);
  }
  
  // Caso contrário, retornar o valor de fallback
  return prefix ? `${prefix}_${fallbackValue}` : fallbackValue;
}

/**
 * Converte um valor de SelectItem de volta para o valor original
 * @param value Valor do SelectItem
 * @param emptyValue Valor a retornar quando o SelectItem representa "vazio"
 * @returns O valor original
 */
export function getOriginalValue(
  value: string,
  emptyValue: string | null = ''
): string | null {
  // Lista de valores que representam "não informado" ou "vazio"
  const emptyValues = [
    'nao_informado',
    'nao_informada',
    'nenhum',
    'nenhuma',
    'nenhum_animal',
    'nenhum_item',
    'nao_selecionado',
    'nao_selecionada',
    'none',
    '_none'
  ];
  
  // Se o valor estiver na lista de valores vazios, retornar o emptyValue
  if (emptyValues.some(empty => value.includes(empty))) {
    return emptyValue;
  }
  
  // Caso contrário, retornar o valor como está
  return value;
}

/**
 * Converte um valor para um número ou null
 * @param value Valor a ser convertido
 * @returns Número ou null
 */
export function toNumberOrNull(value: string | null | undefined): number | null {
  // Se o valor for nulo, indefinido ou representar "vazio", retornar null
  if (!value || getOriginalValue(value) === null || getOriginalValue(value) === '') {
    return null;
  }
  
  // Tentar converter para número
  const num = Number(value);
  
  // Se for um número válido, retorná-lo; caso contrário, retornar null
  return isNaN(num) ? null : num;
}

/**
 * Função recursiva para processar um objeto e converter valores de select
 * @param obj Objeto a ser processado
 * @param emptyValue Valor a retornar quando o SelectItem representa "vazio"
 * @returns Objeto com valores convertidos
 */
export function processSelectValues<T extends Record<string, any>>(
  obj: T,
  emptyValue: string | null = ''
): T {
  // Caso base: se não for um objeto ou for null, retornar como está
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  
  // Criar uma cópia do objeto para não modificar o original
  const result = { ...obj };
  
  // Processar cada propriedade do objeto
  Object.keys(result).forEach(key => {
    const value = result[key];
    
    // Se for um objeto, processar recursivamente
    if (typeof value === 'object' && value !== null) {
      result[key] = processSelectValues(value, emptyValue);
    }
    // Se for uma string, verificar se é um valor de select
    else if (typeof value === 'string') {
      result[key] = getOriginalValue(value, emptyValue);
    }
  });
  
  return result;
}
