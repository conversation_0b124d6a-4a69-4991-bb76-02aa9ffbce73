import { useState, useEffect, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";

// Interface para armazenamento local de mensagens
interface IndexedDBStore {
  saveConversation: (messages: Message[]) => Promise<void>;
  loadConversation: () => Promise<Message[]>;
  clearConversation: () => Promise<void>;
}

// Função para criar armazenamento local
const createLocalStore = (): IndexedDBStore => {
  return {
    saveConversation: async (messages: Message[]): Promise<void> => {
      try {
        localStorage.setItem('equigestor_messages', JSON.stringify(messages));
      } catch (error) {
        console.error('Erro ao salvar mensagens localmente:', error);
      }
    },
    
    loadConversation: async (): Promise<Message[]> => {
      try {
        const stored = localStorage.getItem('equigestor_messages');
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.error('Erro ao carregar mensagens locais:', error);
        return [];
      }
    },
    
    clearConversation: async (): Promise<void> => {
      try {
        localStorage.removeItem('equigestor_messages');
      } catch (error) {
        console.error('Erro ao limpar mensagens locais:', error);
      }
    }
  };
};

// Definição de tipos
export type IntentType = 
  | "cadastrar_cavalo"
  | "editar_cavalo"
  | "visualizar_ficha"
  | "excluir_cavalo"
  | "registrar_peso"
  | "registrar_entrada_saida"
  | "registrar_genealogia"
  | "enviar_arquivo"
  | "registrar_vacina"
  | "registrar_treino" 
  | "registrar_procedimento_vet"
  | "controle_reproducao"
  | "registrar_alimentacao"
  | "agendar_evento"
  | "consultar_agenda"
  | "unknown";

export interface Intent {
  name: IntentType;
  phrases: string[];
}

export interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
}

interface CollectionState {
  expectedFields: string[];
  collectedFields: Record<string, any>;
  missingFields: string[];
  detectedIntent: IntentType | null;
  confidenceScore: number;
}

// Catálogo de intents baseado no arquivo compartilhado
export const INTENTS_CATALOG: Intent[] = [
  {
    name: "cadastrar_cavalo",
    phrases: [
      "quero cadastrar um cavalo novo",
      "preciso registrar mais um cavalo",
      "adicionar cavalo chamado",
      "cadastra a égua",
      "botar mais um cavalo no sistema",
      "tenho um potro pra cadastrar"
    ]
  },
  {
    name: "editar_cavalo",
    phrases: [
      "quero editar o cavalo",
      "preciso corrigir os dados do",
      "alterar informações do cavalo"
    ]
  },
  {
    name: "visualizar_ficha",
    phrases: [
      "ver ficha do cavalo",
      "mostrar dados do",
      "consultar detalhes do cavalo"
    ]
  },
  {
    name: "excluir_cavalo",
    phrases: [
      "excluir o cavalo",
      "quero apagar a ficha do",
      "remover cavalo"
    ]
  },
  {
    name: "registrar_peso",
    phrases: [
      "peso do",
      "anotar que o",
      "pesei o cavalo"
    ]
  },
  {
    name: "registrar_entrada_saida",
    phrases: [
      "registrar entrada do cavalo",
      "saiu ontem",
      "registrar saída da"
    ]
  },
  {
    name: "registrar_genealogia",
    phrases: [
      "pai do cavalo",
      "registrar mãe da",
      "avô paterno do"
    ]
  },
  {
    name: "enviar_arquivo",
    phrases: [
      "quero mandar uma foto do cavalo",
      "subir exame do",
      "anexar pdf da"
    ]
  },
  {
    name: "registrar_vacina",
    phrases: [
      "vacinar o cavalo",
      "botar vacina no",
      "registrar vacinação da"
    ]
  },
  {
    name: "registrar_treino",
    phrases: [
      "anotar treino do",
      "treino da",
      "registrar que o"
    ]
  },
  {
    name: "registrar_procedimento_vet",
    phrases: [
      "exame do",
      "anotar cirurgia da",
      "registrar recomendação médica da"
    ]
  },
  {
    name: "controle_reproducao",
    phrases: [
      "cobrir a égua",
      "pariu hoje",
      "inseminar a",
      "transferir embrião da"
    ]
  },
  {
    name: "registrar_alimentacao",
    phrases: [
      "comida do",
      "alimentar",
      "alimentação da"
    ]
  },
  {
    name: "agendar_evento",
    phrases: [
      "marcar ferrageamento do",
      "consulta veterinária do",
      "treino da"
    ]
  },
  {
    name: "consultar_agenda",
    phrases: [
      "o que tenho hoje",
      "mostrar tarefas da",
      "tem alguma vacina pra fazer"
    ]
  }
];

// Mapeamento de campos necessários para cada intent
const INTENT_REQUIRED_FIELDS: Record<IntentType, string[]> = {
  "cadastrar_cavalo": ["nome", "raca"],
  "editar_cavalo": ["nome", "campo_a_editar", "novo_valor"],
  "visualizar_ficha": ["nome"],
  "excluir_cavalo": ["nome", "confirmacao"],
  "registrar_peso": ["nome", "peso"],
  "registrar_entrada_saida": ["nome", "tipo", "data"],
  "registrar_genealogia": ["nome", "tipo_relacao", "parente"],
  "enviar_arquivo": ["nome", "tipo_arquivo"],
  "registrar_vacina": ["nome", "tipo_vacina", "data"],
  "registrar_treino": ["nome", "tipo_treino", "duracao"],
  "registrar_procedimento_vet": ["nome", "tipo_procedimento", "resultado"],
  "controle_reproducao": ["nome", "tipo_evento", "detalhes"],
  "registrar_alimentacao": ["nome", "tipo_alimento", "quantidade"],
  "agendar_evento": ["nome", "tipo_evento", "data", "hora"],
  "consultar_agenda": ["periodo"],
  "unknown": ["mensagem"]
};

// Extrator de entidades nomeadas simplificado
const extractEntityFromText = (text: string, entityType: string): string | null => {
  const lowerText = text.toLowerCase();
  
  // Regex simples para datas no formato dd/mm/yyyy ou menções a dias da semana
  if (entityType === "data") {
    const dateRegex = /\b\d{1,2}\/\d{1,2}\/\d{2,4}\b/;
    const dateMatch = text.match(dateRegex);
    if (dateMatch) return dateMatch[0];
    
    const weekdayRegex = /\b(hoje|amanhã|segunda|terça|quarta|quinta|sexta|sábado|domingo)\b/i;
    const weekdayMatch = text.match(weekdayRegex);
    if (weekdayMatch) return weekdayMatch[0];
  }
  
  // Regex para horários no formato hh:mm ou menções como "15h"
  if (entityType === "hora") {
    const timeRegex = /\b(\d{1,2}:\d{2}|\d{1,2}h)\b/i;
    const timeMatch = text.match(timeRegex);
    if (timeMatch) return timeMatch[0];
  }
  
  // Regex para peso no formato "NNNkg" ou "NNN quilos"
  if (entityType === "peso") {
    const weightRegex = /\b(\d+)\s*(kg|quilos?)\b/i;
    const weightMatch = text.match(weightRegex);
    if (weightMatch) return weightMatch[1];
  }
  
  // Para detecção de nomes de cavalos (isso é mais complexo na prática)
  if (entityType === "nome") {
    // Padrões comuns para identificar nomes de cavalos na frase
    const patterns = [
      /(?:cavalo|égua|potro|potra)\s+([A-Z][a-z]+)/i,  // "cavalo Trovão"
      /\b(do|da|no|na)\s+([A-Z][a-z]+)/i,              // "do Trovão" 
      /\bo\s+([A-Z][a-z]+)/i,                          // "o Trovão"
      /\ba\s+([A-Z][a-z]+)/i                           // "a Princesa"
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match.length > 1) {
        return match[match.length - 1];  // Último grupo capturado
      }
    }
  }
  
  return null;
};

/**
 * Hook para coletar e entender o contexto natural do chat,
 * transformando em intenções estruturadas
 */
export function useChatCollector() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [userInput, setUserInput] = useState<string>("");
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [pendingMessages, setPendingMessages] = useState<Message[]>([]);
  
  // Estado para controle da coleta de informações
  const [collectionState, setCollectionState] = useState<CollectionState>({
    expectedFields: [],
    collectedFields: {},
    missingFields: [],
    detectedIntent: null,
    confidenceScore: 0
  });
  
  // Detectar estado de conexão
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Processar mensagens pendentes quando voltar online
  useEffect(() => {
    if (isOnline && pendingMessages.length > 0) {
      // Aqui implementaríamos o envio das mensagens pendentes para o servidor
      setPendingMessages([]);
    }
  }, [isOnline, pendingMessages]);


  
  // Detectar intenção do usuário combinando regex e catálogo de intenções
  const detectIntent = useCallback((text: string): { intent: IntentType, confidence: number } => {
    const lowerText = text.toLowerCase();
    let bestMatch: IntentType = "unknown";
    let highestScore = 0;
    
    // Método 1: Detecção por catálogo de frases comuns
    for (const intent of INTENTS_CATALOG) {
      for (const phrase of intent.phrases) {
        if (lowerText.includes(phrase.toLowerCase())) {
          // Pontuação baseada no comprimento da frase correspondente
          const score = phrase.length / text.length;
          if (score > highestScore) {
            highestScore = score;
            bestMatch = intent.name;
          }
        }
      }
    }

    // Método 2: Detecção por expressões regulares para casos comuns
    // Este método pode detectar padrões mesmo quando as frases exatas não estão no catálogo
    if (bestMatch === "unknown" || highestScore < 0.3) {
      const regexPatterns: Record<IntentType, RegExp> = {
        "cadastrar_cavalo": /cadastrar|registrar|novo\s+cavalo|nova\s+égua|potro|potra/i,
        "editar_cavalo": /editar|alterar|mudar|atualizar\s+(dados|informações)/i,
        "visualizar_ficha": /ver|mostrar|exibir|ficha|detalhes|informações/i,
        "excluir_cavalo": /excluir|apagar|remover|deletar/i,
        "registrar_peso": /peso|pesar|kg|quilos|pesagem/i,
        "registrar_entrada_saida": /entrada|chegada|saída|saiu|entrou/i,
        "registrar_genealogia": /genealogia|pai|mãe|avô|avó|parentesco/i,
        "enviar_arquivo": /arquivo|upload|documento|foto|imagem|pdf/i,
        "registrar_vacina": /vacina|vacinação|injeção|imunização/i,
        "registrar_treino": /treino|treinar|exercitar|exercício/i,
        "registrar_procedimento_vet": /procedimento|veterinário|exame|cirurgia/i,
        "controle_reproducao": /reprodução|cobertura|inseminação|embrião|pariu/i,
        "registrar_alimentacao": /alimentação|comida|ração|alimento|feno/i,
        "agendar_evento": /agendar|marcar|programar|calendário/i,
        "consultar_agenda": /agenda|compromissos|tarefas|pendências/i,
        "unknown": /./i
      };

      for (const [intentType, pattern] of Object.entries(regexPatterns)) {
        if (intentType === "unknown") continue;
        
        if (pattern.test(lowerText)) {
          // Calculamos uma pontuação baseada na correspondência da regex
          const regexScore = 0.4; // Uma pontuação intermediária
          
          if (regexScore > highestScore) {
            highestScore = regexScore;
            bestMatch = intentType as IntentType;
          }
        }
      }
    }
    
    return { intent: bestMatch, confidence: highestScore };
  }, []);
  
  // Coletar campos para completar a intenção
  const collectEntityFields = useCallback((text: string, intent: IntentType): Record<string, any> => {
    const fields: Record<string, any> = {};
    const requiredFields = INTENT_REQUIRED_FIELDS[intent] || [];
    
    for (const field of requiredFields) {
      const entity = extractEntityFromText(text, field);
      if (entity) {
        fields[field] = entity;
      }
    }
    
    return fields;
  }, []);
  
  // Processar a mensagem do usuário com o contexto acumulado
  const processWithContext = useCallback((newText: string) => {
    // Combinar o texto novo com o contexto prévio de todas as mensagens
    const fullContext = messages
      .filter(m => m.role === "user")
      .map(m => m.content)
      .join(" ") + " " + newText;
    
    // Detectar a intenção mais provável
    const { intent, confidence } = detectIntent(fullContext);
    
    // Se já temos uma intenção detectada, mantemos ela
    const currentIntent = collectionState.detectedIntent || intent;
    
    // Coletar os campos disponíveis
    const newFields = collectEntityFields(fullContext, currentIntent);
    
    // Combinar com campos já coletados
    const allCollectedFields = {
      ...collectionState.collectedFields,
      ...newFields
    };
    
    // Calcular campos em falta
    const expectedFields = INTENT_REQUIRED_FIELDS[currentIntent] || [];
    const missingFields = expectedFields.filter(field => !allCollectedFields[field]);
    
    // Atualizar o estado da coleta
    setCollectionState({
      expectedFields,
      collectedFields: allCollectedFields,
      missingFields,
      detectedIntent: currentIntent,
      confidenceScore: Math.max(collectionState.confidenceScore, confidence)
    });
    
    return {
      intent: currentIntent,
      confidence,
      collected: allCollectedFields,
      missing: missingFields
    };
  }, [messages, collectionState, detectIntent, collectEntityFields]);
  
  // Adicionar nova mensagem do usuário
  const handleUserMessage = useCallback((text: string) => {
    try {
      console.log("Processando mensagem do usuário:", text);
      
      // Criar mensagem do usuário
      const newUserMessage: Message = {
        id: uuidv4(),
        role: "user",
        content: text
      };
      
      // Adicionar mensagem do usuário ao histórico
      setMessages(prevMessages => [...prevMessages, newUserMessage]);
      
      // Use setTimeout para garantir que a UI seja atualizada antes de continuar
      setTimeout(() => {
        try {
          // Processar com contexto acumulado
          const result = processWithContext(text);
          console.log("Resultado do processamento:", result);
          
          // Preparar resposta do assistente
          let assistantMessage: Message;
          
          // Se há campos faltando, perguntar ao usuário
          if (result.missing.length > 0) {
            // Gerar perguntas para os campos em falta
            const nextField = result.missing[0];
            let question = "";
            
            switch (nextField) {
              case "nome":
                question = "Qual é o nome do cavalo?";
                break;
              case "data":
                question = "Para qual data isso deve ser registrado?";
                break;
              case "peso":
                question = "Qual é o peso do cavalo?";
                break;
              default:
                question = `Por favor, informe ${nextField.replace('_', ' ')}`;
            }
            
            assistantMessage = {
              id: uuidv4(),
              role: "assistant",
              content: question
            };
          } else if (result.intent !== "unknown" && result.missing.length === 0) {
            // Temos todos os dados necessários, podemos executar a ação!
            assistantMessage = {
              id: uuidv4(),
              role: "assistant",
              content: `✅ Vou ${result.intent.replace('_', ' ')} com os detalhes informados.`
            };
            
            if (!isOnline) {
              assistantMessage.content = "📌 Mensagem anotada. Vou registrar no sistema assim que o sinal voltar.";
              setPendingMessages(prev => [...prev, newUserMessage]);
            }
            
            // Limpar estado de coleta após ação completa
            setCollectionState({
              expectedFields: [],
              collectedFields: {},
              missingFields: [],
              detectedIntent: null,
              confidenceScore: 0
            });
          } else {
            // Intent desconhecida
            assistantMessage = {
              id: uuidv4(),
              role: "assistant",
              content: "Desculpe, não entendi bem o que você quer fazer. Pode explicar melhor, usando o nome do cavalo e o que deseja fazer?"
            };
          }
          
          // Adicionar mensagem do assistente ao histórico
          setMessages(prevMessages => [...prevMessages, assistantMessage]);
          console.log("Mensagem do assistente adicionada:", assistantMessage.content);
          
        } catch (error) {
          console.error("Erro ao processar mensagem:", error);
          // Adicionar mensagem de erro do assistente
          const errorMessage: Message = {
            id: uuidv4(),
            role: "assistant",
            content: "Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente."
          };
          
          setMessages(prevMessages => [...prevMessages, errorMessage]);
        }
      }, 10); // Pequeno atraso para garantir que a UI atualize
      
    } catch (error) {
      console.error("Erro crítico no handleUserMessage:", error);
    }
  }, [processWithContext, isOnline]);



  // Exportar resultado para API
  const exportToAI = useCallback(() => {
    const userMessages = messages
      .filter(m => m.role === "user")
      .map(m => m.content)
      .join("\n");
      
    return {
      messages,
      intent: collectionState.detectedIntent,
      fields: collectionState.collectedFields,
      text: userMessages
    };
  }, [messages, collectionState]);

  // Limpar histórico de mensagens
  const clearBuffer = useCallback(() => {
    setMessages([]);
    setCollectionState({
      expectedFields: [],
      collectedFields: {},
      missingFields: [],
      detectedIntent: null,
      confidenceScore: 0
    });
  }, []);

  // Aqui podem ser adicionadas funções adicionais para manipulação de mensagens conforme necessário

  // Salvar mensagens no armazenamento local quando elas mudarem
  const localStore = createLocalStore();
  useEffect(() => {
    if (messages.length > 0) {
      localStore.saveConversation(messages);
    }
  }, [messages]);
  
  // Carregar mensagens do armazenamento local na inicialização
  useEffect(() => {
    const loadSavedMessages = async () => {
      const savedMessages = await localStore.loadConversation();
      if (savedMessages.length > 0) {
        setMessages(savedMessages);
        
        // Reconstruir o estado de coleta a partir das mensagens salvas
        const userContext = savedMessages
          .filter(m => m.role === 'user')
          .map(m => m.content)
          .join(' ');
          
        processWithContext(userContext);
      }
    };
    
    loadSavedMessages();
  }, [processWithContext]);

  return {
    messages,
    userInput,
    setUserInput,
    isOnline,
    handleUserMessage,
    exportToAI,
    clearBuffer,
    collectionState
  };
}