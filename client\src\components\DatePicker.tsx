import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ptBR } from 'date-fns/locale';
import { Calendar as CalendarIcon } from "lucide-react";

export interface DatePickerProps {
  date: Date | undefined;
  setDate?: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  buttonClassName?: string;
  disabled?: boolean;
  
  // Aliases para compatibilidade com código antigo
  onDateChange?: (date: Date | undefined) => void;
}

export function DatePicker({
  date,
  setDate,
  onDateChange,
  placeholder = "Selecionar data",
  className,
  buttonClassName,
  disabled = false
}: DatePickerProps) {
  // Usar onDateChange como fallback se setDate não for fornecido
  const handleDateChange = (newDate: Date | undefined) => {
    if (setDate) {
      setDate(newDate);
    } else if (onDateChange) {
      onDateChange(newDate);
    }
  };
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Ajustar largura do popover para corresponder à largura do botão
  useEffect(() => {
    if (open && buttonRef.current) {
      const buttonWidth = buttonRef.current.getBoundingClientRect().width;
      document.documentElement.style.setProperty('--date-picker-width', `${buttonWidth}px`);
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={buttonRef}
          variant="outline"
          size="sm"
          className={cn(
            "w-full text-left font-normal justify-start",
            !date && "text-muted-foreground",
            className,
            buttonClassName
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "dd/MM/yyyy", { locale: ptBR }) : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="p-0" 
        style={{ width: 'var(--date-picker-width)' }}
        align="start"
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateChange}
          locale={ptBR}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

export default DatePicker;