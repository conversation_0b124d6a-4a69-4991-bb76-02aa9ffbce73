/**
 * Database Query Helper - EquiGestor AI
 * Centralized database query functionality with proper connection handling
 */

import { dbHand<PERSON> } from './db-connection-handler';

export class DatabaseQueryHelper {
  
  static async executeQuery<T = any>(
    query: string, 
    params: any[] = []
  ): Promise<{ rows: T[]; rowCount: number }> {
    
    if (!dbHandler.isConnected()) {
      console.warn('[DatabaseQueryHelper] Database not connected, returning empty result');
      return { rows: [], rowCount: 0 };
    }

    try {
      const pool = dbHandler.getPool();
      const result = await pool.query(query, params);
      
      return {
        rows: result.rows || [],
        rowCount: result.rowCount || 0
      };
    } catch (error) {
      console.error('[DatabaseQueryHelper] Query execution error:', error);
      return { rows: [], rowCount: 0 };
    }
  }

  static async executeQuerySingle<T = any>(
    query: string, 
    params: any[] = []
  ): Promise<T | null> {
    
    const result = await this.executeQuery<T>(query, params);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  static isConnected(): boolean {
    return dbHandler.isConnected();
  }

  static getConnectionStatus(): { connected: boolean; error?: string } {
    try {
      const connected = dbHandler.isConnected();
      return { connected };
    } catch (error) {
      return { 
        connected: false, 
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export for backward compatibility
export const dbQuery = DatabaseQueryHelper;