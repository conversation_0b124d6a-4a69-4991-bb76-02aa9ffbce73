import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { GitBranchPlus, Heart, User, TreePine } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import { Cavalo } from '@shared/schema';
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";

interface EnhancedGenealogyViewerProps {
  cavaloId: number;
}

interface HorseNode {
  id: number | null;
  name: string;
  tipo: 'sistema' | 'externo' | 'nenhum';
  sexo?: string | null;
  breed?: string | null;
  cor?: string | null;
  birth_date?: string | null;
}

export function EnhancedGenealogyViewer({ cavaloId }: EnhancedGenealogyViewerProps) {
  const [cavalo, setCavalo] = useState<Cavalo | null>(null);
  const [pai, setPai] = useState<Cavalo | null>(null);
  const [mae, setMae] = useState<Cavalo | null>(null);
  const [loading, setLoading] = useState(true);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);

  // Função para obter informações do pai
  const getPaiInfo = (cavalo: Cavalo | null): HorseNode => {
    if (!cavalo) return { id: null, nome: 'Não informado', tipo: 'nenhum' };
    
    if (cavalo.pai_id) {
      return { 
        id: cavalo.pai_id,
        name: pai?.name || 'Carregando...', 
        tipo: 'sistema',
        sexo: pai?.sexo,
        breed: pai?.breed,
        cor: pai?.cor,
        dataNascimento: pai?.dataNascimento
      };
    }
    
    if (cavalo.pai_nome) {
      return { 
        id: null,
        name: cavalo.pai_nome, 
        tipo: 'externo'
      };
    }
    
    if (cavalo.pai) {
      const isNumeric = /^\d+$/.test(cavalo.pai.toString());
      if (isNumeric) {
        return { 
          id: Number(cavalo.pai),
          name: pai?.name || 'Carregando...', 
          tipo: 'sistema',
          sexo: pai?.sexo,
          breed: pai?.breed,
          cor: pai?.cor,
          dataNascimento: pai?.dataNascimento
        };
      } else {
        return { 
          id: null,
          name: cavalo.pai.toString(), 
          tipo: 'externo'
        };
      }
    }
    
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações da mãe
  const getMaeInfo = (cavalo: Cavalo | null): HorseNode => {
    if (!cavalo) return { id: null, name: 'Não informado', tipo: 'nenhum' };
    
    if (cavalo.mae_id) {
      return { 
        id: cavalo.mae_id,
        name: mae?.name || 'Carregando...', 
        tipo: 'sistema',
        sexo: mae?.sexo,
        breed: mae?.breed,
        cor: mae?.cor,
        dataNascimento: mae?.dataNascimento
      };
    }
    
    if (cavalo.mae_nome) {
      return { 
        id: null,
        name: cavalo.mae_nome, 
        tipo: 'externo'
      };
    }
    
    if (cavalo.mae) {
      const isNumeric = /^\d+$/.test(cavalo.mae.toString());
      if (isNumeric) {
        return { 
          id: Number(cavalo.mae),
          name: mae?.name || 'Carregando...', 
          tipo: 'sistema',
          sexo: mae?.sexo,
          breed: mae?.breed,
          cor: mae?.cor,
          dataNascimento: mae?.dataNascimento
        };
      } else {
        return { 
          id: null,
          name: cavalo.mae.toString(), 
          tipo: 'externo'
        };
      }
    }
    
    return { id: null, name: 'Não informado', tipo: 'nenhum' };
  };

  // Carregar dados
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const userJson = localStorage.getItem('user');
        const user_id = userJson ? JSON.parse(userJson).id : null;
        
        if (!user_id) {
          console.error('❌ [EnhancedGenealogyViewer] Usuário não encontrado no localStorage');
          setLoading(false);
          return;
        }
        
        const options = {
          headers: {
            'user-id': user_id.toString(),
            'Content-Type': 'application/json'
          }
        };
        
        // Buscar cavalo principal
        const cavaloData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloId}`, 'GET', undefined, options);
        setCavalo(cavaloData);
        
        // Buscar pai se houver
        if (cavaloData.pai_id) {
          try {
            const paiData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloData.pai_id}`, 'GET', undefined, options);
            setPai(paiData);
          } catch (error) {
            console.error('❌ Erro ao buscar pai:', error);
          }
        } else if (cavaloData.pai && /^\d+$/.test(cavaloData.pai.toString())) {
          try {
            const paiData = await apiRequest<Cavalo>(`/api/cavalos/${Number(cavaloData.pai)}`, 'GET', undefined, options);
            setPai(paiData);
          } catch (error) {
            console.error('❌ Erro ao buscar pai (campo legado):', error);
          }
        }
        
        // Buscar mãe se houver
        if (cavaloData.mae_id) {
          try {
            const maeData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloData.mae_id}`, 'GET', undefined, options);
            setMae(maeData);
          } catch (error) {
            console.error('❌ Erro ao buscar mãe:', error);
          }
        } else if (cavaloData.mae && /^\d+$/.test(cavaloData.mae.toString())) {
          try {
            const maeData = await apiRequest<Cavalo>(`/api/cavalos/${Number(cavaloData.mae)}`, 'GET', undefined, options);
            setMae(maeData);
          } catch (error) {
            console.error('❌ Erro ao buscar mãe (campo legado):', error);
          }
        }
      } catch (error) {
        console.error('❌ Erro ao buscar dados:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [cavaloId]);

  const paiInfo = getPaiInfo(cavalo);
  const maeInfo = getMaeInfo(cavalo);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  const HorseCard = ({ horse, position, isMain = false }: { 
    horse: HorseNode & { name: string }, 
    position: string, 
    isMain?: boolean 
  }) => {
    const isHovered = hoveredNode === position;
    
    return (
      <div 
        className={`
          relative transition-all duration-300 transform hover:scale-105 hover:z-10
          ${isHovered ? 'scale-110 z-20' : ''}
          ${isMain ? 'min-w-[250px]' : 'min-w-[220px]'}
        `}
        onMouseEnter={() => setHoveredNode(position)}
        onMouseLeave={() => setHoveredNode(null)}
      >
        <Card className={`
          overflow-hidden transition-all duration-300 shadow-lg
          ${isMain 
            ? 'border-2 border-primary bg-gradient-to-br from-blue-50 to-indigo-50' 
            : horse.tipo === 'sistema' 
              ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' 
              : horse.tipo === 'externo'
                ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-sky-50'
                : 'border-gray-200 bg-gradient-to-br from-gray-50 to-slate-50'
          }
          ${isHovered ? 'shadow-2xl border-opacity-80' : 'shadow-md'}
        `}>
          <CardContent className="p-4 text-center">
            {/* Ícone e tipo */}
            <div className="flex justify-center mb-2">
              {horse.tipo === 'sistema' ? (
                <div className="p-2 bg-green-100 rounded-full">
                  <User className="h-5 w-5 text-green-600" />
                </div>
              ) : horse.tipo === 'externo' ? (
                <div className="p-2 bg-blue-100 rounded-full">
                  <GitBranchPlus className="h-5 w-5 text-blue-600" />
                </div>
              ) : (
                <div className="p-2 bg-gray-100 rounded-full">
                  <Heart className="h-5 w-5 text-gray-400" />
                </div>
              )}
            </div>

            {/* Nome do cavalo */}
            <div className={`font-bold mb-2 ${isMain ? 'text-lg' : 'text-base'}`}>
              {horse.name}
            </div>

            {/* Badge de tipo */}
            <div className="mb-3">
              <Badge 
                variant={
                  horse.tipo === 'sistema' ? 'default' : 
                  horse.tipo === 'externo' ? 'secondary' : 
                  'outline'
                }
                className="text-xs"
              >
                {horse.tipo === 'sistema' ? `ID: ${horse.id}` : 
                 horse.tipo === 'externo' ? 'Externo' : 
                 'Não Informado'}
              </Badge>
            </div>

            {/* Separador */}
            <Separator className="my-3" />

            {/* Informações adicionais */}
            <div className="space-y-1 text-xs text-gray-600">
              {horse.breed && (
                <div><span className="font-medium">Raça:</span> {horse.breed}</div>
              )}
              {horse.sexo && (
                <div><span className="font-medium">Sexo:</span> {horse.sexo === 'M' ? 'macho' : 'femea'}</div>
              )}
              {horse.cor && (
                <div><span className="font-medium">Cor:</span> {horse.cor}</div>
              )}
              {horse.dataNascimento && (
                <div><span className="font-medium">Nascimento:</span> {new Date(horse.dataNascimento).toLocaleDateString()}</div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-6">
        <TreePine className="h-6 w-6 text-green-600" />
        <h2 className="text-2xl font-bold text-gray-800">Árvore Genealógica Interativa</h2>
        <Badge variant="secondary" className="ml-2">
          Com Zoom & Pan
        </Badge>
      </div>

      <TransformWrapper
        initialScale={1}
        minScale={0.5}
        maxScale={3}
        centerOnInit={true}
        wheel={{ step: 0.1 }}
        pinch={{ step: 5 }}
        doubleClick={{ mode: "reset" }}
      >
        {({ zoomIn, zoomOut, resetTransform }) => (
          <>
            {/* Controles de zoom */}
            <div className="flex gap-2 mb-4">
              <button
                onClick={() => zoomIn()}
                className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Zoom +
              </button>
              <button
                onClick={() => zoomOut()}
                className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Zoom -
              </button>
              <button
                onClick={() => resetTransform()}
                className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Reset
              </button>
            </div>

            <TransformComponent>
              <div className="p-8 min-h-[600px]">
                {/* SVG para linhas conectoras */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
                  {/* Linha do cavalo principal para os pais */}
                  <line
                    x1="50%"
                    y1="200"
                    x2="50%"
                    y2="300"
                    stroke="#cbd5e1"
                    strokeWidth="2"
                    className="animate-pulse"
                  />
                  
                  {/* Linha horizontal conectando os pais */}
                  <line
                    x1="25%"
                    y1="400"
                    x2="75%"
                    y2="400"
                    stroke="#cbd5e1"
                    strokeWidth="2"
                    className="animate-pulse"
                  />
                  
                  {/* Linhas dos pais para a linha horizontal */}
                  <line
                    x1="25%"
                    y1="300"
                    x2="25%"
                    y2="400"
                    stroke="#cbd5e1"
                    strokeWidth="2"
                    className="animate-pulse"
                  />
                  <line
                    x1="75%"
                    y1="300"
                    x2="75%"
                    y2="400"
                    stroke="#cbd5e1"
                    strokeWidth="2"
                    className="animate-pulse"
                  />
                </svg>

                {/* Layout da árvore */}
                <div className="relative" style={{ zIndex: 2 }}>
                  {/* Cavalo principal */}
                  <div className="flex justify-center mb-16">
                    <HorseCard 
                      horse={{
                        id: cavalo?.id || null,
                        name: cavalo?.name || 'Carregando...',
                        tipo: 'sistema',
                        sexo: cavalo?.sexo,
                        breed: cavalo?.breed,
                        cor: cavalo?.cor,
                        dataNascimento: cavalo?.dataNascimento
                      }} 
                      position="main" 
                      isMain={true}
                    />
                  </div>

                  {/* Pais */}
                  <div className="flex justify-between items-start px-8">
                    {/* Pai */}
                    <div className="flex flex-col items-center">
                      <div className="text-sm font-medium text-gray-500 mb-3">Pai</div>
                      <HorseCard horse={paiInfo} position="pai" />
                    </div>

                    {/* Mãe */}
                    <div className="flex flex-col items-center">
                      <div className="text-sm font-medium text-gray-500 mb-3">Mãe</div>
                      <HorseCard horse={maeInfo} position="mae" />
                    </div>
                  </div>
                </div>
              </div>
            </TransformComponent>
          </>
        )}
      </TransformWrapper>
    </div>
  );
}