/**
 * Utilitários para depuração do EquiGestor
 * 
 * Este módulo fornece funções auxiliares para depuração e diagnóstico
 * de problemas no sistema. Ele complementa o sistema de logging principal.
 */

import fs from 'fs';
import path from 'path';
import { getModuleLogger } from './logger';

const debugLogger = getModuleLogger('debug');

/**
 * Interface para objetos que podem ser inspecionados em profundidade
 */
export interface Inspectable {
  [key: string]: any;
}

/**
 * Gera um snapshot de um objeto para depuração
 * @param data Objeto a ser salvo
 * @param name Nome identificador do snapshot
 */
export function saveDebugSnapshot(data: Inspectable, name: string): string {
  try {
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const filename = `debug_${name}_${timestamp}.json`;
    const fullPath = path.join('logs', filename);
    
    // Certifique-se de que o diretório de logs existe
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }
    
    // Salva o objeto no arquivo
    fs.writeFileSync(
      fullPath, 
      JSON.stringify(data, null, 2)
    );
    
    debugLogger.info(`Snapshot de depuração salvo: ${filename}`, { 
      path: fullPath, 
      size: Buffer.byteLength(JSON.stringify(data))
    });
    
    return fullPath;
  } catch (error) {
    debugLogger.error(`Erro ao salvar snapshot de depuração`, { 
      name, 
      error: error instanceof Error ? error.message : String(error)
    });
    return '';
  }
}

/**
 * Rastreador de solicitações para depuração de problemas específicos
 */
export class RequestTracker {
  private logs: any[] = [];
  private name: string;
  private maxEvents: number;
  
  /**
   * Cria um novo rastreador de solicitações
   * @param name Nome do rastreador (ex: 'atualizacao-cavalo')
   * @param maxEvents Número máximo de eventos a serem armazenados (opcional)
   */
  constructor(name: string, maxEvents: number = 100) {
    this.name = name;
    this.maxEvents = maxEvents;
    debugLogger.info(`Rastreador '${name}' inicializado com capacidade para ${maxEvents} eventos`);
  }
  
  /**
   * Adiciona um evento ao rastreador
   * @param event Nome do evento
   * @param data Dados associados ao evento
   */
  public trackEvent(event: string, data: any = {}): void {
    const timestamp = new Date().toISOString();
    
    this.logs.push({
      timestamp,
      event,
      data
    });
    
    // Limita o tamanho do log
    if (this.logs.length > this.maxEvents) {
      this.logs.shift();
    }
  }
  
  /**
   * Salva o conteúdo atual do rastreador em um arquivo
   * @param comment Comentário opcional sobre o motivo do registro
   * @returns Caminho do arquivo salvo
   */
  public saveTrace(comment: string = ''): string {
    const data = {
      tracker: this.name,
      events: this.logs.length,
      comment,
      savedAt: new Date().toISOString(),
      logs: this.logs
    };
    
    return saveDebugSnapshot(data, `trace_${this.name}`);
  }
  
  /**
   * Limpa os logs atuais do rastreador
   */
  public clear(): void {
    this.logs = [];
    debugLogger.info(`Rastreador '${this.name}' foi limpo`);
  }
}

/**
 * Cria um relatório de diagnóstico do sistema
 * Útil para depurar problemas complexos
 */
export function generateSystemDiagnostic(): string {
  const diagnostic: Inspectable = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    memoryUsage: process.memoryUsage(),
    uptime: process.uptime(),
    cpuUsage: process.cpuUsage(),
    resourceUsage: process.resourceUsage(),
  };
  
  return saveDebugSnapshot(diagnostic, 'system_diagnostic');
}

/**
 * Configuração para registro de exceções não tratadas
 */
export function setupUncaughtExceptionHandler(): void {
  process.on('uncaughtException', (error) => {
    debugLogger.error('Exceção não tratada:', { 
      error: error.message,
      stack: error.stack,
      name: error.name
    });
    
    // Salva um diagnóstico do sistema no momento do erro
    generateSystemDiagnostic();
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    debugLogger.error('Rejeição de promessa não tratada:', { 
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined
    });
  });
  
  debugLogger.info('Handlers para exceções não tratadas configurados');
}

export default {
  saveDebugSnapshot,
  RequestTracker,
  generateSystemDiagnostic,
  setupUncaughtExceptionHandler
};