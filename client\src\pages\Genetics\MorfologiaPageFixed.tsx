import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useGeneticsContext } from "@/contexts/GeneticsContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Loader2, 
  BarChart3, 
  PlusCircle, 
  Save,
  TrendingUp,
  Target,
  Award,
  Activity,
  Calendar,
  User
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON>Grid, <PERSON><PERSON>ngle<PERSON><PERSON>s, PolarRadiusAxis, Radar } from 'recharts';
import { apiRequest } from '@/lib/queryClient';

interface MorfologiaData {
  id: number;
  horse_id: number;
  dataMedicao: string;
  alturaCernelha: number;
  alturaDorso: number;
  alturaGarupa: number;
  comprimentoCorpo: number;
  comprimentoPescoco: number;
  larguraPeito: number;
  perimetroToracico: number;
  perimetroPescoco: number;
  perimetroCanela: number;
  pontuacaoCabeca: number;
  pontuacaoPescoco: number;
  pontuacaoEspalda: number;
  pontuacaoDorso: number;
  pontuacaoGarupa: number;
  pontuacaoMembros: number;
  pontuacaoAprumos: number;
  pontuacaoAndamento: number;
  pontuacaoHarmonia: number;
  pontuacaoTotal: number;
  observacoes: string;
  responsavelMedicao: string;
}

export default function MorfologiaPageFixed() {
  const { selectedHorseId } = useGeneticsContext();
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    horse_id: 0,
    dataMedicao: new Date().toISOString().split('T')[0],
    alturaCernelha: '',
    alturaDorso: '',
    alturaGarupa: '',
    comprimentoCorpo: '',
    comprimentoPescoco: '',
    larguraPeito: '',
    perimetroToracico: '',
    perimetroPescoco: '',
    perimetroCanela: '',
    pontuacaoCabeca: '',
    pontuacaoPescoco: '',
    pontuacaoEspalda: '',
    pontuacaoDorso: '',
    pontuacaoGarupa: '',
    pontuacaoMembros: '',
    pontuacaoAprumos: '',
    pontuacaoAndamento: '',
    pontuacaoHarmonia: '',
    observacoes: '',
    responsavelMedicao: ''
  });

  const { toast } = useToast();

  // Buscar cavalos
  const { data: cavalos, isLoading: isLoadingCavalos } = useQuery<any[]>({
    queryKey: ['/api/cavalos'],
  });

  // Buscar dados de morfologia
  const { data: morfologiaData, isLoading: isLoadingMorfologia, refetch } = useQuery<MorfologiaData[]>({
    queryKey: ['/api/morfologia'],
  });

  const selectedHorse = cavalos?.find(cavalo => cavalo.id === selectedHorseId);
  const horseMorfologia = morfologiaData?.filter(m => m.horse_id === selectedHorseId) || [];

  // Calcular pontuação total automaticamente
  const calculateTotal = () => {
    const scores = [
      parseFloat(formData.pontuacaoCabeca) || 0,
      parseFloat(formData.pontuacaoPescoco) || 0,
      parseFloat(formData.pontuacaoEspalda) || 0,
      parseFloat(formData.pontuacaoDorso) || 0,
      parseFloat(formData.pontuacaoGarupa) || 0,
      parseFloat(formData.pontuacaoMembros) || 0,
      parseFloat(formData.pontuacaoAprumos) || 0,
      parseFloat(formData.pontuacaoAndamento) || 0,
      parseFloat(formData.pontuacaoHarmonia) || 0,
    ];
    
    const validScores = scores.filter(score => score > 0);
    if (validScores.length === 0) return 0;
    
    return (validScores.reduce((sum, score) => sum + score, 0) / validScores.length).toFixed(1);
  };

  // Dados para gráfico radar
  const getRadarData = (avaliacao: MorfologiaData) => [
    { categoria: 'Cabeça', valor: avaliacao.pontuacaoCabeca, fullMark: 10 },
    { categoria: 'Pescoço', valor: avaliacao.pontuacaoPescoco, fullMark: 10 },
    { categoria: 'Espalda', valor: avaliacao.pontuacaoEspalda, fullMark: 10 },
    { categoria: 'Dorso', valor: avaliacao.pontuacaoDorso, fullMark: 10 },
    { categoria: 'Garupa', valor: avaliacao.pontuacaoGarupa, fullMark: 10 },
    { categoria: 'Membros', valor: avaliacao.pontuacaoMembros, fullMark: 10 },
    { categoria: 'Aprumos', valor: avaliacao.pontuacaoAprumos, fullMark: 10 },
    { categoria: 'Andamento', valor: avaliacao.pontuacaoAndamento, fullMark: 10 },
    { categoria: 'Harmonia', valor: avaliacao.pontuacaoHarmonia, fullMark: 10 },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedHorseId) {
      toast({
        title: "Erro",
        description: "Selecione um cavalo primeiro",
        variant: "destructive",
      });
      return;
    }

    try {
      const submitData = {
        ...formData,
        horse_id: selectedHorseId,
        pontuacaoTotal: parseFloat(calculateTotal()),
        alturaCernelha: parseFloat(formData.alturaCernelha),
        alturaDorso: parseFloat(formData.alturaDorso),
        alturaGarupa: parseFloat(formData.alturaGarupa),
        comprimentoCorpo: parseFloat(formData.comprimentoCorpo),
        comprimentoPescoco: parseFloat(formData.comprimentoPescoco),
        larguraPeito: parseFloat(formData.larguraPeito),
        perimetroToracico: parseFloat(formData.perimetroToracico),
        perimetroPescoco: parseFloat(formData.perimetroPescoco),
        perimetroCanela: parseFloat(formData.perimetroCanela),
        pontuacaoCabeca: parseFloat(formData.pontuacaoCabeca),
        pontuacaoPescoco: parseFloat(formData.pontuacaoPescoco),
        pontuacaoEspalda: parseFloat(formData.pontuacaoEspalda),
        pontuacaoDorso: parseFloat(formData.pontuacaoDorso),
        pontuacaoGarupa: parseFloat(formData.pontuacaoGarupa),
        pontuacaoMembros: parseFloat(formData.pontuacaoMembros),
        pontuacaoAprumos: parseFloat(formData.pontuacaoAprumos),
        pontuacaoAndamento: parseFloat(formData.pontuacaoAndamento),
        pontuacaoHarmonia: parseFloat(formData.pontuacaoHarmonia),
      };

      await apiRequest('/api/morfologia', 'POST', submitData);
      
      toast({
        title: "Sucesso!",
        description: "Avaliação morfológica salva com sucesso",
      });
      
      refetch();
      setShowForm(false);
      
      // Limpar formulário
      setFormData({
        horse_id: 0,
        dataMedicao: new Date().toISOString().split('T')[0],
        alturaCernelha: '',
        alturaDorso: '',
        alturaGarupa: '',
        comprimentoCorpo: '',
        comprimentoPescoco: '',
        larguraPeito: '',
        perimetroToracico: '',
        perimetroPescoco: '',
        perimetroCanela: '',
        pontuacaoCabeca: '',
        pontuacaoPescoco: '',
        pontuacaoEspalda: '',
        pontuacaoDorso: '',
        pontuacaoGarupa: '',
        pontuacaoMembros: '',
        pontuacaoAprumos: '',
        pontuacaoAndamento: '',
        pontuacaoHarmonia: '',
        observacoes: '',
        responsavelMedicao: ''
      });
      
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao salvar avaliação morfológica",
        variant: "destructive",
      });
    }
  };

  const ScoreCard = ({ title, score, maxScore = 10 }: {
    title: string;
    score: number;
    maxScore?: number;
  }) => (
    <div className="p-3 border rounded-lg bg-gray-50">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">{title}</span>
        <Badge variant={score >= 8 ? 'default' : score >= 6 ? 'secondary' : 'outline'}>
          {score}/{maxScore}
        </Badge>
      </div>
      <Progress value={(score / maxScore) * 100} className="h-2" />
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Cabeçalho da análise morfológica */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Análise Morfológica</CardTitle>
              <CardDescription>
                {selectedHorseId && selectedHorse 
                  ? `Avaliações morfológicas de ${selectedHorse.name}`
                  : "Selecione um cavalo no topo da página para começar"
                }
              </CardDescription>
            </div>
            <Button 
              onClick={() => setShowForm(!showForm)} 
              disabled={!selectedHorseId}
              className="flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              {showForm ? 'Cancelar' : 'Nova Avaliação'}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Formulário de nova avaliação */}
      {showForm && selectedHorse && (
        <Card>
          <CardHeader>
            <CardTitle>Nova Avaliação - {selectedHorse.name}</CardTitle>
            <CardDescription>
              Preencha todos os dados da avaliação morfológica
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Dados básicos */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dataMedicao">Data da Medição</Label>
                  <Input
                    id="dataMedicao"
                    type="date"
                    value={formData.dataMedicao}
                    onChange={(e) => setFormData({...formData, dataMedicao: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="responsavelMedicao">Responsável pela Medição</Label>
                  <Input
                    id="responsavelMedicao"
                    value={formData.responsavelMedicao}
                    onChange={(e) => setFormData({...formData, responsavelMedicao: e.target.value})}
                    placeholder="Nome do responsável"
                    required
                  />
                </div>
              </div>

              {/* Medidas */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Medidas (em metros)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="alturaCernelha">Altura da Cernelha</Label>
                    <Input
                      id="alturaCernelha"
                      type="number"
                      step="0.01"
                      value={formData.alturaCernelha}
                      onChange={(e) => setFormData({...formData, alturaCernelha: e.target.value})}
                      placeholder="1.50"
                    />
                  </div>
                  <div>
                    <Label htmlFor="alturaDorso">Altura do Dorso</Label>
                    <Input
                      id="alturaDorso"
                      type="number"
                      step="0.01"
                      value={formData.alturaDorso}
                      onChange={(e) => setFormData({...formData, alturaDorso: e.target.value})}
                      placeholder="1.40"
                    />
                  </div>
                  <div>
                    <Label htmlFor="alturaGarupa">Altura da Garupa</Label>
                    <Input
                      id="alturaGarupa"
                      type="number"
                      step="0.01"
                      value={formData.alturaGarupa}
                      onChange={(e) => setFormData({...formData, alturaGarupa: e.target.value})}
                      placeholder="1.48"
                    />
                  </div>
                  <div>
                    <Label htmlFor="comprimentoCorpo">Comprimento do Corpo</Label>
                    <Input
                      id="comprimentoCorpo"
                      type="number"
                      step="0.01"
                      value={formData.comprimentoCorpo}
                      onChange={(e) => setFormData({...formData, comprimentoCorpo: e.target.value})}
                      placeholder="1.60"
                    />
                  </div>
                  <div>
                    <Label htmlFor="comprimentoPescoco">Comprimento do Pescoço</Label>
                    <Input
                      id="comprimentoPescoco"
                      type="number"
                      step="0.01"
                      value={formData.comprimentoPescoco}
                      onChange={(e) => setFormData({...formData, comprimentoPescoco: e.target.value})}
                      placeholder="0.70"
                    />
                  </div>
                  <div>
                    <Label htmlFor="larguraPeito">Largura do Peito</Label>
                    <Input
                      id="larguraPeito"
                      type="number"
                      step="0.01"
                      value={formData.larguraPeito}
                      onChange={(e) => setFormData({...formData, larguraPeito: e.target.value})}
                      placeholder="0.45"
                    />
                  </div>
                  <div>
                    <Label htmlFor="perimetroToracico">Perímetro Torácico</Label>
                    <Input
                      id="perimetroToracico"
                      type="number"
                      step="0.01"
                      value={formData.perimetroToracico}
                      onChange={(e) => setFormData({...formData, perimetroToracico: e.target.value})}
                      placeholder="1.75"
                    />
                  </div>
                  <div>
                    <Label htmlFor="perimetroPescoco">Perímetro do Pescoço</Label>
                    <Input
                      id="perimetroPescoco"
                      type="number"
                      step="0.01"
                      value={formData.perimetroPescoco}
                      onChange={(e) => setFormData({...formData, perimetroPescoco: e.target.value})}
                      placeholder="0.85"
                    />
                  </div>
                  <div>
                    <Label htmlFor="perimetroCanela">Perímetro da Canela</Label>
                    <Input
                      id="perimetroCanela"
                      type="number"
                      step="0.01"
                      value={formData.perimetroCanela}
                      onChange={(e) => setFormData({...formData, perimetroCanela: e.target.value})}
                      placeholder="0.20"
                    />
                  </div>
                </div>
              </div>

              {/* Pontuações */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Pontuações (1-10)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="pontuacaoCabeca">Cabeça</Label>
                    <Input
                      id="pontuacaoCabeca"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoCabeca}
                      onChange={(e) => setFormData({...formData, pontuacaoCabeca: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoPescoco">Pescoço</Label>
                    <Input
                      id="pontuacaoPescoco"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoPescoco}
                      onChange={(e) => setFormData({...formData, pontuacaoPescoco: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoEspalda">Espalda</Label>
                    <Input
                      id="pontuacaoEspalda"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoEspalda}
                      onChange={(e) => setFormData({...formData, pontuacaoEspalda: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoDorso">Dorso</Label>
                    <Input
                      id="pontuacaoDorso"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoDorso}
                      onChange={(e) => setFormData({...formData, pontuacaoDorso: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoGarupa">Garupa</Label>
                    <Input
                      id="pontuacaoGarupa"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoGarupa}
                      onChange={(e) => setFormData({...formData, pontuacaoGarupa: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoMembros">Membros</Label>
                    <Input
                      id="pontuacaoMembros"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoMembros}
                      onChange={(e) => setFormData({...formData, pontuacaoMembros: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoAprumos">Aprumos</Label>
                    <Input
                      id="pontuacaoAprumos"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoAprumos}
                      onChange={(e) => setFormData({...formData, pontuacaoAprumos: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoAndamento">Andamento</Label>
                    <Input
                      id="pontuacaoAndamento"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoAndamento}
                      onChange={(e) => setFormData({...formData, pontuacaoAndamento: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pontuacaoHarmonia">Harmonia</Label>
                    <Input
                      id="pontuacaoHarmonia"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.pontuacaoHarmonia}
                      onChange={(e) => setFormData({...formData, pontuacaoHarmonia: e.target.value})}
                    />
                  </div>
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-700">
                    <strong>Pontuação Total Calculada: {calculateTotal()}/10</strong>
                  </div>
                </div>
              </div>

              {/* Observações */}
              <div>
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea
                  id="observacoes"
                  value={formData.observacoes}
                  onChange={(e) => setFormData({...formData, observacoes: e.target.value})}
                  placeholder="Observações sobre a avaliação..."
                  rows={3}
                />
              </div>

              <div className="flex gap-4">
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Salvar Avaliação
                </Button>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancelar
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Visualização das avaliações */}
      {selectedHorseId && selectedHorse && (
        <Tabs defaultValue="list" className="w-full">
          <TabsList>
            <TabsTrigger value="list">Lista de Avaliações</TabsTrigger>
            <TabsTrigger value="radar">Análise Radar</TabsTrigger>
            <TabsTrigger value="evolution">Evolução</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            {isLoadingMorfologia ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : horseMorfologia.length > 0 ? (
              <div className="space-y-4">
                {horseMorfologia.map((avaliacao, index) => (
                  <Card key={avaliacao.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">
                            Avaliação #{index + 1}
                          </CardTitle>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(avaliacao.dataMedicao).toLocaleDateString()}
                            </div>
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {avaliacao.responsavelMedicao}
                            </div>
                          </div>
                        </div>
                        <Badge 
                          variant={avaliacao.pontuacaoTotal >= 8 ? 'default' : 
                                 avaliacao.pontuacaoTotal >= 6 ? 'secondary' : 'outline'}
                          className="text-lg px-3 py-1"
                        >
                          {avaliacao.pontuacaoTotal}/10
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <ScoreCard title="Cabeça" score={avaliacao.pontuacaoCabeca} />
                        <ScoreCard title="Pescoço" score={avaliacao.pontuacaoPescoco} />
                        <ScoreCard title="Espalda" score={avaliacao.pontuacaoEspalda} />
                        <ScoreCard title="Dorso" score={avaliacao.pontuacaoDorso} />
                        <ScoreCard title="Garupa" score={avaliacao.pontuacaoGarupa} />
                        <ScoreCard title="Membros" score={avaliacao.pontuacaoMembros} />
                        <ScoreCard title="Aprumos" score={avaliacao.pontuacaoAprumos} />
                        <ScoreCard title="Andamento" score={avaliacao.pontuacaoAndamento} />
                        <ScoreCard title="Harmonia" score={avaliacao.pontuacaoHarmonia} />
                      </div>
                      
                      {avaliacao.observacoes && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium mb-1">Observações:</h4>
                          <p className="text-sm text-gray-600">{avaliacao.observacoes}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nenhuma avaliação encontrada</h3>
                  <p className="text-gray-500 mb-4">
                    Este cavalo ainda não possui avaliações morfológicas registradas.
                  </p>
                  <Button onClick={() => setShowForm(true)}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Criar primeira avaliação
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="radar" className="space-y-4">
            {horseMorfologia.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Análise Radar - Última Avaliação</CardTitle>
                  <CardDescription>
                    Visualização multidimensional das características morfológicas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={getRadarData(horseMorfologia[horseMorfologia.length - 1])}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="categoria" />
                        <PolarRadiusAxis angle={90} domain={[0, 10]} />
                        <Radar
                          name="Pontuação"
                          dataKey="valor"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.3}
                          strokeWidth={2}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sem dados para análise</h3>
                  <p className="text-gray-500">
                    Adicione avaliações morfológicas para visualizar o gráfico radar.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="evolution" className="space-y-4">
            {horseMorfologia.length > 1 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Evolução das Pontuações</CardTitle>
                  <CardDescription>
                    Acompanhe o progresso morfológico ao longo do tempo
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={horseMorfologia.map((av, index) => ({
                        avaliacao: `#${index + 1}`,
                        pontuacao: av.pontuacaoTotal,
                        data: new Date(av.dataMedicao).toLocaleDateString()
                      }))}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="avaliacao" />
                        <YAxis domain={[0, 10]} />
                        <Tooltip />
                        <Bar dataKey="pontuacao" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Histórico insuficiente</h3>
                  <p className="text-gray-500">
                    São necessárias pelo menos 2 avaliações para mostrar a evolução.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}