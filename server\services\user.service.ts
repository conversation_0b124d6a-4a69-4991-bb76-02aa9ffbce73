// User service with improved architecture
import { BaseService } from './base.service';
import { User, ServiceResponse } from '../types';
import { ValidationError, NotFoundError, AuthenticationError } from '../core/errors';
import { validateRequest } from '../core/validation';
import { z } from 'zod';
import bcrypt from 'bcrypt';

const userCreateSchema = z.object({
  name: z.string().min(1).max(255),
  username: z.string().min(3).max(120),
  email: z.string().email().max(255),
  password: z.string().min(6),
  role: z.enum(['ADMIN', 'USER']).default('USER'),
  avatar_url: z.string().url().optional(),
  flags: z.record(z.any()).default({})
});

const userUpdateSchema = userCreateSchema.partial().omit({ password: true });

const userLoginSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1)
});

const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1),
  newPassword: z.string().min(6)
});

export class UserService extends BaseService<User> {
  constructor(db: any) {
    super(db, 'users');
  }

  protected validateCreate(data: any): User {
    return validateRequest(userCreateSchema, data);
  }

  protected validateUpdate(data: any): Partial<User> {
    return validateRequest(userUpdateSchema, data);
  }

  async createUser(userData: any): Promise<ServiceResponse<User>> {
    try {
      const validatedData = this.validateCreate(userData);
      
      // Check if username already exists
      const existingUser = await this.findByUsername(validatedData.username);
      if (existingUser.success) {
        throw new ValidationError('Username already exists');
      }

      // Check if email already exists
      const existingEmail = await this.findByEmail(validatedData.email);
      if (existingEmail.success) {
        throw new ValidationError('Email already exists');
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(validatedData.password, 10);
      
      const userToCreate = {
        ...validatedData,
        password_hash: hashedPassword
      };

      delete (userToCreate as any).password;

      return await this.create(userToCreate);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'USER_CREATE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'USER_CREATE_ERROR',
          message: 'Failed to create user',
          statusCode: 500
        }
      };
    }
  }

  async findByUsername(username: string): Promise<ServiceResponse<User>> {
    try {
      const query = 'SELECT * FROM users WHERE username = $1';
      const result = await this.db.query<User>(query, [username]);

      if (result.count === 0) {
        throw new NotFoundError('User');
      }

      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'NOT_FOUND',
          message: error.message,
          statusCode: 404
        } : {
          code: 'QUERY_ERROR',
          message: 'Failed to fetch user',
          statusCode: 500
        }
      };
    }
  }

  async findByEmail(email: string): Promise<ServiceResponse<User>> {
    try {
      const query = 'SELECT * FROM users WHERE email = $1';
      const result = await this.db.query<User>(query, [email]);

      if (result.count === 0) {
        throw new NotFoundError('User');
      }

      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'NOT_FOUND',
          message: error.message,
          statusCode: 404
        } : {
          code: 'QUERY_ERROR',
          message: 'Failed to fetch user',
          statusCode: 500
        }
      };
    }
  }

  async authenticateUser(credentials: any): Promise<ServiceResponse<User>> {
    try {
      const { username, password } = validateRequest(userLoginSchema, credentials);
      
      const userResult = await this.findByUsername(username);
      if (!userResult.success) {
        throw new AuthenticationError('Invalid credentials');
      }

      const user = userResult.data;
      
      // Check password against both password_hash and legacy password field
      const passwordHash = user.password_hash || user.password;
      if (!passwordHash) {
        throw new AuthenticationError('Invalid credentials');
      }

      const isPasswordValid = await bcrypt.compare(password, passwordHash);
      if (!isPasswordValid) {
        throw new AuthenticationError('Invalid credentials');
      }

      // Remove password fields from response
      const { password_hash, password: legacyPassword, ...userWithoutPassword } = user;
      
      return {
        data: userWithoutPassword as User,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'AUTHENTICATION_ERROR',
          message: error.message,
          statusCode: 401
        } : {
          code: 'AUTHENTICATION_ERROR',
          message: 'Authentication failed',
          statusCode: 401
        }
      };
    }
  }

  async changePassword(userId: number, passwordData: any): Promise<ServiceResponse<boolean>> {
    try {
      const { currentPassword, newPassword } = validateRequest(passwordChangeSchema, passwordData);
      
      const userResult = await this.findById(userId);
      if (!userResult.success) {
        throw new NotFoundError('User');
      }

      const user = userResult.data;
      const passwordHash = user.password_hash || user.password;
      
      if (!passwordHash) {
        throw new AuthenticationError('Current password verification failed');
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, passwordHash);
      if (!isCurrentPasswordValid) {
        throw new AuthenticationError('Current password is incorrect');
      }

      const newPasswordHash = await bcrypt.hash(newPassword, 10);
      
      const updateResult = await this.update(userId, { password_hash: newPasswordHash });
      
      return {
        data: updateResult.success,
        success: updateResult.success
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'PASSWORD_CHANGE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'PASSWORD_CHANGE_ERROR',
          message: 'Failed to change password',
          statusCode: 500
        }
      };
    }
  }

  async updateProfile(userId: number, profileData: any): Promise<ServiceResponse<User>> {
    try {
      const validatedData = this.validateUpdate(profileData);
      
      // Check if username is being changed and if it already exists
      if (validatedData.username) {
        const existingUser = await this.findByUsername(validatedData.username);
        if (existingUser.success && existingUser.data.id !== userId) {
          throw new ValidationError('Username already exists');
        }
      }

      // Check if email is being changed and if it already exists
      if (validatedData.email) {
        const existingEmail = await this.findByEmail(validatedData.email);
        if (existingEmail.success && existingEmail.data.id !== userId) {
          throw new ValidationError('Email already exists');
        }
      }

      return await this.update(userId, validatedData);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'PROFILE_UPDATE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'PROFILE_UPDATE_ERROR',
          message: 'Failed to update profile',
          statusCode: 500
        }
      };
    }
  }

  async getUserStatistics(userId: number): Promise<ServiceResponse<any>> {
    try {
      const query = `
        SELECT 
          (SELECT COUNT(*) FROM cavalos WHERE user_id = $1) as total_horses,
          (SELECT COUNT(*) FROM cavalos WHERE user_id = $1 AND status = 'ativo') as active_horses,
          (SELECT COUNT(*) FROM manejos WHERE user_id = $1) as total_manejos,
          (SELECT COUNT(*) FROM procedimentos_vet WHERE user_id = $1) as total_procedures
      `;

      const result = await this.db.query(query, [userId]);
      
      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATISTICS_ERROR',
          message: 'Failed to get user statistics',
          statusCode: 500
        }
      };
    }
  }

  async getAllUsers(adminUserId: number): Promise<ServiceResponse<User[]>> {
    try {
      // Verify admin access
      const adminResult = await this.findById(adminUserId);
      if (!adminResult.success || adminResult.data.role !== 'ADMIN') {
        throw new AuthenticationError('Admin access required');
      }

      const query = `
        SELECT id, name, username, email, role, avatar_url, flags, created_at, updated_at 
        FROM users 
        ORDER BY created_at DESC
      `;

      const result = await this.db.query<User>(query);
      
      return {
        data: result.data,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'AUTHORIZATION_ERROR',
          message: error.message,
          statusCode: 403
        } : {
          code: 'QUERY_ERROR',
          message: 'Failed to fetch users',
          statusCode: 500
        }
      };
    }
  }
}