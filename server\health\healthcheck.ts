/**
 * Sistema de healthcheck e métricas para o EquiGestor AI
 */
import { Request, Response } from 'express';
import { db } from '../db';
import { sql } from 'drizzle-orm';

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  services: {
    database: ServiceStatus;
    firebase: ServiceStatus;
    openai: ServiceStatus;
  };
  metrics: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    process: {
      pid: number;
      uptime: number;
      version: string;
    };
  };
}

interface ServiceStatus {
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
  lastCheck: string;
}

/**
 * Verifica a conectividade com PostgreSQL
 */
async function checkDatabase(): Promise<ServiceStatus> {
  const start = Date.now();
  try {
    await db.execute(sql`SELECT 1`);
    return {
      status: 'healthy',
      responseTime: Date.now() - start,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown database error',
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * Verifica a conectividade com Firebase
 */
async function checkFirebase(): Promise<ServiceStatus> {
  const start = Date.now();
  try {
    // Verificação básica - se as variáveis de ambiente existem
    const hasConfig = !!(
      process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_PRIVATE_KEY &&
      process.env.FIREBASE_CLIENT_EMAIL
    );
    
    if (!hasConfig) {
      return {
        status: 'unhealthy',
        error: 'Firebase configuration missing',
        lastCheck: new Date().toISOString()
      };
    }

    return {
      status: 'healthy',
      responseTime: Date.now() - start,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown Firebase error',
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * Verifica a conectividade com OpenAI
 */
async function checkOpenAI(): Promise<ServiceStatus> {
  const start = Date.now();
  try {
    // Verificação básica - se a API key existe
    const hasApiKey = !!process.env.OPENAI_API_KEY;
    
    if (!hasApiKey) {
      return {
        status: 'unhealthy',
        error: 'OpenAI API key missing',
        lastCheck: new Date().toISOString()
      };
    }

    return {
      status: 'healthy',
      responseTime: Date.now() - start,
      lastCheck: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown OpenAI error',
      lastCheck: new Date().toISOString()
    };
  }
}

/**
 * Coleta métricas do sistema
 */
function getSystemMetrics() {
  const memUsage = process.memoryUsage();
  return {
    memory: {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal,
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    },
    process: {
      pid: process.pid,
      uptime: Math.floor(process.uptime()),
      version: process.version
    }
  };
}

/**
 * Handler do endpoint /healthz
 */
export async function healthcheck(req: Request, res: Response): Promise<void> {
  const startTime = Date.now();
  
  try {
    // Executar todas as verificações em paralelo
    const [database, firebase, openai] = await Promise.all([
      checkDatabase(),
      checkFirebase(),
      checkOpenAI()
    ]);

    const services = { database, firebase, openai };
    const metrics = getSystemMetrics();
    
    // Determinar status geral
    const unhealthyServices = Object.values(services).filter(s => s.status === 'unhealthy');
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded';
    
    if (unhealthyServices.length === 0) {
      overallStatus = 'healthy';
    } else if (unhealthyServices.length === Object.keys(services).length) {
      overallStatus = 'unhealthy';
    } else {
      overallStatus = 'degraded';
    }

    const health: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      services,
      metrics
    };

    // Log da verificação
    const logger = (req as any).logger;
    if (logger) {
      logger.info({
        event: 'healthcheck',
        status: overallStatus,
        duration: Date.now() - startTime,
        services: Object.entries(services).map(([name, status]) => ({
          name,
          status: status.status,
          responseTime: status.responseTime
        }))
      }, 'Health check completed');
    }

    // Retornar status HTTP apropriado
    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 207 : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    const logger = (req as any).logger;
    if (logger) {
      logger.error({
        event: 'healthcheck_error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      }, 'Health check failed');
    }

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      uptime: Math.floor(process.uptime())
    });
  }
}