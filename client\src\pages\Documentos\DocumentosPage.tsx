import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  PlusCircle, Search, FileText, File, FileIcon, FileSpreadsheet, 
  Download, Eye, Edit, Trash2, UploadCloud, ChevronDown, 
  ChevronUp, Calendar, User, Folder
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LayoutWrapper } from '@/components/Layout';
import { DatePicker } from '@/components/DatePicker';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

/**
 * Interface para definir a estrutura de um documento
 */
interface Documento {
  id: number;
  nome: string;
  tipo: string;
  categoria: string;
  dataUpload: string;
  tamanho: string;
  usuarioUpload: string;
  descricao?: string;
  tags?: string[];
  associado?: {
    tipo: 'cavalo' | 'manejo' | 'procedimento' | 'financeiro';
    id: number;
    nome: string;
  };
  url: string;
  icone: JSX.Element;
}

/**
 * Componente principal da página de Documentos
 */
export function DocumentosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoriaFilter, setCategoriaFilter] = useState('todos');
  const [tipoFilter, setTipoFilter] = useState('todos');
  const [dataInicio, setDataInicio] = useState<Date | undefined>(undefined);
  const [dataFim, setDataFim] = useState<Date | undefined>(undefined);
  const [expandedRows, setExpandedRows] = useState<number[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedCategoria, setSelectedCategoria] = useState('');
  const [selectedAssociacao, setSelectedAssociacao] = useState('');
  const [activeTab, setActiveTab] = useState('todos');

  // Filtrar documentos com base nos filtros aplicados e na tab ativa
  const filteredDocumentos = documentosData.filter(documento => {
    const matchesSearch = 
      documento.nome.toLowerCase().includes(searchTerm.toLowerCase()) || 
      (documento.descricao && documento.descricao.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (documento.tags && documento.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));
    
    const matchesCategoria = categoriaFilter === 'todos' || documento.categoria === categoriaFilter;
    
    const matchesTipo = tipoFilter === 'todos' || documento.tipo === tipoFilter;
    
    // Filtro de período
    let matchesPeriodo = true;
    const dataDocumento = new Date(documento.dataUpload);
    
    if (dataInicio) {
      // Ignorar a parte de horas para comparação justa por data
      const startDate = new Date(dataInicio);
      startDate.setHours(0, 0, 0, 0);
      dataDocumento.setHours(0, 0, 0, 0);
      
      if (dataDocumento < startDate) {
        matchesPeriodo = false;
      }
    }
    
    if (dataFim && matchesPeriodo) {
      // Ignorar a parte de horas para comparação justa por data
      const endDate = new Date(dataFim);
      endDate.setHours(23, 59, 59, 999);
      dataDocumento.setHours(0, 0, 0, 0);
      
      if (dataDocumento > endDate) {
        matchesPeriodo = false;
      }
    }
    
    // Filtro por tab
    let matchesTab = true;
    if (activeTab !== 'todos') {
      if (activeTab === 'animais') {
        matchesTab = documento.associado?.tipo === 'cavalo';
      } else if (activeTab === 'manejos') {
        matchesTab = documento.associado?.tipo === 'manejo';
      } else if (activeTab === 'veterinario') {
        matchesTab = documento.associado?.tipo === 'procedimento';
      } else if (activeTab === 'financeiro') {
        matchesTab = documento.associado?.tipo === 'financeiro';
      } else if (activeTab === 'nao-associados') {
        matchesTab = !documento.associado;
      }
    }
    
    return matchesSearch && matchesCategoria && matchesTipo && matchesPeriodo && matchesTab;
  })
  // Ordenar documentos por data de upload (mais recentes primeiro e futuros primeiro)
  .sort((a, b) => {
    // Para priorizar datas futuras mais próximas primeiro, seguidas pelas datas passadas mais recentes
    const now = new Date().getTime();
    const dateA = new Date(a.dataUpload).getTime();
    const dateB = new Date(b.dataUpload).getTime();
    
    // Dividir em documentos futuros e passados
    const aFuturo = dateA >= now;
    const bFuturo = dateB >= now;
    
    // Se um é futuro e o outro é passado, o futuro vem primeiro
    if (aFuturo && !bFuturo) return -1;
    if (!aFuturo && bFuturo) return 1;
    
    // Se ambos são futuros, o mais próximo vem primeiro
    if (aFuturo && bFuturo) {
      return dateA - dateB; // Ordem crescente de data para documentos futuros
    }
    
    // Se ambos são passados, o mais recente (mais próximo de hoje) vem primeiro
    return dateB - dateA; // Ordem decrescente de data para documentos passados
  });

  // Função para formatar a data
  const formatarData = (dataString: string) => {
    const data = new Date(dataString);
    return data.toLocaleDateString('pt-BR');
  };

  // Função para alternar a expansão da linha
  const toggleRowExpand = (id: number) => {
    if (expandedRows.includes(id)) {
      setExpandedRows(expandedRows.filter(rowId => rowId !== id));
    } else {
      setExpandedRows([...expandedRows, id]);
    }
  };

  // Função para renderizar o ícone do tipo do documento
  const renderTipoIcon = (tipo: string) => {
    switch (tipo.toLowerCase()) {
      case 'pdf':
        return <FileIcon className="h-4 w-4 text-red-600" />;
      case 'excel':
      case 'xls':
      case 'xlsx':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
      case 'imagem':
      case 'jpg':
      case 'png':
      case 'gif':
        return <File className="h-4 w-4 text-blue-600" />;
      case 'documento':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="h-4 w-4 text-blue-900" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  return (
    <LayoutWrapper pageTitle="Documentos">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Gerenciamento de Documentos</h1>
            <p className="text-muted-foreground">
              Organize, visualize e compartilhe documentos importantes
            </p>
          </div>
          
          <Dialog open={showUploadModal} onOpenChange={setShowUploadModal}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <UploadCloud className="h-4 w-4" />
                <span>Fazer Upload</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Upload de Documento</DialogTitle>
                <DialogDescription>
                  Faça upload de um novo documento para o sistema
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="file">Arquivo</Label>
                  <Input id="file" type="file" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nome">Nome do Documento</Label>
                  <Input id="nome" placeholder="Digite um nome para o documento" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="descricao">Descrição</Label>
                  <Input id="descricao" placeholder="Descreva o documento brevemente" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="categoria">Categoria</Label>
                  <Select value={selectedCategoria} onValueChange={setSelectedCategoria}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione uma categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="contrato">Contrato</SelectItem>
                      <SelectItem value="certificado">Certificado</SelectItem>
                      <SelectItem value="registro">Registro</SelectItem>
                      <SelectItem value="exame">Exame</SelectItem>
                      <SelectItem value="financeiro">Financeiro</SelectItem>
                      <SelectItem value="outros">Outros</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="associado">Associar a:</Label>
                  <Select value={selectedAssociacao} onValueChange={setSelectedAssociacao}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione para associar" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cavalo">Cavalo</SelectItem>
                      <SelectItem value="manejo">Manejo</SelectItem>
                      <SelectItem value="procedimento">Procedimento Veterinário</SelectItem>
                      <SelectItem value="financeiro">Registro Financeiro</SelectItem>
                      <SelectItem value="nenhum">Não associar</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="tag-importante" />
                      <label
                        htmlFor="tag-importante"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Importante
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="tag-medico" />
                      <label
                        htmlFor="tag-medico"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Médico
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="tag-legal" />
                      <label
                        htmlFor="tag-legal"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Legal
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-4">
                <Button variant="outline" onClick={() => setShowUploadModal(false)}>
                  Cancelar
                </Button>
                <Button onClick={() => setShowUploadModal(false)}>
                  Enviar Documento
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        
        {/* Tabs de categorias de documentos */}
        <Tabs 
          defaultValue="todos" 
          value={activeTab} 
          onValueChange={setActiveTab} 
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 md:grid-cols-6 lg:w-fit">
            <TabsTrigger value="todos" className="text-xs md:text-sm">
              Todos
            </TabsTrigger>
            <TabsTrigger value="animais" className="text-xs md:text-sm">
              Animais
            </TabsTrigger>
            <TabsTrigger value="manejos" className="text-xs md:text-sm">
              Manejos
            </TabsTrigger>
            <TabsTrigger value="veterinario" className="text-xs md:text-sm">
              Veterinário
            </TabsTrigger>
            <TabsTrigger value="financeiro" className="text-xs md:text-sm">
              Financeiro
            </TabsTrigger>
            <TabsTrigger value="nao-associados" className="text-xs md:text-sm">
              Não Associados
            </TabsTrigger>
          </TabsList>
          
          <div className="mt-6">
            {/* Card de filtros e busca */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Filtros</CardTitle>
                <CardDescription>
                  Encontre documentos específicos usando os filtros abaixo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col gap-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Buscar por nome, descrição ou tags..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-3">
                    <Select 
                      value={categoriaFilter} 
                      onValueChange={setCategoriaFilter}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Categoria" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="todos">Todas as Categorias</SelectItem>
                        <SelectItem value="contrato">Contratos</SelectItem>
                        <SelectItem value="certificado">Certificados</SelectItem>
                        <SelectItem value="registro">Registros</SelectItem>
                        <SelectItem value="exame">Exames</SelectItem>
                        <SelectItem value="financeiro">Financeiros</SelectItem>
                        <SelectItem value="outros">Outros</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select 
                      value={tipoFilter} 
                      onValueChange={setTipoFilter}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Tipo de Arquivo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="todos">Todos os Tipos</SelectItem>
                        <SelectItem value="pdf">PDF</SelectItem>
                        <SelectItem value="excel">Excel</SelectItem>
                        <SelectItem value="imagem">Imagens</SelectItem>
                        <SelectItem value="documento">Documentos</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <div className="flex items-center gap-2">
                      <DatePicker
                        date={dataInicio}
                        setDate={setDataInicio}
                        placeholder="Data inicial"
                      />
                      <span className="text-muted-foreground">até</span>
                      <DatePicker
                        date={dataFim}
                        setDate={setDataFim}
                        placeholder="Data final"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Lista de documentos */}
            {filteredDocumentos.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 mt-6 bg-muted/30 rounded-lg border border-dashed">
                <Folder className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold">Nenhum documento encontrado</h3>
                <p className="text-muted-foreground max-w-md text-center mt-1 mb-4">
                  {(searchTerm || categoriaFilter !== 'todos' || tipoFilter !== 'todos') ? 
                    'Tente ajustar os critérios de busca ou remover alguns filtros.' : 
                    'Faça upload de documentos para começar a gerenciá-los aqui.'}
                </p>
                <Button onClick={() => setShowUploadModal(true)}>
                  <UploadCloud className="mr-2 h-4 w-4" />
                  Fazer Upload
                </Button>
              </div>
            ) : (
              <div className="mt-6 rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-8"></TableHead>
                      <TableHead>Nome</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Tamanho</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocumentos.map(documento => (
                      <>
                        <TableRow 
                          key={documento.id} 
                          className="hover:bg-muted/50 cursor-pointer" 
                          onClick={() => toggleRowExpand(documento.id)}
                        >
                          <TableCell className="p-2">
                            {expandedRows.includes(documento.id) ? (
                              <ChevronUp className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              {documento.icone}
                              <div>
                                <div className="font-medium">{documento.nome}</div>
                                {documento.tags && documento.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {documento.tags.map((tag, index) => (
                                      <Badge key={index} variant="outline" className="text-xs">
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {documento.categoria}
                            </Badge>
                          </TableCell>
                          <TableCell className="flex items-center gap-1">
                            {renderTipoIcon(documento.tipo)}
                            <span>.{documento.tipo.toLowerCase()}</span>
                          </TableCell>
                          <TableCell>
                            {formatarData(documento.dataUpload)}
                          </TableCell>
                          <TableCell>
                            {documento.tamanho}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                  <span className="sr-only">Abrir menu</span>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    className="h-5 w-5"
                                  >
                                    <path d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z"></path>
                                  </svg>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Ações</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <Link href={documento.url}>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Visualizar
                                  </DropdownMenuItem>
                                </Link>
                                <Link href={documento.url}>
                                  <DropdownMenuItem>
                                    <Download className="mr-2 h-4 w-4" />
                                    Download
                                  </DropdownMenuItem>
                                </Link>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Editar Detalhes
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Excluir
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                        
                        {/* Linha expansível com detalhes */}
                        {expandedRows.includes(documento.id) && (
                          <TableRow className="bg-muted/50">
                            <TableCell colSpan={7} className="py-4">
                              <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                  <h4 className="text-sm font-semibold mb-2">Detalhes do Documento</h4>
                                  <div className="text-sm space-y-1">
                                    <div className="flex gap-2">
                                      <span className="font-medium">Nome completo:</span>
                                      <span>{documento.nome}</span>
                                    </div>
                                    {documento.descricao && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Descrição:</span>
                                        <span>{documento.descricao}</span>
                                      </div>
                                    )}
                                    <div className="flex gap-2">
                                      <span className="font-medium">Categoria:</span>
                                      <span>{documento.categoria}</span>
                                    </div>
                                    <div className="flex gap-2">
                                      <span className="font-medium">Tipo:</span>
                                      <span>{documento.tipo}</span>
                                    </div>
                                    <div className="flex gap-2">
                                      <span className="font-medium">Tamanho:</span>
                                      <span>{documento.tamanho}</span>
                                    </div>
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="text-sm font-semibold mb-2">Informações Adicionais</h4>
                                  <div className="text-sm space-y-1">
                                    <div className="flex gap-2">
                                      <span className="font-medium">Data de upload:</span>
                                      <span className="flex items-center gap-1">
                                        <Calendar className="h-3 w-3" /> 
                                        {formatarData(documento.dataUpload)}
                                      </span>
                                    </div>
                                    <div className="flex gap-2">
                                      <span className="font-medium">Enviado por:</span>
                                      <span className="flex items-center gap-1">
                                        <User className="h-3 w-3" /> 
                                        {documento.usuarioUpload}
                                      </span>
                                    </div>
                                    {documento.associado && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Associado a:</span>
                                        <Link href={`/${documento.associado.tipo}s/${documento.associado.id}`}>
                                          <span className="text-blue-600 hover:underline">
                                            {documento.associado.nome} 
                                            ({documento.associado.tipo === 'cavalo' ? 'Animal' : 
                                             documento.associado.tipo === 'manejo' ? 'Manejo' : 
                                             documento.associado.tipo === 'procedimento' ? 'Procedimento' : 
                                             'Financeiro'})
                                          </span>
                                        </Link>
                                      </div>
                                    )}
                                    {documento.tags && documento.tags.length > 0 && (
                                      <div className="flex gap-2">
                                        <span className="font-medium">Tags:</span>
                                        <div className="flex flex-wrap gap-1">
                                          {documento.tags.map((tag, index) => (
                                            <Badge key={index} variant="outline" className="text-xs">
                                              {tag}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                              
                              <div className="mt-4 flex justify-end gap-2">
                                <Link href={documento.url}>
                                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                                    <Download className="h-4 w-4" />
                                    Download
                                  </Button>
                                </Link>
                                <Link href={documento.url}>
                                  <Button size="sm" className="flex items-center gap-2">
                                    <Eye className="h-4 w-4" />
                                    Visualizar
                                  </Button>
                                </Link>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}

// Dados de exemplo para documentos (apenas para demonstração)
const documentosData: Documento[] = [
  {
    id: 1,
    nome: 'Contrato de Compra - Trovão',
    tipo: 'PDF',
    categoria: 'contrato',
    dataUpload: '2025-01-15',
    tamanho: '1.2 MB',
    usuarioUpload: 'Admin',
    descricao: 'Contrato de compra do cavalo Trovão',
    tags: ['importante', 'legal'],
    associado: {
      tipo: 'cavalo',
      id: 1,
      nome: 'Trovão'
    },
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  },
  {
    id: 2,
    nome: 'Certificado de Registro - Pegasus',
    tipo: 'PDF',
    categoria: 'certificado',
    dataUpload: '2025-02-03',
    tamanho: '890 KB',
    usuarioUpload: 'Maria Silva',
    tags: ['registro', 'oficial'],
    associado: {
      tipo: 'cavalo',
      id: 2,
      nome: 'Pegasus'
    },
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  },
  {
    id: 3,
    nome: 'Exame Veterinário - Tempestade',
    tipo: 'PDF',
    categoria: 'exame',
    dataUpload: '2025-03-10',
    tamanho: '1.5 MB',
    usuarioUpload: 'Dr. Carlos',
    descricao: 'Exame completo realizado em 10/03/2025',
    tags: ['médico', 'importante'],
    associado: {
      tipo: 'procedimento',
      id: 5,
      nome: 'Exame Geral'
    },
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  },
  {
    id: 4,
    nome: 'Controle de Vacinação 2025',
    tipo: 'Excel',
    categoria: 'registro',
    dataUpload: '2025-03-15',
    tamanho: '750 KB',
    usuarioUpload: 'Admin',
    descricao: 'Planilha com controle completo de vacinações',
    tags: ['médico', 'controle'],
    associado: {
      tipo: 'manejo',
      id: 3,
      nome: 'Vacinações Periódicas'
    },
    url: '#',
    icone: <FileSpreadsheet className="h-10 w-10 text-green-600" />
  },
  {
    id: 5,
    nome: 'Relatório Financeiro - Q1 2025',
    tipo: 'Excel',
    categoria: 'financeiro',
    dataUpload: '2025-03-28',
    tamanho: '1.1 MB',
    usuarioUpload: 'Fernanda Oliveira',
    descricao: 'Relatório financeiro do primeiro trimestre de 2025',
    tags: ['trimestral', 'financeiro'],
    associado: {
      tipo: 'financeiro',
      id: 8,
      nome: 'Relatório Q1 2025'
    },
    url: '#',
    icone: <FileSpreadsheet className="h-10 w-10 text-green-600" />
  },
  {
    id: 6,
    nome: 'Foto Cavalo Relâmpago',
    tipo: 'imagem',
    categoria: 'outros',
    dataUpload: '2025-03-05',
    tamanho: '2.3 MB',
    usuarioUpload: 'Maria Silva',
    descricao: 'Foto oficial para registro',
    tags: ['foto', 'registro'],
    associado: {
      tipo: 'cavalo',
      id: 4,
      nome: 'Relâmpago'
    },
    url: '#',
    icone: <File className="h-10 w-10 text-blue-600" />
  },
  {
    id: 7,
    nome: 'Manual de Procedimentos',
    tipo: 'PDF',
    categoria: 'outros',
    dataUpload: '2025-02-18',
    tamanho: '3.2 MB',
    usuarioUpload: 'Admin',
    descricao: 'Manual com todos os procedimentos padrão',
    tags: ['interno', 'processos'],
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  },
  {
    id: 8,
    nome: 'Nota Fiscal - Compra de Medicamentos',
    tipo: 'PDF',
    categoria: 'financeiro',
    dataUpload: '2025-03-22',
    tamanho: '450 KB',
    usuarioUpload: 'Fernanda Oliveira',
    descricao: 'Nota fiscal da compra de medicamentos em 22/03/2025',
    tags: ['compra', 'fiscal'],
    associado: {
      tipo: 'financeiro',
      id: 12,
      nome: 'Compra de Medicamentos'
    },
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  },
  {
    id: 9,
    nome: 'Certificado de Premiação - Pegasus',
    tipo: 'PDF',
    categoria: 'certificado',
    dataUpload: '2025-03-18',
    tamanho: '680 KB',
    usuarioUpload: 'Maria Silva',
    descricao: 'Certificado da premiação obtida na competição regional',
    tags: ['premiação', 'concurso'],
    associado: {
      tipo: 'cavalo',
      id: 2,
      nome: 'Pegasus'
    },
    url: '#',
    icone: <FileIcon className="h-10 w-10 text-red-600" />
  }
];

export default DocumentosPage;