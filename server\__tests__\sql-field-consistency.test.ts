/**
 * Unit tests to verify SQL field name consistency and prevent SQL syntax errors
 * Tests all database update operations use correct snake_case field names
 */

import { describe, it, expect } from 'vitest';
import { db } from '../db';
import { cavalos, pelagens, genealogia } from '../../shared/schema';
import { eq } from 'drizzle-orm';

describe('SQL Field Name Consistency Tests', () => {
  
  describe('Horse Table Updates', () => {
    it('should generate correct SQL with is_external field', () => {
      const updateQuery = db.update(cavalos)
        .set({ is_external: true })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"is_external"/);
      expect(sql.sql).not.toMatch(/isExternal/);
    });

    it('should generate correct SQL with birth_date field', () => {
      const updateQuery = db.update(cavalos)
        .set({ birth_date: '2020-01-01' })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"birth_date"/);
      expect(sql.sql).not.toMatch(/birth_date/);
    });

    it('should generate correct SQL with numero_registro field', () => {
      const updateQuery = db.update(cavalos)
        .set({ numero_registro: 'B123456' })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"numero_registro"/);
      expect(sql.sql).not.toMatch(/numero_registro/);
    });

    it('should generate correct SQL with user_id field', () => {
      const updateQuery = db.update(cavalos)
        .set({ user_id: 1 })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"user_id"/);
      expect(sql.sql).not.toMatch(/userId/);
    });

    it('should generate correct SQL with created_at field', () => {
      const updateQuery = db.update(cavalos)
        .set({ created_at: new Date() })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"created_at"/);
      expect(sql.sql).not.toMatch(/createdAt/);
    });

    it('should generate correct SQL with multiple snake_case fields', () => {
      const updateQuery = db.update(cavalos)
        .set({ 
          is_external: true,
          birth_date: '2020-01-01',
          numero_registro: 'B123456',
          user_id: 1,
          created_at: new Date()
        })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"is_external".*"birth_date".*"numero_registro".*"user_id".*"created_at"/);
      expect(sql.sql).not.toMatch(/isExternal|birth_date|numero_registro|userId|createdAt/);
    });
  });

  describe('Pelagens Table Updates', () => {
    it('should generate correct SQL with nome field', () => {
      const updateQuery = db.update(pelagens)
        .set({ nome: 'Tordilho' })
        .where(eq(pelagens.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"nome"/);
    });

    it('should generate correct SQL with fonte field', () => {
      const updateQuery = db.update(pelagens)
        .set({ fonte: 'ABCCC Import' })
        .where(eq(pelagens.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"fonte"/);
      expect(sql.sql).not.toMatch(/user_id/); // Ensure we don't use wrong field
    });
  });

  describe('Genealogia Table Updates', () => {
    it('should generate correct SQL with horse_id field', () => {
      const updateQuery = db.update(genealogia)
        .set({ horse_id: 1 })
        .where(eq(genealogia.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"horse_id"/);
      expect(sql.sql).not.toMatch(/horseId/);
    });

    it('should generate correct SQL with pai_id field', () => {
      const updateQuery = db.update(genealogia)
        .set({ pai_id: 2 })
        .where(eq(genealogia.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"pai_id"/);
      expect(sql.sql).not.toMatch(/pai_id/);
    });

    it('should generate correct SQL with mae_id field', () => {
      const updateQuery = db.update(genealogia)
        .set({ mae_id: 3 })
        .where(eq(genealogia.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"mae_id"/);
      expect(sql.sql).not.toMatch(/mae_id/);
    });

    it('should generate correct SQL with avo_paterno_id field', () => {
      const updateQuery = db.update(genealogia)
        .set({ avo_paterno_id: 4 })
        .where(eq(genealogia.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"avo_paterno_id"/);
      expect(sql.sql).not.toMatch(/avoPaternoId/);
    });
  });

  describe('Empty Update Object Prevention', () => {
    it('should not allow empty update objects', () => {
      expect(() => {
        db.update(cavalos)
          .set({})
          .where(eq(cavalos.id, 1));
      }).toThrow(); // Should prevent empty updates
    });

    it('should require at least one field in update', () => {
      const validUpdate = db.update(cavalos)
        .set({ name: 'Test Horse' })
        .where(eq(cavalos.id, 1));
      
      const sql = validUpdate.toSQL();
      expect(sql.sql).toMatch(/SET .*"name"/);
    });
  });

  describe('ABCCC Import Field Validation', () => {
    it('should use correct field names for external horses', () => {
      const updateQuery = db.update(cavalos)
        .set({ 
          is_external: true,
          name: 'AS MALKE BORBOLETA',
          numero_registro: 'B096895',
          sexo: 'Égua',
          cor: 'Tordilho Negro'
        })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/SET .*"is_external".*"name".*"numero_registro".*"sexo".*"cor"/);
      expect(sql.sql).not.toMatch(/isExternal|numero_registro/);
    });

    it('should validate all critical ABCCC fields exist', () => {
      const criticalFields = [
        'name', 'numero_registro', 'sexo', 'birth_date', 'cor',
        'pelagem_id', 'criador', 'proprietario', 'inspetor',
        'origem', 'is_external', 'pai_id', 'mae_id', 'status'
      ];

      criticalFields.forEach(field => {
        const updateObj = { [field]: field === 'is_external' ? true : 'test' };
        const updateQuery = db.update(cavalos)
          .set(updateObj as any)
          .where(eq(cavalos.id, 1));
        
        const sql = updateQuery.toSQL();
        expect(sql.sql).toMatch(new RegExp(`"${field}"`));
      });
    });
  });

  describe('Type Safety Validation', () => {
    it('should enforce correct data types for boolean fields', () => {
      const updateQuery = db.update(cavalos)
        .set({ is_external: true })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.params).toContain(true);
    });

    it('should enforce correct data types for date fields', () => {
      const testDate = new Date('2020-01-01');
      const updateQuery = db.update(cavalos)
        .set({ birth_date: '2020-01-01' })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.params).toContain('2020-01-01');
    });

    it('should enforce correct data types for integer fields', () => {
      const updateQuery = db.update(cavalos)
        .set({ user_id: 1, pai_id: 2, mae_id: 3 })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.params).toContain(1);
      expect(sql.params).toContain(2);
      expect(sql.params).toContain(3);
    });
  });

  describe('SQL Injection Prevention', () => {
    it('should properly escape field names', () => {
      const updateQuery = db.update(cavalos)
        .set({ name: "Test'; DROP TABLE cavalos; --" })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/\$\d+/); // Should use parameterized queries
      expect(sql.params).toContain("Test'; DROP TABLE cavalos; --");
    });

    it('should use parameterized queries for all values', () => {
      const updateQuery = db.update(cavalos)
        .set({ 
          name: 'Test Horse',
          numero_registro: 'B123456',
          is_external: true
        })
        .where(eq(cavalos.id, 1));
      
      const sql = updateQuery.toSQL();
      expect(sql.sql).toMatch(/\$\d+.*\$\d+.*\$\d+/); // Multiple parameters
      expect(sql.params.length).toBeGreaterThan(0);
    });
  });
});