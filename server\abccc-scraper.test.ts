/**
 * Testes para o módulo de scraping da ABCCC
 * 
 * Este arquivo contém testes para verificar a validade dos seletores utilizados
 * e implementa fallbacks para quando os seletores padrão não funcionarem.
 */

import * as cheerio from 'cheerio';
import { ABCCCScrapedData } from './abccc-scraper-service';

/**
 * Extrai dados usando seletores primários ou tenta alternativas
 * @param html HTML da página a ser analisada
 * @returns Dados extraídos do cavalo ou null se não foi possível extrair
 */
export function extrairDadosComFallback(html: string): Partial<ABCCCScrapedData> | null {
  const $ = cheerio.load(html);
  const resultado: Partial<ABCCCScrapedData> = {};
  
  // Tentativa 1: Seletores primários específicos
  try {
    resultado.nome = $('.wrap.cavalos-wrap h1').text().trim() || 
                    $('h1.nome-cavalo').text().trim();
                    
    resultado.registro = $('.infos-cavalo tr:contains("SBB:")').find('td').eq(1).text().trim() || 
                        $('td:contains("SBB")').next().text().trim();
                        
    resultado.pelagem = $('.infos-cavalo tr:contains("Pelagem:")').find('td').eq(1).text().trim() || 
                       $('td:contains("Pelagem")').next().text().trim();
  } catch (e) {
    console.warn('Falha nos seletores primários, tentando alternativas...');
  }
  
  // Verificação específica para garantir que estamos em uma página de cavalo
  // Se não encontrar elementos específicos da página de cavalo, não tenta extrair dados
  const isHorsePage = $('.infos-cavalo').length > 0 || 
                     $('h1.nome-cavalo').length > 0 || 
                     $('.cavalos-wrap').length > 0;
  
  // Se não estiver em uma página de cavalo, retorna null para evitar dados incorretos
  if (!isHorsePage) {
    return null;
  }
  
  // Se os seletores específicos falharem, mas estamos em uma página de cavalo,
  // tenta seletores mais específicos sem busca genérica no texto da página
  if (!resultado.nome || !resultado.registro) {
    try {
      // Tenta novamente com seletores mais específicos para nome
      if (!resultado.nome) {
        const nomeCandidatos = [
          $('.titulo-cavalo').text().trim(),
          $('.ficha-tecnica h1').text().trim(),
          $('div.cavalo-info h2').text().trim()
        ];
        
        for (const candidato of nomeCandidatos) {
          if (candidato && candidato.length > 3 && candidato.length < 100 && 
              !candidato.includes('ABCCC') && !candidato.includes('Studbook')) {
            resultado.nome = candidato;
            break;
          }
        }
      }
      
      // Tenta novamente com seletores mais específicos para registro
      if (!resultado.registro) {
        // Procura apenas em elementos que provavelmente contêm o registro
        $('.ficha-tecnica td, .infos-dados span, .dados-cavalo li').each((_, el) => {
          const texto = $(el).text().trim();
          // Verifica se o texto corresponde ao formato de registro (letra seguida por números)
          if (/^[A-Z][0-9]+$/i.test(texto)) {
            resultado.registro = texto;
            return false; // interrompe o loop each
          }
        });
      }
    } catch (e) {
      console.error('Falha nos seletores alternativos específicos');
    }
  }
  
  // Retorna dados parciais se pelo menos nome ou registro foram encontrados
  if (resultado.nome || resultado.registro) {
    return resultado;
  }
  
  return null;
}

/**
 * Verifica se os seletores estão funcionando para um HTML de exemplo
 * @param html HTML da página a ser testada
 * @returns Resultado do teste com informação de quais seletores funcionaram
 */
export function testarSeletores(html: string): { validos: string[], invalidos: string[] } {
  const $ = cheerio.load(html);
  const resultado = {
    validos: [] as string[],
    invalidos: [] as string[]
  };
  
  // Lista de seletores a serem testados
  const seletores = {
    'nome': '.wrap.cavalos-wrap h1',
    'registro': '.infos-cavalo tr:contains("SBB:") td:nth-child(2)',
    'sexo': '.infos-cavalo tr:contains("Sexo:") td:nth-child(2)',
    'nascimento': '.infos-cavalo tr:contains("Nascimento:") td:nth-child(2)',
    'pelagem': '.infos-cavalo tr:contains("Pelagem:") td:nth-child(2)',
    'criador': '.infos-cavalo tr:contains("Criador:") td:nth-child(2)',
    'proprietario': '.infos-cavalo tr:contains("Proprietário:") td:nth-child(2)',
    'pai': '.genealogia-cavalo tr:contains("Pai:") td:nth-child(2)',
    'mae': '.genealogia-cavalo tr:contains("Mãe:") td:nth-child(2)',
    'avoPaternoNome': '.genealogia-cavalo tr:contains("Avô paterno:") td:nth-child(2)',
    'avoMaternaNome': '.genealogia-cavalo tr:contains("Avó materna:") td:nth-child(2)',
    'foto': '.img-responsive',
    'tabela': '.respons__horses table tbody tr'
  };
  
  // Testar cada seletor
  for (const [campo, seletor] of Object.entries(seletores)) {
    const elementos = $(seletor);
    if (elementos.length > 0) {
      resultado.validos.push(campo);
    } else {
      resultado.invalidos.push(campo);
    }
  }
  
  return resultado;
}

// Exporta funções para uso em testes unitários
export default {
  extrairDadosComFallback,
  testarSeletores
};