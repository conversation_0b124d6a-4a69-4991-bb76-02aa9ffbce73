import { pgTable, text, timestamp, boolean, integer } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

/**
 * Esquema para tabela de tokens de acesso ao site da ABCCC
 */
export const abcccTokens = pgTable("abccc_tokens", {
  id: text("id").primaryKey().notNull(),
  token: text("token").notNull(),
  origem: text("origem").default("sistema"),
  data_criacao: timestamp("data_criacao").defaultNow(),
  ultimo_uso: timestamp("ultimo_uso"),
  is_active: boolean("is_active").default(true),
  sucessos: integer("sucessos").default(0),
  falhas: integer("falhas").default(0),
  observacoes: text("observacoes")
});

// Schema para inserção de novos tokens
export const insertAbcccTokenSchema = createInsertSchema(abcccTokens).omit({
  id: true,
  data_criacao: true,
  ultimo_uso: true,
  sucessos: true,
  falhas: true
});

// Schema para resposta de estatísticas de tokens
export const tokenStatsSchema = z.object({
  total_tokens: z.number(),
  tokens_validos: z.number(),
  tokens_invalidos: z.number(),
  top_tokens: z.array(
    z.object({
      id: z.string(),
      token: z.string(),
      origem: z.string(),
      sucessos: z.number(),
      falhas: z.number(),
      taxa_sucesso: z.number()
    })
  )
});

// Tipos para uso no frontend
export type AbcccToken = typeof abcccTokens.$inferSelect;
export type InsertAbcccToken = z.infer<typeof insertAbcccTokenSchema>;
export type TokenStats = z.infer<typeof tokenStatsSchema>;