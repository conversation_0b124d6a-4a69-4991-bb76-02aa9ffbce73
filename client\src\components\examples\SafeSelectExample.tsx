import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  SafeSelect, 
  SafeSelectItem, 
  useSafeSelect 
} from '@/components/ui/safe-select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * Exemplo de uso do SafeSelect e SafeSelectItem
 * 
 * Este componente demonstra como usar os componentes seguros de select
 * para evitar erros com valores vazios.
 */
export function SafeSelectExample() {
  // Estado para o select normal (que pode causar erros)
  const [normalValue, setNormalValue] = useState<string>('');
  
  // Estado para o SafeSelect
  const [safeValue, setSafeValue] = useState<string | null>(null);
  
  // Estado para o select com hook
  const [hookValue, setHookValue] = useState<string | null>(null);
  
  // Estado para o select com conversão para número
  const [numberValue, setNumberValue] = useState<number | null>(null);
  
  // Usar o hook useSafeSelect
  const hookSelect = useSafeSelect(hookValue, setHookValue, {
    emptyValue: null,
    fallbackValue: 'nao_selecionado',
    prefix: 'opcao'
  });
  
  // Usar o hook useSafeSelect com conversão para número
  const numberSelect = useSafeSelect(numberValue, setNumberValue, {
    emptyValue: null,
    fallbackValue: 'nao_selecionado',
    prefix: 'id',
    convertToNumber: true
  });
  
  // Função para limpar todos os valores
  const clearAll = () => {
    setNormalValue('');
    setSafeValue(null);
    setHookValue(null);
    setNumberValue(null);
  };
  
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Exemplo de Select Seguro</CardTitle>
        <CardDescription>
          Demonstração de como usar o SafeSelect e SafeSelectItem para evitar erros com valores vazios
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="normal">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="normal">Select Normal</TabsTrigger>
            <TabsTrigger value="safe">SafeSelect</TabsTrigger>
            <TabsTrigger value="hook">useSafeSelect</TabsTrigger>
            <TabsTrigger value="number">Conversão para Número</TabsTrigger>
          </TabsList>
          
          <TabsContent value="normal" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="normal-select">Select Normal (pode causar erros)</Label>
              <Select value={normalValue} onValueChange={setNormalValue}>
                <SelectTrigger id="normal-select">
                  <SelectValue placeholder="Selecione uma opção" />
                </SelectTrigger>
                <SelectContent>
                  {/* Este SelectItem com value="" causará erro */}
                  <SelectItem value="">Nenhuma opção</SelectItem>
                  <SelectItem value="opcao1">Opção 1</SelectItem>
                  <SelectItem value="opcao2">Opção 2</SelectItem>
                  <SelectItem value="opcao3">Opção 3</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-red-500">
                ⚠️ Este select usa um SelectItem com value="", o que causará erro.
              </p>
            </div>
            
            <div className="p-4 bg-gray-100 rounded-md">
              <p className="text-sm font-medium">Valor selecionado:</p>
              <pre className="mt-1 p-2 bg-white rounded border">
                {JSON.stringify(normalValue, null, 2) || "null"}
              </pre>
            </div>
          </TabsContent>
          
          <TabsContent value="safe" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="safe-select">SafeSelect (previne erros)</Label>
              <SafeSelect 
                value={safeValue} 
                onValueChange={setSafeValue}
                emptyValue={null}
              >
                <SelectTrigger id="safe-select">
                  <SelectValue placeholder="Selecione uma opção" />
                </SelectTrigger>
                <SelectContent>
                  <SafeSelectItem originalValue="" fallbackValue="nenhuma_opcao">
                    Nenhuma opção
                  </SafeSelectItem>
                  <SafeSelectItem value="opcao1">Opção 1</SafeSelectItem>
                  <SafeSelectItem value="opcao2">Opção 2</SafeSelectItem>
                  <SafeSelectItem value="opcao3">Opção 3</SafeSelectItem>
                </SelectContent>
              </SafeSelect>
              <p className="text-sm text-green-600">
                ✓ Este select usa SafeSelectItem, que previne erros com valores vazios.
              </p>
            </div>
            
            <div className="p-4 bg-gray-100 rounded-md">
              <p className="text-sm font-medium">Valor selecionado:</p>
              <pre className="mt-1 p-2 bg-white rounded border">
                {JSON.stringify(safeValue, null, 2) || "null"}
              </pre>
            </div>
          </TabsContent>
          
          <TabsContent value="hook" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="hook-select">Select com useSafeSelect</Label>
              <Select 
                value={hookSelect.value} 
                onValueChange={hookSelect.onChange}
              >
                <SelectTrigger id="hook-select">
                  <SelectValue placeholder="Selecione uma opção" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="opcao_nao_selecionado">Nenhuma opção</SelectItem>
                  <SelectItem value="opcao1">Opção 1</SelectItem>
                  <SelectItem value="opcao2">Opção 2</SelectItem>
                  <SelectItem value="opcao3">Opção 3</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-green-600">
                ✓ Este select usa o hook useSafeSelect, que facilita o uso seguro de selects.
              </p>
            </div>
            
            <div className="p-4 bg-gray-100 rounded-md">
              <p className="text-sm font-medium">Valor selecionado:</p>
              <pre className="mt-1 p-2 bg-white rounded border">
                {JSON.stringify(hookValue, null, 2) || "null"}
              </pre>
            </div>
          </TabsContent>
          
          <TabsContent value="number" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="number-select">Select com conversão para número</Label>
              <Select 
                value={numberSelect.value} 
                onValueChange={numberSelect.onChange}
              >
                <SelectTrigger id="number-select">
                  <SelectValue placeholder="Selecione um ID" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="id_nao_selecionado">Nenhum ID</SelectItem>
                  <SelectItem value="1">ID 1</SelectItem>
                  <SelectItem value="2">ID 2</SelectItem>
                  <SelectItem value="3">ID 3</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-green-600">
                ✓ Este select converte automaticamente o valor para número.
              </p>
            </div>
            
            <div className="p-4 bg-gray-100 rounded-md">
              <p className="text-sm font-medium">Valor selecionado:</p>
              <pre className="mt-1 p-2 bg-white rounded border">
                {JSON.stringify(numberValue, null, 2) || "null"}
              </pre>
              <p className="text-sm mt-2">
                <strong>Tipo:</strong> {numberValue === null ? "null" : typeof numberValue}
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={clearAll}>
          Limpar Todos os Valores
        </Button>
        <Button>
          Salvar
        </Button>
      </CardFooter>
    </Card>
  );
}

export default SafeSelectExample;
