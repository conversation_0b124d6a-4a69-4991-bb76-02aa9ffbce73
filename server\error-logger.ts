/**
 * Sistema de Logging de Erros Centralizado para EquiGestor AI
 * Captura, armazena e permite consulta de todos os erros do sistema
 */

import { z } from 'zod';
import { db } from './db.js';
import { sql } from 'drizzle-orm';

// Schema para logs de erro
export const ErrorLogSchema = z.object({
  id: z.string(),
  timestamp: z.string(),
  level: z.enum(['error', 'warn', 'info', 'debug']),
  module: z.string(),
  message: z.string(),
  stack: z.string().optional(),
  context: z.record(z.any()).optional(),
  user_id: z.number().optional(),
  request_id: z.string().optional(),
  endpoint: z.string().optional(),
  method: z.string().optional(),
  ip_address: z.string().optional(),
  user_agent: z.string().optional(),
});

export type ErrorLog = z.infer<typeof ErrorLogSchema>;

class ErrorLogger {
  private static instance: ErrorLogger;
  private logs: ErrorLog[] = [];
  private maxLogs = 1000; // Manter até 1000 logs em memória

  public static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  public log(
    level: 'error' | 'warn' | 'info' | 'debug',
    module: string,
    message: string,
    context?: {
      error?: Error;
      user_id?: number;
      request_id?: string;
      endpoint?: string;
      method?: string;
      ip_address?: string;
      user_agent?: string;
      data?: any;
    }
  ): string {
    const errorId = this.generateId();
    const timestamp = new Date().toISOString();

    const errorLog: ErrorLog = {
      id: errorId,
      timestamp,
      level,
      module,
      message,
      stack: context?.error?.stack,
      context: context?.data ? { data: context.data } : undefined,
      user_id: context?.user_id,
      request_id: context?.request_id,
      endpoint: context?.endpoint,
      method: context?.method,
      ip_address: context?.ip_address,
      user_agent: context?.user_agent,
    };

    // Adicionar ao array em memória
    this.logs.unshift(errorLog);
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Log no console para desenvolvimento
    const logMessage = `[${timestamp}] ${level.toUpperCase()} [${module}] ${message}`;
    if (level === 'error') {
      console.error(logMessage, context?.error?.stack || '');
    } else if (level === 'warn') {
      console.warn(logMessage);
    } else {
      console.log(logMessage);
    }

    // Tentar salvar no banco de dados (se disponível)
    this.saveToDatabase(errorLog).catch((dbError) => {
      console.warn('Falha ao salvar erro no banco:', dbError.message);
    });

    return errorId;
  }

  private async saveToDatabase(errorLog: ErrorLog): Promise<void> {
    try {
      await db.execute(sql`
        INSERT INTO error_logs (
          id, timestamp, level, module, message, stack, context, 
          user_id, request_id, endpoint, method, ip_address, user_agent
        ) VALUES (
          ${errorLog.id}, ${errorLog.timestamp}, ${errorLog.level}, 
          ${errorLog.module}, ${errorLog.message}, ${errorLog.stack}, 
          ${JSON.stringify(errorLog.context)}, ${errorLog.user_id}, 
          ${errorLog.request_id}, ${errorLog.endpoint}, ${errorLog.method}, 
          ${errorLog.ip_address}, ${errorLog.user_agent}
        )
      `);
    } catch (error) {
      // Silenciar erros de banco para evitar loops
    }
  }

  public error(module: string, message: string, context?: any): string {
    return this.log('error', module, message, context);
  }

  public warn(module: string, message: string, context?: any): string {
    return this.log('warn', module, message, context);
  }

  public info(module: string, message: string, context?: any): string {
    return this.log('info', module, message, context);
  }

  public debug(module: string, message: string, context?: any): string {
    return this.log('debug', module, message, context);
  }

  public getLogs(filter?: {
    level?: string;
    module?: string;
    limit?: number;
    user_id?: number;
  }): ErrorLog[] {
    let filteredLogs = this.logs;

    if (filter?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filter.level);
    }

    if (filter?.module) {
      filteredLogs = filteredLogs.filter(log => log.module.includes(filter.module!));
    }

    if (filter?.user_id) {
      filteredLogs = filteredLogs.filter(log => log.user_id === filter.user_id);
    }

    return filteredLogs.slice(0, filter?.limit || 100);
  }

  public getLogById(id: string): ErrorLog | undefined {
    return this.logs.find(log => log.id === id);
  }

  public getStats(): {
    total: number;
    byLevel: Record<string, number>;
    byModule: Record<string, number>;
    recent: number;
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const byLevel: Record<string, number> = {};
    const byModule: Record<string, number> = {};
    let recent = 0;

    this.logs.forEach(log => {
      // Contar por nível
      byLevel[log.level] = (byLevel[log.level] || 0) + 1;

      // Contar por módulo
      byModule[log.module] = (byModule[log.module] || 0) + 1;

      // Contar recentes (última hora)
      if (new Date(log.timestamp) > oneHourAgo) {
        recent++;
      }
    });

    return {
      total: this.logs.length,
      byLevel,
      byModule,
      recent,
    };
  }

  // Middleware para Express
  public expressMiddleware() {
    return (req: any, res: any, next: any) => {
      const originalSend = res.send;
      
      res.send = function(data: any) {
        if (res.statusCode >= 400) {
          ErrorLogger.getInstance().log(
            res.statusCode >= 500 ? 'error' : 'warn',
            'HTTP',
            `${res.statusCode} ${req.method} ${req.path}`,
            {
              user_id: req.user?.id,
              request_id: req.id,
              endpoint: req.path,
              method: req.method,
              ip_address: req.ip,
              user_agent: req.get('User-Agent'),
              data: { response: data, status: res.statusCode }
            }
          );
        }
        return originalSend.call(this, data);
      };

      next();
    };
  }
}

// Criar tabela se não existir
async function createErrorLogsTable() {
  try {
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS error_logs (
        id VARCHAR(255) PRIMARY KEY,
        timestamp TIMESTAMP NOT NULL,
        level VARCHAR(10) NOT NULL,
        module VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        stack TEXT,
        context JSONB,
        user_id INTEGER,
        request_id VARCHAR(255),
        endpoint VARCHAR(255),
        method VARCHAR(10),
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Criar índices para melhor performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_error_logs_timestamp ON error_logs(timestamp DESC)
    `);
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_error_logs_level ON error_logs(level)
    `);
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_error_logs_module ON error_logs(module)
    `);
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id)
    `);
  } catch (error) {
    console.warn('Não foi possível criar tabela de logs:', error);
  }
}

// Inicializar tabela
createErrorLogsTable();

export const logger = ErrorLogger.getInstance();
export default logger;