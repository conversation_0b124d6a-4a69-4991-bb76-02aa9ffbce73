/**
 * Ferramentas de depuração para o EquiGestor
 * Fornece botões e atalhos para acessar funcionalidades de depuração
 */

import React, { useState, useEffect } from 'react';
import { LogViewer } from './LogViewer';
import { logger, LogLevel } from '@/lib/logger';
import { Bug, Database, Laptop, List, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

/**
 * Props do componente de ferramentas de depuração
 */
interface DebugToolsProps {
  // Opções para controlar a visibilidade das ferramentas
  visible?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  showErrorCount?: boolean;
  enableKeyboardShortcut?: boolean;
}

/**
 * Componente principal de ferramentas de depuração
 */
export function DebugTools({
  visible = process.env.NODE_ENV !== 'production',
  position = 'bottom-right',
  showErrorCount = true,
  enableKeyboardShortcut = true
}: DebugToolsProps) {
  // Estados para controlar os modais
  const [logViewerOpen, setLogViewerOpen] = useState(false);
  
  // Estado para contagem de erros
  const [errorCount, setErrorCount] = useState(0);
  
  // Posicionamento do componente
  const getPositionClass = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };
  
  // Atualizar contador de erros
  useEffect(() => {
    // Definir handler para keypress
    const keyboardHandler = (e: KeyboardEvent) => {
      // Ctrl+Alt+D para abrir visualizador de logs
      if (enableKeyboardShortcut && e.ctrlKey && e.altKey && e.key === 'd') {
        e.preventDefault();
        setLogViewerOpen(true);
      }
    };
    
    // Atualizar contador de erros regularmente
    const updateErrorCount = () => {
      const logs = logger.getAllLogs();
      const errors = logs.filter(log => log.level === LogLevel.ERROR);
      setErrorCount(errors.length);
    };
    
    // Atribuir listeners
    if (visible) {
      window.addEventListener('keydown', keyboardHandler);
      
      // Atualizar contador inicialmente
      updateErrorCount();
      
      // Configurar intervalo para atualizar o contador
      const interval = setInterval(updateErrorCount, 5000);
      
      return () => {
        window.removeEventListener('keydown', keyboardHandler);
        clearInterval(interval);
      };
    }
  }, [visible, enableKeyboardShortcut]);
  
  // Se não for visível, não renderizar nada
  if (!visible) {
    return null;
  }
  
  return (
    <>
      {/* Botão flutuante para acessar ferramentas */}
      <div className={`fixed z-50 ${getPositionClass()}`}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="icon" 
              className="rounded-full shadow-md flex items-center justify-center h-12 w-12 bg-background/80 backdrop-blur-sm border-muted-foreground/40"
            >
              <Bug className="h-5 w-5" />
              {showErrorCount && errorCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center"
                >
                  {errorCount}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Ferramentas de Depuração</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setLogViewerOpen(true)}>
              <List className="mr-2 h-4 w-4" />
              <span>Visualizador de Logs</span>
              {errorCount > 0 && (
                <Badge variant="destructive" className="ml-auto">
                  {errorCount}
                </Badge>
              )}
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Database className="mr-2 h-4 w-4" />
              <span>Explorador de Estado</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Laptop className="mr-2 h-4 w-4" />
              <span>Informações do Ambiente</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>Configurações de Debug</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Componentes de modais */}
      <LogViewer
        open={logViewerOpen}
        onClose={() => setLogViewerOpen(false)}
      />
    </>
  );
}

export default DebugTools;