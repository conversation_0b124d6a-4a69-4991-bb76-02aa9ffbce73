import express from "express";
import cors from "cors";
import { createServer } from "http";
import { setupVite, serveStatic, log } from "./vite";

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({ 
    status: 'EquiGestor AI Online!', 
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Port configuration for external deployment
function getPort() {
  // Priority 1: Environment PORT variable (for external deployments)
  if (process.env.PORT) {
    const envPort = parseInt(process.env.PORT, 10);
    if (!isNaN(envPort) && envPort > 0) {
      console.log(`🌐 Using environment PORT: ${envPort}`);
      return envPort;
    }
  }

  // Priority 2: REPLIT_PORT for Replit environment
  if (process.env.REPLIT_PORT) {
    const replitPort = parseInt(process.env.REPLIT_PORT, 10);
    if (!isNaN(replitPort) && replitPort > 0) {
      console.log(`📱 Using REPLIT_PORT: ${replitPort}`);
      return replitPort;
    }
  }

  // Priority 3: Detect specific platforms
  if (process.env.REPL_ID || process.env.REPLIT_DB_URL) {
    console.log('📱 Replit detected, using port 5000');
    return 5000;
  }

  if (process.env.K_SERVICE || process.env.GOOGLE_CLOUD_PROJECT) {
    console.log('☁️ Google Cloud Run detected, using port 8080');
    return 8080;
  }

  // Default for development
  console.log('🏠 Development mode, using port 5000');
  return 5000;
}

const port = getPort();
const host = '0.0.0.0';
const server = createServer(app);

(async () => {
  try {
    console.log('🚀 Starting EquiGestor AI server...');
    
    // Initialize database connection first
    try {
      console.log('Initializing database connection...');
      const { dbHandler } = await import("./db-connection-handler");
      await dbHandler.connect();
      console.log('✅ Database connection initialized successfully');
    } catch (dbError: any) {
      console.error('❌ Database connection failed:', dbError?.message);
      console.log('🔄 Server will continue in offline mode');
    }
    
    // Add authentication routes BEFORE Vite setup
    try {
      console.log('🔄 Loading authentication routes...');
      const { addAuthRoutes } = await import("./auth-routes");
      addAuthRoutes(app);
      console.log('✅ Authentication routes loaded successfully');
    } catch (authError: any) {
      console.error('❌ Authentication routes failed to load:', authError?.message);
    }

    // Add API routes BEFORE Vite setup
    try {
      console.log('🔄 Loading API routes...');
      const { addApiRoutes } = await import("./routes");
      await addApiRoutes(app);
      console.log('✅ API routes loaded successfully');
    } catch (routeError: any) {
      console.warn('⚠️  API routes failed to load:', routeError?.message);
      console.log('🔄 Server running in limited mode');
    }

    // Add admin user management routes
    try {
      console.log('🔄 Loading admin user management routes...');
      const { db } = await import("./db");
      const { eq } = await import("drizzle-orm");
      const { users } = await import("../shared/schema");
      const { authenticateUser } = await import("./auth");



      // Middleware para verificar se é admin
      const requireAdmin = async (req: any, res: any, next: any) => {
        try {
          const userId = (req as any).user_id;
          const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
          
          if (!user.length || user[0].role !== 'ADMIN') {
            return res.status(403).json({ error: 'Acesso negado. Apenas administradores podem acessar este recurso.' });
          }
          
          (req as any).user = user[0];
          next();
        } catch (error) {
          console.error('Erro na verificação de admin:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      };

      // GET /api/users - Lista todos os usuários (apenas admin)
      app.get('/api/users', authenticateUser, requireAdmin, async (req: any, res: any) => {
        try {
          const allUsers = await db
            .select({
              id: users.id,
              name: users.name,
              username: users.username,
              email: users.email,
              role: users.role,
              created_at: users.created_at,
              updated_at: users.updated_at
            })
            .from(users)
            .orderBy(users.created_at);

          res.json(allUsers);
        } catch (error) {
          console.error('Erro ao buscar usuários:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // POST /api/users - Criar novo usuário (apenas admin)
      app.post('/api/users', authenticateUser, requireAdmin, async (req: any, res: any) => {
        try {
          const { name, username, email, password, role = 'USER' } = req.body;
          
          if (!name || !username || !email || !password) {
            return res.status(400).json({ error: 'Nome, usuário, email e senha são obrigatórios' });
          }

          // Verificar se username ou email já existem
          const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.username, username))
            .limit(1);

          if (existingUser.length > 0) {
            return res.status(409).json({ error: 'Nome de usuário já está em uso' });
          }

          const existingEmail = await db
            .select()
            .from(users)
            .where(eq(users.email, email))
            .limit(1);

          if (existingEmail.length > 0) {
            return res.status(409).json({ error: 'Email já está em uso' });
          }

          // Hash da senha
          const bcrypt = await import('bcrypt');
          const hashedPassword = await bcrypt.hash(password, 10);

          // Criar usuário
          const [newUser] = await db
            .insert(users)
            .values({
              name,
              username,
              email,
              password_hash: hashedPassword,
              role: role as 'USER' | 'ADMIN'
            })
            .returning({
              id: users.id,
              name: users.name,
              username: users.username,
              email: users.email,
              role: users.role,
              created_at: users.created_at,
              updated_at: users.updated_at
            });

          res.status(201).json(newUser);
        } catch (error) {
          console.error('Erro ao criar usuário:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // PUT /api/users/:id - Atualizar usuário (apenas admin)
      app.put('/api/users/:id', authenticateUser, requireAdmin, async (req: any, res: any) => {
        try {
          const userId = parseInt(req.params.id);
          const { name, email, role } = req.body;
          
          if (isNaN(userId)) {
            return res.status(400).json({ error: 'ID de usuário inválido' });
          }

          // Verificar se o usuário existe
          const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.id, userId))
            .limit(1);

          if (existingUser.length === 0) {
            return res.status(404).json({ error: 'Usuário não encontrado' });
          }

          // Atualizar usuário
          const [updatedUser] = await db
            .update(users)
            .set({
              ...(name && { name }),
              ...(email && { email }),
              ...(role && { role: role as 'USER' | 'ADMIN' }),
              updated_at: new Date()
            })
            .where(eq(users.id, userId))
            .returning({
              id: users.id,
              name: users.name,
              username: users.username,
              email: users.email,
              role: users.role,
              created_at: users.created_at,
              updated_at: users.updated_at
            });

          res.json(updatedUser);
        } catch (error) {
          console.error('Erro ao atualizar usuário:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // DELETE /api/users/:id - Deletar usuário (apenas admin)
      app.delete('/api/users/:id', authenticateUser, requireAdmin, async (req: any, res: any) => {
        try {
          const userId = parseInt(req.params.id);
          const currentUserId = (req as any).user_id;
          
          if (isNaN(userId)) {
            return res.status(400).json({ error: 'ID de usuário inválido' });
          }

          // Não permitir que admin delete a si mesmo
          if (userId === currentUserId) {
            return res.status(400).json({ error: 'Você não pode deletar sua própria conta' });
          }

          // Verificar se o usuário existe
          const existingUser = await db
            .select()
            .from(users)
            .where(eq(users.id, userId))
            .limit(1);

          if (existingUser.length === 0) {
            return res.status(404).json({ error: 'Usuário não encontrado' });
          }

          // Deletar usuário
          await db.delete(users).where(eq(users.id, userId));

          res.status(200).json({ message: 'Usuário deletado com sucesso' });
        } catch (error) {
          console.error('Erro ao deletar usuário:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      console.log('✅ Admin user management routes loaded successfully');
    } catch (adminRoutesError: any) {
      console.warn('⚠️  Admin user management routes failed to load:', adminRoutesError?.message);
    }

    // Add essential financial API routes as fallback
    try {
      console.log('🔄 Loading essential financial API routes...');
      const { db } = await import("./db");
      const { sum, eq, and, gte, lte } = await import("drizzle-orm");
      const { categorias_financeiras, lancamentos_financeiros, cavalos } = await import("../shared/schema");
      const { authenticateUser } = await import("./auth");

      // Financial categories endpoints
      app.get('/api/financeiro/categorias', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const categorias = await db
            .select()
            .from(categorias_financeiras)
            .where(eq(categorias_financeiras.user_id, user_id))
            .orderBy(categorias_financeiras.nome);
          res.json(categorias);
        } catch (error) {
          console.error('Erro ao buscar categorias:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.post('/api/financeiro/categorias', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const { nome, tipo, descricao, ativo = true } = req.body;
          
          if (!nome || !tipo) {
            return res.status(400).json({ error: 'Campos obrigatórios: nome, tipo' });
          }

          if (!['receita', 'despesa'].includes(tipo)) {
            return res.status(400).json({ error: 'Tipo deve ser "receita" ou "despesa"' });
          }

          const [categoria] = await db
            .insert(categorias_financeiras)
            .values({
              nome,
              tipo,
              descricao,
              ativo,
              user_id
            })
            .returning();
          
          res.status(201).json(categoria);
        } catch (error) {
          console.error('Erro ao criar categoria:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.put('/api/financeiro/categorias/:id', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const id = parseInt(req.params.id);
          const { nome, tipo, descricao, ativo } = req.body;
          
          if (isNaN(id)) {
            return res.status(400).json({ error: 'ID de categoria inválido' });
          }

          if (tipo && !['receita', 'despesa'].includes(tipo)) {
            return res.status(400).json({ error: 'Tipo deve ser "receita" ou "despesa"' });
          }

          const updateData: any = {};
          if (nome) updateData.nome = nome;
          if (tipo) updateData.tipo = tipo;
          if (descricao !== undefined) updateData.descricao = descricao;
          if (ativo !== undefined) updateData.ativo = ativo;

          const [categoria] = await db
            .update(categorias_financeiras)
            .set(updateData)
            .where(and(
              eq(categorias_financeiras.id, id),
              eq(categorias_financeiras.user_id, user_id)
            ))
            .returning();

          if (!categoria) {
            return res.status(404).json({ error: 'Categoria não encontrada' });
          }

          res.json(categoria);
        } catch (error) {
          console.error('Erro ao atualizar categoria:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.delete('/api/financeiro/categorias/:id', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const id = parseInt(req.params.id);
          
          if (isNaN(id)) {
            return res.status(400).json({ error: 'ID de categoria inválido' });
          }

          // Verificar se há lançamentos vinculados
          const lancamentosVinculados = await db
            .select({ count: eq(lancamentos_financeiros.categoria_id, id) })
            .from(lancamentos_financeiros)
            .where(and(
              eq(lancamentos_financeiros.categoria_id, id),
              eq(lancamentos_financeiros.user_id, user_id)
            ))
            .limit(1);

          if (lancamentosVinculados.length > 0) {
            return res.status(400).json({ 
              error: 'Não é possível excluir categoria que possui lançamentos vinculados' 
            });
          }

          const [categoria] = await db
            .delete(categorias_financeiras)
            .where(and(
              eq(categorias_financeiras.id, id),
              eq(categorias_financeiras.user_id, user_id)
            ))
            .returning();

          if (!categoria) {
            return res.status(404).json({ error: 'Categoria não encontrada' });
          }

          res.status(204).end();
        } catch (error) {
          console.error('Erro ao deletar categoria:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // Financial lancamentos endpoints
      app.get('/api/financeiro/lancamentos', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const lancamentos = await db
            .select({
              id: lancamentos_financeiros.id,
              data: lancamentos_financeiros.data,
              tipo: lancamentos_financeiros.tipo,
              categoria_id: lancamentos_financeiros.categoria_id,
              descricao: lancamentos_financeiros.descricao,
              valor: lancamentos_financeiros.valor,
              cavalo_id: lancamentos_financeiros.cavalo_id,
              observacoes: lancamentos_financeiros.observacoes,
              user_id: lancamentos_financeiros.user_id,
              created_at: lancamentos_financeiros.created_at,
              categoria_nome: categorias_financeiras.nome,
              cavalo_nome: cavalos.name
            })
            .from(lancamentos_financeiros)
            .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
            .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
            .where(eq(lancamentos_financeiros.user_id, user_id))
            .orderBy(lancamentos_financeiros.created_at);
          res.json(lancamentos);
        } catch (error) {
          console.error('Erro ao buscar lançamentos:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.post('/api/financeiro/lancamentos', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const { data, tipo, categoria_id, descricao, valor, cavalo_id, observacoes } = req.body;
          
          if (!data || !tipo || !categoria_id || !descricao || valor === undefined) {
            return res.status(400).json({ error: 'Campos obrigatórios: data, tipo, categoria_id, descricao, valor' });
          }

          const [lancamento] = await db
            .insert(lancamentos_financeiros)
            .values({
              data,
              tipo,
              categoria_id: parseInt(categoria_id),
              descricao,
              valor: parseFloat(valor),
              cavalo_id: cavalo_id ? parseInt(cavalo_id) : null,
              observacoes,
              user_id
            })
            .returning();
          
          res.status(201).json(lancamento);
        } catch (error) {
          console.error('Erro ao criar lançamento:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.put('/api/financeiro/lancamentos/:id', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const id = parseInt(req.params.id);
          const { data, tipo, categoria_id, descricao, valor, cavalo_id, observacoes } = req.body;
          
          if (isNaN(id)) {
            return res.status(400).json({ error: 'ID de lançamento inválido' });
          }

          const updateData: any = {};
          if (data) updateData.data = data;
          if (tipo) updateData.tipo = tipo;
          if (categoria_id) updateData.categoria_id = parseInt(categoria_id);
          if (descricao) updateData.descricao = descricao;
          if (valor !== undefined) updateData.valor = parseFloat(valor);
          if (cavalo_id !== undefined) updateData.cavalo_id = cavalo_id ? parseInt(cavalo_id) : null;
          if (observacoes !== undefined) updateData.observacoes = observacoes;

          const [lancamento] = await db
            .update(lancamentos_financeiros)
            .set(updateData)
            .where(and(
              eq(lancamentos_financeiros.id, id),
              eq(lancamentos_financeiros.user_id, user_id)
            ))
            .returning();

          if (!lancamento) {
            return res.status(404).json({ error: 'Lançamento não encontrado' });
          }

          res.json(lancamento);
        } catch (error) {
          console.error('Erro ao atualizar lançamento:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      app.delete('/api/financeiro/lancamentos/:id', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const id = parseInt(req.params.id);
          
          if (isNaN(id)) {
            return res.status(400).json({ error: 'ID de lançamento inválido' });
          }

          const [lancamento] = await db
            .delete(lancamentos_financeiros)
            .where(and(
              eq(lancamentos_financeiros.id, id),
              eq(lancamentos_financeiros.user_id, user_id)
            ))
            .returning();

          if (!lancamento) {
            return res.status(404).json({ error: 'Lançamento não encontrado' });
          }

          res.status(204).end();
        } catch (error) {
          console.error('Erro ao deletar lançamento:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // Monthly report endpoint - the critical one that was failing
      app.get('/api/financeiro/relatorio-mensal', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          const { mes, ano } = req.query;
          
          if (!mes || !ano) {
            return res.status(400).json({ error: 'Mês e ano são obrigatórios' });
          }
          
          const mesInt = parseInt(mes as string);
          const anoInt = parseInt(ano as string);
          
          if (mesInt < 1 || mesInt > 12) {
            return res.status(400).json({ error: 'Mês deve estar entre 1 e 12' });
          }
          
          const dataInicio = `${anoInt}-${mesInt.toString().padStart(2, '0')}-01`;
          const ultimoDia = new Date(anoInt, mesInt, 0).getDate();
          const dataFim = `${anoInt}-${mesInt.toString().padStart(2, '0')}-${ultimoDia}`;
          
          // Get income totals
          const receitasResult = await db
            .select({ total: sum(lancamentos_financeiros.valor) })
            .from(lancamentos_financeiros)
            .where(and(
              eq(lancamentos_financeiros.user_id, user_id),
              eq(lancamentos_financeiros.tipo, 'receita'),
              gte(lancamentos_financeiros.data, dataInicio),
              lte(lancamentos_financeiros.data, dataFim)
            ));
          
          // Get expense totals
          const despesasResult = await db
            .select({ total: sum(lancamentos_financeiros.valor) })
            .from(lancamentos_financeiros)
            .where(and(
              eq(lancamentos_financeiros.user_id, user_id),
              eq(lancamentos_financeiros.tipo, 'despesa'),
              gte(lancamentos_financeiros.data, dataInicio),
              lte(lancamentos_financeiros.data, dataFim)
            ));

          // Get category breakdown
          const categoriaBreakdown = await db
            .select({
              categoria_id: lancamentos_financeiros.categoria_id,
              categoria_nome: categorias_financeiras.nome,
              tipo: lancamentos_financeiros.tipo,
              total: sum(lancamentos_financeiros.valor)
            })
            .from(lancamentos_financeiros)
            .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
            .where(and(
              eq(lancamentos_financeiros.user_id, user_id),
              gte(lancamentos_financeiros.data, dataInicio),
              lte(lancamentos_financeiros.data, dataFim)
            ))
            .groupBy(lancamentos_financeiros.categoria_id, categorias_financeiras.nome, lancamentos_financeiros.tipo);

          const receitas = Number(receitasResult[0]?.total || 0);
          const despesas = Number(despesasResult[0]?.total || 0);
          const saldo = receitas - despesas;

          const relatorio = {
            periodo: { mes: mesInt, ano: anoInt },
            resumo: {
              receitas,
              despesas,
              saldo
            },
            porCategoria: categoriaBreakdown.map(cat => ({
              categoria_id: cat.categoria_id,
              categoria_nome: cat.categoria_nome || 'Sem categoria',
              tipo: cat.tipo,
              total: Number(cat.total || 0)
            })),
            porCavalo: [] // Empty for now
          };
          
          res.json(relatorio);
        } catch (error) {
          console.error('Erro ao gerar relatório mensal:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      // Initialize categories endpoint
      app.post('/api/financeiro/init-categorias', authenticateUser, async (req: any, res: any) => {
        try {
          const user_id = req.user?.id || 1;
          
          const categoriasPadrao = [
            { nome: 'Alimentação', tipo: 'despesa' as const, descricao: 'Ração, feno, concentrados', ativo: true, user_id },
            { nome: 'Medicamentos', tipo: 'despesa' as const, descricao: 'Medicamentos e suplementos', ativo: true, user_id },
            { nome: 'Serviços Veterinários', tipo: 'despesa' as const, descricao: 'Consultas e procedimentos veterinários', ativo: true, user_id },
            { nome: 'Ferrageamento', tipo: 'despesa' as const, descricao: 'Serviços de ferrageamento', ativo: true, user_id },
            { nome: 'Pensão', tipo: 'receita' as const, descricao: 'Receita de pensão de cavalos', ativo: true, user_id },
            { nome: 'Serviços', tipo: 'receita' as const, descricao: 'Prestação de serviços equinos', ativo: true, user_id }
          ];
          
          await db.insert(categorias_financeiras).values(categoriasPadrao);
          
          res.json({ message: 'Categorias padrão inicializadas com sucesso' });
        } catch (error) {
          console.error('Erro ao inicializar categorias:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      console.log('✅ Essential financial API routes loaded successfully');
    } catch (financialRoutesError: any) {
      console.warn('⚠️  Financial API routes failed to load:', financialRoutesError?.message);
    }

    // Setup Vite for development AFTER API routes
    const nodeEnv = process.env.NODE_ENV || "development";
    if (nodeEnv === "development") {
      await setupVite(app, server);
    } else {
      serveStatic(app);
    }

    // Start server
    server.listen(port, host, () => {
      console.log(`🎉 EquiGestor AI server running on ${host}:${port}`);
      console.log(`⚙️  Environment: ${nodeEnv}`);
      console.log(`🔗 API Documentation: http://${host}:${port}/api/health`);
      console.log(`📡 External access: ${process.env.REPL_SLUG ? `https://${process.env.REPL_SLUG}.${process.env.REPL_OWNER}.repl.co` : 'localhost'}`);
      log(`serving on port ${port}`);
    });

    // Error handling
    server.on('error', (error: any) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`💥 Port ${port} is already in use`);
        process.exit(1);
      }
    });

  } catch (error) {
    console.error('❌ Critical startup error:', error);
    process.exit(1);
  }
})();

export default app;