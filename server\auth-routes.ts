import express from 'express';
import { Pool, neonConfig } from '@neondatabase/serverless';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import ws from 'ws';
import { validate, authValidation } from './validation';
import { getModuleLogger } from './logger';

const logger = getModuleLogger('auth-routes');
const JWT_SECRET = process.env.JWT_SECRET || "default-jwt-secret";

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

export function addAuthRoutes(app: express.Application) {
  // Signup endpoint
  app.post('/api/auth/signup', validate(authValidation.signup), async (req, res) => {
    try {
      logger.info("Signup attempt", { username: req.body.username, email: req.body.email });
      const { name, username, email, password } = req.body;

      const client = await pool.connect();

      try {
        // Verificar se o usuário já existe
        const existingUser = await client.query(
          'SELECT username FROM users WHERE username = $1',
          [username]
        );

        if (existingUser.rows.length > 0) {
          client.release();
          return res.status(400).json({
            message: "Nome de usuário já está em uso"
          });
        }

        // Hash da senha
        const hashedPassword = await bcrypt.hash(password, 10);

        logger.info("Creating user", { username, email });

        // Criar usuário
        const result = await client.query(
          'INSERT INTO users (name, username, email, password_hash, role) VALUES ($1, $2, $3, $4, $5) RETURNING id, username, email, name',
          [name, username, email, hashedPassword, 'USER']
        );

        const newUser = result.rows[0];
        logger.info("User created successfully", { id: newUser.id, username: newUser.username });

        // Generate proper JWT token with user information
        const tokenPayload = {
          id: newUser.id,
          user_id: newUser.id,
          userId: newUser.id,
          username: newUser.username,
          email: newUser.email,
          role: 'USER',
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
        };

        const token = jwt.sign(tokenPayload, JWT_SECRET);

        // Resposta de sucesso
        res.status(201).json({
          message: "Usuário criado com sucesso",
          user: {
            id: newUser.id,
            name: newUser.name,
            username: newUser.username,
            email: newUser.email,
            accessLevel: 'user',
            isActive: true
          },
          token: token,
          expiration: Date.now() + (24 * 60 * 60 * 1000)
        });

        client.release();

      } catch (dbError) {
        client.release();
        throw dbError;
      }

    } catch (error) {
      logger.error("Erro no signup:", error);
      res.status(500).json({
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Logout endpoint
  app.post('/api/auth/logout', (req, res) => {
    res.json({ message: "Logout successful" });
  });

  // User verification endpoint
  app.get('/api/auth/user', (req, res) => {
    // For now, return unauthorized - proper session handling would go here
    res.status(401).json({ message: "Unauthorized" });
  });
}
