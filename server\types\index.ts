// Core type definitions for the backend
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  password_hash: string;
  role: 'ADMIN' | 'USER';
  avatar_url?: string;
  flags?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

export interface Horse {
  id: number;
  name: string;
  breed?: string;
  birth_date?: Date;
  sexo?: 'Macho' | 'Fêmea' | 'Macho (Castrado)' | 'Garanhão' | 'Égua';
  cor?: string;
  pelagem_id?: number;
  status: 'ativo' | 'inativo' | 'vendido' | 'falecido' | 'nao_informado' | 'competicao' | 'reproducao';
  user_id: number;
  created_at: Date;
  notes?: string;
  peso?: number;
  altura?: number;
  data_entrada?: Date;
  data_saida?: Date;
  motivo_saida?: string;
  numero_registro?: string;
  origem?: string;
  criador?: string;
  proprietario?: string;
  valor_compra?: string;
  data_compra?: Date;
  inspetor?: string;
  is_external?: boolean;
  pai_id?: number;
  mae_id?: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  statusCode: number;
}

export interface QueryResult<T> {
  data: T[];
  count: number;
  success: boolean;
  error?: ApiError;
}

export interface ServiceResponse<T> {
  data?: T;
  success: boolean;
  error?: ApiError;
}

export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterOptions {
  search?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  userId?: number;
}