// Interface para dados de cavalo

export interface Cavalo {
  id: number;
  name: string;
  breed?: string;
  birth_date?: string;
  user_id: number;
  
  // Dados físicos
  peso?: number;
  altura?: number;
  sexo?: string;
  cor?: string; // Pelagem do cavalo
  
  // Status e identificação
  status?: string;
  registroNumero?: string;
  chip?: string;
  
  // Dados de criação
  criador?: string;
  proprietario?: string;
  
  // Família
  pai_id?: number;
  mae_id?: number;
  
  // Arquivos e observações
  fotoUrl?: string;
  notes?: string;
  
  // Metadados
  created_at?: string;
  updatedAt?: string;
}