import { useState } from 'react';
import { useLocation, Link } from 'wouter';
import { LayoutWrapper } from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Plus, Edit, Trash2, Filter, Search,
  ArrowDownRight, ArrowUpRight, RefreshCw
} from 'lucide-react';
import { formatMoney } from "@/utils/finance";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

/**
 * Interface para centros de custo/categorias financeiras
 */
interface CategoriaCusto {
  id: number;
  nome: string;
  tipo: 'receita' | 'despesa';
  descricao: string;
  valorTotal: number;
  countLancamentos: number;
  ativo: boolean;
}

/**
 * Página de Centros de Custos (Categorias Financeiras)
 */
export default function CentrosCustoPage() {
  const { toast } = useToast();
  const [tipoFiltro, setTipoFiltro] = useState<string>("todos");
  const [statusFiltro, setStatusFiltro] = useState<string>("todos");
  const [termoBusca, setTermoBusca] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tabAtual, setTabAtual] = useState<string>("todos");
  
  const [formData, setFormData] = useState<Partial<CategoriaCusto>>({
    tipo: 'despesa',
    ativo: true
  });
  
  // Dados simulados para demonstração
  const categorias: CategoriaCusto[] = [
    {
      id: 1,
      nome: 'Alimentação',
      tipo: 'despesa',
      descricao: 'Ração, feno, suplementos e outros alimentos',
      valorTotal: 5750.80,
      countLancamentos: 12,
      ativo: true
    },
    {
      id: 2,
      nome: 'Medicamentos',
      tipo: 'despesa',
      descricao: 'Remédios, vacinas e outros medicamentos',
      valorTotal: 2840.30,
      countLancamentos: 8,
      ativo: true
    },
    {
      id: 3,
      nome: 'Serviços Veterinários',
      tipo: 'despesa',
      descricao: 'Consultas e procedimentos veterinários',
      valorTotal: 3200.00,
      countLancamentos: 5,
      ativo: true
    },
    {
      id: 4,
      nome: 'Ferrageamento',
      tipo: 'despesa',
      descricao: 'Serviços de ferrageamento e casqueamento',
      valorTotal: 1800.00,
      countLancamentos: 6,
      ativo: true
    },
    {
      id: 5,
      nome: 'Pensão',
      tipo: 'receita',
      descricao: 'Receitas de pensionamento de cavalos',
      valorTotal: 12500.00,
      countLancamentos: 5,
      ativo: true
    },
    {
      id: 6,
      nome: 'Serviços',
      tipo: 'receita',
      descricao: 'Aulas, treinamentos e outros serviços',
      valorTotal: 8350.00,
      countLancamentos: 22,
      ativo: true
    },
    {
      id: 7,
      nome: 'Competições',
      tipo: 'despesa',
      descricao: 'Inscrições e gastos com competições',
      valorTotal: 2400.00,
      countLancamentos: 3,
      ativo: true
    },
    {
      id: 8,
      nome: 'Manutenção',
      tipo: 'despesa',
      descricao: 'Manutenção de instalações e equipamentos',
      valorTotal: 1250.00,
      countLancamentos: 4,
      ativo: false
    },
    {
      id: 9,
      nome: 'Vendas',
      tipo: 'receita',
      descricao: 'Venda de cavalos e equipamentos',
      valorTotal: 25000.00,
      countLancamentos: 2,
      ativo: true
    },
  ];
  
  // Aplicar filtros
  const categoriasFiltradas = categorias.filter(categoria => {
    // Filtro por tipo
    const tipoMatch = tipoFiltro === 'todos' || categoria.tipo === tipoFiltro;
    
    // Filtro por status (ativo/inativo)
    const statusMatch = statusFiltro === 'todos' || 
                      (statusFiltro === 'ativo' && categoria.ativo) ||
                      (statusFiltro === 'inativo' && !categoria.ativo);
    
    // Filtro por aba
    const tabMatch = tabAtual === 'todos' || 
                    (tabAtual === 'receitas' && categoria.tipo === 'receita') ||
                    (tabAtual === 'despesas' && categoria.tipo === 'despesa');
    
    // Filtro por busca
    const buscaMatch = termoBusca === '' || 
                      categoria.nome.toLowerCase().includes(termoBusca.toLowerCase()) ||
                      categoria.descricao.toLowerCase().includes(termoBusca.toLowerCase());
    
    return tipoMatch && statusMatch && tabMatch && buscaMatch;
  });
  
  // Totais
  const totalReceitas = categorias
    .filter(c => c.tipo === 'receita')
    .reduce((sum, c) => sum + c.valorTotal, 0);
  
  const totalDespesas = categorias
    .filter(c => c.tipo === 'despesa')
    .reduce((sum, c) => sum + c.valorTotal, 0);
  
  
  // Obter classes de estilo para o tipo
  const getTipoClasses = (tipo: string, forText = true) => {
    if (tipo === 'receita') {
      return forText ? 'text-green-600' : 'bg-green-100 text-green-700 border-green-200';
    } else {
      return forText ? 'text-red-600' : 'bg-red-100 text-red-700 border-red-200';
    }
  };
  
  // Limpar filtros
  const limparFiltros = () => {
    setTipoFiltro("todos");
    setStatusFiltro("todos");
    setTermoBusca("");
  };
  
  // Funções para o formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Aqui você implementaria a lógica para salvar a categoria
    toast({
      title: "Categoria salva com sucesso",
      description: `${formData.nome} foi ${formData.id ? 'atualizada' : 'adicionada'} como categoria de ${formData.tipo === 'receita' ? 'receita' : 'despesa'}.`,
    });
    
    setDialogOpen(false);
    setFormData({ 
      tipo: 'despesa',
      ativo: true
    });
  };
  
  // Editar categoria
  const handleEdit = (categoria: CategoriaCusto) => {
    setFormData(categoria);
    setDialogOpen(true);
  };
  
  // Excluir categoria
  const handleDelete = (id: number) => {
    // Aqui você implementaria a lógica para excluir a categoria
    toast({
      title: "Categoria excluída com sucesso",
      description: "A categoria foi removida permanentemente.",
      variant: "destructive"
    });
  };
  
  // Alternar status da categoria
  const toggleStatus = (id: number, novoStatus: boolean) => {
    // Aqui você implementaria a lógica para alternar o status
    toast({
      title: novoStatus ? "Categoria ativada" : "Categoria desativada",
      description: `A categoria foi ${novoStatus ? 'ativada' : 'desativada'} com sucesso.`,
    });
  };
  
  // Lidar com mudanças no formulário
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  return (
    <LayoutWrapper pageTitle="Centros de Custos" showBackButton backUrl="/financeiro">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-blue-700">Centros de Custos</h1>
            <p className="text-gray-600 mt-1">
              Gerencie suas categorias de receitas e despesas
            </p>
          </div>
          
          <div className="flex gap-3 mt-4 md:mt-0">
            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 h-4 w-4" /> Nova Categoria
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    {formData.id ? 'Editar Categoria' : 'Nova Categoria'}
                  </DialogTitle>
                  <DialogDescription>
                    {formData.id 
                      ? 'Edite os detalhes da categoria.'
                      : 'Preencha os dados para criar uma nova categoria.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="nome">Nome</Label>
                        <Input 
                          id="nome" 
                          name="nome" 
                          placeholder="Nome da categoria"
                          value={formData.nome || ''}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="tipo">Tipo</Label>
                        <Select 
                          name="tipo" 
                          value={formData.tipo} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, tipo: value as 'receita' | 'despesa' }))}
                        >
                          <SelectTrigger id="tipo">
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent position="popper">
                            <SelectItem value="receita">Receita</SelectItem>
                            <SelectItem value="despesa">Despesa</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="descricao">Descrição</Label>
                      <Input 
                        id="descricao" 
                        name="descricao" 
                        placeholder="Descrição da categoria"
                        value={formData.descricao || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="ativo"
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        checked={formData.ativo}
                        onChange={(e) => setFormData(prev => ({ ...prev, ativo: e.target.checked }))}
                      />
                      <Label htmlFor="ativo" className="text-sm font-medium">
                        Categoria ativa
                      </Label>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setDialogOpen(false)}
                    >
                      Cancelar
                    </Button>
                    <Button type="submit">
                      {formData.id ? 'Atualizar' : 'Cadastrar'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Cards de resumo */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card className="border-green-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-green-700">Total Receitas</p>
                <p className="text-2xl font-bold text-green-600">{formatMoney(totalReceitas)}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {categorias.filter(c => c.tipo === 'receita').length} categorias de receita
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <ArrowDownRight className="h-5 w-5 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-red-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-red-700">Total Despesas</p>
                <p className="text-2xl font-bold text-red-600">{formatMoney(totalDespesas)}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {categorias.filter(c => c.tipo === 'despesa').length} categorias de despesa
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <ArrowUpRight className="h-5 w-5 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Filter className="h-5 w-5 mr-1.5" />
              Filtros
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Tipo</Label>
                <Select value={tipoFiltro} onValueChange={setTipoFiltro}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="receita">Receitas</SelectItem>
                    <SelectItem value="despesa">Despesas</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Status</Label>
                <Select value={statusFiltro} onValueChange={setStatusFiltro}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="ativo">Ativos</SelectItem>
                    <SelectItem value="inativo">Inativos</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Busca</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Input 
                    placeholder="Buscar..." 
                    className="pl-10"
                    value={termoBusca}
                    onChange={(e) => setTermoBusca(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={limparFiltros}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Limpar Filtros
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabs e Tabela */}
        <Card>
          <CardHeader className="pb-0">
            <CardTitle>Categorias</CardTitle>
            <CardDescription>
              Gerencie as categorias para classificação de lançamentos
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-6">
            <Tabs defaultValue="todos" className="w-full" onValueChange={setTabAtual}>
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="todos">Todos</TabsTrigger>
                <TabsTrigger value="receitas">Receitas</TabsTrigger>
                <TabsTrigger value="despesas">Despesas</TabsTrigger>
              </TabsList>
              
              <TabsContent value="todos" className="mt-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">Nome</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead className="hidden md:table-cell">Descrição</TableHead>
                      <TableHead>Lançamentos</TableHead>
                      <TableHead className="text-right">Valor Total</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categoriasFiltradas.map((categoria) => (
                      <TableRow key={categoria.id}>
                        <TableCell className="font-medium">{categoria.nome}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getTipoClasses(categoria.tipo, false)}>
                            {categoria.tipo === 'receita' ? 'Receita' : 'Despesa'}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell max-w-[200px] truncate">
                          {categoria.descricao}
                        </TableCell>
                        <TableCell>{categoria.countLancamentos}</TableCell>
                        <TableCell className="text-right">
                          <span className={getTipoClasses(categoria.tipo)}>
                            {formatMoney(categoria.valorTotal)}
                          </span>
                        </TableCell>
                        <TableCell className="text-center">
                          <Badge variant={categoria.ativo ? 'default' : 'secondary'}>
                            {categoria.ativo ? 'Ativo' : 'Inativo'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8" 
                              onClick={() => handleEdit(categoria)}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Editar</span>
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8" 
                              onClick={() => toggleStatus(categoria.id, !categoria.ativo)}
                            >
                              <span className={`inline-block h-3 w-3 rounded-full ${categoria.ativo ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                              <span className="sr-only">Alternar status</span>
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50" 
                              onClick={() => handleDelete(categoria.id)}
                              disabled={categoria.countLancamentos > 0}
                              title={categoria.countLancamentos > 0 ? "Não é possível excluir categorias com lançamentos" : "Excluir categoria"}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Remover</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                    
                    {categoriasFiltradas.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          Nenhuma categoria encontrada com os filtros aplicados.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TabsContent>
              
              <TabsContent value="receitas" className="mt-0">
                {/* Conteúdo idêntico, filtrado por receitas */}
              </TabsContent>
              
              <TabsContent value="despesas" className="mt-0">
                {/* Conteúdo idêntico, filtrado por despesas */}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}