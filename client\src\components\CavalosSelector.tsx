import { useState, useEffect } from 'react';
import { <PERSON><PERSON>lo } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { 
  Squirrel, Search, Check, PlusCircle, ChevronDown, RotateCw, Filter 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Link } from 'wouter';

interface CavalosSelectorProps {
  onChange?: (cavaloId: number | null) => void;
  value?: number | null;
  includeAllOption?: boolean;
  label?: string;
  className?: string;
  placeholder?: string;
}

/**
 * Componente CavalosSelector
 * 
 * Dropdown seletor de cavalos com busca e opção para adicionar novo
 */
export function CavalosSelector({
  onChange,
  value = null,
  includeAllOption = true,
  label = "Selecionar Cavalo",
  className = "",
  placeholder = "Buscar cavalo..."
}: CavalosSelectorProps) {
  const [cavalos, setCavalos] = useState<Cavalo[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  // Obter o cavalo selecionado
  const selectedCavalo = value 
    ? cavalos.find(cavalo => cavalo.id === value) 
    : null;

  // Buscar lista de cavalos
  useEffect(() => {
    const fetchCavalos = async () => {
      try {
        setLoading(true);
        
        // Verificar se o usuário está autenticado e recuperar ID
        const user = localStorage.getItem('user');
        if (!user) {
          console.error("Usuário não autenticado");
          toast({
            title: "Erro de autenticação",
            description: "Por favor, faça login novamente",
            variant: "destructive"
          });
          
          // Não buscar cavalos se não houver usuário autenticado
          return;
        }
        
        // Obter ID do usuário para passar no cabeçalho
        const userData = JSON.parse(user);
        const user_id = userData.id;
        
        if (!user_id) {
          console.error("ID de usuário não encontrado");
          toast({
            title: "Erro de autenticação",
            description: "Dados de usuário inválidos, faça login novamente",
            variant: "destructive"
          });
          return;
        }
        
        // Configurar cabeçalhos com ID do usuário
        const headers = new Headers();
        headers.set('user-id', user_id.toString());
        
        // Usar o apiRequest com cabeçalhos personalizados
        const data = await apiRequest<Cavalo[]>('/api/cavalos', 'GET', undefined, { headers });
        
        // Verificar e processar a resposta
        if (Array.isArray(data)) {
          setCavalos(data);
        } else {
          console.error("Resposta da API em formato inválido:", data);
          setCavalos([]);
        }
      } catch (error) {
        console.error("Erro ao buscar cavalos:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar a lista de cavalos",
          variant: "destructive"
        });
        setCavalos([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCavalos();
  }, [toast]);

  // Filtrar cavalos com base na busca
  const filteredCavalos = searchQuery 
    ? cavalos.filter(cavalo => 
        cavalo.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
        (cavalo.breed && cavalo.breed.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : cavalos;

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedCavalo ? (
              <div className="flex items-center">
                <Squirrel className="mr-2 h-4 w-4 text-primary" />
                <span>{selectedCavalo.name}</span>
                {selectedCavalo.breed && (
                  <Badge variant="outline" className="ml-2 text-xs">
                    {selectedCavalo.breed}
                  </Badge>
                )}
              </div>
            ) : value === null && includeAllOption ? (
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>Todos os cavalos</span>
              </div>
            ) : (
              label
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" style={{ width: "var(--radix-popover-trigger-width)" }}>
          <Command>
            <CommandInput 
              placeholder={placeholder} 
              value={searchQuery} 
              onValueChange={setSearchQuery}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty className="p-2 text-center text-sm">
                {loading ? (
                  <div className="flex items-center justify-center py-2">
                    <RotateCw className="h-4 w-4 animate-spin mr-2" />
                    <span>Carregando...</span>
                  </div>
                ) : (
                  <>
                    Nenhum cavalo encontrado.
                    <Link href="/cavalo/cadastro">
                      <div>
                        <Button 
                          variant="link" 
                          className="p-0 h-auto text-xs text-primary hover:text-primary/70"
                        >
                          Adicionar novo?
                        </Button>
                      </div>
                    </Link>
                  </>
                )}
              </CommandEmpty>
              <CommandGroup heading="Seus Cavalos">
                {includeAllOption && (
                  <CommandItem
                    value="all-horses"
                    onSelect={() => {
                      onChange && onChange(null);
                      setOpen(false);
                    }}
                    className="flex items-center"
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    <span>Todos os cavalos</span>
                    {value === null && <Check className="ml-auto h-4 w-4" />}
                  </CommandItem>
                )}
                
                <ScrollArea className="h-72">
                  {filteredCavalos.map(cavalo => (
                    <CommandItem
                      key={cavalo.id}
                      value={cavalo.name}
                      onSelect={() => {
                        onChange && onChange(cavalo.id);
                        setOpen(false);
                      }}
                      className="flex items-center"
                    >
                      <Squirrel className="mr-2 h-4 w-4 text-primary" />
                      <div className="flex flex-col">
                        <span>{cavalo.name}</span>
                        <div className="flex items-center mt-1">
                          {cavalo.breed && (
                            <Badge variant="outline" className="text-xs mr-1">
                              {cavalo.breed}
                            </Badge>
                          )}
                          {cavalo.sexo && (
                            <Badge variant="outline" className={`text-xs ${
                              cavalo.sexo.toLowerCase().includes('fêmea') || 
                              cavalo.sexo.toLowerCase().includes('femea') 
                                ? 'bg-pink-50 text-pink-800 border-pink-200' 
                                : 'bg-blue-50 text-blue-800 border-blue-200'
                            }`}>
                              {cavalo.sexo}
                            </Badge>
                          )}
                        </div>
                      </div>
                      {value === cavalo.id && <Check className="ml-auto h-4 w-4" />}
                    </CommandItem>
                  ))}
                </ScrollArea>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <Link href="/cavalo/cadastro">
                  <CommandItem
                    onSelect={() => {
                      setOpen(false);
                    }}
                    className="flex items-center text-primary"
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    <span>Adicionar novo cavalo</span>
                  </CommandItem>
                </Link>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default CavalosSelector;