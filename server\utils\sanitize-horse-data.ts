/**
 * Utility function to sanitize horse data before database operations
 * Removes invalid field names that don't exist in the PostgreSQL cavalos table
 */

// Valid fields that exist in the PostgreSQL cavalos table
const VALID_HORSE_FIELDS = [
  'name', 'breed', 'birth_date', 'sexo', 'cor', 'pelagem_id', 'status', 
  'user_id', 'notes', 'peso', 'altura', 'data_entrada', 'data_saida', 
  'motivo_saida', 'numero_registro', 'origem', 'criador', 'proprietario', 
  'valor_compra', 'data_compra', 'inspetor', 'is_external', 'pai_id', 'mae_id'
];

// Fields that should be removed from horse data before database operations
const FIELDS_TO_REMOVE = [
  'pai_nome', 'mae_nome', 'avo_paterno', 'avo_materno', 'avoPaterno', 'avoMaterno',
  'pai', 'mae', 'genealogia', 'genealogy', 'avo_paterno_nome', 'avo_materno_nome',
  'avoPaternoNome', 'avoMaternoNome', 'avoPaternoId', 'avoMaternoId',
  'avo_paterno_id', 'avo_materno_id'
];

/**
 * Sanitizes horse data by removing invalid fields and processing special cases
 * @param data - Raw horse data from frontend
 * @returns Sanitized data ready for database operations
 */
export function sanitizeHorseData(data: any): Record<string, any> {
  const sanitized = { ...data };

  // Remove genealogy object fields that don't belong in cavalos table
  FIELDS_TO_REMOVE.forEach(field => {
    if (sanitized[field] !== undefined) {
      delete sanitized[field];
    }
  });

  // Remove complex genealogy objects (pai/mae as objects) before SQL insert
  if (sanitized.pai && typeof sanitized.pai === 'object') {
    delete sanitized.pai;
  }
  if (sanitized.mae && typeof sanitized.mae === 'object') {
    delete sanitized.mae;
  }

  // Map observacoes to notes field (database uses 'notes' column)
  if (sanitized.observacoes && !sanitized.notes) {
    sanitized.notes = sanitized.observacoes;
  }
  delete sanitized.observacoes;

  // Convert empty strings to null for optional fields
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] === '' || sanitized[key] === undefined) {
      sanitized[key] = null;
    }
  });

  // Filter to only include valid table fields
  const finalData: Record<string, any> = {};
  VALID_HORSE_FIELDS.forEach(field => {
    if (sanitized[field] !== undefined) {
      finalData[field] = sanitized[field];
    }
  });

  return finalData;
}

/**
 * Validates that required fields are present in horse data
 * @param data - Sanitized horse data
 * @throws Error if required fields are missing
 */
export function validateRequiredHorseFields(data: Record<string, any>): void {
  const requiredFields = ['name', 'user_id'];
  
  const missingFields = requiredFields.filter(field => !data[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);
  }
}

/**
 * Complete sanitization pipeline for horse data
 * @param data - Raw horse data from frontend
 * @returns Sanitized and validated data ready for database operations
 */
export function sanitizeAndValidateHorseData(data: any): Record<string, any> {
  const sanitized = sanitizeHorseData(data);
  validateRequiredHorseFields(sanitized);
  return sanitized;
}