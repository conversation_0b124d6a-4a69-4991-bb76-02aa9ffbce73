import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProcedimentoVet } from '@shared/schema';
import { Eye, Edit, Calendar, Trash2 } from 'lucide-react';
import { StatusBadge } from './StatusBadge';

interface ProcedimentoVetCardProps {
  procedimento: ProcedimentoVet;
  cavaloNome: string;
  onView: (id: number) => void;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onSchedule?: (id: number) => void;
  formatarData: (data: string) => string;
  getStatus?: (procedimento: ProcedimentoVet) => string;
  statusType?: 'vacinacao' | 'vermifugacao' | 'exame' | 'procedimento';
}

/**
 * Componente de cartão para exibir um procedimento veterinário
 * 
 * Este componente exibe detalhes de um procedimento veterinário
 * de forma visualmente agradável e pode ser usado para procedimentos
 * em geral, vacinações, vermifugações ou exames.
 */
export const ProcedimentoVetCard: React.FC<ProcedimentoVetCardProps> = ({
  procedimento,
  cavaloNome,
  onView,
  onEdit,
  onDelete,
  onSchedule,
  formatarData,
  getStatus,
  statusType = 'procedimento'
}) => {
  const status = getStatus ? getStatus(procedimento) : (procedimento.status || 'realizado');
  
  const isVermifugacao = procedimento.tipo.toLowerCase().includes('vermifug');
  const isVacinacao = procedimento.tipo.toLowerCase().includes('vacina');
  
  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{procedimento.tipo}</CardTitle>
            <CardDescription>
              {cavaloNome} • {formatarData(procedimento.data)}
            </CardDescription>
          </div>
          <StatusBadge status={status} type={statusType} />
        </div>
      </CardHeader>
      
      <CardContent className="flex-grow">
        <div className="space-y-2 text-sm">
          <p className="line-clamp-2">{procedimento.descricao}</p>
          
          {(isVacinacao || isVermifugacao || procedimento.medicamentos) && (
            <div className="flex justify-between pt-2">
              <span className="text-gray-500">
                {isVacinacao ? 'Vacina:' : isVermifugacao ? 'Vermífugo:' : 'Medicamento:'}
              </span>
              <span className="font-medium">{procedimento.medicamentos || 'Não especificado'}</span>
            </div>
          )}
          
          {procedimento.dosagem && (
            <div className="flex justify-between">
              <span className="text-gray-500">Dosagem:</span>
              <span className="font-medium">{procedimento.dosagem}</span>
            </div>
          )}
          
          {procedimento.veterinario && (
            <div className="flex justify-between">
              <span className="text-gray-500">Veterinário:</span>
              <span className="font-medium">{procedimento.veterinario}</span>
            </div>
          )}
          
          {procedimento.dataProximoProcedimento && (
            <div className="flex justify-between">
              <span className="text-gray-500">Próxima data:</span>
              <span className="font-medium text-blue-600">
                {formatarData(procedimento.dataProximoProcedimento)}
              </span>
            </div>
          )}
          
          {procedimento.custo !== null && procedimento.custo !== undefined && procedimento.custo > 0 && (
            <div className="flex justify-between pt-2">
              <span className="text-gray-500">Custo:</span>
              <span className="font-medium">R$ {procedimento.custo.toFixed(2)}</span>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end space-x-1 pt-2 border-t">
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => onView(procedimento.id)}
          title="Visualizar detalhes"
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => onEdit(procedimento.id)}
          title="Editar"
        >
          <Edit className="h-4 w-4" />
        </Button>
        {onSchedule && (
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => onSchedule(procedimento.id)}
            title="Agendar próximo"
          >
            <Calendar className="h-4 w-4" />
          </Button>
        )}
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => {
            if (window.confirm("Tem certeza que deseja excluir este procedimento?")) {
              onDelete(procedimento.id);
            }
          }}
          title="Excluir"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};