import React, { useState, useRef, useEffect } from "react";
import { Send, Loader2, X, ChevronDown, ChevronUp, MessageSquare, Bot } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/hooks/use-toast";
import { useAssistente } from "@/hooks/use-assistente";
import { C<PERSON>lo } from "@shared/schema";
import { useCavalo } from "@/hooks/use-cavalo";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Definindo tipos para mensagens
type MessageType = "user" | "assistant";

interface Message {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
}

export function ChatBot() {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [selectedHorseId, setSelectedHorseId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();
  const {
    messages,
    sendMessage,
    isLoading,
    clearMessages
  } = useAssistente(selectedHorseId);
  const cavalosQuery = useCavalo();
  const cavalos = cavalosQuery?.cavalosQuery?.data || [];

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Scroll para a última mensagem quando novas mensagens chegarem
  useEffect(() => {
    if (messagesEndRef.current && isOpen) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isOpen]);

  // Focar no input quando o chat for aberto
  useEffect(() => {
    if (isOpen) {
      setTimeout(focusInput, 100);
    }
  }, [isOpen]);

  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (message.trim() === "") return;
    
    try {
      console.log("Enviando mensagem:", message);
      
      // Tentar enviar a mensagem e definir um timeout de 10 segundos
      const messagePromise = sendMessage(message);
      
      // Criar uma promise de timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Tempo limite de conexão excedido")), 10000);
      });
      
      // Qual promise resolver primeiro
      await Promise.race([messagePromise, timeoutPromise]);
      
      setMessage("");
      setTimeout(focusInput, 100);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      let errorMsg = "Não foi possível conectar ao assistente. Tente novamente mais tarde.";
      
      if (error instanceof Error) {
        errorMsg = error.message;
      }
      
      toast({
        title: "Erro ao enviar mensagem",
        description: errorMsg,
        variant: "destructive",
      });
    }
  };

  const handleChatToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClearChat = () => {
    clearMessages();
    toast({
      title: "Chat limpo",
      description: "Todas as mensagens foram removidas.",
    });
  };

  return (
    <>
      {/* Botão flutuante para abrir o chat */}
      <Button
        onClick={handleChatToggle}
        className="fixed bottom-6 right-6 rounded-full p-3 w-14 h-14 shadow-lg bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099] z-50"
        aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Bot className="h-6 w-6" />
        )}
      </Button>

      {/* Janela do chat */}
      <div
        className={`fixed bottom-24 right-6 z-50 transition-all duration-300 ease-in-out ${
          isOpen
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-8 pointer-events-none"
        }`}
      >
        <Card className="w-[350px] md:w-[450px] h-[500px] shadow-xl border border-gray-200 flex flex-col">
          <CardHeader className="bg-gradient-to-r from-[#0A3364] to-[#134282] text-white py-4 rounded-t-lg flex flex-row items-center justify-between space-y-0">
            <div className="flex flex-col space-y-1.5">
              <CardTitle className="text-lg font-bold">Assistente EquiGestor</CardTitle>
              <CardDescription className="text-gray-200 text-sm">
                Tire suas dúvidas sobre cuidados equinos
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClearChat}
                className="h-8 w-8 text-white hover:bg-blue-600"
                aria-label="Limpar conversa"
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleChatToggle}
                className="h-8 w-8 text-white hover:bg-blue-600"
                aria-label="Fechar assistente"
              >
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </CardHeader>

          <div className="px-4 pt-3">
            <Select
              value={selectedHorseId || "nenhum"}
              onValueChange={(value) => setSelectedHorseId(value === "nenhum" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um cavalo (opcional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="nenhum">Nenhum cavalo selecionado</SelectItem>
                {cavalos.map((cavalo: Cavalo) => (
                  <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                    {cavalo.name} - {cavalo.breed}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <CardContent className="flex-1 overflow-hidden p-4">
            <ScrollArea className="h-full pr-4" role="log">
              {messages.length === 0 ? (
                <div className="h-full flex flex-col items-center justify-center text-center p-4 text-gray-500">
                  <Bot className="h-12 w-12 mb-4 text-gray-400" />
                  <p className="text-sm">
                    Olá! Sou o assistente virtual do EquiGestor AI. Como posso ajudar você com seus cavalos hoje?
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${
                        msg.type === "user" ? "justify-end" : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-3 ${
                          msg.type === "user"
                            ? "bg-blue-600 text-white ml-4"
                            : "bg-gray-100 dark:bg-gray-800 mr-4"
                        }`}
                      >
                        {msg.type === "assistant" && (
                          <div className="flex items-center mb-2">
                            <Avatar className="h-6 w-6 mr-2">
                              <AvatarImage src="/assets/eq-icon.png" alt="Assistente" />
                              <AvatarFallback>EQ</AvatarFallback>
                            </Avatar>
                            <span className="text-xs font-semibold">Assistente EquiGestor</span>
                          </div>
                        )}
                        <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                        <div className="mt-1 text-right">
                          <span
                            className={`text-xs ${
                              msg.type === "user" ? "text-blue-200" : "text-gray-500"
                            }`}
                          >
                            {new Intl.DateTimeFormat("pt-BR", {
                              hour: "2-digit",
                              minute: "2-digit",
                            }).format(msg.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>
          </CardContent>

          <CardFooter className="border-t p-3">
            <form onSubmit={handleSendMessage} className="flex space-x-2 w-full">
              <Input
                ref={inputRef}
                type="text"
                placeholder="Digite sua mensagem..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={isLoading}
                className="flex-1"
                aria-label="Mensagem para o assistente"
              />
              <Button
                type="submit"
                disabled={isLoading || message.trim() === ""}
                className="bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099]"
                aria-label="Enviar mensagem"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
              </Button>
            </form>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}

export default ChatBot;