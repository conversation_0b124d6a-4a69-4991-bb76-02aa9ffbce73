/**
 * Migração para criar a tabela de pelagens
 */
import { db } from '../db';
import { pelagens } from '../../shared/schema';
import { getModuleLogger } from '../logger';

const logger = getModuleLogger('migration-pelagens');

export async function createPelagensTable() {
  try {
    logger.info('Iniciando migração para criar tabela de pelagens');
    
    // Verifica se a tabela já existe
    const tableExists = await checkIfTableExists('pelagens');
    
    if (tableExists) {
      logger.info('Tabela "pelagens" já existe. Pulando migração.');
      return;
    }
    
    // Cria a tabela
    await db.execute(`
      CREATE TABLE IF NOT EXISTS pelagens (
        id SERIAL PRIMARY KEY,
        nome TEXT NOT NULL UNIQUE,
        descricao TEXT,
        fonte TEXT DEFAULT 'manual',
        user_id INTEGER REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    logger.info('Tabela de pelagens criada com sucesso');
    
    // Insere pelagens padrão do cavalo crioulo
    const pelagensBasicas = [
      { nome: 'Alazão', descricao: 'Pelos amarelados e crinas da mesma cor', fonte: 'sistema' },
      { nome: 'Tordilho', descricao: 'Pelagem com mistura de pelos brancos e pretos', fonte: 'sistema' },
      { nome: 'Zaino', descricao: 'Pelagem avermelhada com crinas e extremidades pretas', fonte: 'sistema' },
      { nome: 'Baio', descricao: 'Pelo amarelo e crina preta', fonte: 'sistema' },
      { nome: 'Rosilho', descricao: 'Mistura de pelos brancos, vermelhos e pretos', fonte: 'sistema' },
      { nome: 'Gateado', descricao: 'Tom amarelado com faixa dorsal escura', fonte: 'sistema' },
      { nome: 'Lobuno', descricao: 'Coloração acinzentada semelhante à do lobo', fonte: 'sistema' },
      { nome: 'Mouro', descricao: 'Mistura de pelos pretos e brancos', fonte: 'sistema' },
      { nome: 'Oveiro', descricao: 'Pelagem com manchas brancas irregulares', fonte: 'sistema' },
      { nome: 'Tobiano', descricao: 'Pelagem com manchas brancas grandes e bem definidas', fonte: 'sistema' },
      { nome: 'Pampa', descricao: 'Manchas brancas grandes sobre pelagem base', fonte: 'sistema' },
    ];
    
    // Insere as pelagens padrão
    for (const pelagem of pelagensBasicas) {
      try {
        await db.insert(pelagens).values(pelagem).onConflictDoNothing();
      } catch (error) {
        logger.warn(`Não foi possível inserir a pelagem ${pelagem.nome}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
    
    logger.info(`Pelagens padrão inseridas: ${pelagensBasicas.length}`);
    
    return true;
  } catch (error) {
    logger.error(`Erro ao criar tabela de pelagens: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Verifica se uma tabela existe no banco de dados
 */
async function checkIfTableExists(tableName: string): Promise<boolean> {
  try {
    const result = await db.execute(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
      )
    `);
    
    return result.rows[0].exists === true;
  } catch (error) {
    logger.error(`Erro ao verificar existência da tabela ${tableName}: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}