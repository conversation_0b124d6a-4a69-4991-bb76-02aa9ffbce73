/**
 * <PERSON><PERSON><PERSON> para upload e gestão de fotos de cavalos
 */
import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { z } from 'zod';
import { db } from '../db';
import { arquivos, cavalos } from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { authenticateUser } from '../auth';

const router = Router();

// Configuração do diretório de upload
const UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'horse-photos');

// Garantir que o diretório existe
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOAD_DIR);
  },
  filename: (req, file, cb) => {
    // Gerar nome único: horseId_timestamp_originalname
    const horse_id = req.params.horse_id;
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `horse_${horse_id}_${timestamp}_${name}${ext}`);
  }
});

// Filtro para aceitar apenas imagens
const fileFilter = (req: any, file: any, cb: any) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Tipo de arquivo não permitido. Use apenas JPEG, PNG, GIF ou WebP.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB máximo
  }
});

// Schema de validação para metadados da foto
const photoMetadataSchema = z.object({
  description: z.string().optional(),
  isProfilePhoto: z.boolean().default(false),
});

/**
 * POST /api/cavalos/:horse_id/photos
 * Upload de nova foto para um cavalo
 */
router.post('/:horse_id/photos', authenticateUser, upload.single('photo'), async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);
    const user_id = (req as any).user.id;
    
    if (isNaN(horse_id)) {
      return res.status(400).json({
        success: false,
        error: 'ID do cavalo inválido'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Nenhum arquivo foi enviado'
      });
    }

    // Validar metadados
    const metadata = photoMetadataSchema.parse({
      description: req.body.description,
      isProfilePhoto: req.body.isProfilePhoto === 'true'
    });

    // Verificar se o cavalo pertence ao usuário
    const [horse] = await db
      .select()
      .from(cavalos)
      .where(and(eq(cavalos.id, horse_id), eq(cavalos.user_id, user_id)))
      .limit(1);

    if (!horse) {
      // Remover arquivo se cavalo não existe
      fs.unlinkSync(req.file.path);
      return res.status(404).json({
        success: false,
        error: 'Cavalo não encontrado'
      });
    }

    // Se esta for definida como foto de perfil, remover flag de outras fotos
    if (metadata.isProfilePhoto) {
      await db
        .update(arquivos)
        .set({ is_primary_photo: false })
        .where(and(
          eq(arquivos.cavalo_id, horse_id),
          eq(arquivos.user_id, user_id),
          eq(arquivos.tipo_arquivo, 'image')
        ));
    }

    // Salvar informações do arquivo no banco
    const [arquivo] = await db
      .insert(arquivos)
      .values({

        cavalo_id: horse_id,
        user_id,
        is_primary_photo: metadata.isProfilePhoto,
        is_photo: true,
        tamanho_arquivo: req.file.size,
      })
      .returning();

    // Log da ação
    (req as any).logger?.info({
      event: 'horse_photo_uploaded',
      horse_id,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      isProfilePhoto: metadata.isProfilePhoto
    }, 'Foto do cavalo enviada com sucesso');

    res.status(201).json({
      success: true,
      message: 'Foto enviada com sucesso',
      data: {
        id: arquivo.id,
        fileName: arquivo.nome_arquivo,
        description: arquivo.descricao,
        isProfilePhoto: arquivo.is_primary_photo,
        uploadDate: arquivo.created_at,
        fileSize: arquivo.tamanho_arquivo
      }
    });

  } catch (error) {
    // Remover arquivo em caso de erro
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    (req as any).logger?.error({
      event: 'horse_photo_upload_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao fazer upload da foto');

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * GET /api/cavalos/:horse_id/photos
 * Listar todas as fotos de um cavalo
 */
router.get('/:horse_id/photos', authenticateUser, async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);
    const user_id = (req as any).user.id;

    if (isNaN(horse_id)) {
      return res.status(400).json({
        success: false,
        error: 'ID do cavalo inválido'
      });
    }

    // Verificar se o cavalo pertence ao usuário
    const [horse] = await db
      .select()
      .from(cavalos)
      .where(and(eq(cavalos.id, horse_id), eq(cavalos.user_id, user_id)))
      .limit(1);

    if (!horse) {
      return res.status(404).json({
        success: false,
        error: 'Cavalo não encontrado'
      });
    }

    // Buscar todas as fotos do cavalo
    const photos = await db
      .select({
        id: arquivos.id,
        fileName: arquivos.nome_arquivo,
        description: arquivos.descricao,
        isProfilePhoto: arquivos.is_primary_photo,
        uploadDate: arquivos.created_at,
        fileSize: arquivos.tamanho_arquivo
      })
      .from(arquivos)
      .where(and(
        eq(arquivos.cavalo_id, horse_id),
        eq(arquivos.user_id, user_id),
        eq(arquivos.tipo_arquivo, 'image')
      ))
      .orderBy(arquivos.created_at);

    res.json({
      success: true,
      data: photos
    });

  } catch (error) {
    (req as any).logger?.error({
      event: 'horse_photos_list_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao listar fotos do cavalo');

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * GET /api/cavalos/:horse_id/photos/:photoId
 * Servir uma foto específica
 */
router.get('/:horse_id/photos/:photoId', authenticateUser, async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);
    const photoId = parseInt(req.params.photoId);
    const user_id = (req as any).user.id;

    if (isNaN(horse_id) || isNaN(photoId)) {
      return res.status(400).json({
        success: false,
        error: 'IDs inválidos'
      });
    }

    // Buscar a foto
    const [photo] = await db
      .select()

      .limit(1);

    if (!photo) {
      return res.status(404).json({
        success: false,
        error: 'Foto não encontrada'
      });
    }

    // Verificar se o arquivo existe no sistema de arquivos
    if (!fs.existsSync(photo.caminho_arquivo)) {
      return res.status(404).json({
        success: false,
        error: 'Arquivo de foto não encontrado'
      });
    }

    // Servir o arquivo
    res.sendFile(path.resolve(photo.caminho_arquivo));

  } catch (error) {
    (req as any).logger?.error({
      event: 'horse_photo_serve_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao servir foto do cavalo');

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * PUT /api/cavalos/:horse_id/photos/:photoId
 * Atualizar metadados de uma foto
 */
router.put('/:horse_id/photos/:photoId', authenticateUser, async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);
    const photoId = parseInt(req.params.photoId);
    const user_id = (req as any).user.id;

    if (isNaN(horse_id) || isNaN(photoId)) {
      return res.status(400).json({
        success: false,
        error: 'IDs inválidos'
      });
    }

    // Validar dados de entrada
    const updateData = photoMetadataSchema.parse(req.body);

    // Se esta for definida como foto de perfil, remover flag de outras fotos
    if (updateData.isProfilePhoto) {
      await db
        .update(arquivos)

    }

    // Atualizar a foto específica
    const [updatedPhoto] = await db
      .update(arquivos)
      .set({
        descricao: updateData.description,
        is_primary_photo: updateData.isProfilePhoto
      })

      .returning();

    if (!updatedPhoto) {
      return res.status(404).json({
        success: false,
        error: 'Foto não encontrada'
      });
    }

    res.json({
      success: true,
      message: 'Foto atualizada com sucesso',
      data: {
        id: updatedPhoto.id,
        description: updatedPhoto.descricao,
        isProfilePhoto: updatedPhoto.is_primary_photo
      }
    });

  } catch (error) {
    (req as any).logger?.error({
      event: 'horse_photo_update_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao atualizar foto do cavalo');

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * DELETE /api/cavalos/:horse_id/photos/:photoId
 * Excluir uma foto
 */
router.delete('/:horse_id/photos/:photoId', authenticateUser, async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);
    const photoId = parseInt(req.params.photoId);
    const user_id = (req as any).user.id;

    if (isNaN(horse_id) || isNaN(photoId)) {
      return res.status(400).json({
        success: false,
        error: 'IDs inválidos'
      });
    }

    // Buscar a foto para obter o caminho do arquivo
    const [photo] = await db
      .select()
      .from(arquivos)

      .limit(1);

    if (!photo) {
      return res.status(404).json({
        success: false,
        error: 'Foto não encontrada'
      });
    }

    // Remover do banco de dados
    await db
      .delete(arquivos)
      .where(eq(arquivos.id, photoId));

    // Remover arquivo do sistema de arquivos
    if (fs.existsSync(photo.caminho_arquivo)) {
      fs.unlinkSync(photo.caminho_arquivo);
    }

    (req as any).logger?.info({
      event: 'horse_photo_deleted',
      horse_id,
      photoId,
      fileName: photo.nome_arquivo
    }, 'Foto do cavalo removida com sucesso');

    res.json({
      success: true,
      message: 'Foto removida com sucesso'
    });

  } catch (error) {
    (req as any).logger?.error({
      event: 'horse_photo_delete_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao remover foto do cavalo');

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * GET /api/cavalos/:horse_id/photo
 * Obter foto de perfil de um cavalo (compatibilidade com código existente)
 * Rota pública para permitir exibição de avatares
 */
router.get('/:horse_id/photo', async (req: Request, res: Response) => {
  try {
    const horse_id = parseInt(req.params.horse_id);

    if (isNaN(horse_id)) {
      return res.status(400).json({
        success: false,
        error: 'ID do cavalo inválido'
      });
    }

    // Buscar foto de perfil (sem restrição de usuário para permitir visualização)
    const [profilePhoto] = await db
      .select()
      .from(arquivos)
      .where(and(

      ))
      .limit(1);

    if (!profilePhoto || !fs.existsSync(profilePhoto.caminho_arquivo)) {
      // Retornar placeholder ou erro 404
      return res.status(404).json({
        success: false,
        error: 'Foto de perfil não encontrada'
      });
    }

    // Servir o arquivo
    res.sendFile(path.resolve(profilePhoto.caminho_arquivo));

  } catch (error) {
    (req as any).logger?.error({
      event: 'horse_profile_photo_serve_error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 'Erro ao servir foto de perfil do cavalo');

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

export default router;