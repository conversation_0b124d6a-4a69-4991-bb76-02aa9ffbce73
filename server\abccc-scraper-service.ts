/**
 * Serviço de scraping do site da ABCCC.
 * 
 * Este módulo é responsável por extrair dados de cavalos Crioulos do site da ABCCC.
 * Implementa cache, rate limiting e múltiplas estratégias de busca para aumentar
 * a robustez do serviço.
 */
import axios from 'axios';
import * as cheerio from 'cheerio';
import { getModuleLogger } from './logger';
import NodeCache from 'node-cache';
import Bottleneck from 'bottleneck';
import * as dotenv from 'dotenv';
import { obterTokensDisponiveis, obterURLsComTokens, registrarUsoToken } from './abccc-config';
import { extrairDadosComFallback } from './abccc-scraper.test';

// Carregar variáveis de ambiente
dotenv.config();

// Logger específico para o módulo de scraping da ABCCC
const scraperLogger = getModuleLogger('abccc-scraper');

// Sistema de cache para armazenar resultados do scraping por 24 horas
// Adicionado cache com node-cache para evitar requisições repetidas
const scrapeCache = new NodeCache({
  stdTTL: 86400, // 24 horas em segundos
  checkperiod: 3600, // Verificar expiração a cada hora
  useClones: false // Não clonar objetos para economizar memória
});

// Rate limiting para evitar sobrecarga no site da ABCCC
// Implementado com bottleneck para limitar a quantidade de requisições
// Otimizado para permitir mais requisições simultâneas e processamento mais rápido
const limiter = new Bottleneck({
  maxConcurrent: 5, // Aumentado para 5 requisições simultâneas (era 2)
  minTime: 100, // Reduzido para 100ms entre requisições (era 200ms)
  highWater: 20, // Aumentado para 20 requisições na fila (era 10)
  strategy: Bottleneck.strategy.LEAK, // Estratégia de vazamento para a fila
  retryCount: 2, // Reduzido para 2 tentativas em caso de falha (era 3)
  retryDelay: 500 // Reduzido para 500ms antes de tentar novamente (era 1000ms)
});

// Obter timeout das variáveis de ambiente ou usar valor padrão (10 segundos)
// Reduzido de 15 para 10 segundos para acelerar o processamento
const SCRAPING_TIMEOUT = parseInt(process.env.SCRAPING_TIMEOUT || '10000', 10);

/**
 * Interface para dados extraídos do site da ABCCC
 */
export interface ABCCCScrapedData {
  nome: string;
  registro: string;
  sexo: string;
  nascimento: string;
  pelagem: string;
  pai: string;
  mae: string;
  criador: string;
  proprietario: string;
  foto?: string;
  genealogia: any;
  // Campos adicionais
  avoPaternoNome?: string;
  avoPaternoPai?: string;
  avoPaternoMae?: string;
  avoMaternaNome?: string;
  avoMaternaPai?: string;
  avoMaternaMae?: string;
  bisavos?: any[];
  titulos?: string[];
  exposicoes?: string[];
  // Flags para indicar o estado dos dados
  resultadoSimplificado?: boolean; // Dados da tabela sem detalhes
  partial?: boolean; // Indica se os dados estão completos ou parciais (falha em alguns campos)
  timestamp?: number; // Timestamp de quando os dados foram obtidos
  source?: string; // Fonte dos dados: 'cache', 'direct', 'token', etc.
}

/**
 * Verifica se o texto contém um registro de cavalo ABCCC válido
 * @param registro Texto a ser verificado
 * @returns true se o registro for válido, false caso contrário
 */
function verificaRegistroValido(registro: string): boolean {
  if (!registro) return false;
  
  // Registros ABCCC geralmente começam com letra e seguem com números
  const regexRegistro = /^[A-Z][0-9]+$/i;
  return regexRegistro.test(registro.trim());
}

/**
 * Extrai um campo usando múltiplos seletores como fallback
 * @param $ Instância do Cheerio carregada com HTML
 * @param seletores Array de seletores CSS para tentar
 * @returns Texto extraído ou string vazia se nenhum seletor funcionar
 */
function extrairCampoComFallback($: cheerio.CheerioAPI, seletores: string[]): string {
  for (const seletor of seletores) {
    try {
      const valor = $(seletor).first().text().trim();
      if (valor && valor.length > 0 && valor !== '-' && valor !== 'N/A') {
        scraperLogger.debug(`Campo extraído com seletor "${seletor}": ${valor.substring(0, 50)}...`);
        return valor;
      }
    } catch (error) {
      // Seletor falhou, tentar o próximo
      continue;
    }
  }
  return '';
}

/**
 * Busca dados do cavalo pelo número de registro na ABCCC (studbook).
 * Implementa múltiplas abordagens e usa cache para otimizar requisições.
 * 
 * @param {string} registro - Ex: 'B405132' ou 'B230532'
 * @returns {Promise<ABCCCScrapedData>} - Objeto com os principais campos extraídos.
 */
export async function getCriouloDataByRegistro(registro: string): Promise<ABCCCScrapedData> {
  // Padronizar formato do registro para buscas (maiúsculo sem espaços)
  const registroFormatado = registro.trim().toUpperCase();
  
  scraperLogger.info(`Buscando dados do registro: ${registroFormatado}`);
  
  if (!verificaRegistroValido(registroFormatado)) {
    throw new Error(`Formato de registro inválido: ${registroFormatado}`);
  }
  
  // Verificar cache primeiro
  const cacheKey = `cavalo_${registroFormatado}`;
  const cachedData = scrapeCache.get<ABCCCScrapedData>(cacheKey);
  
  if (cachedData) {
    scraperLogger.info(`Dados encontrados no cache para registro: ${registroFormatado}`);
    return {
      ...cachedData,
      source: 'cache'
    };
  }
  
  // Implementação robusta para todos os registros - Tratamento universal
  try {
    // Obter URLs dinâmicas com tokens para tentar múltiplas abordagens
    const urls = await obterURLsComTokens(registroFormatado);
    
    // Variável para armazenar erros encontrados durante as tentativas
    const errosAcumulados: string[] = [];
    
    // Tentar cada URL até encontrar dados do cavalo
    for (const url of urls) {
      try {
        scraperLogger.debug(`Tentando URL: ${url}`);
        
        if (url.includes('/ficha_cavalo') || url.includes('/cavalo/')) {
          // URL para ficha direta
          const resultado = await extrairDadosFichaDetalhada(url);
          
          // Se encontrou resultado com ficha direta, registrar sucesso do token e retornar
          if (url.includes('_token=')) {
            const tokenMatch = url.match(/_token=([^&]+)/);
            if (tokenMatch && tokenMatch[1]) {
              await registrarUsoToken(tokenMatch[1], true);
            }
          }
          
          return resultado;
        } else {
          // URL para página de pesquisa
          // Fazer a requisição para a página de busca com cabeçalhos completos
          const response = await limiter.schedule(() => axios.get(url, {
            timeout: SCRAPING_TIMEOUT,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
              'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          }));
          
          const html = response.data;
          const $ = cheerio.load(html);
          
          // Verificar se há resultados na tabela
          const tabelaResultados = $('.respons__horses table tbody tr');
          
          if (tabelaResultados.length > 0) {
            // Extrair dados da tabela de resultados
            const dadosTabela = extrairDadosTabela($, registroFormatado);
            
            if (dadosTabela) {
              // Se encontrou na tabela, tentar obter a ficha detalhada
              const linkDetalhes = $('.respons__horses table tbody tr a').attr('href');
              
              if (linkDetalhes) {
                try {
                  // Extrair dados completos da ficha detalhada
                  const urlDetalhes = linkDetalhes.startsWith('http') ? 
                    linkDetalhes : `https://www.cavalocrioulo.org.br${linkDetalhes}`;
                  
                  scraperLogger.debug(`Encontrou link para ficha detalhada: ${urlDetalhes}`);
                  
                  const dadosCompletos = await extrairDadosFichaDetalhada(urlDetalhes);
                  scraperLogger.info(`Dados completos extraídos com sucesso para ${registroFormatado}`);
                  
                  // Registrar sucesso do token se usado
                  if (url.includes('_token=')) {
                    const tokenMatch = url.match(/_token=([^&]+)/);
                    if (tokenMatch && tokenMatch[1]) {
                      await registrarUsoToken(tokenMatch[1], true);
                    }
                  }
                  
                  return dadosCompletos;
                } catch (erroFicha) {
                  // Se falhar ao obter a ficha detalhada, retorna os dados básicos da tabela
                  const erroMsg = erroFicha instanceof Error ? erroFicha.message : String(erroFicha);
                  scraperLogger.warn(`Falha ao obter ficha detalhada. Usando dados básicos da tabela para ${registroFormatado}: ${erroMsg}`);
                  
                  const dadosSimplificados: ABCCCScrapedData = {
                    ...dadosTabela as ABCCCScrapedData,
                    resultadoSimplificado: true,
                    timestamp: Date.now(),
                    source: 'tabela'
                  };
                  
                  // Guardar no cache por um período mais curto (6 horas)
                  scrapeCache.set(cacheKey, dadosSimplificados, 21600);
                  
                  return dadosSimplificados;
                }
              } else {
                // Se não encontrou link para detalhes, retorna dados básicos
                scraperLogger.warn(`Link para ficha detalhada não encontrado para ${registroFormatado}. Usando dados básicos.`);
                
                const dadosSimplificados: ABCCCScrapedData = {
                  ...dadosTabela as ABCCCScrapedData,
                  resultadoSimplificado: true,
                  timestamp: Date.now(),
                  source: 'tabela_sem_link'
                };
                
                // Guardar no cache por um período mais curto (6 horas)
                scrapeCache.set(cacheKey, dadosSimplificados, 21600);
                
                return dadosSimplificados;
              }
            }
          } else {
            // Tentar extração com fallback
            const dadosFallback = extrairDadosComFallback(html);
            // Verificação adicional - só considerar dados de fallback quando estamos em uma página de cavalo
            // e temos dados mínimos para identificação
            if (dadosFallback && (dadosFallback.nome || dadosFallback.registro)) {
              // Verificação para garantir que os dados são realmente de um cavalo
              // e não textos aleatórios da página inicial
              const nomeEhValido = dadosFallback.nome && 
                                   dadosFallback.nome.length > 3 && 
                                   dadosFallback.nome.length < 100 &&
                                   !dadosFallback.nome.includes('ABCCC') &&
                                   !dadosFallback.nome.includes('Esqu') &&
                                   !dadosFallback.nome.includes('Área') &&
                                   !dadosFallback.nome.includes('Seja');
                                   
              const registroEhValido = dadosFallback.registro && 
                                      /^[A-Z][0-9]+$/i.test(dadosFallback.registro);
                                      
              if (nomeEhValido || registroEhValido) {
                scraperLogger.info(`Dados obtidos via fallback para ${registroFormatado}`);
                
                const dadosSimplificados: ABCCCScrapedData = {
                  nome: dadosFallback.nome || `Cavalo ${registroFormatado}`,
                  registro: dadosFallback.registro || registroFormatado,
                  sexo: dadosFallback.sexo || 'Não informado',
                  nascimento: 'Não informado',
                  pelagem: dadosFallback.pelagem || 'Não informada',
                  criador: 'Não informado',
                  proprietario: 'Não informado',
                  pai: 'Não disponível',
                  mae: 'Não disponível',
                  genealogia: [],
                  resultadoSimplificado: true,
                  partial: true,
                  timestamp: Date.now(),
                  source: 'fallback'
                };
                
                // Guardar no cache por um período mais curto (6 horas)
                scrapeCache.set(cacheKey, dadosSimplificados, 21600);
                
                return dadosSimplificados;
              } else {
                // Detectou que os dados extraídos são da página inicial/404, não do cavalo
                scraperLogger.warn(`Fallback detectou texto da página inicial/erro, ignorando para ${registroFormatado}`);
              }
            }
          }
        }
      } catch (erroUrl) {
        // Registrar erro individual de cada URL e continuar para a próxima
        const erroMsg = erroUrl instanceof Error ? erroUrl.message : String(erroUrl);
        errosAcumulados.push(`URL ${url}: ${erroMsg}`);
        
        // Registrar falha do token se usado
        if (url.includes('_token=')) {
          const tokenMatch = url.match(/_token=([^&]+)/);
          if (tokenMatch && tokenMatch[1]) {
            await registrarUsoToken(tokenMatch[1], false);
          }
        }
        
        scraperLogger.debug(`Erro com URL ${url}: ${erroMsg}. Tentando próxima...`);
        continue;
      }
    }
    
    // Se chegamos aqui, todas as URLs falharam
    const erroDetalhado = `Não foi possível encontrar dados para o registro: ${registroFormatado}. Erros: ${errosAcumulados.join(' | ')}`;
    scraperLogger.error(erroDetalhado);
    throw new Error(`Falha na busca do cavalo ${registroFormatado}: tentativas esgotadas`);
    
  } catch (error) {
    // Erro principal na busca
    const erroMsg = error instanceof Error ? error.message : String(error);
    scraperLogger.error(`Erro na busca por registro ${registroFormatado}: ${erroMsg}`);
    throw new Error(`Falha na busca do cavalo: ${erroMsg}`);
  }
}

/**
 * Extrai dados de um cavalo a partir de uma ficha detalhada
 * Implementa verificação de seletores e fallback quando necessário
 * @param url URL da ficha detalhada
 * @returns Dados extraídos do cavalo
 */
async function extrairDadosFichaDetalhada(url: string): Promise<ABCCCScrapedData> {
  scraperLogger.debug(`Buscando dados da ficha detalhada: ${url}`);
  
  try {
    // Usando o rate limiter para controlar as requisições
    const response = await limiter.schedule(() => axios.get(url, {
      timeout: SCRAPING_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    }));
    
    const html = response.data;
    const $ = cheerio.load(html);
    
    // Múltiplos seletores para extrair dados básicos com fallback
    const nome = extrairCampoComFallback($, [
      '.wrap.cavalos-wrap h1',
      '.cavalo-titulo h1',
      'h1.titulo-cavalo',
      '.nome-cavalo',
      'h1:contains("Cavalo")',
      '.page-header h1'
    ]);
    
    const registro = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("SBB:") td:nth-child(2)',
      '.infos-cavalo tr:contains("Registro:") td:nth-child(2)',
      '.dados-cavalo tr:contains("SBB:") td:last-child',
      '.info tr:contains("SBB:") td:eq(1)',
      'td:contains("SBB"):next()',
      '.registro-numero'
    ]);
    
    const sexo = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("Sexo:") td:nth-child(2)',
      '.dados-cavalo tr:contains("Sexo:") td:last-child',
      '.info tr:contains("Sexo:") td:eq(1)',
      'td:contains("Sexo"):next()'
    ]);
    
    const nascimento = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("Nascimento:") td:nth-child(2)',
      '.dados-cavalo tr:contains("Nascimento:") td:last-child',
      '.info tr:contains("Nascimento:") td:eq(1)',
      'td:contains("Nascimento"):next()'
    ]);
    
    const pelagem = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("Pelagem:") td:nth-child(2)',
      '.dados-cavalo tr:contains("Pelagem:") td:last-child',
      '.info tr:contains("Pelagem:") td:eq(1)',
      'td:contains("Pelagem"):next()'
    ]);
    
    const criador = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("Criador:") td:nth-child(2)',
      '.dados-cavalo tr:contains("Criador:") td:last-child',
      '.info tr:contains("Criador:") td:eq(1)',
      'td:contains("Criador"):next()'
    ]);
    
    const proprietario = extrairCampoComFallback($, [
      '.infos-cavalo tr:contains("Proprietário:") td:nth-child(2)',
      '.dados-cavalo tr:contains("Proprietário:") td:last-child',
      '.info tr:contains("Proprietário:") td:eq(1)',
      'td:contains("Proprietário"):next()'
    ]);
    
    // Extrair dados de genealogia com múltiplos seletores
    const pai = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Pai:") td:nth-child(2)',
      '.genealogia tr:contains("Pai:") td:last-child',
      '.pedigree tr:contains("Pai:") td:eq(1)',
      'td:contains("Pai"):next()',
      '.pai-nome'
    ]);
    
    const mae = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Mãe:") td:nth-child(2)',
      '.genealogia tr:contains("Mãe:") td:last-child',
      '.pedigree tr:contains("Mãe:") td:eq(1)',
      'td:contains("Mãe"):next()',
      '.mae-nome'
    ]);
    
    // Extrair avós com seletores aprimorados
    const avoPaternoNome = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Avô paterno:") td:nth-child(2)',
      '.genealogia tr:contains("Avô paterno:") td:last-child',
      '.pedigree tr:contains("Avô paterno:") td:eq(1)',
      'td:contains("Avô paterno"):next()'
    ]);
    
    const avoPaternoPai = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Pai do avô paterno:") td:nth-child(2)',
      '.genealogia tr:contains("Pai do avô paterno:") td:last-child'
    ]);
    
    const avoPaternoMae = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Mãe do avô paterno:") td:nth-child(2)',
      '.genealogia tr:contains("Mãe do avô paterno:") td:last-child'
    ]);
    
    const avoMaternaNome = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Avó materna:") td:nth-child(2)',
      '.genealogia tr:contains("Avó materna:") td:last-child',
      '.pedigree tr:contains("Avó materna:") td:eq(1)',
      'td:contains("Avó materna"):next()'
    ]);
    
    const avoMaternaPai = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Pai da avó materna:") td:nth-child(2)',
      '.genealogia tr:contains("Pai da avó materna:") td:last-child'
    ]);
    
    const avoMaternaMae = extrairCampoComFallback($, [
      '.genealogia-cavalo tr:contains("Mãe da avó materna:") td:nth-child(2)',
      '.genealogia tr:contains("Mãe da avó materna:") td:last-child'
    ]);
    
    // Extrair foto se disponível
    let foto: string | undefined = undefined;
    const imgSrc = $('.img-responsive').attr('src');
    if (imgSrc) {
      foto = imgSrc.startsWith('http') ? imgSrc : `https://www.cavalocrioulo.org.br${imgSrc}`;
    }
    
    // Log para depuração dos dados extraídos
    scraperLogger.debug(`Dados extraídos da ficha detalhada: nome=${nome}, registro=${registro}, pelagem=${pelagem}`);
    
    // Verificar se os dados básicos foram encontrados, caso contrário tentar alternativa
    if ((!nome || !registro) && html) {
      scraperLogger.warn(`Dados incompletos com seletores primários, tentando fallback`);
      
      // Tentar extrair dados com método alternativo
      const dadosFallback = extrairDadosComFallback(html);
      
      if (dadosFallback) {
        scraperLogger.info(`Dados parciais extraídos via fallback: ${dadosFallback.nome || dadosFallback.registro}`);
        
        // Construir resultado combinando dados extraídos
        const resultado: ABCCCScrapedData = {
          nome: nome || dadosFallback.nome || 'Nome não disponível',
          registro: registro || dadosFallback.registro || 'Registro não disponível',
          sexo: sexo || dadosFallback.sexo || 'Não informado',
          nascimento: nascimento || 'Não informado',
          pelagem: pelagem || dadosFallback.pelagem || 'Não informada',
          criador: criador || 'Não informado',
          proprietario: proprietario || 'Não informado',
          pai: pai || 'Não disponível',
          mae: mae || 'Não disponível',
          genealogia: [],
          partial: true,
          timestamp: Date.now(),
          source: 'fallback_html'
        };
        
        if (avoPaternoNome) resultado.avoPaternoNome = avoPaternoNome;
        if (avoPaternoPai) resultado.avoPaternoPai = avoPaternoPai;
        if (avoPaternoMae) resultado.avoPaternoMae = avoPaternoMae;
        if (avoMaternaNome) resultado.avoMaternaNome = avoMaternaNome;
        if (avoMaternaPai) resultado.avoMaternaPai = avoMaternaPai;
        if (avoMaternaMae) resultado.avoMaternaMae = avoMaternaMae;
        if (foto) resultado.foto = foto;
        
        // Armazenar no cache para futuras consultas
        if (resultado.registro && resultado.registro !== 'Registro não disponível') {
          const cacheKey = `cavalo_${resultado.registro.trim().toUpperCase()}`;
          scrapeCache.set(cacheKey, resultado, 43200); // Cache por 12 horas para dados parciais
        }
        
        return resultado;
      }
    }
    
    // Se chegamos aqui, os dados foram extraídos normalmente
    const dadosCompletos: ABCCCScrapedData = {
      nome: nome || 'Nome não disponível',
      registro: registro || 'Registro não disponível',
      sexo: sexo || 'Não informado',
      nascimento: nascimento || 'Não informado',
      pelagem: pelagem || 'Não informada',
      criador: criador || 'Não informado',
      proprietario: proprietario || 'Não informado',
      pai: pai || 'Não disponível',
      mae: mae || 'Não disponível',
      genealogia: [],
      timestamp: Date.now(),
      source: 'ficha_completa'
    };
    
    if (avoPaternoNome) dadosCompletos.avoPaternoNome = avoPaternoNome;
    if (avoPaternoPai) dadosCompletos.avoPaternoPai = avoPaternoPai;
    if (avoPaternoMae) dadosCompletos.avoPaternoMae = avoPaternoMae;
    if (avoMaternaNome) dadosCompletos.avoMaternaNome = avoMaternaNome;
    if (avoMaternaPai) dadosCompletos.avoMaternaPai = avoMaternaPai;
    if (avoMaternaMae) dadosCompletos.avoMaternaMae = avoMaternaMae;
    if (foto) dadosCompletos.foto = foto;
    
    // Armazenar no cache para futuras consultas
    if (dadosCompletos.registro && dadosCompletos.registro !== 'Registro não disponível') {
      const cacheKey = `cavalo_${dadosCompletos.registro.trim().toUpperCase()}`;
      scrapeCache.set(cacheKey, dadosCompletos); // Cache padrão (24 horas)
    }
    
    return dadosCompletos;
    
  } catch (error) {
    const erroMsg = error instanceof Error ? error.message : String(error);
    scraperLogger.warn(`Erro ao extrair todos os dados da ficha detalhada: ${erroMsg}`);
    
    // Criar dados parciais quando não há como extrair do HTML
    const registroMatch = url.match(/[A-Z][0-9]+/i);
    const registroExtraido = registroMatch ? registroMatch[0] : 'Registro não disponível';
    
    // Dados mínimos quando tudo falha
    const dadosParciais: ABCCCScrapedData = {
      nome: 'Nome não disponível',
      registro: registroExtraido,
      sexo: 'Não informado',
      nascimento: 'Não informado',
      pelagem: 'Não informada',
      criador: 'Não informado',
      proprietario: 'Não informado',
      pai: 'Não disponível',
      mae: 'Não disponível',
      genealogia: [],
      partial: true,
      timestamp: Date.now(),
      source: 'erro_extracao'
    };
    
    // Armazenar dados parciais no cache com TTL curto
    if (registroExtraido !== 'Registro não disponível') {
      const cacheKey = `cavalo_${registroExtraido.trim().toUpperCase()}`;
      scrapeCache.set(cacheKey, dadosParciais, 3600); // Cache por 1 hora para dados parciais
    }
    
    // Se não conseguimos extrair nada relevante, lançamos erro
    if (registroExtraido === 'Registro não disponível') {
      throw new Error(`Falha ao extrair dados da ficha detalhada: ${erroMsg}`);
    }
    
    return dadosParciais;
  }
}

/**
 * Extrai dados básicos do cavalo a partir da tabela de resultados da pesquisa
 * @param $ Objeto Cheerio para manipulação do HTML
 * @param registro Registro do cavalo a ser buscado
 * @returns Dados parciais do cavalo ou null se não encontrado
 */
function extrairDadosTabela($: cheerio.CheerioAPI, registro: string): Partial<ABCCCScrapedData> | null {
  scraperLogger.debug(`Buscando dados na tabela para registro: ${registro}`);
  
  // Verificar todas as tabelas na página
  let dadosEncontrados: Partial<ABCCCScrapedData> | null = null;
  
  // Procura em todas as tabelas
  let tableCount = 0;
  $('table').each((_, table) => {
    tableCount++;
    let rowCount = 0;
    // Busca em todas as linhas da tabela
    $(table).find('tr').each((_, row) => {
      rowCount++;
      const rowText = $(row).text().trim();
      
      // Se a linha contém o registro ou alguma parte dele
      if (rowText.includes(registro)) {
        scraperLogger.debug(`Encontrada linha contendo registro ${registro} na tabela ${tableCount}, linha ${rowCount}`);
        
        // Extrair dados das células
        const cells = $(row).find('td');
        
        // Se temos pelo menos duas células (registro e nome)
        if (cells.length > 1) {
          // Extrair dados de cada célula
          const cellContents: string[] = [];
          cells.each((cellIndex, cell) => {
            const cellText = $(cell).text().trim();
            cellContents.push(cellText);
          });
          
          const nome = cellContents.length > 1 ? cellContents[1] : `Cavalo ${registro}`;
          let pelagem = '';
          let sexo = '';
          
          // Procurar pelagem na terceira célula, se existir
          if (cellContents.length > 2) {
            const terceiraColuna = cellContents[2];
            if (terceiraColuna && !terceiraColuna.match(/^[0-9]+$/)) {
              pelagem = terceiraColuna;
            }
          }
          
          dadosEncontrados = {
            nome: nome,
            registro: registro,
            pelagem: pelagem || 'Não informada',
            sexo: sexo || 'Não informado',
            nascimento: 'Não informado',
            criador: 'Não informado',
            proprietario: 'Não informado',
            pai: 'Não disponível',
            mae: 'Não disponível',
            genealogia: [],
            resultadoSimplificado: true
          };
          
          return false; // break do loop de linhas
        }
      }
    });
    
    // Se já encontramos dados, interrompe a busca em tabelas
    if (dadosEncontrados) {
      return false; // break do loop de tabelas
    }
  });
  
  scraperLogger.debug(`Número total de tabelas verificadas: ${tableCount}`);
  
  return dadosEncontrados;
}

/**
 * Função para enriquecer dados do cavalo com informações obtidas do site da ABCCC
 * @param cavalo Objeto com dados básicos do cavalo
 * @returns Objeto com dados enriquecidos do cavalo ou null em caso de falha
 */
export async function enriquecerDadosComSite(cavalo: {registro?: string, nome?: string}): Promise<ABCCCScrapedData | null> {
  if (!cavalo.registro) {
    scraperLogger.warn('Não é possível enriquecer dados sem um registro válido');
    return null;
  }
  
  // Tratamos todos os registros da mesma forma
  const registroFormatado = cavalo.registro.trim().toUpperCase();
  
  // Verificar cache primeiro
  const cacheKey = `cavalo_${registroFormatado}`;
  const cachedData = scrapeCache.get<ABCCCScrapedData>(cacheKey);
  
  if (cachedData) {
    scraperLogger.info(`Dados encontrados no cache para o enriquecimento do cavalo ${cavalo.nome || registroFormatado}`);
    return {
      ...cachedData,
      source: 'cache'
    };
  }
  
  try {
    // Buscar dados no site da ABCCC
    const dadosEnriquecidos = await getCriouloDataByRegistro(cavalo.registro);
    
    // Adicionar timestamp e salvar no cache
    const dadosComTimestamp = {
      ...dadosEnriquecidos,
      timestamp: Date.now()
    };
    
    // Salvar no cache (24 horas)
    scrapeCache.set(cacheKey, dadosComTimestamp);
    
    scraperLogger.info(`Dados enriquecidos com sucesso para o cavalo: ${dadosEnriquecidos.nome} (${dadosEnriquecidos.registro})`);
    return dadosComTimestamp;
  } catch (error) {
    const erroMsg = error instanceof Error ? error.message : String(error);
    scraperLogger.error(`Erro ao enriquecer dados do cavalo ${cavalo.nome || cavalo.registro}: ${erroMsg}`);
    
    // Verificar se podemos criar um resultado parcial baseado nas informações já disponíveis
    if (cavalo.nome) {
      scraperLogger.warn(`Criando dados parciais com informações disponíveis para ${cavalo.registro}`);
      
      const dadosParciais: ABCCCScrapedData = {
        nome: cavalo.nome,
        registro: registroFormatado,
        sexo: 'Não informado',
        nascimento: 'Não informado',
        pelagem: 'Não informada',
        criador: 'Não informado',
        proprietario: 'Não informado',
        pai: 'Não disponível',
        mae: 'Não disponível',
        genealogia: [],
        partial: true,
        resultadoSimplificado: true,
        timestamp: Date.now(),
        source: 'fallback'
      };
      
      // Salvar dados parciais no cache por tempo reduzido (1 hora)
      scrapeCache.set(cacheKey, dadosParciais, 3600);
      
      return dadosParciais;
    }
    
    return null;
  }
}