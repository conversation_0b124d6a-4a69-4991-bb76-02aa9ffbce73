import React, { useEffect, useRef, useState } from 'react';
import { GenealogyNode } from './GenealogyTree';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { 
  ChevronDown, ChevronRight, ZoomIn, ZoomOut, 
  <PERSON><PERSON><PERSON><PERSON>c<PERSON>, <PERSON><PERSON>ointer, Maximize, X 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';

interface EnhancedGenealogyTreeProps {
  rootNode: GenealogyNode;
  onNodeClick: (id: number | null) => void;
  maxGenerations?: number;
  className?: string;
}

// Cores por raça
const breedColors: Record<string, string> = {
  'Crioulo': '#4f46e5', // Indigo
  'Quarto de Milha': '#06b6d4', // Ciano
  'Mangalarga': '#0ea5e9', // Sky
  'Puro Sangue Inglês': '#ef4444', // Ver<PERSON><PERSON>
  'Árabe': '#f59e0b', // Âmbar
  'Lusitano': '#84cc16', // Lima
  'Appaloosa': '#8b5cf6', // Violeta
  'default': '#6b7280', // Cinza
};

// Função para obter cor com base na raça
const getBreedColor = (breed?: string): string => {
  if (!breed) return breedColors.default;
  
  // Verificar se há uma cor específica para esta raça
  const foundColor = Object.entries(breedColors).find(
    ([breedName]) => breed.toLowerCase().includes(breedName.toLowerCase())
  );
  
  return foundColor ? foundColor[1] : breedColors.default;
};

// Função para renderizar um nó da árvore com estilo melhorado
const EnhancedTreeNode: React.FC<{
  node: GenealogyNode;
  onNodeClick: (id: number | null) => void;
  generation: number;
  maxGenerations: number;
}> = ({ node, onNodeClick, generation, maxGenerations }) => {
  const [expanded, setExpanded] = useState(true);
  const hasChildren = !!(node.father || node.mother);
  const breedColor = getBreedColor(node.breed);
  const nodeSize = Math.max(160 - generation * 20, 100); // Diminui de tamanho com a profundidade, mais compacto para mobile
  
  // Não renderizar além da geração máxima
  if (generation > maxGenerations) return null;
  
  // Determinar estilos para o nó baseado no gênero e destaque
  const genderStyles = node.gender === 'M' 
    ? 'bg-blue-50 border-blue-200' 
    : 'bg-pink-50 border-pink-200';
  
  const highlightStyles = node.isHighlighted 
    ? 'ring-2 ring-offset-2 ring-primary' 
    : '';

  // Função para mostrar o indicador de consanguinidade se necessário
  const ConsanguinityIndicator = () => {
    if (!node.consanguinity || node.consanguinity === 0) return null;
    
    // Converter para porcentagem para exibição
    const percentage = (node.consanguinity * 100).toFixed(2);
    
    return (
      <div className="absolute -top-2 -right-2 bg-amber-500 text-white text-xs px-1 rounded-full">
        {percentage}%
      </div>
    );
  };
  
  return (
    <div className="flex flex-col items-center">
      {/* Nó principal */}
      <div 
        className={`relative flex flex-col items-center p-3 border rounded-lg shadow-sm 
                   ${genderStyles} ${highlightStyles} cursor-pointer transition-all 
                   hover:shadow-md`}
        style={{ width: nodeSize, minHeight: nodeSize * 0.8 }}
        onClick={() => onNodeClick(node.id)}
      >
        {/* Indicador de consanguinidade */}
        <ConsanguinityIndicator />
        
        {/* Avatar/Foto */}
        <Avatar className={`w-16 h-16 mb-2 border-2`} style={{ borderColor: breedColor }}>
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center text-lg font-semibold">
            {node.gender === 'M' ? '♂' : '♀'}
          </div>
        </Avatar>
        
        {/* Nome */}
        <h3 className="font-bold text-center mb-1 text-sm break-words">{node.name}</h3>
        
        {/* Raça */}
        {node.breed && (
          <div 
            className="px-2 py-0.5 text-xs rounded-full text-white mt-1 mb-1"
            style={{ backgroundColor: breedColor }}
          >
            {node.breed}
          </div>
        )}
        
        {/* Pelagem */}
        {node.color && (
          <span className="text-xs text-gray-500 mt-1">{node.color}</span>
        )}
        
        {/* Botão de expandir/colapsar */}
        {hasChildren && generation < maxGenerations && (
          <button 
            className="absolute bottom-1 right-1 p-1 rounded-full bg-gray-100 hover:bg-gray-200"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(!expanded);
            }}
          >
            {expanded ? 
              <ChevronDown className="h-3 w-3 text-gray-500" /> : 
              <ChevronRight className="h-3 w-3 text-gray-500" />
            }
          </button>
        )}
      </div>
      
      {/* Conexões e nós filhos */}
      {hasChildren && expanded && (
        <>
          {/* Linha vertical */}
          <div className="w-px h-8 bg-gray-300"></div>
          
          {/* Linhas horizontais e nós filhos */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-0">
            {/* Pai */}
            {node.father && (
              <div className="flex flex-col items-center sm:mr-4">
                <div className="w-16 sm:w-32 h-px bg-blue-300"></div>
                <EnhancedTreeNode 
                  node={node.father} 
                  onNodeClick={onNodeClick} 
                  generation={generation + 1}
                  maxGenerations={maxGenerations}
                />
              </div>
            )}
            
            {/* Mãe */}
            {node.mother && (
              <div className="flex flex-col items-center sm:ml-4">
                <div className="w-16 sm:w-32 h-px bg-pink-300"></div>
                <EnhancedTreeNode 
                  node={node.mother} 
                  onNodeClick={onNodeClick} 
                  generation={generation + 1}
                  maxGenerations={maxGenerations}
                />
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

const EnhancedGenealogyTree: React.FC<EnhancedGenealogyTreeProps> = ({
  rootNode,
  onNodeClick,
  maxGenerations = 3,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  if (!rootNode) {
    return <div className="text-center py-4">Nenhum dado genealógico disponível</div>;
  }
  
  return (
    <div className={`relative border rounded-lg ${className}`}>
      <TransformWrapper
        initialScale={1}
        initialPositionX={0}
        initialPositionY={0}
        minScale={0.3}
        maxScale={2}
        centerOnInit={true}
      >
        {({ zoomIn, zoomOut, resetTransform }) => (
          <>
            {/* Controles de zoom e navegação */}
            <div className="absolute top-2 right-2 z-10 flex flex-col sm:flex-row gap-1 bg-white/90 p-1 rounded-lg shadow-sm">
              <Button variant="outline" size="sm" onClick={() => zoomIn()} className="h-8 w-8 p-0">
                <ZoomIn className="h-3 w-3" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => zoomOut()} className="h-8 w-8 p-0">
                <ZoomOut className="h-3 w-3" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => resetTransform()} className="h-8 w-8 p-0">
                <RotateCcw className="h-3 w-3" />
              </Button>
            </div>
            
            {/* Dica de navegação */}
            <div className="absolute bottom-2 left-2 z-10 bg-white/90 p-2 rounded-lg text-xs text-gray-500 flex items-center max-w-[200px] sm:max-w-none">
              <MousePointer className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="hidden sm:inline">Arraste para navegar, use a roda do mouse para zoom</span>
              <span className="sm:hidden">Toque e arraste para navegar</span>
            </div>
            
            {/* Conteúdo da árvore com zoom/pan */}
            <TransformComponent
              wrapperClass="w-full h-full"
              contentClass="flex items-center justify-center p-8"
            >
              <div 
                ref={containerRef}
                className="genealogy-tree flex justify-center items-start pt-8 pb-16 px-8"
              >
                <EnhancedTreeNode 
                  node={rootNode} 
                  onNodeClick={onNodeClick} 
                  generation={0}
                  maxGenerations={maxGenerations}
                />
              </div>
            </TransformComponent>
          </>
        )}
      </TransformWrapper>
    </div>
  );
};

export default EnhancedGenealogyTree;