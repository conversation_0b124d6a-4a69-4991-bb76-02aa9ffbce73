import { pgTable, text, integer, serial, timestamp, boolean, real, pgEnum, varchar, date, numeric, doublePrecision, index, uniqueIndex, jsonb, uuid } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Enums with all status values used in the system
export const horseStatusEnum = pgEnum('horse_status', [
  'ativo', 
  'inativo', 
  'vendido', 
  'falecido', 
  'nao_informado', 
  'competicao', 
  'reproducao'
]);

export const horseSexoEnum = pgEnum('horse_sexo', [
  'Macho', 
  'Fêmea', 
  'Macho (Castrado)',
  'Garanhão',
  'Égua'
]);

export const userRoleEnum = pgEnum('user_role', ['ADMIN', 'USER']);



// Date coercion helper for Zod schemas
const dateField = z.preprocess(
  (v) => typeof v === "string" ? new Date(v) : v,
  z.date().optional()
);

const dateFieldRequired = z.preprocess(
  (v) => typeof v === "string" ? new Date(v) : v,
  z.date()
);

// Core tables
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  username: varchar("username", { length: 120 }).notNull().unique(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  password_hash: varchar("password_hash", { length: 60 }).notNull(),
  role: userRoleEnum("role").notNull().default('USER'),
  avatar_url: varchar("avatar_url", { length: 500 }),
  flags: jsonb("flags").default('{}'),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
  // Keep legacy fields for compatibility
  password: varchar("password", { length: 60 }), // Legacy field
});

export const cavalos: any = pgTable("cavalos", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  breed: varchar("breed", { length: 100 }),
  birth_date: date("birth_date"),
  sexo: horseSexoEnum("sexo"),
  cor: varchar("cor", { length: 100 }),
  pelagem_id: integer("pelagem_id").references(() => pelagens.id, { onDelete: "set null" }),
  status: horseStatusEnum("status").default('ativo'),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
  notes: text("notes"), // Campo observacoes removido - não existe no banco real
  peso: doublePrecision("peso"), // Better precision for measurements
  altura: doublePrecision("altura"), // Better precision for measurements
  data_entrada: date("data_entrada"),
  data_saida: date("data_saida"),
  motivo_saida: text("motivo_saida"),
  numero_registro: varchar("numero_registro", { length: 50 }),
  origem: varchar("origem", { length: 255 }),
  criador: varchar("criador", { length: 255 }),
  proprietario: varchar("proprietario", { length: 255 }),
  valor_compra: numeric("valor_compra", { precision: 12, scale: 2 }), // Proper money type
  data_compra: date("data_compra"),
  inspetor: varchar("inspetor", { length: 255 }),
  is_external: boolean("is_external").default(false),
  // Remove duplicate parentage fields - keep only IDs, names will come from genealogia table
  pai_id: integer("pai_id").references(() => cavalos.id, { onDelete: "set null" }),
  mae_id: integer("mae_id").references(() => cavalos.id, { onDelete: "set null" }),
}, (table): any => {
  return {
    nameIdx: index("cavalos_name_idx").on(table.name),
    numeroRegistroIdx: uniqueIndex("cavalos_numero_registro_idx").on(table.numero_registro),
    userIdIdx: index("cavalos_user_id_idx").on(table.user_id),
    statusIdx: index("cavalos_status_idx").on(table.status),
    paiIdIdx: index("cavalos_pai_id_idx").on(table.pai_id),
    maeIdIdx: index("cavalos_mae_id_idx").on(table.mae_id),
  };
});

export const genealogia = pgTable("genealogia", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  pai: text("pai"),
  mae: text("mae"),
  avo_paterno_id: integer("avo_paterno_id").references(() => cavalos.id, { onDelete: "set null" }),
  avo_paterno: text("avo_paterno"),
  avo_paterna_id: integer("avo_paterna_id").references(() => cavalos.id, { onDelete: "set null" }),
  avo_paterna: text("avo_paterna"),
  avo_materno_id: integer("avo_materno_id").references(() => cavalos.id, { onDelete: "set null" }),
  avo_materno: text("avo_materno"),
  avo_materna_id: integer("avo_materna_id").references(() => cavalos.id, { onDelete: "set null" }),
  avo_materna: text("avo_materna"),
  // Bisavós paternos (lado do pai)
  bisavo_paterno_paterno: text("bisavo_paterno_paterno"),
  bisavo_paterno_paterno_id: integer("bisavo_paterno_paterno_id").references(() => cavalos.id, { onDelete: "set null" }),
  bisavo_paterna_paterno: text("bisavo_paterna_paterno"),
  bisavo_paterna_paterno_id: integer("bisavo_paterna_paterno_id").references(() => cavalos.id, { onDelete: "set null" }),
  bisavo_materno_paterno: text("bisavo_materno_paterno"),
  bisavo_materno_paterno_id: integer("bisavo_materno_paterno_id").references(() => cavalos.id, { onDelete: "set null" }),
  bisavo_materna_paterno: text("bisavo_materna_paterno"),
  bisavo_materna_paterno_id: integer("bisavo_materna_paterno_id").references(() => cavalos.id, { onDelete: "set null" }),
  created_at: timestamp("created_at").defaultNow(),
});

export const manejos = pgTable("manejos", {
  id: serial("id").primaryKey(),
  cavalo_id: integer("cavalo_id").notNull().references(() => cavalos.id),
  tipo: varchar("tipo", { length: 100 }).notNull(),
  data_execucao: date("data_execucao").notNull(),
  descricao: text("descricao"),
  observacoes: text("observacoes"),
  responsavel: varchar("responsavel", { length: 255 }),
  custo: numeric("custo", { precision: 10, scale: 2 }),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
  status: varchar("status", { length: 50 }),
});

export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  dueDate: date("due_date"),
  completed: boolean("completed").default(false),
  horse_id: integer("horse_id").references(() => cavalos.id),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const arquivos = pgTable("arquivos", {
  id: serial("id").primaryKey(),
  nome_arquivo: varchar("nome_arquivo", { length: 255 }).notNull(),
  caminho_arquivo: text("caminho_arquivo").notNull(),
  tipo_arquivo: varchar("tipo_arquivo", { length: 100 }),
  descricao: text("descricao"),
  cavalo_id: integer("cavalo_id").references(() => cavalos.id),
  tamanho_arquivo: integer("tamanho_arquivo"),
  is_photo: boolean("is_photo").default(false),
  is_primary_photo: boolean("is_primary_photo").default(false),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const eventos = pgTable("eventos", {
  id: serial("id").primaryKey(),
  titulo: text("titulo").notNull(),
  descricao: text("descricao"),
  data: date("data").notNull(),
  hora_inicio: text("hora_inicio"),
  hora_fim: text("hora_fim"),
  tipo: text("tipo"),
  status: text("status"),
  prioridade: text("prioridade"),
  horse_id: integer("horse_id").references(() => cavalos.id),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const procedimentosVet = pgTable("procedimentos_vet", {
  id: serial("id").primaryKey(),
  tipo: text("tipo").notNull(),
  descricao: text("descricao"),
  data: date("data").notNull(),
  veterinario: text("veterinario"),
  crmv: text("crmv"),
  medicamentos: text("medicamentos"),
  dosagem: text("dosagem"),
  resultado: text("resultado"),
  recomendacoes: text("recomendacoes"),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const reproducao = pgTable("reproducao", {
  id: serial("id").primaryKey(),
  egua_id: integer("egua_id").notNull().references(() => cavalos.id),
  garanhao_id: integer("garanhao_id").references(() => cavalos.id),
  garanhao_nome: varchar("garanhao_nome", { length: 255 }),
  data_cobertura: date("data_cobertura"),
  data_parto_previsto: date("data_parto_previsto"),
  data_parto_real: date("data_parto_real"),
  tipo_cobertura: varchar("tipo_cobertura", { length: 50 }),
  resultado: varchar("resultado", { length: 100 }),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
  status: varchar("status", { length: 50 }).default("planejado"),
});

export const nutricao = pgTable("nutricao", {
  id: serial("id").primaryKey(),
  cavalo_id: integer("cavalo_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  tipo_alimento: varchar("tipo_alimento", { length: 100 }).notNull(),
  quantidade: numeric("quantidade").notNull(),
  unidade: varchar("unidade", { length: 50 }).notNull(),
  frequencia: varchar("frequencia", { length: 50 }).notNull(),
  horario_alimentacao: text("horario_alimentacao"),
  observacoes: text("observacoes"),
  custo_mensal: numeric("custo_mensal", { precision: 10, scale: 2 }),
  data_inicio: date("data_inicio").notNull(),
  data_fim: date("data_fim"),
  status: varchar("status", { length: 50 }),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

export const medidas_morfologicas = pgTable("medidas_morfologicas", {
  id: serial("id").primaryKey(),
  cavalo_id: integer("cavalo_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  data_medicao: date("data_medicao").notNull(),
  altura_cernelha: doublePrecision("altura_cernelha"),
  altura_dorso: doublePrecision("altura_dorso"),
  altura_garupa: doublePrecision("altura_garupa"),
  comprimento_corpo: doublePrecision("comprimento_corpo"),
  comprimento_pescoco: doublePrecision("comprimento_pescoco"),
  largura_peito: doublePrecision("largura_peito"),
  perimetro_toracico: doublePrecision("perimetro_toracico"),
  perimetro_pescoco: doublePrecision("perimetro_pescoco"),
  perimetro_canela: doublePrecision("perimetro_canela"),
  pontuacao_cabeca: integer("pontuacao_cabeca"),
  pontuacao_pescoco: integer("pontuacao_pescoco"),
  pontuacao_espalda: integer("pontuacao_espalda"),
  pontuacao_dorso: integer("pontuacao_dorso"),
  pontuacao_garupa: integer("pontuacao_garupa"),
  pontuacao_membros: integer("pontuacao_membros"),
  pontuacao_aprumos: integer("pontuacao_aprumos"),
  pontuacao_andamento: integer("pontuacao_andamento"),
  pontuacao_harmonia: integer("pontuacao_harmonia"),
  pontuacao_total: doublePrecision("pontuacao_total"),
  observacoes: text("observacoes"),
  responsavel_medicao: varchar("responsavel_medicao", { length: 255 }),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// N:N relationship table for medidas_morfologicas and arquivos
export const morfologia_arquivos = pgTable("morfologia_arquivos", {
  id: serial("id").primaryKey(),
  morfologia_id: integer("morfologia_id").notNull().references(() => medidas_morfologicas.id, { onDelete: "cascade" }),
  arquivo_id: integer("arquivo_id").notNull().references(() => arquivos.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela para histórico de desempenho
export const desempenhoHistorico = pgTable("desempenho_historico", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  data: date("data").notNull(),
  atividade: text("atividade").notNull(),
  tempo: text("tempo"),
  distancia: real("distancia"),
  velocidade: real("velocidade"),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabela para sugestões de cruzamento
export const sugestoesCruzamento = pgTable("sugestoes_cruzamento", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  padreiro_sugerido: text("padreiro_sugerido").notNull(),
  motivo: text("motivo"),
  probabilidade_cor_pelagem: text("probabilidade_cor_pelagem"),
  caracteristicas_esperadas: text("caracteristicas_esperadas"),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

export const pelagens = pgTable("pelagens", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull().unique(),
  descricao: text("descricao"),
  fonte: text("fonte").default("manual"),
  user_id: integer("user_id").references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const feedTemplates = pgTable("feed_templates", {
  id: serial("id").primaryKey(),
  nome: text("nome").notNull(),
  descricao: text("descricao"),
  categoria: text("categoria"),
  ingredientes: text("ingredientes"),
  valorNutricional: text("valor_nutricional"),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const feedPlanItems = pgTable("feed_plan_items", {
  id: serial("id").primaryKey(),
  templateId: integer("template_id").notNull().references(() => feedTemplates.id),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id),
  quantidade: real("quantidade").notNull(),
  frequencia: text("frequencia").notNull(),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const stockBatches = pgTable("stock_batches", {
  id: serial("id").primaryKey(),
  produto: text("produto").notNull(),
  lote: text("lote"),
  quantidade: real("quantidade").notNull(),
  dataValidade: date("data_validade"),
  fornecedor: text("fornecedor"),
  custo: real("custo"),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

export const stockAlerts = pgTable("stock_alerts", {
  id: serial("id").primaryKey(),
  produto: text("produto").notNull(),
  estoqueMinimo: real("estoque_minimo").notNull(),
  estoqueAtual: real("estoque_atual").notNull(),
  ativo: boolean("ativo").default(true),
  user_id: integer("user_id").notNull().references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Tabelas do módulo financeiro
export const categorias_financeiras = pgTable("categorias_financeiras", {
  id: serial("id").primaryKey(),
  nome: varchar("nome", { length: 255 }).notNull(),
  tipo: varchar("tipo", { length: 20 }).notNull(), // 'receita' ou 'despesa'
  descricao: text("descricao"),
  ativo: boolean("ativo").default(true),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
});

export const lancamentos_financeiros = pgTable("lancamentos_financeiros", {
  id: serial("id").primaryKey(),
  data: date("data").notNull(),
  tipo: varchar("tipo", { length: 20 }).notNull(), // 'receita' ou 'despesa'
  categoria_id: integer("categoria_id").notNull().references(() => categorias_financeiras.id, { onDelete: "restrict" }),
  descricao: text("descricao").notNull(),
  valor: numeric("valor", { precision: 12, scale: 2 }).notNull(),
  cavalo_id: integer("cavalo_id").references(() => cavalos.id, { onDelete: "set null" }),
  observacoes: text("observacoes"),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
}, (table) => {
  return {
    dataIdx: index("lancamentos_financeiros_data_idx").on(table.data),
    tipoIdx: index("lancamentos_financeiros_tipo_idx").on(table.tipo),
    categoriaIdx: index("lancamentos_financeiros_categoria_idx").on(table.categoria_id),
    userIdIdx: index("lancamentos_financeiros_user_id_idx").on(table.user_id),
    cavaloIdx: index("lancamentos_financeiros_cavalo_idx").on(table.cavalo_id),
  };
});

export const veterinario = pgTable("veterinario", {
  id: serial("id").primaryKey(),
  cavalo_id: integer("cavalo_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  tipo_procedimento: varchar("tipo_procedimento", { length: 100 }).notNull(),
  data_procedimento: date("data_procedimento").notNull(),
  veterinario_responsavel: varchar("veterinario_responsavel", { length: 255 }),
  diagnostico: text("diagnostico"),
  tratamento: text("tratamento"),
  medicamentos: text("medicamentos"),
  observacoes: text("observacoes"),
  custo: numeric("custo", { precision: 10, scale: 2 }),
  proxima_consulta: date("proxima_consulta"),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  created_at: timestamp("created_at").defaultNow(),
  status: varchar("status", { length: 50 }).default("concluido"),
}, (table) => {
  return {
    cavaloIdIdx: index("veterinario_cavalo_id_idx").on(table.cavalo_id),
    userIdIdx: index("veterinario_user_id_idx").on(table.user_id),
    dataProcedimentoIdx: index("veterinario_data_procedimento_idx").on(table.data_procedimento),
    statusIdx: index("veterinario_status_idx").on(table.status),
  };
});

// Auto-generated and extended validation schemas using Drizzle-Zod
export const insertUserSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  username: z.string().min(3, "Nome de usuário deve ter pelo menos 3 caracteres"),
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres"),
  accessLevel: z.string().optional(),
  isActive: z.boolean().optional(),
});

export const insertCavaloSchema = z.object({
  name: z.string().min(1),
  breed: z.string().optional(),
  birth_date: z.string().optional(),
  sexo: z.enum(['Macho', 'Fêmea', 'Macho (Castrado)', 'Garanhão', 'Égua']).optional(),
  cor: z.string().optional(),
  pelagem_id: z.number().optional(),
  status: z.enum(['ativo', 'inativo', 'vendido', 'falecido', 'nao_informado', 'competicao', 'reproducao']).optional(),
  user_id: z.number(),
  notes: z.string().optional(),
  peso: z.number().positive().optional(),
  altura: z.number().positive().optional(),
  data_entrada: z.string().optional(),
  data_saida: z.string().optional(),
  motivo_saida: z.string().optional(),
  numero_registro: z.string().optional(),
  origem: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  valor_compra: z.number().positive().optional(),
  data_compra: z.string().optional(),
  inspetor: z.string().optional(),
  is_external: z.boolean().optional(),
  pai_id: z.number().optional(),
  mae_id: z.number().optional(),
});

export const insertManejoSchema = z.object({
  cavalo_id: z.number(),
  tipo: z.string().min(1),
  data_execucao: z.string(),
  descricao: z.string().optional(),
  observacoes: z.string().optional(),
  responsavel: z.string().optional(),
  custo: z.number().optional(),
  user_id: z.number(),
  status: z.string().optional(),
});

export const insertTaskSchema = z.object({
  title: z.string().min(1),
  dueDate: z.string().optional(),
  completed: z.boolean().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const insertArquivoSchema = z.object({
  fileName: z.string().min(1),
  filePath: z.string().min(1).optional(),
  fileType: z.string().optional(),
  description: z.string().optional(),
  isProfilePhoto: z.boolean().default(false),
  fileSize: z.number().positive().optional(),
  mimeType: z.string().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const horsePhotoUploadSchema = insertArquivoSchema;

export const insertEventoSchema = z.object({
  titulo: z.string().min(1),
  descricao: z.string().optional(),
  data: z.string(),
  horaInicio: z.string().optional(),
  horaFim: z.string().optional(),
  tipo: z.string().optional(),
  status: z.string().optional(),
  prioridade: z.string().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const insertVeterinarioSchema = z.object({
  cavalo_id: z.number(),
  tipo_procedimento: z.string().min(1),
  data_procedimento: z.string(),
  veterinario_responsavel: z.string().optional(),
  diagnostico: z.string().optional(),
  tratamento: z.string().optional(),
  medicamentos: z.string().optional(),
  observacoes: z.string().optional(),
  custo: z.number().optional(),
  proxima_consulta: z.string().optional(),
  user_id: z.number(),
  status: z.string().optional(),
});

export const insertProcedimentoVetSchema = z.object({
  tipo: z.string().min(1),
  descricao: z.string().optional(),
  data: z.string(),
  veterinario: z.string().optional(),
  crmv: z.string().optional(),
  medicamentos: z.string().optional(),
  dosagem: z.string().optional(),
  resultado: z.string().optional(),
  recomendacoes: z.string().optional(),
  horse_id: z.number(),
  user_id: z.number(),
});

// Schema de reprodução movido para shared/insert-schemas.ts com campos corretos

export const insertMedidaFisicaSchema = z.object({
  horse_id: z.number(),
  peso: z.number().optional(),
  altura: z.number().optional(),
  comprimentoCabeca: z.number().optional(),
  larguraCabeca: z.number().optional(),
  circunferenciaPescocoBase: z.number().optional(),
  circunferenciaPescocoMeio: z.number().optional(),
  comprimentoPescocoSuperior: z.number().optional(),
  comprimentoPescocoInferior: z.number().optional(),
  user_id: z.number(),
});

export const insertNutricaoSchema = z.object({
  horse_id: z.number(),
  data: z.string(),
  tipo_alimentacao: z.string().min(1),
  nome_alimento: z.string().min(1),
  quantidade: z.number().positive(),
  unidade_medida: z.string(),
  frequencia_diaria: z.number().int().positive(),
  horarios: z.string().optional(),
  observacoes: z.string().optional(),
  custo_unitario: z.number().positive().optional(),
  custo_mensal: z.number().positive().optional(),
  fornecedor: z.string().optional(),
  recomendacao: z.string().optional(),
  status: z.string().optional(),
  user_id: z.number(),
});

export const insertMorfologiaSchema = z.object({
  horse_id: z.number(),
  dataMedicao: z.string(),
  alturaCernelha: z.number().positive().optional(),
  alturaDorso: z.number().positive().optional(),
  alturaGarupa: z.number().positive().optional(),
  comprimentoCorpo: z.number().positive().optional(),
  comprimentoPescoco: z.number().positive().optional(),
  larguraPeito: z.number().positive().optional(),
  perimetroToracico: z.number().positive().optional(),
  perimetroPescoco: z.number().positive().optional(),
  perimetroCanela: z.number().positive().optional(),
  pontuacaoCabeca: z.number().int().min(0).max(10).optional(),
  pontuacaoPescoco: z.number().int().min(0).max(10).optional(),
  pontuacaoEspalda: z.number().int().min(0).max(10).optional(),
  pontuacaoDorso: z.number().int().min(0).max(10).optional(),
  pontuacaoGarupa: z.number().int().min(0).max(10).optional(),
  pontuacaoMembros: z.number().int().min(0).max(10).optional(),
  pontuacaoAprumos: z.number().int().min(0).max(10).optional(),
  pontuacaoAndamento: z.number().int().min(0).max(10).optional(),
  pontuacaoHarmonia: z.number().int().min(0).max(10).optional(),
  pontuacaoTotal: z.number().positive().optional(),
  observacoes: z.string().optional(),
  responsavelMedicao: z.string().optional(),
  user_id: z.number(),
});

export const insertDesempenhoHistoricoSchema = z.object({
  horse_id: z.number(),
  data: z.string(),
  atividade: z.string().min(1),
  tempo: z.string().optional(),
  distancia: z.number().optional(),
  velocidade: z.number().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const insertGenealogiaSchema = z.object({
  horse_id: z.number(),
  avo_paterno_id: z.number().optional(),
  avo_paterno: z.string().optional(),
  avo_materno_id: z.number().optional(),
  avo_materno: z.string().optional(),
  avo_paterna_id: z.number().optional(),
  avo_paterna: z.string().optional(),
  avo_materna_id: z.number().optional(),
  avo_materna: z.string().optional(),
  bisavo_paterno_paterno: z.string().optional(),
  bisavo_paterna_paterno: z.string().optional(),
  bisavo_materno_paterno: z.string().optional(),
  bisavo_materna_paterno: z.string().optional(),
  bisavo_paterno_materno: z.string().optional(),
  bisavo_paterna_materno: z.string().optional(),
  bisavo_materno_materno: z.string().optional(),
  bisavo_materna_materno: z.string().optional(),
});

export const insertSugestoesCruzamentoSchema = z.object({
  horse_id: z.number(),
  padreiro_sugerido: z.string().min(1),
  motivo: z.string().optional(),
  probabilidade_cor_pelagem: z.string().optional(),
  caracteristicas_esperadas: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const insertPelagemSchema = z.object({
  nome: z.string().min(1),
  descricao: z.string().optional(),
  fonte: z.string().optional(),
  user_id: z.number().optional(),
});

// Schemas financeiros
export const insertCategoriaFinanceiraSchema = z.object({
  nome: z.string().min(1, "Nome da categoria é obrigatório"),
  tipo: z.enum(['receita', 'despesa'], { required_error: "Tipo é obrigatório" }),
  descricao: z.string().optional(),
  ativo: z.boolean().default(true),
  user_id: z.number(),
});

export const insertLancamentoFinanceiroSchema = z.object({
  data: z.string().min(1, "Data é obrigatória"),
  tipo: z.enum(['receita', 'despesa'], { required_error: "Tipo é obrigatório" }),
  categoria_id: z.number().min(1, "Categoria é obrigatória"),
  descricao: z.string().min(1, "Descrição é obrigatória"),
  valor: z.number().positive("Valor deve ser positivo"),
  cavalo_id: z.number().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

// Type definitions
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;
export type Cavalo = typeof cavalos.$inferSelect;
export type InsertCavalo = typeof cavalos.$inferInsert;
export type Manejo = typeof manejos.$inferSelect;
export type InsertManejo = typeof manejos.$inferInsert;
export type Task = typeof tasks.$inferSelect;
export type InsertTask = typeof tasks.$inferInsert;
export type Arquivo = typeof arquivos.$inferSelect;
export type InsertArquivo = typeof arquivos.$inferInsert;
export type Evento = typeof eventos.$inferSelect;
export type InsertEvento = typeof eventos.$inferInsert;
export type ProcedimentoVet = typeof procedimentosVet.$inferSelect;
export type InsertProcedimentoVet = typeof procedimentosVet.$inferInsert;
export type Reproducao = typeof reproducao.$inferSelect;
export type InsertReproducao = typeof reproducao.$inferInsert;
export type Nutricao = typeof nutricao.$inferSelect;
export type InsertNutricao = typeof nutricao.$inferInsert;
export type MedidaMorfologica = typeof medidas_morfologicas.$inferSelect;
export type InsertMedidaMorfologica = typeof medidas_morfologicas.$inferInsert;
export type DesempenhoHistorico = typeof desempenhoHistorico.$inferSelect;
export type InsertDesempenhoHistorico = typeof desempenhoHistorico.$inferInsert;
export type Genealogia = typeof genealogia.$inferSelect;
export type InsertGenealogia = typeof genealogia.$inferInsert;
export type SugestoesCruzamento = typeof sugestoesCruzamento.$inferSelect;
export type InsertSugestoesCruzamento = typeof sugestoesCruzamento.$inferInsert;
export type Pelagem = typeof pelagens.$inferSelect;
export type InsertPelagem = typeof pelagens.$inferInsert;
export type FeedTemplate = typeof feedTemplates.$inferSelect;
export type InsertFeedTemplate = typeof feedTemplates.$inferInsert;
export type FeedPlanItem = typeof feedPlanItems.$inferSelect;
export type InsertFeedPlanItem = typeof feedPlanItems.$inferInsert;
export type StockBatch = typeof stockBatches.$inferSelect;
export type InsertStockBatch = typeof stockBatches.$inferInsert;
export type StockAlert = typeof stockAlerts.$inferSelect;
export type InsertStockAlert = typeof stockAlerts.$inferInsert;
export type CategoriaFinanceira = typeof categorias_financeiras.$inferSelect;
export type InsertCategoriaFinanceira = typeof categorias_financeiras.$inferInsert;
export type LancamentoFinanceiro = typeof lancamentos_financeiros.$inferSelect;
export type InsertLancamentoFinanceiro = typeof lancamentos_financeiros.$inferInsert;

// Form schemas for genealogy
export const cavaloFormSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  breed: z.string().optional(),
  birth_date: dateField,
  sexo: z.enum(["macho", "femea", "macho_castrado"]).optional(),
  cor: z.string().optional(),
  pelagem_id: z.number().optional(),
  status: z.enum(["nao_informado", "Ativo", "Vendido", "Falecido", "Doado", "Reprodução", "Competição", "Tratamento", "Aposentado"]).default("nao_informado"),
  peso: z.number().optional(),
  altura: z.number().optional(),
  data_entrada: dateField,
  data_saida: dateField,
  data_compra: dateField,
  motivo_saida: z.string().optional(),
  numero_registro: z.string().optional(),
  origem: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  valor_compra: z.number().optional(),
  inspetor: z.string().optional(),
  pai_id: z.number().optional(),
  pai_nome: z.string().optional(),
  mae_id: z.number().optional(),
  mae_nome: z.string().optional(),
  observacoes: z.string().optional(),
  pai: z.object({
    tipo: z.enum(["sistema", "externo", "nenhum"]),
    cavaloSistemaId: z.string().optional(),
    cavaloNome: z.string().optional(),
  }).optional(),
  mae: z.object({
    tipo: z.enum(["sistema", "externo", "nenhum"]),
    cavaloSistemaId: z.string().optional(),
    cavaloNome: z.string().optional(),
  }).optional(),
  user_id: z.number().optional(),
});

// Comprehensive schema for horses
export const cavaloSchema = z.object({
  id: z.number().optional(),
  nome: z.string(),
  name: z.string().optional(), // Compatibility field
  breed: z.string().optional(),
  birth_date: dateField,
  sexo: z.enum(["Macho", "Fêmea", "Macho (Castrado)", "Garanhão", "Égua"]).optional(),
  cor: z.string().optional(),
  pelagem_id: z.number().optional(),
  status: z.enum(["Ativo", "Vendido", "Falecido", "Doado", "Emprestado"]).optional(),
  peso: z.number().optional(),
  altura: z.number().optional(),
  data_entrada: dateField,
  data_saida: dateField,
  data_compra: dateField,
  motivo_saida: z.string().optional(),
  numero_registro: z.string().optional(),
  origem: z.string().optional(),
  criador: z.string().optional(),
  proprietario: z.string().optional(),
  valor_compra: z.number().optional(),
  inspetor: z.string().optional(),
  pai_id: z.number().optional(),
  mae_id: z.number().optional(),
  pai_nome: z.string().optional(),
  mae_nome: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

// Form schema for nutrition
export const nutricaoFormSchema = z.object({
  horse_id: z.number({ required_error: "Selecione um animal" }),
  data: z.date({ required_error: "Selecione a data" }),
  tipoAlimentacao: z.string().min(1, "Tipo de alimentação é obrigatório"),
  nomeAlimento: z.string().min(1, "Nome do alimento é obrigatório"),
  quantidade: z.coerce.number().min(0.1, "Quantidade deve ser maior que zero"),
  unidadeMedida: z.string().min(1, "Unidade de medida é obrigatória"),
  frequenciaDiaria: z.coerce.number().min(1, "Frequência deve ser pelo menos 1"),
  horarios: z.string().optional(),
  custoUnitario: z.coerce.number().min(0, "Custo deve ser positivo").optional().nullable(),
  observacoes: z.string().optional(),
  recomendacao: z.string().optional(),
  status: z.string().optional(),
  fornecedor: z.string().optional(),
});

// Form schema for morphology
export const morfologiaFormSchema = z.object({
  horse_id: z.number({ required_error: "Selecione um animal" }),
  dataMedicao: z.date({ required_error: "Selecione a data da medição" }),
  responsavelMedicao: z.string().min(3, "Nome do responsável deve ter pelo menos 3 caracteres"),
  alturaCernelha: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  alturaDorso: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  alturaGarupa: z.coerce.number().min(0, "Altura deve ser positiva").optional().nullable(),
  comprimentoCorpo: z.coerce.number().min(0, "Comprimento deve ser positivo").optional().nullable(),
  comprimentoPescoco: z.coerce.number().min(0, "Comprimento deve ser positivo").optional().nullable(),
  larguraPeito: z.coerce.number().min(0, "Largura deve ser positiva").optional().nullable(),
  perimetroToracico: z.coerce.number().min(0, "Perímetro deve ser positivo").optional().nullable(),
  observacoes: z.string().optional(),
});

// Conversion functions
export function converterFormParaBanco(dadosForm: any, user_id: number) {
  return {
    name: dadosForm.nome || dadosForm.name,
    breed: dadosForm.breed || null,
    birth_date: dadosForm.birth_date || null,
    sexo: dadosForm.sexo || null,
    cor: dadosForm.cor || null,
    pelagem_id: dadosForm.pelagem_id || null,
    status: dadosForm.status || 'ativo',
    peso: dadosForm.peso || null,
    altura: dadosForm.altura || null,
    data_entrada: dadosForm.data_entrada || null,
    data_saida: dadosForm.data_saida || null,
    data_compra: dadosForm.data_compra || null,
    motivo_saida: dadosForm.motivo_saida || null,
    numero_registro: dadosForm.numero_registro || null,
    origem: dadosForm.origem || null,
    criador: dadosForm.criador || null,
    proprietario: dadosForm.proprietario || null,
    valor_compra: dadosForm.valor_compra || null,
    inspetor: dadosForm.inspetor || null,
    pai_id: dadosForm.pai_id || null,
    mae_id: dadosForm.mae_id || null,
    observacoes: dadosForm.observacoes || null,
    user_id: user_id,
  };
}

export function converterBancoParaForm(dadosBanco: any) {
  return {
    name: dadosBanco.name,
    breed: dadosBanco.breed || '',
    birth_date: dadosBanco.birth_date || '',
    sexo: dadosBanco.sexo || '',
    cor: dadosBanco.cor || '',
    status: dadosBanco.status || 'ativo',
  };
}

// Nutrition conversion functions
export function converterNutricaoFormParaBanco(dadosForm: any, user_id: number) {
  return {
    horse_id: dadosForm.horse_id,
    data: dadosForm.data,
    tipo_alimentacao: dadosForm.tipoAlimentacao || dadosForm.tipo_alimentacao || null,
    nome_alimento: dadosForm.nomeAlimento || dadosForm.nome_alimento,
    quantidade: dadosForm.quantidade,
    unidade_medida: dadosForm.unidadeMedida || dadosForm.unidade_medida,
    frequencia_diaria: dadosForm.frequenciaDiaria || dadosForm.frequencia_diaria || 1,
    horarios: dadosForm.horarios || null,
    custo_unitario: dadosForm.custoUnitario || dadosForm.custo_unitario || null,
    custo_mensal: (dadosForm.custoUnitario || dadosForm.custo_unitario) ? 
      ((dadosForm.custoUnitario || dadosForm.custo_unitario) * dadosForm.quantidade * (dadosForm.frequenciaDiaria || dadosForm.frequencia_diaria || 1) * 30) : null,
    observacoes: dadosForm.observacoes || null,
    recomendacao: dadosForm.recomendacao || null,
    status: dadosForm.status || "ativo",
    fornecedor: dadosForm.fornecedor || null,
    user_id: user_id,
  };
}

export function converterNutricaoBancoParaForm(dadosBanco: any) {
  return {
    horse_id: dadosBanco.horse_id,
    data: dadosBanco.data,
    tipoAlimentacao: dadosBanco.tipo_alimentacao,
    nomeAlimento: dadosBanco.nome_alimento,
    quantidade: dadosBanco.quantidade,
    unidadeMedida: dadosBanco.unidade_medida,
    frequenciaDiaria: dadosBanco.frequencia_diaria,
    horarios: dadosBanco.horarios,
    custoUnitario: dadosBanco.custo_unitario,
    observacoes: dadosBanco.observacoes,
    recomendacao: dadosBanco.recomendacao,
    status: dadosBanco.status,
    fornecedor: dadosBanco.fornecedor,
  };
}

// Morphology conversion functions
export function converterMorfologiaFormParaBanco(dadosForm: any, user_id: number) {
  return {
    horse_id: dadosForm.horse_id,
    dataMedicao: dadosForm.dataMedicao,
    responsavelMedicao: dadosForm.responsavelMedicao,
    alturaCernelha: dadosForm.alturaCernelha || null,
    alturaDorso: dadosForm.alturaDorso || null,
    alturaGarupa: dadosForm.alturaGarupa || null,
    comprimentoCorpo: dadosForm.comprimentoCorpo || null,
    comprimentoPescoco: dadosForm.comprimentoPescoco || null,
    larguraPeito: dadosForm.larguraPeito || null,
    perimetroToracico: dadosForm.perimetroToracico || null,
    observacoes: dadosForm.observacoes || null,
    user_id: user_id,
  };
}

export function converterMorfologiaBancoParaForm(dadosBanco: any) {
  return {
    horse_id: dadosBanco.horse_id,
    dataMedicao: dadosBanco.dataMedicao,
    responsavelMedicao: dadosBanco.responsavelMedicao,
    alturaCernelha: dadosBanco.alturaCernelha,
    alturaDorso: dadosBanco.alturaDorso,
    alturaGarupa: dadosBanco.alturaGarupa,
    comprimentoCorpo: dadosBanco.comprimentoCorpo,
    comprimentoPescoco: dadosBanco.comprimentoPescoco,
    larguraPeito: dadosBanco.larguraPeito,
    perimetroToracico: dadosBanco.perimetroToracico,
    observacoes: dadosBanco.observacoes,
  };
}

// User management schemas
export const userRegistrationSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  username: z.string().min(3, "Username deve ter pelo menos 3 caracteres"),
  email: z.string().email("Email inválido"),
  password: z.string().min(8, "Senha deve ter pelo menos 8 caracteres"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Senhas não conferem",
  path: ["confirmPassword"],
});

export const userProfileUpdateSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  avatar_url: z.string().url().optional().or(z.literal("")),
  currentPassword: z.string().optional(),
  newPassword: z.string().min(8, "Nova senha deve ter pelo menos 8 caracteres").optional(),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.newPassword && !data.currentPassword) {
    return false;
  }
  if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "Senha atual é obrigatória para alterar senha, e as senhas devem conferir",
  path: ["currentPassword"],
});

export const adminUserUpdateSchema = z.object({
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  role: z.enum(['USER', 'ADMIN']),
  flags: z.record(z.any()).optional(),
});

// User type exports for management system
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
export type UserProfileUpdate = z.infer<typeof userProfileUpdateSchema>;
export type AdminUserUpdate = z.infer<typeof adminUserUpdateSchema>;