{"timestamp": "2025-06-13T21:11:24.818Z", "summary": {"total_tests": 18, "passed": 9, "failed": 9, "success_rate": "50.0%"}, "modules": {"cavalos": {"list": false, "pelagens": false, "create": false}, "manejos": {"list": true, "tipos": false}, "nutricao": {"list": false, "stats": false}, "genealogia": {"list": false}, "veterinario": {"list": true, "tipos": true}, "reproducao": {"list": false, "stats": false}, "abccc": {"tokens": true}, "assistente": {"chat": true}}, "errors": ["/api/cavalos: ID de usuário não fornecido ou inválido", "/api/pelagens: ID de usuário não fornecido ou inválido", "/api/cavalos: ID de usuário não fornecido ou inválido", "/api/manejos/tipos: ID de usuário não fornecido ou inválido", "/api/nutricao: Erro ao buscar registros de nutrição", "/api/nutricao/estatisticas: ID de usuário não fornecido ou inválido", "/api/genealogia: ID de usuário não fornecido ou inválido", "/api/reproducao: Erro ao buscar registros de reprodução", "/api/reproducao/estatisticas: ID de usuário não fornecido ou inválido"]}