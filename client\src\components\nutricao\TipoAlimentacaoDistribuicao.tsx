import React from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Nutricao } from '@/hooks/use-nutricao';

interface TipoAlimentacaoDistribuicaoProps {
  nutricoes: Nutricao[];
  calculateMonthlyCost: (quantidade: number, frequencia: number, custoUnitario: number) => string;
}

/**
 * Componente para visualizar a distribuição de alimentos por tipo
 * 
 * Este componente mostra um resumo da distribuição dos diferentes
 * tipos de alimentos, agrupando-os por categoria e mostrando estatísticas
 * como custo e consumo.
 */
export const TipoAlimentacaoDistribuicao: React.FC<TipoAlimentacaoDistribuicaoProps> = ({
  nutricoes,
  calculateMonthlyCost,
}) => {
  // Obter tipos únicos de alimentação
  const tiposAlimentacao = Array.from(
    new Set(nutricoes.map(n => n.tipoAlimentacao))
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {tiposAlimentacao.map(tipo => {
        // Filtar nutrições do tipo atual
        const nutricoesDoTipo = nutricoes.filter(n => n.tipoAlimentacao === tipo);
        
        // Calcular custo total desta categoria
        const custoTotal = nutricoesDoTipo.reduce((total, n) => {
          return total + (n.quantidade * n.frequenciaDiaria * 30 * n.custoUnitario);
        }, 0);
        
        // Calcular consumo diário desta categoria (padronizado para kg)
        const consumoDiario = nutricoesDoTipo.reduce((total, n) => {
          if (n.unidadeMedida === 'kg') {
            return total + (n.quantidade * n.frequenciaDiaria);
          }
          if (n.unidadeMedida === 'g') {
            return total + ((n.quantidade / 1000) * n.frequenciaDiaria);
          }
          return total;
        }, 0);
        
        return (
          <Card key={tipo}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base">{tipo}</CardTitle>
                <Badge variant="secondary">
                  {nutricoesDoTipo.length} {nutricoesDoTipo.length === 1 ? 'item' : 'itens'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Consumo Diário:</span>
                  <span className="font-medium">
                    {consumoDiario.toFixed(2)} kg
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Custo Mensal:</span>
                  <span className="font-medium">R$ {custoTotal.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};