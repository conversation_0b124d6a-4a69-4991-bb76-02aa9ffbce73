import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { randomBytes } from 'crypto';
import { createError } from '../error-handler';

/**
 * PROTEÇÃO CONTRA ATAQUES DE FORÇA BRUTA
 */

/**
 * Limitador para rotas de autenticação
 * Mais restritivo para proteger contra ataques de força bruta
 */
export const limiteAutenticacao = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 10, // limite de 10 requisições por janela
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    erro: "Muitas tentativas de login. Por favor, aguarde 15 minutos antes de tentar novamente."
  }
});

/**
 * Limitador para rotas de API que modificam dados
 */
export const limiteModificacao = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: 30, // limite de 30 requisições por janela
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    erro: "Muitas requisições. Por favor, tente novamente em breve."
  }
});

/**
 * Limitador para rotas de API gerais
 */
export const limiteGeral = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: 100, // limite de 100 requisições por janela
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    erro: "Muitas requisições. Por favor, tente novamente em breve."
  }
});

/**
 * PROTEÇÃO CSRF SIMPLIFICADA
 */

// Armazenamento temporário de tokens (em produção, usar Redis ou similar)
const tokenStorage = new Map<string, { token: string, expires: number }>();

// Limpar tokens expirados periodicamente
setInterval(() => {
  const agora = Date.now();
  for (const [sessionId, data] of tokenStorage.entries()) {
    if (data.expires < agora) {
      tokenStorage.delete(sessionId);
    }
  }
}, 60 * 60 * 1000); // Limpar a cada hora

/**
 * Middleware para gerar e verificar tokens CSRF
 */
export function protecaoCsrf(options: { cookieName?: string, headerName?: string, expiresIn?: number } = {}) {
  const cookieName = options.cookieName || 'csrf-token';
  const headerName = options.headerName || 'x-csrf-token';
  const expiresIn = options.expiresIn || 2 * 60 * 60 * 1000; // 2 horas em milissegundos
  
  return {
    /**
     * Middleware para gerar token CSRF
     */
    gerarToken: (req: Request, res: Response, next: NextFunction) => {
      try {
        // Gerar identificador de sessão único se não existir
        const sessionId = req.cookies?.sessionId || 
                         randomBytes(16).toString('hex');
        
        // Gerar token CSRF
        const csrfToken = randomBytes(32).toString('hex');
        const expires = Date.now() + expiresIn;
        
        // Armazenar token
        tokenStorage.set(sessionId, { token: csrfToken, expires });
        
        // Definir cookie de sessão se não existir
        if (!req.cookies?.sessionId) {
          res.cookie('sessionId', sessionId, {
            httpOnly: true,
            sameSite: 'lax',
            secure: process.env.NODE_ENV === 'production',
            maxAge: expiresIn
          });
        }
        
        // Definir cookie CSRF
        res.cookie(cookieName, csrfToken, {
          httpOnly: false, // Precisa ser acessível via JavaScript
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production',
          maxAge: expiresIn
        });
        
        // Adicionar ao objeto de resposta para templates
        res.locals = res.locals || {};
        res.locals.csrfToken = csrfToken;
        
        next();
      } catch (error) {
        next(error);
      }
    },
    
    /**
     * Middleware para verificar token CSRF
     */
    verificarToken: (req: Request, res: Response, next: NextFunction) => {
      // Ignorar métodos que não modificam estado
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
        return next();
      }
      
      try {
        const sessionId = req.cookies?.sessionId;
        const tokenEnviado = req.headers[headerName.toLowerCase()] as string || 
                            req.body?._csrf ||
                            req.query?._csrf;
        
        // Verificar se a sessão existe
        if (!sessionId || !tokenStorage.has(sessionId)) {
          return next(createError.authorization('Sessão inválida ou expirada. Recarregue a página.'));
        }
        
        // Verificar se o token foi enviado
        if (!tokenEnviado) {
          return next(createError.authorization('Token CSRF não fornecido.'));
        }
        
        // Verificar se o token é válido
        const storedData = tokenStorage.get(sessionId);
        
        if (!storedData || storedData.token !== tokenEnviado) {
          return next(createError.authorization('Token CSRF inválido. Possível ataque CSRF.'));
        }
        
        // Verificar se o token expirou
        if (storedData.expires < Date.now()) {
          tokenStorage.delete(sessionId);
          return next(createError.authorization('Token CSRF expirado. Recarregue a página.'));
        }
        
        // Token válido
        next();
      } catch (error) {
        next(createError.authorization('Erro na verificação do token CSRF.', { 
          error: error instanceof Error ? error.message : String(error) 
        }));
      }
    },
    
    /**
     * Endpoint para fornecer o token atual via API
     */
    fornecerToken: (req: Request, res: Response) => {
      res.json({ csrfToken: res.locals.csrfToken });
    }
  };
}

// Criar instância com configurações padrão
export const csrf = protecaoCsrf();