import { Router } from 'express';
import { z } from 'zod';
import { financeiroService } from '../services/financeiro.service';
import { authenticateUser } from '../auth';
import { 
  insertCategoriaFinanceiraSchema, 
  insertLancamentoFinanceiroSchema 
} from '../../shared/schema';

const router = Router();

// Schema para validação de filtros de lançamentos
const filtrosLancamentosSchema = z.object({
  dataInicio: z.string().optional(),
  dataFim: z.string().optional(),
  tipo: z.enum(['receita', 'despesa']).optional(),
  categoria_id: z.string().transform(val => val ? parseInt(val) : undefined).optional(),
  cavalo_id: z.string().transform(val => val ? parseInt(val) : undefined).optional(),
});

const relatorioMensalSchema = z.object({
  mes: z.string().transform(val => parseInt(val)),
  ano: z.string().transform(val => parseInt(val)),
});

// ===== ROTAS DE CATEGORIAS =====

/**
 * GET /api/financeiro/categorias
 * Lista todas as categorias do usuário
 */
router.get('/categorias', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    const categorias = await financeiroService.getCategorias(user_id);
    
    res.json(categorias);
  } catch (error) {
    console.error('Erro ao buscar categorias:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao buscar categorias' 
    });
  }
});

/**
 * GET /api/financeiro/categorias/:id
 * Busca categoria específica
 */
router.get('/categorias/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const categoria = await financeiroService.getCategoria(id, user_id);
    
    if (!categoria) {
      return res.status(404).json({ error: 'Categoria não encontrada' });
    }
    
    res.json(categoria);
  } catch (error) {
    console.error('Erro ao buscar categoria:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao buscar categoria' 
    });
  }
});

/**
 * POST /api/financeiro/categorias
 * Cria nova categoria
 */
router.post('/categorias', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    const data = { ...req.body, user_id };
    
    const categoria = await financeiroService.createCategoria(data);
    
    res.status(201).json(categoria);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Dados inválidos',
        details: error.errors 
      });
    }
    
    console.error('Erro ao criar categoria:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao criar categoria' 
    });
  }
});

/**
 * PUT /api/financeiro/categorias/:id
 * Atualiza categoria
 */
router.put('/categorias/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const categoria = await financeiroService.updateCategoria(id, user_id, req.body);
    
    if (!categoria) {
      return res.status(404).json({ error: 'Categoria não encontrada' });
    }
    
    res.json(categoria);
  } catch (error) {
    console.error('Erro ao atualizar categoria:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao atualizar categoria' 
    });
  }
});

/**
 * DELETE /api/financeiro/categorias/:id
 * Delete categoria
 */
router.delete('/categorias/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const deleted = await financeiroService.deleteCategoria(id, user_id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Categoria não encontrada' });
    }
    
    res.json({ message: 'Categoria deletada com sucesso' });
  } catch (error) {
    if (error.message?.includes('possui lançamentos')) {
      return res.status(400).json({ 
        error: 'Não é possível excluir categoria que possui lançamentos' 
      });
    }
    
    console.error('Erro ao deletar categoria:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao deletar categoria' 
    });
  }
});

// ===== ROTAS DE LANÇAMENTOS =====

/**
 * GET /api/financeiro/lancamentos
 * Lista lançamentos com filtros opcionais
 */
router.get('/lancamentos', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    const filtros = filtrosLancamentosSchema.parse(req.query);
    
    const lancamentos = await financeiroService.getLancamentos(user_id, filtros);
    
    res.json(lancamentos);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Parâmetros inválidos',
        details: error.errors 
      });
    }
    
    console.error('Erro ao buscar lançamentos:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao buscar lançamentos' 
    });
  }
});

/**
 * GET /api/financeiro/lancamentos/:id
 * Busca lançamento específico
 */
router.get('/lancamentos/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const lancamento = await financeiroService.getLancamento(id, user_id);
    
    if (!lancamento) {
      return res.status(404).json({ error: 'Lançamento não encontrado' });
    }
    
    res.json(lancamento);
  } catch (error) {
    console.error('Erro ao buscar lançamento:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao buscar lançamento' 
    });
  }
});

/**
 * POST /api/financeiro/lancamentos
 * Cria novo lançamento
 */
router.post('/lancamentos', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    const data = { ...req.body, user_id };
    
    const lancamento = await financeiroService.createLancamento(data);
    
    res.status(201).json(lancamento);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Dados inválidos',
        details: error.errors 
      });
    }
    
    console.error('Erro ao criar lançamento:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao criar lançamento' 
    });
  }
});

/**
 * PUT /api/financeiro/lancamentos/:id
 * Atualiza lançamento
 */
router.put('/lancamentos/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const lancamento = await financeiroService.updateLancamento(id, user_id, req.body);
    
    if (!lancamento) {
      return res.status(404).json({ error: 'Lançamento não encontrado' });
    }
    
    res.json(lancamento);
  } catch (error) {
    console.error('Erro ao atualizar lançamento:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao atualizar lançamento' 
    });
  }
});

/**
 * DELETE /api/financeiro/lancamentos/:id
 * Delete lançamento
 */
router.delete('/lancamentos/:id', authenticateUser, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const user_id = req.user.id;
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'ID inválido' });
    }
    
    const deleted = await financeiroService.deleteLancamento(id, user_id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Lançamento não encontrado' });
    }
    
    res.json({ message: 'Lançamento deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar lançamento:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao deletar lançamento' 
    });
  }
});

// ===== ROTAS DE RELATÓRIOS =====

/**
 * GET /api/financeiro/relatorio-mensal
 * Gera relatório mensal com agregações
 */
router.get('/relatorio-mensal', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    const { mes, ano } = relatorioMensalSchema.parse(req.query);
    
    if (mes < 1 || mes > 12) {
      return res.status(400).json({ error: 'Mês deve estar entre 1 e 12' });
    }
    
    if (ano < 2000 || ano > 2100) {
      return res.status(400).json({ error: 'Ano inválido' });
    }
    
    const relatorio = await financeiroService.getRelatorioMensal(user_id, mes, ano);
    
    res.json(relatorio);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Parâmetros inválidos',
        details: error.errors 
      });
    }
    
    console.error('Erro ao gerar relatório mensal:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao gerar relatório mensal' 
    });
  }
});

/**
 * POST /api/financeiro/init-categorias
 * Inicializa categorias padrão para usuário
 */
router.post('/init-categorias', authenticateUser, async (req, res) => {
  try {
    const user_id = req.user.id;
    
    await financeiroService.initializeCategoriasPadrao(user_id);
    
    res.json({ message: 'Categorias padrão inicializadas com sucesso' });
  } catch (error) {
    console.error('Erro ao inicializar categorias:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Falha ao inicializar categorias padrão' 
    });
  }
});

export default router;