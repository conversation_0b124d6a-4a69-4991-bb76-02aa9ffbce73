import React from "react";
import { useLocation } from "wouter";
import { CavaloFormSimples } from "../components/cavalo/CavaloFormSimples";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function CadastroSimples() {
  const [, navigate] = useLocation();

  const handleSuccess = (cavalo: any) => {
    console.log("Cavalo cadastrado:", cavalo);
    navigate("/cavalos");
  };

  const handleCancel = () => {
    navigate("/cavalos");
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/cavalos")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Voltar
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Cadastrar Novo Cavalo</h1>
          <p className="text-gray-600">
            Formulário simples - apenas nome é obrigatório
          </p>
        </div>
      </div>

      <CavaloFormSimples
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}