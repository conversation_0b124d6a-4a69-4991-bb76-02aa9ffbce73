import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Calendar,
  Filter,
  Search,
  DollarSign,
  AlertCircle
} from 'lucide-react';
import { formatMoney } from '@/utils/finance';
import { useToast } from '@/hooks/use-toast';
import { 
  useLancamentos, 
  useCategorias, 
  useCreateLancamento, 
  useUpdateLancamento, 
  useDeleteLancamento,
  type LancamentoFinanceiro,
  type FiltrosLancamentos
} from '@/hooks/use-financeiro';
import { useCavalo } from '@/hooks/use-cavalo';

/**
 * Página de lançamentos financeiros refatorada
 * 
 * Funcionalidades MVP:
 * - Listagem de lançamentos com filtros
 * - Criação e edição de lançamentos
 * - Exclusão de lançamentos
 * - Associação com cavalos e categorias
 * - Interface responsiva e intuitiva
 */
export default function LancamentosPageRefactored() {
  const { toast } = useToast();
  
  // Estados locais
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLancamento, setEditingLancamento] = useState<LancamentoFinanceiro | null>(null);
  const [filtros, setFiltros] = useState<FiltrosLancamentos>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Hooks para dados
  const { data: lancamentos, isLoading: loadingLancamentos } = useLancamentos(filtros);
  const { data: categorias } = useCategorias();
  const { cavalosFiltrados: cavalos } = useCavalo();
  
  // Mutations
  const createLancamento = useCreateLancamento();
  const updateLancamento = useUpdateLancamento();
  const deleteLancamento = useDeleteLancamento();

  // Estados do formulário
  const [formData, setFormData] = useState({
    data: new Date().toISOString().split('T')[0],
    tipo: 'despesa' as 'receita' | 'despesa',
    categoria_id: '',
    descricao: '',
    valor: '',
    cavalo_id: '',
    observacoes: '',
  });

  // Filtrar lançamentos por termo de busca
  const lancamentosFiltrados = lancamentos?.filter(lancamento =>
    lancamento.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lancamento.categoria_nome?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lancamento.cavalo_nome?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.categoria_id || !formData.descricao || !formData.valor) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    try {
      const data = {
        data: formData.data,
        tipo: formData.tipo,
        categoria_id: parseInt(formData.categoria_id),
        descricao: formData.descricao,
        valor: parseFloat(formData.valor),
        cavalo_id: formData.cavalo_id ? parseInt(formData.cavalo_id) : undefined,
        observacoes: formData.observacoes || undefined,
      };

      if (editingLancamento) {
        await updateLancamento.mutateAsync({ id: editingLancamento.id, data });
        toast({
          title: "Sucesso",
          description: "Lançamento atualizado com sucesso",
        });
      } else {
        await createLancamento.mutateAsync(data);
        toast({
          title: "Sucesso",
          description: "Lançamento criado com sucesso",
        });
      }

      handleCloseDialog();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao salvar lançamento",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (lancamento: LancamentoFinanceiro) => {
    setEditingLancamento(lancamento);
    setFormData({
      data: lancamento.data,
      tipo: lancamento.tipo,
      categoria_id: lancamento.categoria_id.toString(),
      descricao: lancamento.descricao,
      valor: lancamento.valor.toString(),
      cavalo_id: lancamento.cavalo_id?.toString() || '',
      observacoes: lancamento.observacoes || '',
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Tem certeza que deseja excluir este lançamento?')) return;
    
    try {
      await deleteLancamento.mutateAsync(id);
      toast({
        title: "Sucesso",
        description: "Lançamento excluído com sucesso",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao excluir lançamento",
        variant: "destructive",
      });
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingLancamento(null);
    setFormData({
      data: new Date().toISOString().split('T')[0],
      tipo: 'despesa',
      categoria_id: '',
      descricao: '',
      valor: '',
      cavalo_id: '',
      observacoes: '',
    });
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        <div>
          <Label htmlFor="data">Data *</Label>
          <Input
            id="data"
            type="date"
            value={formData.data}
            onChange={(e) => setFormData(prev => ({ ...prev, data: e.target.value }))}
            required
          />
        </div>
        
        <div>
          <Label htmlFor="tipo">Tipo *</Label>
          <Select value={formData.tipo} onValueChange={(value: 'receita' | 'despesa') => 
            setFormData(prev => ({ ...prev, tipo: value }))
          }>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="receita">Receita</SelectItem>
              <SelectItem value="despesa">Despesa</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        <div>
          <Label htmlFor="categoria">Categoria *</Label>
          <Select value={formData.categoria_id} onValueChange={(value) =>
            setFormData(prev => ({ ...prev, categoria_id: value }))
          }>
            <SelectTrigger>
              <SelectValue placeholder="Selecione uma categoria" />
            </SelectTrigger>
            <SelectContent>
              {categorias?.filter(c => c.tipo === formData.tipo).map((categoria) => (
                <SelectItem key={categoria.id} value={categoria.id.toString()}>
                  {categoria.nome}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="valor">Valor *</Label>
          <Input
            id="valor"
            type="number"
            step="0.01"
            min="0"
            value={formData.valor}
            onChange={(e) => setFormData(prev => ({ ...prev, valor: e.target.value }))}
            placeholder="0,00"
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="descricao">Descrição *</Label>
        <Input
          id="descricao"
          value={formData.descricao}
          onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
          placeholder="Descrição do lançamento"
          required
        />
      </div>

      <div>
        <Label htmlFor="cavalo">Cavalo (Opcional)</Label>
        <Select value={formData.cavalo_id} onValueChange={(value) =>
          setFormData(prev => ({ ...prev, cavalo_id: value }))
        }>
          <SelectTrigger>
            <SelectValue placeholder="Selecione um cavalo (opcional)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="sem-cavalo">Nenhum cavalo</SelectItem>
            {cavalos?.map((cavalo) => (
              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                {cavalo.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="observacoes">Observações</Label>
        <Textarea
          id="observacoes"
          value={formData.observacoes}
          onChange={(e) => setFormData(prev => ({ ...prev, observacoes: e.target.value }))}
          placeholder="Observações adicionais (opcional)"
          rows={3}
        />
      </div>

      <div className="flex flex-col sm:flex-row gap-2 pt-4">
        <Button 
          type="submit" 
          disabled={createLancamento.isPending || updateLancamento.isPending}
          className="w-full sm:w-auto"
        >
          {editingLancamento ? 'Atualizar' : 'Criar'} Lançamento
        </Button>
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleCloseDialog}
          className="w-full sm:w-auto"
        >
          Cancelar
        </Button>
      </div>
    </form>
  );

  return (
    <div className="space-y-3 sm:space-y-4">
      {/* Controles */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar lançamentos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full sm:w-64"
            />
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                onClick={() => setEditingLancamento(null)} 
                className="w-full sm:w-auto"
              >
                <Plus className="mr-2 h-4 w-4" />
                <span className="sm:inline">Novo Lançamento</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="mx-2 sm:mx-0 w-[calc(100vw-1rem)] sm:w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              style={{ 
                padding: '15px',
                position: 'fixed',
                top: '5%',
                left: '50%',
                transform: 'translateX(-50%)'
              }}>
              <DialogHeader>
                <DialogTitle>
                  {editingLancamento ? 'Editar' : 'Novo'} Lançamento
                </DialogTitle>
              </DialogHeader>
              {renderForm()}
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Verificação de categorias */}
      {categorias?.length === 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Você precisa criar categorias antes de adicionar lançamentos. 
            Acesse a aba "Categorias" para criar suas primeiras categorias.
          </AlertDescription>
        </Alert>
      )}

      {/* Lista de lançamentos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Lançamentos Recentes
          </CardTitle>
          <CardDescription>
            {lancamentosFiltrados.length} lançamento(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingLancamentos ? (
            <div className="text-center py-8">Carregando lançamentos...</div>
          ) : lancamentosFiltrados.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Nenhum lançamento encontrado
            </div>
          ) : (
            <>
              {/* Tabela para desktop */}
              <div className="hidden sm:block overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead className="hidden lg:table-cell">Cavalo</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead className="text-right">Valor</TableHead>
                      <TableHead className="text-center">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                <TableBody>
                  {lancamentosFiltrados.map((lancamento) => (
                    <TableRow key={lancamento.id}>
                      <TableCell>
                        {new Date(lancamento.data).toLocaleDateString('pt-BR')}
                      </TableCell>
                      <TableCell className="font-medium">
                        {lancamento.descricao}
                      </TableCell>
                      <TableCell>
                        {lancamento.categoria_nome || 'N/A'}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {lancamento.cavalo_nome || '-'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={lancamento.tipo === 'receita' ? 'default' : 'destructive'}>
                          {lancamento.tipo === 'receita' ? 'Receita' : 'Despesa'}
                        </Badge>
                      </TableCell>
                      <TableCell className={`text-right font-bold ${
                        lancamento.tipo === 'receita' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatMoney(lancamento.valor)}
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex gap-2 justify-center">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(lancamento)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(lancamento.id)}
                            disabled={deleteLancamento.isPending}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  </TableBody>
                </Table>
              </div>

              {/* Cards para mobile */}
              <div className="sm:hidden space-y-3">
                {lancamentosFiltrados.map((lancamento) => (
                  <Card key={lancamento.id} className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="font-medium text-base">{lancamento.descricao}</div>
                        <div className="text-sm text-gray-500">
                          {new Date(lancamento.data).toLocaleDateString('pt-BR')}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-bold text-lg ${
                          lancamento.tipo === 'receita' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatMoney(lancamento.valor)}
                        </div>
                        <Badge 
                          variant={lancamento.tipo === 'receita' ? 'default' : 'destructive'}
                          className="text-xs"
                        >
                          {lancamento.tipo === 'receita' ? 'Receita' : 'Despesa'}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-600">
                        <div>Categoria: {lancamento.categoria_nome || 'N/A'}</div>
                        {lancamento.cavalo_nome && (
                          <div>Cavalo: {lancamento.cavalo_nome}</div>
                        )}
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(lancamento)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(lancamento.id)}
                          disabled={deleteLancamento.isPending}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}