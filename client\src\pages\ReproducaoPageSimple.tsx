import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Heart, Search, Filter, BarChart3 } from 'lucide-react';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

interface ReproducaoData {
  id: number;
  egua_id: number;        // Campo correto da API
  garanhao_id?: number;   // Campo correto da API
  horse_id?: number;      // Manter compatibilidade
  padreiro_id?: number;   // Manter compatibilidade
  data_cobertura: string;
  tipo_cobertura: string;
  estado?: string;
  resultado?: string;     // Campo alternativo da API
  observacoes?: string;
  user_id: number;
  created_at: string;
}

const ReproducaoPageSimple = () => {
  const [user, setUser] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<ReproducaoData | null>(null);
  const [formData, setFormData] = useState({
    egua_id: '',
    garanhao_id: '',
    data_cobertura: '',
    tipo_cobertura: '',
    resultado: '',
    observacoes: ''
  });

  // Estados para filtros e busca
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEstado, setFilterEstado] = useState('todos');
  const [filterTipo, setFilterTipo] = useState('todos');

  const toast = useToast();
  const queryClient = useQueryClient();

  // Carregar usuário
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);

  // Buscar reproduções
  const { data: reproducoes = [], isLoading } = useQuery({
    queryKey: ['/api/reproducao'],
    queryFn: async () => {
      const userStr = localStorage.getItem('user');
      if (!userStr) throw new Error("Usuário não autenticado");
      
      const user = JSON.parse(userStr);
      const response = await fetch('/api/reproducao', {
        headers: { 'user-id': user.id.toString() }
      });
      
      if (!response.ok) throw new Error('Erro ao buscar reproduções');
      return response.json();
    },
    enabled: !!user
  });

  // Buscar cavalos
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      const userStr = localStorage.getItem('user');
      if (!userStr) throw new Error("Usuário não autenticado");
      
      const user = JSON.parse(userStr);
      const response = await fetch('/api/cavalos', {
        headers: { 'user-id': user.id.toString() }
      });
      
      if (!response.ok) throw new Error('Erro ao buscar cavalos');
      return response.json();
    },
    enabled: !!user
  });

  // Mutation para criar/atualizar
  const saveMutation = useMutation({
    mutationFn: async (data: any) => {
      const userStr = localStorage.getItem('user');
      if (!userStr) throw new Error("Usuário não autenticado");
      
      const user = JSON.parse(userStr);
      const url = editingItem ? `/api/reproducao/${editingItem.id}` : '/api/reproducao';
      const method = editingItem ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'user-id': user.id.toString()
        },
        body: JSON.stringify({
          ...data,
          user_id: user.id,
          egua_id: parseInt(data.egua_id),
          garanhao_id: data.garanhao_id ? parseInt(data.garanhao_id) : null
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao salvar');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast.toast({
        title: "Sucesso",
        description: `Registro ${editingItem ? 'atualizado' : 'criado'} com sucesso`
      });
      queryClient.invalidateQueries({ queryKey: ['/api/reproducao'] });
      handleCloseDialog();
    },
    onError: (error: any) => {
      toast.toast({
        title: "Erro",
        description: error.message || "Erro ao salvar registro",
        variant: "destructive"
      });
    }
  });

  // Mutation para deletar
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const userStr = localStorage.getItem('user');
      if (!userStr) throw new Error("Usuário não autenticado");
      
      const user = JSON.parse(userStr);
      const response = await fetch(`/api/reproducao/${id}`, {
        method: 'DELETE',
        headers: { 'user-id': user.id.toString() }
      });

      if (!response.ok) throw new Error('Erro ao excluir');
      return true;
    },
    onSuccess: () => {
      toast.toast({
        title: "Sucesso",
        description: "Registro excluído com sucesso"
      });
      queryClient.invalidateQueries({ queryKey: ['/api/reproducao'] });
    },
    onError: (error: any) => {
      toast.toast({
        title: "Erro",
        description: error.message || "Erro ao excluir registro",
        variant: "destructive"
      });
    }
  });

  const handleOpenDialog = (item?: ReproducaoData) => {
    if (item && (item.egua_id || item.horse_id)) {
      setEditingItem(item);
      setFormData({
        egua_id: (item.egua_id || item.horse_id)?.toString() || '',
        garanhao_id: (item.garanhao_id || item.padreiro_id)?.toString() || '',
        data_cobertura: item.data_cobertura?.split('T')[0] || '',
        tipo_cobertura: item.tipo_cobertura || '',
        resultado: item.resultado || item.estado || '',
        observacoes: item.observacoes || ''
      });
    } else {
      setEditingItem(null);
      setFormData({
        egua_id: '',
        garanhao_id: '',
        data_cobertura: '',
        tipo_cobertura: '',
        resultado: '',
        observacoes: ''
      });
    }
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingItem(null);
    setFormData({
      egua_id: '',
      garanhao_id: '',
      data_cobertura: '',
      tipo_cobertura: '',
      resultado: '',
      observacoes: ''
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveMutation.mutate(formData);
  };

  const handleDelete = (id: number) => {
    if (confirm('Tem certeza que deseja excluir este registro?')) {
      deleteMutation.mutate(id);
    }
  };

  const getHorseName = (id: number | undefined | null) => {
    if (!id || !cavalos || cavalos.length === 0) return 'Não especificado';
    
    // Garantir que estamos comparando os tipos corretos
    const horseId = Number(id);
    const horse = cavalos.find((h: any) => Number(h.id) === horseId);
    
    if (horse && horse.name) {
      return horse.name;
    }
    
    return 'Não especificado';
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: pt });
    } catch {
      return 'Data inválida';
    }
  };

  const formatTipoCobertura = (tipo: string) => {
    const tipos: Record<string, string> = {
      'monta_natural': 'Monta Natural',
      'inseminacao_artificial': 'Inseminação Artificial',
      'transferencia_embriao': 'Transferência de Embrião'
    };
    return tipos[tipo] || tipo;
  };

  const formatEstado = (estado: string) => {
    const estados: Record<string, string> = {
      'pre_cobertura': 'Pré-Cobertura',
      'em_cobertura': 'Em Cobertura',
      'gestante': 'Gestante',
      'parto_realizado': 'Parto Realizado',
      'falha_embriao': 'Falha de Embrião',
      'aborto': 'Aborto',
      'nao_prenhe': 'Não Prenhe'
    };
    return estados[estado] || estado;
  };

  const getEstadoBadgeClass = (estado: string) => {
    const classes: Record<string, string> = {
      'gestante': 'bg-green-100 text-green-800',
      'parto_realizado': 'bg-blue-100 text-blue-800',
      'pre_cobertura': 'bg-yellow-100 text-yellow-800',
      'em_cobertura': 'bg-orange-100 text-orange-800',
      'falha_embriao': 'bg-red-100 text-red-800',
      'aborto': 'bg-red-100 text-red-800',
      'nao_prenhe': 'bg-gray-100 text-gray-800'
    };
    return classes[estado] || 'bg-gray-100 text-gray-800';
  };

  // Filtrar dados baseado na busca e filtros
  const reproducoesFiltradas = reproducoes.filter((item: ReproducaoData) => {
    const horseName = getHorseName(item.egua_id || item.horse_id).toLowerCase();
    const padreiroName = getHorseName(item.garanhao_id || item.padreiro_id).toLowerCase();
    const searchMatch = searchTerm === '' || 
      horseName.includes(searchTerm.toLowerCase()) || 
      padreiroName.includes(searchTerm.toLowerCase());
    
    const estadoMatch = filterEstado === 'todos' || (item.estado || item.resultado) === filterEstado;
    const tipoMatch = filterTipo === 'todos' || item.tipo_cobertura === filterTipo;
    
    return searchMatch && estadoMatch && tipoMatch;
  });

  // Calcular estatísticas
  const estatisticas = {
    total: reproducoes.length,
    gestantes: reproducoes.filter((r: any) => (r.estado || r.resultado) === 'gestante').length,
    emCobertura: reproducoes.filter((r: any) => (r.estado || r.resultado) === 'em_cobertura').length,
    partosRealizados: reproducoes.filter((r: any) => (r.estado || r.resultado) === 'parto_realizado').length,
    montaNatural: reproducoes.filter((r: any) => r.tipo_cobertura === 'monta_natural').length,
    inseminacao: reproducoes.filter((r: any) => r.tipo_cobertura === 'inseminacao_artificial').length
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Carregando...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Heart className="h-8 w-8 text-red-500" />
            Reprodução
          </h1>
          <p className="text-muted-foreground">
            Gerenciamento de reprodução e cobertura
          </p>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.total}</p>
                <p className="text-xs text-muted-foreground">Total</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.gestantes}</p>
                <p className="text-xs text-muted-foreground">Gestantes</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.emCobertura}</p>
                <p className="text-xs text-muted-foreground">Em Cobertura</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.partosRealizados}</p>
                <p className="text-xs text-muted-foreground">Partos</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.montaNatural}</p>
                <p className="text-xs text-muted-foreground">Monta Natural</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Heart className="h-4 w-4 text-cyan-500" />
              <div>
                <p className="text-2xl font-bold">{estatisticas.inseminacao}</p>
                <p className="text-xs text-muted-foreground">Inseminação</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros e Busca */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por égua ou garanhão..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={filterEstado} onValueChange={setFilterEstado}>
                <SelectTrigger className="w-40">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos Estados</SelectItem>
                  <SelectItem value="gestante">Gestantes</SelectItem>
                  <SelectItem value="em_cobertura">Em Cobertura</SelectItem>
                  <SelectItem value="parto_realizado">Parto Realizado</SelectItem>
                  <SelectItem value="pre_cobertura">Pré-Cobertura</SelectItem>
                  <SelectItem value="nao_prenhe">Não Prenhe</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterTipo} onValueChange={setFilterTipo}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos Tipos</SelectItem>
                  <SelectItem value="monta_natural">Monta Natural</SelectItem>
                  <SelectItem value="inseminacao_artificial">Inseminação</SelectItem>
                  <SelectItem value="transferencia_embriao">Transfer. Embrião</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center justify-between">
        <div></div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => handleOpenDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Novo Registro
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingItem ? 'Editar' : 'Novo'} Registro de Reprodução
              </DialogTitle>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Égua *</Label>
                  <Select 
                    value={formData.egua_id} 
                    onValueChange={(value) => setFormData({...formData, egua_id: value})}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a égua" />
                    </SelectTrigger>
                    <SelectContent>
                      {cavalos.filter((c: any) => c.sexo === 'Fêmea' || c.sexo === 'Égua').map((cavalo: any) => (
                        <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                          {cavalo.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Garanhão</Label>
                  <Select 
                    value={formData.garanhao_id} 
                    onValueChange={(value) => setFormData({...formData, garanhao_id: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o garanhão" />
                    </SelectTrigger>
                    <SelectContent>
                      {cavalos.filter((c: any) => c.sexo === 'Garanhão').map((cavalo: any) => (
                        <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                          {cavalo.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Data da Cobertura *</Label>
                  <Input 
                    type="date" 
                    value={formData.data_cobertura}
                    onChange={(e) => setFormData({...formData, data_cobertura: e.target.value})}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Tipo de Cobertura *</Label>
                  <Select 
                    value={formData.tipo_cobertura} 
                    onValueChange={(value) => setFormData({...formData, tipo_cobertura: value})}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monta_natural">Monta Natural</SelectItem>
                      <SelectItem value="inseminacao_artificial">Inseminação Artificial</SelectItem>
                      <SelectItem value="transferencia_embriao">Transferência de Embrião</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Estado *</Label>
                <Select 
                  value={formData.resultado} 
                  onValueChange={(value) => setFormData({...formData, resultado: value})}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pre_cobertura">Pré-Cobertura</SelectItem>
                    <SelectItem value="em_cobertura">Em Cobertura</SelectItem>
                    <SelectItem value="gestante">Gestante</SelectItem>
                    <SelectItem value="parto_realizado">Parto Realizado</SelectItem>
                    <SelectItem value="falha_embriao">Falha de Embrião</SelectItem>
                    <SelectItem value="aborto">Aborto</SelectItem>
                    <SelectItem value="nao_prenhe">Não Prenhe</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Observações</Label>
                <Textarea 
                  value={formData.observacoes}
                  onChange={(e) => setFormData({...formData, observacoes: e.target.value})}
                  placeholder="Observações sobre a reprodução..."
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={saveMutation.isPending}>
                  {saveMutation.isPending ? 'Salvando...' : (editingItem ? 'Atualizar' : 'Criar')}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Registros de Reprodução</CardTitle>
          <CardDescription>
            {reproducoesFiltradas.length} de {reproducoes.length} registro(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <p>Carregando registros...</p>
            </div>
          ) : reproducoesFiltradas.length === 0 ? (
            <div className="text-center py-8">
              <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {reproducoes.length === 0 ? 'Nenhum registro encontrado' : 'Nenhum resultado para os filtros aplicados'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {reproducoes.length === 0 
                  ? 'Comece criando seu primeiro registro de reprodução.'
                  : 'Tente ajustar os filtros ou limpar a busca.'
                }
              </p>
              {reproducoes.length === 0 ? (
                <Button onClick={() => handleOpenDialog()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Primeiro Registro
                </Button>
              ) : (
                <div className="space-x-2">
                  <Button variant="outline" onClick={() => {
                    setSearchTerm('');
                    setFilterEstado('todos');
                    setFilterTipo('todos');
                  }}>
                    Limpar Filtros
                  </Button>
                  <Button onClick={() => handleOpenDialog()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Novo Registro
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Égua</TableHead>
                  <TableHead>Garanhão</TableHead>
                  <TableHead>Data Cobertura</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reproducoesFiltradas.map((item: ReproducaoData) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{getHorseName(item.egua_id || item.horse_id)}</TableCell>
                      <TableCell>{getHorseName(item.garanhao_id || item.padreiro_id)}</TableCell>
                      <TableCell>{formatDate(item.data_cobertura)}</TableCell>
                      <TableCell>{formatTipoCobertura(item.tipo_cobertura || '')}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoBadgeClass(item.estado || item.resultado || '')}`}>
                          {formatEstado(item.estado || item.resultado || '')}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleOpenDialog(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDelete(item.id)}
                            disabled={deleteMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ReproducaoPageSimple;