import { sanitizeHorseData, sanitizeAndValidateHorseData, validateRequiredHorseFields } from '../utils/sanitize-horse-data';

describe('sanitizeHorseData', () => {
  it('should remove invalid genealogy fields', () => {
    const input = {
      name: 'Test Horse',
      breed: 'Crioulo',
      user_id: 1,
      pai_nome: 'Invalid Field',
      mae_nome: 'Invalid Field',
      avo_paterno: 'Invalid Field',
      avo_materno: 'Invalid Field',
      pai_id: 10,
      mae_id: 11
    };

    const result = sanitizeHorseData(input);

    expect(result.name).toBe('Test Horse');
    expect(result.breed).toBe('Crioulo');
    expect(result.user_id).toBe(1);
    expect(result.pai_id).toBe(10);
    expect(result.mae_id).toBe(11);
    expect(result.pai_nome).toBeUndefined();
    expect(result.mae_nome).toBeUndefined();
    expect(result.avo_paterno).toBeUndefined();
    expect(result.avo_materno).toBeUndefined();
  });

  it('should remove complex genealogy objects', () => {
    const input = {
      name: 'Test Horse',
      user_id: 1,
      pai: { tipo: 'sistema', cavaloSistemaId: 10 },
      mae: { tipo: 'externo', cavaloNome: 'External Mare' },
      genealogia: { some: 'data' }
    };

    const result = sanitizeHorseData(input);

    expect(result.name).toBe('Test Horse');
    expect(result.user_id).toBe(1);
    expect(result.pai).toBeUndefined();
    expect(result.mae).toBeUndefined();
    expect(result.genealogia).toBeUndefined();
  });

  it('should map observacoes to notes', () => {
    const input = {
      name: 'Test Horse',
      user_id: 1,
      observacoes: 'Some observations'
    };

    const result = sanitizeHorseData(input);

    expect(result.notes).toBe('Some observations');
    expect(result.observacoes).toBeUndefined();
  });

  it('should convert empty strings to null', () => {
    const input = {
      name: 'Test Horse',
      user_id: 1,
      breed: '',
      peso: '',
      altura: undefined
    };

    const result = sanitizeHorseData(input);

    expect(result.name).toBe('Test Horse');
    expect(result.user_id).toBe(1);
    expect(result.breed).toBeNull();
    expect(result.peso).toBeNull();
    expect(result.altura).toBeNull();
  });

  it('should preserve valid fields', () => {
    const input = {
      name: 'Test Horse',
      breed: 'Crioulo',
      birth_date: '2020-01-01',
      sexo: 'Macho',
      cor: 'Tordilho',
      pelagem_id: 1,
      status: 'ativo',
      user_id: 1,
      notes: 'Valid notes',
      peso: 450,
      altura: 1.5,
      pai_id: 10,
      mae_id: 11
    };

    const result = sanitizeHorseData(input);

    expect(result.name).toBe('Test Horse');
    expect(result.breed).toBe('Crioulo');
    expect(result.birth_date).toBe('2020-01-01');
    expect(result.sexo).toBe('Macho');
    expect(result.cor).toBe('Tordilho');
    expect(result.pelagem_id).toBe(1);
    expect(result.status).toBe('ativo');
    expect(result.user_id).toBe(1);
    expect(result.notes).toBe('Valid notes');
    expect(result.peso).toBe(450);
    expect(result.altura).toBe(1.5);
    expect(result.pai_id).toBe(10);
    expect(result.mae_id).toBe(11);
  });
});

describe('validateRequiredHorseFields', () => {
  it('should pass validation with required fields', () => {
    const data = {
      name: 'Test Horse',
      user_id: 1
    };

    expect(() => validateRequiredHorseFields(data)).not.toThrow();
  });

  it('should throw error when name is missing', () => {
    const data = {
      user_id: 1
    };

    expect(() => validateRequiredHorseFields(data)).toThrow('Campos obrigatórios ausentes: name');
  });

  it('should throw error when user_id is missing', () => {
    const data = {
      name: 'Test Horse'
    };

    expect(() => validateRequiredHorseFields(data)).toThrow('Campos obrigatórios ausentes: user_id');
  });

  it('should throw error when both fields are missing', () => {
    const data = {};

    expect(() => validateRequiredHorseFields(data)).toThrow('Campos obrigatórios ausentes: name, user_id');
  });
});

describe('sanitizeAndValidateHorseData', () => {
  it('should sanitize and validate successfully', () => {
    const input = {
      name: 'Test Horse',
      breed: 'Crioulo',
      user_id: 1,
      pai_nome: 'Invalid Field',
      observacoes: 'Some notes'
    };

    const result = sanitizeAndValidateHorseData(input);

    expect(result.name).toBe('Test Horse');
    expect(result.breed).toBe('Crioulo');
    expect(result.user_id).toBe(1);
    expect(result.notes).toBe('Some notes');
    expect(result.pai_nome).toBeUndefined();
    expect(result.observacoes).toBeUndefined();
  });

  it('should throw error when required fields are missing after sanitization', () => {
    const input = {
      breed: 'Crioulo',
      pai_nome: 'Invalid Field'
    };

    expect(() => sanitizeAndValidateHorseData(input)).toThrow('Campos obrigatórios ausentes: name, user_id');
  });
});