import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { GenealogyNode } from '@/components/genetics/GenealogyTree';
import { Cavalo } from '@/types/cavalo';

export interface UseGenealogyDataProps {
  initialHorseId?: number | null;
}

// Interface para a resposta da API de cavalo
interface CavaloResponse {
  id: number;
  name: string;
  breed?: string;
  cor?: string;
  sexo?: string;
  pai_id?: number | null;
  pai_nome?: string | null;
  pai?: string | null;
  mae_id?: number | null;
  mae_nome?: string | null;
  mae?: string | null;
  avo_paterno?: string | null;
  avo_materno?: string | null;
  [key: string]: any;
}

// Interface para adaptar a resposta da API de genealogia
interface GenealogyApiResponse {
  id: number;
  horse_id: number;
  pai: string | null; // Nome do pai
  mae: string | null; // Nome da mãe
  coeficienteConsanguinidade?: number | null;
  observacoes?: string | null;
  [key: string]: any;
}

// Interface adaptada para facilitar o uso nos componentes
export interface AdaptedGenealogia {
  cavalo?: CavaloResponse;
  pai?: CavaloResponse;
  mae?: CavaloResponse;
  consanguinidade: number;
  observacoes?: string;
  bisavosConhecidos: number;
}

export const useGenealogyData = ({ initialHorseId }: UseGenealogyDataProps = {}) => {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(initialHorseId || null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { toast } = useToast();

  // Buscar cavalos
  const {
    data: cavalos,
    isLoading: isLoadingCavalos,
    error: cavalosError,
  } = useQuery<CavaloResponse[]>({
    queryKey: ['/api/cavalos'],
  });

  // Buscar cavalo selecionado para obter detalhes
  const {
    data: selectedCavaloData,
    isLoading: isLoadingSelectedCavalo,
  } = useQuery<CavaloResponse>({
    queryKey: ['/api/cavalos', selectedHorseId],
    enabled: selectedHorseId !== null,
  });

  // Buscar genealogia do cavalo selecionado
  const {
    data: genealogiaApi,
    isLoading: isLoadingGenealogia,
    error: genealogiaError,
    refetch: refetchGenealogia,
  } = useQuery<GenealogyApiResponse>({
    queryKey: ['/api/cavalos', selectedHorseId, 'genealogia'],
    enabled: selectedHorseId !== null,
  });
  
  // Log para debug
  useEffect(() => {
    if (genealogiaApi) {
      console.log("Dados de genealogia recebidos:", genealogiaApi);
    }
  }, [genealogiaApi]);

  // Selecionar o primeiro cavalo quando a lista for carregada
  useEffect(() => {
    if (cavalos && Array.isArray(cavalos) && cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Mostrar erros
  useEffect(() => {
    if (cavalosError) {
      toast({
        title: 'Erro ao carregar cavalos',
        description: 'Não foi possível carregar a lista de cavalos.',
        variant: 'destructive',
      });
    }
    if (genealogiaError) {
      toast({
        title: 'Erro ao carregar genealogia',
        description: 'Não foi possível carregar os dados genealógicos.',
        variant: 'destructive',
      });
    }
  }, [cavalosError, genealogiaError, toast]);

  // Encontrar um cavalo por ID
  const findHorseById = (id: number | null | undefined): CavaloResponse | undefined => {
    if (!id || !Array.isArray(cavalos)) return undefined;
    return cavalos.find(c => c.id === id);
  };

  // Encontrar um cavalo por nome
  const findHorseByName = (name: string | null | undefined): CavaloResponse | undefined => {
    if (!name || !Array.isArray(cavalos)) return undefined;
    return cavalos.find(c => c.name && c.name.toLowerCase() === name.toLowerCase());
  };

  // Obter o cavalo selecionado
  const selectedHorse = selectedCavaloData || findHorseById(selectedHorseId);

  // Funções de manipulação
  const handleEditGenealogia = () => {
    setIsEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    refetchGenealogia();
  };

  const handleParentClick = (id: number | null) => {
    if (id === null) return;
    
    // Se clicar no cavalo atual, abrir edição
    if (id === selectedHorseId) {
      handleEditGenealogia();
      return;
    }
    
    // Se clicar em outro cavalo, navegar para sua página de genealogia
    setSelectedHorseId(id);
    toast({
      title: 'Carregando genealogia',
      description: 'Buscando informações do ancestral selecionado.'
    });
  };

  // Converter dados da API para o formato esperado pelo componente de árvore genealógica
  const convertToGenealogyNode = (cavalo: CavaloResponse | undefined): GenealogyNode | null => {
    if (!cavalo) return null;

    const convertToGender = (sexo?: string): 'M' | 'F' => {
      if (!sexo) return 'M';
      return sexo.toUpperCase().includes('F') ? 'F' : 'M';
    };

    // Buscar nomes dos pais nos campos corretos do banco de dados
    let pai_nome = cavalo.pai_nome || cavalo.pai || '';
    let mae_nome = cavalo.mae_nome || cavalo.mae || '';
    
    console.log(`Cavalo ${cavalo.name}: pai_nome=${cavalo.pai_nome}, mae_nome=${cavalo.mae_nome}, pai=${cavalo.pai}, mae=${cavalo.mae}`);
    
    // Se tivermos dados da API de genealogia, usar esses dados primeiro
    if (genealogiaApi) {
      if (genealogiaApi.pai) pai_nome = genealogiaApi.pai;
      if (genealogiaApi.mae) mae_nome = genealogiaApi.mae;
    }

    // Encontrar pai pelo ID ou nome
    const pai = cavalo.pai_id ? findHorseById(cavalo.pai_id) : 
                pai_nome ? findHorseByName(pai_nome) : 
                undefined;

    // Encontrar mãe pelo ID ou nome
    const mae = cavalo.mae_id ? findHorseById(cavalo.mae_id) : 
                mae_nome ? findHorseByName(mae_nome) : 
                undefined;

    // Calcular coeficiente de consanguinidade (usando dados da API se disponíveis)
    const consanguinity = genealogiaApi?.coeficienteConsanguinidade !== null && 
                          genealogiaApi?.coeficienteConsanguinidade !== undefined ?
                          genealogiaApi.coeficienteConsanguinidade / 100 : 0;

    // Criar nó raiz
    const rootNode: GenealogyNode = {
      id: cavalo.id,
      name: cavalo.name,
      gender: convertToGender(cavalo.sexo),
      breed: cavalo.breed,
      color: cavalo.cor,
      consanguinity: consanguinity,
      isHighlighted: true,
    };

    // Adicionar pai se disponível
    if (pai) {
      rootNode.father = {
        id: pai.id,
        name: pai.name,
        gender: 'M',
        breed: pai.breed,
        color: pai.cor,
      };
    } else if (pai_nome) {
      // Se temos apenas o nome do pai (da API ou do cavalo)
      rootNode.father = {
        id: null,
        name: pai_nome,
        gender: 'M',
      };
    }

    // Adicionar mãe se disponível
    if (mae) {
      rootNode.mother = {
        id: mae.id,
        name: mae.name,
        gender: 'F',
        breed: mae.breed,
        color: mae.cor,
      };
    } else if (mae_nome) {
      // Se temos apenas o nome da mãe (da API ou do cavalo)
      rootNode.mother = {
        id: null,
        name: mae_nome,
        gender: 'F',
      };
    }

    return rootNode;
  };

  // Adaptador para converter a resposta da API para o formato esperado pelos componentes
  const genealogia: AdaptedGenealogia | undefined = selectedHorse ? {
    cavalo: selectedHorse,
    pai: selectedHorse.pai_id ? findHorseById(selectedHorse.pai_id) : 
         ((selectedHorse.pai_nome || selectedHorse.pai_nome || selectedHorse.pai) ? 
          findHorseByName(selectedHorse.pai_nome || selectedHorse.pai_nome || selectedHorse.pai) : undefined),
    mae: selectedHorse.mae_id ? findHorseById(selectedHorse.mae_id) : 
         ((selectedHorse.mae_nome || selectedHorse.mae_nome || selectedHorse.mae) ? 
          findHorseByName(selectedHorse.mae_nome || selectedHorse.mae_nome || selectedHorse.mae) : undefined),
    bisavosConhecidos: 0,
    consanguinidade: genealogiaApi?.coeficienteConsanguinidade || 0,
    observacoes: genealogiaApi?.observacoes || undefined
  } : undefined;

  return {
    // Estado
    selectedHorseId,
    isEditDialogOpen,
    selectedHorse,
    cavalos,
    genealogia,
    isLoadingCavalos,
    isLoadingGenealogia: isLoadingGenealogia || isLoadingSelectedCavalo,
    
    // Manipuladores de eventos
    setSelectedHorseId,
    handleEditGenealogia,
    handleCloseEditDialog,
    handleParentClick,
    setIsEditDialogOpen,
    
    // Funções utilitárias
    convertToGenealogyNode,
  };
};