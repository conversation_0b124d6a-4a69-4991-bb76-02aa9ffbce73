/**
 * Comprehensive Error Tracking System for EquiGestor AI
 * Monitors and logs Error 500s across all application modules
 */

import fs from 'fs';
import path from 'path';
import { logger } from './simple-logger';

interface ErrorReport {
  id: string;
  timestamp: string;
  module: string;
  endpoint: string;
  user_id?: number;
  error: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  request: {
    method: string;
    url: string;
    headers?: any;
    body?: any;
    params?: any;
    query?: any;
  };
  context: any;
}

interface ModuleErrorStats {
  module: string;
  totalErrors: number;
  lastError: string;
  commonErrors: { [key: string]: number };
  endpoints: { [key: string]: number };
}

class ErrorTracker {
  private errorReports: ErrorReport[] = [];
  private errorStatsFile: string;
  private maxReports = 1000; // Keep last 1000 error reports

  constructor() {
    const logDir = path.join(process.cwd(), 'logs');
    this.errorStatsFile = path.join(logDir, 'error-stats.json');
    this.loadExistingReports();
  }

  /**
   * Records a new error occurrence
   */
  recordError(error: any, req: any, module: string): string {
    const errorId = this.generateErrorId();
    
    const errorReport: ErrorReport = {
      id: errorId,
      timestamp: new Date().toISOString(),
      module,
      endpoint: `${req.method} ${req.url}`,
      user_id: req.headers['user-id'] ? parseInt(req.headers['user-id']) : undefined,
      error: {
        name: error.name || 'UnknownError',
        message: error.message || 'No error message',
        stack: error.stack,
        code: error.code
      },
      request: {
        method: req.method,
        url: req.url,
        headers: this.sanitizeHeaders(req.headers),
        body: this.sanitizeBody(req.body),
        params: req.params,
        query: req.query
      },
      context: {
        userAgent: req.headers['user-agent']?.substring(0, 100),
        ip: req.ip,
        timestamp: new Date().toISOString()
      }
    };

    this.errorReports.push(errorReport);
    this.maintainReportLimit();
    this.saveErrorStats();
    
    // Log the error with comprehensive details
    logger.error(`Error 500 in ${module}`, {
      errorId,
      module,
      endpoint: errorReport.endpoint,
      errorType: error.name,
      message: error.message,
      user_id: errorReport.user_id
    });

    return errorId;
  }

  /**
   * Gets error statistics by module
   */
  getModuleStats(): ModuleErrorStats[] {
    const moduleMap = new Map<string, ModuleErrorStats>();

    this.errorReports.forEach(report => {
      const module = report.module;
      
      if (!moduleMap.has(module)) {
        moduleMap.set(module, {
          module,
          totalErrors: 0,
          lastError: report.timestamp,
          commonErrors: {},
          endpoints: {}
        });
      }

      const stats = moduleMap.get(module)!;
      stats.totalErrors++;
      
      // Track common error types
      const errorKey = `${report.error.name}: ${report.error.message}`;
      stats.commonErrors[errorKey] = (stats.commonErrors[errorKey] || 0) + 1;
      
      // Track problematic endpoints
      stats.endpoints[report.endpoint] = (stats.endpoints[report.endpoint] || 0) + 1;
      
      // Update last error timestamp
      if (new Date(report.timestamp) > new Date(stats.lastError)) {
        stats.lastError = report.timestamp;
      }
    });

    return Array.from(moduleMap.values()).sort((a, b) => b.totalErrors - a.totalErrors);
  }

  /**
   * Gets recent errors for a specific module
   */
  getModuleErrors(module: string, limit: number = 20): ErrorReport[] {
    return this.errorReports
      .filter(report => report.module === module)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Gets error details by ID
   */
  getErrorById(errorId: string): ErrorReport | undefined {
    return this.errorReports.find(report => report.id === errorId);
  }

  /**
   * Generates a comprehensive error report
   */
  generateErrorReport(): any {
    const moduleStats = this.getModuleStats();
    const recentErrors = this.errorReports
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 50);

    return {
      summary: {
        totalErrors: this.errorReports.length,
        uniqueModules: moduleStats.length,
        timeRange: {
          oldest: this.errorReports.length > 0 ? this.errorReports[0].timestamp : null,
          newest: this.errorReports.length > 0 ? this.errorReports[this.errorReports.length - 1].timestamp : null
        }
      },
      moduleStats,
      recentErrors: recentErrors.map(error => ({
        id: error.id,
        timestamp: error.timestamp,
        module: error.module,
        endpoint: error.endpoint,
        errorType: error.error.name,
        message: error.error.message,
        user_id: error.user_id
      })),
      criticalPatterns: this.identifyCriticalPatterns()
    };
  }

  /**
   * Identifies critical error patterns that need immediate attention
   */
  private identifyCriticalPatterns(): any[] {
    const patterns = [];
    const moduleStats = this.getModuleStats();

    // Pattern 1: High frequency errors in critical modules
    const criticalModules = ['cavalos', 'auth', 'database'];
    moduleStats.forEach(stats => {
      if (criticalModules.includes(stats.module) && stats.totalErrors > 10) {
        patterns.push({
          type: 'high_frequency_critical',
          module: stats.module,
          errorCount: stats.totalErrors,
          severity: 'high',
          recommendation: `Immediate attention required for ${stats.module} module`
        });
      }
    });

    // Pattern 2: Database connection errors
    const dbErrors = this.errorReports.filter(report => 
      report.error.message.includes('database') || 
      report.error.message.includes('connection') ||
      report.error.code?.includes('28P01') ||
      report.error.code?.includes('42703')
    );
    
    if (dbErrors.length > 5) {
      patterns.push({
        type: 'database_connection_issues',
        errorCount: dbErrors.length,
        severity: 'critical',
        recommendation: 'Check database connectivity and schema consistency'
      });
    }

    // Pattern 3: Authentication failures
    const authErrors = this.errorReports.filter(report =>
      report.error.message.includes('unauthorized') ||
      report.error.message.includes('authentication') ||
      report.endpoint.includes('/auth')
    );

    if (authErrors.length > 15) {
      patterns.push({
        type: 'authentication_failures',
        errorCount: authErrors.length,
        severity: 'medium',
        recommendation: 'Review authentication middleware and user session handling'
      });
    }

    return patterns;
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    // Remove sensitive headers
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    return sanitized;
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sanitized = { ...body };
    // Remove sensitive fields
    delete sanitized.password;
    delete sanitized.token;
    delete sanitized.secret;
    return sanitized;
  }

  private loadExistingReports(): void {
    try {
      if (fs.existsSync(this.errorStatsFile)) {
        const data = fs.readFileSync(this.errorStatsFile, 'utf8');
        const parsed = JSON.parse(data);
        this.errorReports = parsed.reports || [];
      }
    } catch (error) {
      logger.warn('Failed to load existing error reports', { error: error.message });
    }
  }

  private saveErrorStats(): void {
    try {
      const statsData = {
        lastUpdated: new Date().toISOString(),
        totalReports: this.errorReports.length,
        reports: this.errorReports
      };
      
      fs.writeFileSync(this.errorStatsFile, JSON.stringify(statsData, null, 2));
    } catch (error) {
      logger.warn('Failed to save error statistics', { error: error.message });
    }
  }

  private maintainReportLimit(): void {
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(-this.maxReports);
    }
  }
}

// Export singleton instance
export const errorTracker = new ErrorTracker();

/**
 * Express middleware for automatic error tracking
 */
export function errorTrackingMiddleware(module: string) {
  return (error: any, req: any, res: any, next: any) => {
    // Record the error
    const errorId = errorTracker.recordError(error, req, module);
    
    // Add error ID to response headers for debugging
    res.setHeader('X-Error-ID', errorId);
    
    // Send structured error response
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Internal Server Error',
        errorId,
        module,
        timestamp: new Date().toISOString(),
        message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred'
      });
    }
    
    next();
  };
}

/**
 * Wraps async route handlers with error tracking
 */
export function trackErrors(module: string, handler: Function) {
  return async (req: any, res: any, next: any) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      const errorId = errorTracker.recordError(error, req, module);
      res.setHeader('X-Error-ID', errorId);
      
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Internal Server Error',
          errorId,
          module,
          timestamp: new Date().toISOString()
        });
      }
    }
  };
}

export default errorTracker;