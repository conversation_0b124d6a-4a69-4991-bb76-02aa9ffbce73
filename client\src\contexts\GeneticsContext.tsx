import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';

interface Horse {
  id: number;
  name: string;
  breed?: string;
  sexo?: string;
  birth_date?: string;
  status?: string;
}

interface GeneticsContextType {
  selectedHorseId: number | null;
  setSelectedHorseId: (horse_id: number | null) => void;
  selectedHorse: Horse | null;
  setSelectedHorse: (horse: Horse | null) => void;
}

const GeneticsContext = createContext<GeneticsContextType | undefined>(undefined);

export function GeneticsProvider({ children }: { children: ReactNode }) {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [selectedHorse, setSelectedHorse] = useState<Horse | null>(null);

  // Buscar dados dos cavalos para atualizar o selectedHorse
  const { data: cavalos } = useQuery<Horse[]>({
    queryKey: ['/api/cavalos'],
    staleTime: 0,
    gcTime: 0,
  });

  // Atualizar selectedHorse quando selectedHorseId ou cavalos mudarem
  useEffect(() => {
    if (selectedHorseId && cavalos) {
      const horse = cavalos.find(cavalo => cavalo.id === selectedHorseId);
      setSelectedHorse(horse || null);
    } else {
      setSelectedHorse(null);
    }
  }, [selectedHorseId, cavalos]);

  return (
    <GeneticsContext.Provider value={{ 
      selectedHorseId, 
      setSelectedHorseId,
      selectedHorse,
      setSelectedHorse
    }}>
      {children}
    </GeneticsContext.Provider>
  );
}

export function useGeneticsContext() {
  const context = useContext(GeneticsContext);
  if (context === undefined) {
    throw new Error('useGeneticsContext must be used within a GeneticsProvider');
  }
  return context;
}