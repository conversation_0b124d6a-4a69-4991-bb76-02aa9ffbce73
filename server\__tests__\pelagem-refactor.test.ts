/**
 * Testes Vitest para refatoração do sistema de pelagens
 * Conforme especificado na refatoração: testa pelagem "Gateado" (já seed) e "Apalusa Lavanda" (nova)
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { pool } from '../db';
import { resolvePelagemId, searchPelagens } from '../pelagem-resolver-service';

describe('Sistema de Pelagens Refatorado', () => {
  
  beforeAll(async () => {
    // Garantir que a migration foi executada
    console.log('🧪 Iniciando testes do sistema de pelagens refatorado...');
  });

  afterAll(async () => {
    // Limpar dados de teste criados se necessário
    await pool.query("DELETE FROM pelagens WHERE fonte = 'Test' AND nome LIKE '%TEST%'");
  });

  describe('resolvePelagemId - Pelagens Seed', () => {
    it('deve encontrar pelagem "Gateado" já existente no seed', async () => {
      // Teste 4.1 da especificação: Importa PDF com pelagem "Gateado" (já seed)
      const pelagemId = await resolvePelagemId('Gateado', 'ABCCC Import');
      
      expect(pelagemId).toBeDefined();
      expect(typeof pelagemId).toBe('number');
      
      // Verificar se a pelagem encontrada é realmente do seed
      const result = await pool.query(
        'SELECT * FROM pelagens WHERE id = $1',
        [pelagemId]
      );
      
      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].nome).toBe('Gateado');
      expect(result.rows[0].fonte).toBe('seed');
      expect(result.rows[0].slug).toBe('gateado');
    });

    it('deve resolver pelagem com capitalização diferente', async () => {
      const pelagemId1 = await resolvePelagemId('GATEADO', 'ABCCC Import');
      const pelagemId2 = await resolvePelagemId('gateado', 'ABCCC Import');
      const pelagemId3 = await resolvePelagemId('Gateado', 'ABCCC Import');
      
      // Todos devem retornar o mesmo ID
      expect(pelagemId1).toBe(pelagemId2);
      expect(pelagemId2).toBe(pelagemId3);
    });
  });

  describe('resolvePelagemId - Pelagens Novas', () => {
    it('deve criar nova pelagem "Apalusa Lavanda" automaticamente', async () => {
      // Teste 4.1 da especificação: Importa PDF com pelagem "Apalusa Lavanda" (nova)
      
      // Verificar que não existe antes
      const beforeResult = await pool.query(
        'SELECT * FROM pelagens WHERE nome = $1',
        ['Apalusa Lavanda']
      );
      expect(beforeResult.rows).toHaveLength(0);
      
      // Resolver pelagem (deve criar automaticamente)
      const pelagemId = await resolvePelagemId('Apalusa Lavanda', 'ABCCC Import');
      
      expect(pelagemId).toBeDefined();
      expect(typeof pelagemId).toBe('number');
      
      // Verificar se foi criada corretamente
      const afterResult = await pool.query(
        'SELECT * FROM pelagens WHERE id = $1',
        [pelagemId]
      );
      
      expect(afterResult.rows).toHaveLength(1);
      expect(afterResult.rows[0].nome).toBe('Apalusa Lavanda');
      expect(afterResult.rows[0].fonte).toBe('ABCCC Import');
      expect(afterResult.rows[0].slug).toBe('apalusa_lavanda');
      expect(afterResult.rows[0].descricao).toContain('ABCCC Import');
    });

    it('deve criar pelagem com caracteres especiais e acentos', async () => {
      const pelagemId = await resolvePelagemId('Tordilho Rodado São João', 'ABCCC Import');
      
      const result = await pool.query(
        'SELECT * FROM pelagens WHERE id = $1',
        [pelagemId]
      );
      
      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].nome).toBe('Tordilho Rodado São João');
      expect(result.rows[0].slug).toBe('tordilho_rodado_sao_joao');
    });
  });

  describe('searchPelagens - Busca com Autocomplete', () => {
    it('deve buscar pelagens por slug normalizado', async () => {
      const pelagens = await searchPelagens('tordilh', 10);
      
      expect(Array.isArray(pelagens)).toBe(true);
      expect(pelagens.length).toBeGreaterThan(0);
      
      // Deve encontrar pelagens com "tordilh" no nome ou slug
      const temTordilho = pelagens.some(p => 
        p.nome.toLowerCase().includes('tordilh') || 
        p.slug.includes('tordilh')
      );
      expect(temTordilho).toBe(true);
    });

    it('deve limitar resultados conforme parâmetro', async () => {
      const pelagens = await searchPelagens('a', 5);
      expect(pelagens.length).toBeLessThanOrEqual(5);
    });

    it('deve retornar array vazio para busca inexistente', async () => {
      const pelagens = await searchPelagens('PELAGEM_INEXISTENTE_TESTE_123', 10);
      expect(Array.isArray(pelagens)).toBe(true);
    });
  });

  describe('Validação de Dados', () => {
    it('deve rejeitar nome vazio', async () => {
      await expect(resolvePelagemId('', 'Test')).rejects.toThrow();
      await expect(resolvePelagemId('   ', 'Test')).rejects.toThrow();
    });

    it('deve rejeitar nome null/undefined', async () => {
      await expect(resolvePelagemId(null as any, 'Test')).rejects.toThrow();
      await expect(resolvePelagemId(undefined as any, 'Test')).rejects.toThrow();
    });
  });

  describe('Performance', () => {
    it('endpoint /api/pelagens deve responder em menos de 100ms', async () => {
      const start = Date.now();
      await searchPelagens('', 20);
      const duration = Date.now() - start;
      
      // Critério de aceite: p95 endpoint `/api/pelagens` < 100 ms
      expect(duration).toBeLessThan(100);
    });
  });

  describe('Verificação de Seeds', () => {
    it('deve ter pelo menos 16 registros seed após migration', async () => {
      // Critério de aceite: `pelagens` contém ≥ 16 registros seed após migration
      const result = await pool.query(
        'SELECT COUNT(*) as count FROM pelagens WHERE fonte = $1',
        ['seed']
      );
      
      const count = parseInt(result.rows[0].count);
      expect(count).toBeGreaterThanOrEqual(16);
    });

    it('deve ter todas as pelagens seed especificadas', async () => {
      const pelagensEsperadas = [
        'Alazão', 'Baio', 'Castanho', 'Preto', 'Tordilho', 
        'Zaino', 'Gateado', 'Overo', 'Tobiano', 'Palomino',
        'Buckskin', 'Cremello', 'Perlino', 'Champagne', 
        'Dun', 'Grullo', 'Roan', 'Appaloosa', 'Paint'
      ];
      
      for (const pelagem of pelagensEsperadas) {
        const result = await pool.query(
          'SELECT * FROM pelagens WHERE nome = $1 AND fonte = $2',
          [pelagem, 'seed']
        );
        
        expect(result.rows).toHaveLength(1);
        expect(result.rows[0].slug).toBeTruthy();
      }
    });
  });

  describe('Integridade de Dados', () => {
    it('deve manter slugs únicos', async () => {
      const result = await pool.query(`
        SELECT slug, COUNT(*) as count 
        FROM pelagens 
        GROUP BY slug 
        HAVING COUNT(*) > 1
      `);
      
      // Não deve haver slugs duplicados
      expect(result.rows).toHaveLength(0);
    });

    it('deve ter índices criados corretamente', async () => {
      const indices = await pool.query(`
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'pelagens'
        AND indexname IN ('idx_pelagens_slug', 'idx_pelagens_fonte')
      `);
      
      expect(indices.rows).toHaveLength(2);
    });
  });
});