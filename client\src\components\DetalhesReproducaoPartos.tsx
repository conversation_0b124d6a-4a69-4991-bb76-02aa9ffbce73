import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, FileText, Calendar } from 'lucide-react';
import { Reproducao, Cavalo } from '@shared/schema';

/**
 * Componente de Detalhes de Partos
 * Exibe informações detalhadas sobre partos registrados no sistema
 */
export function DetalhesReproducaoPartos() {
  // Consultar dados de reprodução
  const { data: reproducoes = [], isLoading: loadingReproducoes } = useQuery<Reproducao[]>({
    queryKey: ['/api/reproducao'],
  });
  
  // Consultar dados de cavalos 
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });
  
  // Filtrar apenas reproduções com parto
  const partos = reproducoes.filter(rep => 
    rep.resultado === 'parto_realizado' || 
    rep.observacoes?.toLowerCase().includes('parto') ||
    rep.observacoes?.toLowerCase().includes('nasceu')
  );

  // Função para obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    const cavalo = cavalos.find(c => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };
  
  // Função para formatar data
  const formatarData = (dataString: string | null) => {
    if (!dataString) return 'Não informada';
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Registro de Partos</h1>
        <p className="text-muted-foreground">
          Histórico completo de partos realizados e suas informações
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader className="bg-blue-50">
          <CardTitle>Resumo</CardTitle>
          <CardDescription>Visão geral dos partos registrados</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="flex flex-col p-4 bg-blue-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Total de Partos</span>
              <span className="text-2xl font-bold">{partos.length}</span>
            </div>
            <div className="flex flex-col p-4 bg-green-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Partos Bem-sucedidos</span>
              <span className="text-2xl font-bold">
                {partos.filter(p => !p.observacoes?.toLowerCase().includes('complicação')).length}
              </span>
            </div>
            <div className="flex flex-col p-4 bg-amber-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Com Complicações</span>
              <span className="text-2xl font-bold">
                {partos.filter(p => p.observacoes?.toLowerCase().includes('complicação')).length}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Histórico de Partos</CardTitle>
          <CardDescription>Detalhes de todos os partos registrados</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingReproducoes || loadingCavalos ? (
            <div className="text-center py-8">Carregando informações...</div>
          ) : partos.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Égua</TableHead>
                  <TableHead>Padreador</TableHead>
                  <TableHead>Potro(a)</TableHead>
                  <TableHead>Condição</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {partos.map((parto) => (
                  <TableRow key={parto.id}>
                    <TableCell>{formatarData(parto.data_parto_real || parto.data_cobertura)}</TableCell>
                    <TableCell>{getCavaloName(parto.egua_id)}</TableCell>
                    <TableCell>
                      {parto.garanhao_id ? getCavaloName(parto.garanhao_id) : 'Não informado'}
                    </TableCell>
                    <TableCell>
                      {extrairNomePotro(parto.observacoes) || 'Não registrado'}
                    </TableCell>
                    <TableCell>
                      {parto.observacoes?.toLowerCase().includes('complicação') ? (
                        <Badge variant="destructive">Com complicações</Badge>
                      ) : (
                        <Badge variant="default">Normal</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">Finalizado</Badge>
                    </TableCell>
                    <TableCell className="text-right space-x-1">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <FileText className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Calendar className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhum parto registrado</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Função auxiliar para tentar extrair o nome do potro das observações
function extrairNomePotro(observacoes: string | null): string | null {
  if (!observacoes) return null;
  
  // Padrões comuns em anotações de parto
  const padroes = [
    /potro(?:\(a\))?\s+(?:chamado|nomeado)?\s*[":]\s*([^,.;]+)/i,
    /nasceu\s+(?:o|a)\s+potro(?:\(a\))?\s+([^,.;]+)/i,
    /nome\s+(?:do|da)\s+potro(?:\(a\))?\s*[":]\s*([^,.;]+)/i,
    /potro(?:\(a\))?\s+([^,.;]+)/i
  ];
  
  for (const padrao of padroes) {
    const match = observacoes.match(padrao);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return null;
}