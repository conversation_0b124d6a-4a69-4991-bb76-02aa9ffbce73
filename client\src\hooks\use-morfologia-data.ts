import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { AvaliacaoMorfologica } from '@/types/genetica';
import { queryClient } from '@/lib/queryClient';

// Interface para cavalos
interface Cavalo {
  id: number;
  name: string;
  breed?: string;
  birth_date?: string;
  gender?: string;
  [key: string]: any;
}

// Resultado do hook
interface UseMorfologiaDataResult {
  // Cavalos
  cavalos: Cavalo[];
  isLoadingCavalos: boolean;
  
  // Seleção de cavalo
  selectedHorseId: number | null;
  setSelectedHorseId: (id: number | null) => void;
  selectedHorse: Cavalo | undefined;
  
  // Avaliações morfológicas
  avaliacoes: AvaliacaoMorfologica[];
  isLoadingAvaliacoes: boolean;
  
  // Funções de ajuda
  getDestaquesMorfologicos: (avaliacao: AvaliacaoMorfologica) => string[];
  getPontosFracos: (avaliacao: AvaliacaoMorfologica) => string[];
  
  // Operações da API
  reloadAvaliacoes: () => void;
}

/**
 * Hook personalizado para gerenciar dados de morfologia de cavalos
 */
export function useMorfologiaData(): UseMorfologiaDataResult {
  const { toast } = useToast();
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);

  // Buscar cavalos
  const {
    data: cavalos = [],
    isLoading: isLoadingCavalos,
    error: cavalosError,
  } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });

  // Buscar avaliações morfológicas para o cavalo selecionado
  const {
    data: avaliacoes = [],
    isLoading: isLoadingAvaliacoes,
    error: avaliacoesError,
  } = useQuery<AvaliacaoMorfologica[]>({
    queryKey: ['/api/cavalos', selectedHorseId, 'morfologia'],
    enabled: selectedHorseId !== null,
  });

  // Selecionar o primeiro cavalo por padrão
  useEffect(() => {
    if (cavalos && cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Exibir erros
  useEffect(() => {
    if (cavalosError) {
      toast({
        title: 'Erro ao carregar cavalos',
        description: 'Não foi possível carregar a lista de cavalos.',
        variant: 'destructive',
      });
    }
    if (avaliacoesError) {
      toast({
        title: 'Erro ao carregar avaliações',
        description: 'Não foi possível carregar as avaliações morfológicas.',
        variant: 'destructive',
      });
    }
  }, [cavalosError, avaliacoesError, toast]);

  // Encontrar o cavalo selecionado
  const selectedHorse = useMemo(() => {
    return cavalos.find(c => c.id === selectedHorseId);
  }, [cavalos, selectedHorseId]);

  // Função para extrair destaques morfológicos (pontuações >= 8)
  const getDestaquesMorfologicos = (avaliacao: AvaliacaoMorfologica): string[] => {
    const destaques = [];
    
    if (avaliacao.pontuacaoCabeca >= 8) destaques.push('Cabeça');
    if (avaliacao.pontuacaoPescoco >= 8) destaques.push('Pescoço');
    if (avaliacao.pontuacaoEspadua >= 8) destaques.push('Espádua');
    if (avaliacao.pontuacaoDorso >= 8) destaques.push('Dorso');
    if (avaliacao.pontuacaoGarupa >= 8) destaques.push('Garupa');
    if (avaliacao.pontuacaoMembrosDianteiros >= 8) destaques.push('Membros Dianteiros');
    if (avaliacao.pontuacaoMembrosPosteriores >= 8) destaques.push('Membros Posteriores');
    if (avaliacao.pontuacaoAndamento >= 8) destaques.push('Andamento');
    
    return destaques;
  };
  
  // Função para extrair pontos fracos morfológicos (pontuações <= 5)
  const getPontosFracos = (avaliacao: AvaliacaoMorfologica): string[] => {
    const pontosFracos = [];
    
    if (avaliacao.pontuacaoCabeca <= 5) pontosFracos.push('Cabeça');
    if (avaliacao.pontuacaoPescoco <= 5) pontosFracos.push('Pescoço');
    if (avaliacao.pontuacaoEspadua <= 5) pontosFracos.push('Espádua');
    if (avaliacao.pontuacaoDorso <= 5) pontosFracos.push('Dorso');
    if (avaliacao.pontuacaoGarupa <= 5) pontosFracos.push('Garupa');
    if (avaliacao.pontuacaoMembrosDianteiros <= 5) pontosFracos.push('Membros Dianteiros');
    if (avaliacao.pontuacaoMembrosPosteriores <= 5) pontosFracos.push('Membros Posteriores');
    if (avaliacao.pontuacaoAndamento <= 5) pontosFracos.push('Andamento');
    
    return pontosFracos;
  };
  
  // Função para recarregar avaliações
  const reloadAvaliacoes = () => {
    if (selectedHorseId) {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/cavalos', selectedHorseId, 'morfologia'] 
      });
    }
  };
  
  return {
    cavalos,
    isLoadingCavalos,
    selectedHorseId,
    setSelectedHorseId,
    selectedHorse,
    avaliacoes,
    isLoadingAvaliacoes,
    getDestaquesMorfologicos,
    getPontosFracos,
    reloadAvaliacoes
  };
}