# Example environment configuration

# Banco de dados local
DATABASE_URL=

# Configurações individuais do banco de dados
DB_HOST=                # Host do banco de dados
DB_PORT=                # Porta do banco de dados
DB_NAME=                # Nome do banco de dados
DB_USER=                # Usuário do banco de dados
DB_PASSWORD=            # Senha do banco de dados

# Banco de dados online (substitua pela URL real do seu banco de dados online)
ONLINE_DATABASE_URL=

# Configuração para usar banco de dados online (true/false)
USE_ONLINE_DB=true

# Configuração de sincronização
SYNC_INTERVAL=3600000  # Intervalo de sincronização em milissegundos (1 hora)

# Configurações de timeout
CONNECT_TIMEOUT=30000  # Timeout de conexão em milissegundos
IDLE_TIMEOUT=30000     # Timeout de inatividade em milissegundos

# Configurações de monitoramento e debug
SENTRY_DSN=  # DSN do Sentry para captura de erros
OTEL_COLLECTOR_URL=http://otel-collector:4317  # URL do coletor OpenTelemetry

# Integração com OpenAI
codex/update-.env.example-with-new-variables
OPENAI_API_KEY=

OPENAI_API_KEY=your-openai-api-key
# Lista opcional de tokens iniciais da ABCCC (separados por vírgula)
INITIAL_ABCCC_TOKENS=


# Nível de log
LOG_LEVEL=debug

# Porta do servidor
PORT=3000

# Ambiente da aplicacao
NODE_ENV=development

# Chave de acesso administrativa
ADMIN_ACCESS_KEY=
ADMIN_USERNAME=          # Usuário administrador padrão
ADMIN_PASSWORD=          # Senha do usuário administrador
JWT_SECRET=              # Segredo para assinar JWT
INITIAL_ABCCC_TOKENS=    # Tokens iniciais para integração ABCCC

# Credenciais padrão do administrador para servidores simples
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Seguranca CSRF
CSRF_SECRET=

# Configuracoes do Firebase
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=

# Variaveis do Vite para o frontend
VITE_API_URL=http://localhost:3000/api
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_APP_ID=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_OPENAI_API_KEY=
