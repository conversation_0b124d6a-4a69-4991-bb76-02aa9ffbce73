import { ReactNode, useEffect, useState } from "react";
import { useLocation } from "wouter";

interface ProtectedRouteProps {
  children: ReactNode;
}

// Interface para o usuário
interface User {
  id: number;
  username: string;
  email: string;
}

/**
 * ProtectedRoute component
 * 
 * Redirects unauthenticated users to the login page.
 * Renders a loading spinner while authentication state is being determined.
 * Renders the child components once user is authenticated.
 */
const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [, navigate] = useLocation();
  
  // Verificar autenticação no localStorage
  useEffect(() => {
    console.log("ProtectedRoute: Verificando autenticação...");
    const checkAuth = () => {
      try {
        const userJson = localStorage.getItem('user');
        if (userJson) {
          console.log("ProtectedRoute: Usuário encontrado no localStorage");
          const userData = JSON.parse(userJson);
          setUser(userData);
        } else {
          console.log("ProtectedRoute: Usuário não autenticado, redirecionando para login");
          // Inserir um pequeno delay para garantir que o redirecionamento funcione
          setTimeout(() => {
            navigate("/login");
          }, 100);
        }
      } catch (error) {
        console.error("Erro ao verificar autenticação:", error);
        setTimeout(() => {
          navigate("/login");
        }, 100);
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [navigate]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Return null during redirect (handled in useEffect)
  if (!user) {
    return null;
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default ProtectedRoute;
