import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import { ChevronLeft, Dna } from 'lucide-react';
import TraitInheritanceAnimation from '@/components/genetics/TraitInheritanceAnimation';

// Tipos necessários
interface Horse {
  id: number;
  name: string;
  breed?: string;
  sexo?: string;
}

interface TraitInheritancePageProps {}

const TraitInheritancePage: React.FC<TraitInheritancePageProps> = () => {
  // Buscar todos os cavalos
  const { data: cavalos } = useQuery<Horse[]>({
    queryKey: ['/api/cavalos'],
  });
  
  // Selecionar um garanhão e uma égua para demonstração
  const selecionarGaranhao = () => {
    const garanhoes = cavalos?.filter(cavalo => cavalo.sexo === 'M') || [];
    return garanhoes.length > 0 ? garanhoes[0] : undefined;
  };
  
  const selecionarEgua = () => {
    const eguas = cavalos?.filter(cavalo => cavalo.sexo === 'F') || [];
    return eguas.length > 0 ? eguas[0] : undefined;
  };
  
  const selectedSire = selecionarGaranhao();
  const selectedDam = selecionarEgua();
  
  return (
    <div className="container mx-auto py-4 px-4 md:px-0">
      <div className="mb-6">
        <Link to="/genetica" className="flex items-center text-blue-500 hover:text-blue-700">
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para Genética
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold flex items-center">
          <Dna className="mr-2 h-6 w-6 text-primary" />
          Simulador de Herança Genética
        </h1>
      </div>

      {/* Componente de animação de herança */}
      <TraitInheritanceAnimation 
        sire={selectedSire}
        dam={selectedDam}
      />
      
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-medium mb-2">Sobre a Herança Genética</h2>
        <p className="text-sm text-gray-700">
          Os cavalos, como todos os organismos, herdam características genéticas de seus pais. Cada característica 
          é controlada por um ou mais genes, e cada gene pode ter diferentes variantes chamadas alelos. O simulador 
          acima demonstra como esses alelos são passados dos pais para os filhos e como eles determinam as 
          características visíveis (fenótipo) do potro resultante.
        </p>
        
        <h3 className="text-md font-medium mt-4 mb-1">Tipos de Herança</h3>
        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
          <li><span className="font-medium">Dominante:</span> O alelo dominante sempre se expressa, mesmo na presença de um alelo recessivo.</li>
          <li><span className="font-medium">Recessiva:</span> O alelo recessivo só se expressa quando dois alelos recessivos estão presentes.</li>
          <li><span className="font-medium">Codominante:</span> Ambos os alelos se expressam simultaneamente no fenótipo.</li>
          <li><span className="font-medium">Dominância incompleta:</span> Os heterozigotos têm um fenótipo intermediário entre os dois homozigotos.</li>
        </ul>
        
        <p className="text-sm text-gray-700 mt-4">
          Esta ferramenta é educativa e visa demonstrar os princípios básicos da genética. Para decisões reais 
          de reprodução, recomenda-se consultar um especialista em genética equina e realizar testes genéticos 
          apropriados.
        </p>
      </div>
    </div>
  );
};

export default TraitInheritancePage;