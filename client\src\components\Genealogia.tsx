import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { HelpCircle, GitBranchPlus, Users, Baby } from "lucide-react";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { apiRequest } from '@/lib/queryClient';
import { useQuery } from '@tanstack/react-query';
import { Cavalo } from '@shared/schema';

interface GenealogiaProps {
  horse: {
    id: number;
    name: string;
    pai?: string | number | null;
    mae?: string | number | null;
    pai_id?: number | null;
    mae_id?: number | null;
    pai_nome?: string | null;
    mae_nome?: string | null;
    avo_paterno?: string | null;
    avo_materno?: string | null;
    sexo?: string | null;
  };
}

/**
 * Componente Genealogia
 * 
 * Exibe a árvore genealógica de um cavalo com informações 
 * sobre pais e avós quando disponíveis
 */
export function Genealogia({ horse }: GenealogiaProps) {
  const [pai_nome, setPai_nome] = useState<string | null>(null);
  const [mae_nome, setMae_nome] = useState<string | null>(null);
  
  // Buscar lista de cavalos para resolver nomes a partir dos IDs
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => await apiRequest<Cavalo[]>('/api/cavalos', 'GET')
  });
  
  // Quando cavalos ou horse mudar, resolver os nomes
  useEffect(() => {
    // Verificar primeiro se temos nomes externos (nova implementação)
    if (horse.pai_nome) {
      console.log('Usando nome externo do pai:', horse.pai_nome);
      setPai_nome(horse.pai_nome);
    } 
    else if (horse.mae_nome) {
      console.log('Usando nome externo da mãe:', horse.mae_nome);
      setMae_nome(horse.mae_nome);
    }
    
    // Depois tentar resolver os IDs do sistema (implementação original)
    if (cavalos && cavalos.length > 0) {
      console.log('Resolvendo nomes de pai/mãe a partir dos IDs', { 
        pai_id: horse.pai_id || horse.pai, 
        mae_id: horse.mae_id || horse.mae,
        todos_cavalos: cavalos.map(c => ({ id: c.id, nome: c.name }))
      });
      
      // Resolver nome do pai pelo pai_id (nova implementação) ou pelo pai (legado)
      const pai_idToUse = horse.pai_id || horse.pai;
      if (pai_idToUse && !horse.pai_nome) { // Só buscar ID se não tiver nome externo
        const paiEncontrado = cavalos.find(c => c.id.toString() === pai_idToUse.toString());
        if (paiEncontrado) {
          setPai_nome(paiEncontrado.name);
          console.log('Pai encontrado:', paiEncontrado.name);
        } else {
          console.log('Pai não encontrado para ID:', pai_idToUse);
        }
      }
      
      // Resolver nome da mãe pelo mae_id (nova implementação) ou pelo mae (legado)
      const mae_idToUse = horse.mae_id || horse.mae;
      if (mae_idToUse && !horse.mae_nome) { // Só buscar ID se não tiver nome externo
        const maeEncontrada = cavalos.find(c => c.id.toString() === mae_idToUse.toString());
        if (maeEncontrada) {
          setMae_nome(maeEncontrada.name);
          console.log('Mãe encontrada:', maeEncontrada.name);
        } else {
          console.log('Mãe não encontrada para ID:', mae_idToUse);
        }
      }
    }
  }, [cavalos, horse.pai, horse.mae, horse.pai_id, horse.mae_id, horse.pai_nome, horse.mae_nome]);
  
  // Função para determinar se exibir um ancestral ou placeholder
  const getAncestorClass = (value: any) => {
    return value ? "bg-green-50 border-green-200" : "bg-gray-50 border-gray-200";
  };

  // Função para obter o nome de um ancestral (exibe nome externo, nome resolvido ou ID)
  const getAncestorName = (id: string | number | null | undefined, resolvedName: string | null) => {
    // Log para diagnóstico 
    console.log(`Tentando obter nome do ancestral - ID: ${id}, Nome resolvido: ${resolvedName}`);
    
    // Se tiver nome resolvido (pode ser do sistema ou nome externo), usa ele
    if (resolvedName) return resolvedName;
    
    // Verificar nomes externos no cavalo
    if (id === horse.pai_id && horse.pai_nome) {
      return horse.pai_nome;
    }
    
    if (id === horse.mae_id && horse.mae_nome) {
      return horse.mae_nome;
    }
    
    // Se tiver ID mas não tiver nome resolvido, exibe o ID
    return id ? `ID: ${id}` : "Não informado";
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <GitBranchPlus className="h-5 w-5 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-800">Árvore Genealógica</h2>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>A genealogia exibe os ancestrais do animal quando registrados no sistema.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span className="text-xs">Pedigree Completo</span>
          </Button>
          <Button size="sm" variant="outline" className="flex items-center gap-1">
            <Baby className="h-4 w-4" />
            <span className="text-xs">Descendentes</span>
          </Button>
        </div>
      </div>

      <Card className="overflow-hidden border border-gray-200">
        <CardContent className="p-0">
          {/* Nível 1 - O próprio cavalo */}
          <div className="p-4 flex justify-center">
            <div className="w-48 border-2 border-blue-500 bg-blue-50 rounded p-2 text-center">
              <div className="font-semibold">{horse.name}</div>
              <div className="text-sm text-gray-500">{horse.sexo || "Não informado"}</div>
            </div>
          </div>

          {/* Linha conectora */}
          <div className="h-6 w-0.5 bg-gray-300 mx-auto"></div>
          
          {/* Nível 2 - Pais */}
          <div className="flex justify-center gap-12 px-4">
            {/* Pai */}
            <div className="flex flex-col items-center">
              <div className={`w-40 border rounded p-2 text-center ${getAncestorClass(horse.pai_id || horse.pai || horse.pai_nome)}`}>
                <div className="font-medium">Pai</div>
                <Separator className="my-1" />
                <div className="text-sm">{getAncestorName(horse.pai_id || horse.pai, pai_nome)}</div>
              </div>
              {/* Linha conectora para avô paterno */}
              {(horse.pai_id || horse.pai || horse.pai_nome) && <div className="h-6 w-0.5 bg-gray-300 mx-auto mt-1"></div>}
            </div>

            {/* Mãe */}
            <div className="flex flex-col items-center">
              <div className={`w-40 border rounded p-2 text-center ${getAncestorClass(horse.mae_id || horse.mae || horse.mae_nome)}`}>
                <div className="font-medium">Mãe</div>
                <Separator className="my-1" />
                <div className="text-sm">{getAncestorName(horse.mae_id || horse.mae, mae_nome)}</div>
              </div>
              {/* Linha conectora para avô materno */}
              {(horse.mae_id || horse.mae || horse.mae_nome) && <div className="h-6 w-0.5 bg-gray-300 mx-auto mt-1"></div>}
            </div>
          </div>

          {/* Nível 3 - Avós (exibidos apenas se pai/mãe existirem) */}
          <div className="flex justify-center gap-12 p-4">
            {/* Avô paterno - exibido apenas se pai existir */}
            <div className="flex flex-col items-center">
              {horse.pai && (
                <div className={`w-36 border rounded p-2 text-center ${getAncestorClass(horse.avo_paterno)}`}>
                  <div className="font-medium text-sm">Avô Paterno</div>
                  <Separator className="my-1" />
                  <div className="text-xs">{getAncestorName(horse.avo_paterno, null)}</div>
                </div>
              )}
            </div>

            {/* Avô materno - exibido apenas se mãe existir */}
            <div className="flex flex-col items-center">
              {horse.mae && (
                <div className={`w-36 border rounded p-2 text-center ${getAncestorClass(horse.avo_materno)}`}>
                  <div className="font-medium text-sm">Avô Materno</div>
                  <Separator className="my-1" />
                  <div className="text-xs">{getAncestorName(horse.avo_materno, null)}</div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 mb-2 text-sm text-gray-500">
        <p className="mb-2">
          <span className="inline-block w-3 h-3 bg-green-50 border border-green-200 mr-2"></span>
          Ancestrais com informações registradas
        </p>
        <p>
          <span className="inline-block w-3 h-3 bg-gray-50 border border-gray-200 mr-2"></span>
          Ancestrais não informados
        </p>
      </div>
    </div>
  );
}