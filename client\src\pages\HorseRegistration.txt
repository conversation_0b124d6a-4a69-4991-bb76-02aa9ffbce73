import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarIcon, ChevronLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Form validation schema extremamente simplificado
const horseSchema = z.object({
  // Campos obrigatórios
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  breed: z.string().min(1, "Por favor selecione uma raça"),
  birthDate: z.string().min(1, "Data de nascimento é obrigatória"),

  // Todos os outros campos são opcionais
  peso: z.any().optional(),
  altura: z.any().optional(),
  sexo: z.any().optional(),
  status: z.any().optional(),
  dataEntrada: z.any().optional(),
  dataSaida: z.any().optional(),
  motivoSaida: z.any().optional(),
  pai: z.any().optional(),
  mae: z.any().optional(),
  avoPaterno: z.any().optional(),
  avoMaterno: z.any().optional(),
  notes: z.any().optional(),
});

type HorseFormData = z.infer<typeof horseSchema>;

const HorseRegistration = () => {
  // Obter usuário do localStorage
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });
  const [loading, setLoading] = useState(false);
  const [, navigate] = useLocation();
  const { toast } = useToast();

  const form = useForm<HorseFormData>({
    resolver: zodResolver(horseSchema),
    defaultValues: {
      name: "",
      breed: "",
      birthDate: "",
      peso: "",
      altura: "",
      sexo: "",
      status: "ativo",
      dataEntrada: "",
      dataSaida: "",
      motivoSaida: "",
      pai: "",
      mae: "",
      avoPaterno: "",
      avoMaterno: "",
      notes: "",
    },
  });

  // Função auxiliar para realizar requisições com o ID do usuário
  const fetchWithAuth = async (url: string, options: RequestInit = {}) => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    console.log("User ID para autenticação:", user.id);

    // Create a new headers object
    const headers = new Headers(options.headers);
    // Add the user-id header
    headers.set('user-id', user.id.toString());

    console.log("Headers enviados:", Object.fromEntries(headers.entries()));

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (!response.ok) {
      // Tentar obter detalhes do erro
      try {
        const errorData = await response.json();
        console.error("Resposta de erro da API:", errorData);

        // Verificar se há erros de validação
        if (errorData.errors) {
          throw new Error(`${response.status}: ${JSON.stringify(errorData)}`);
        }

        throw new Error(errorData.message || `Erro na requisição: ${response.status}`);
      } catch (e) {
        // Se não conseguir obter JSON, usar mensagem genérica
        if (e instanceof SyntaxError) {
          throw new Error(`Erro na requisição: ${response.status}`);
        }
        throw e;
      }
    }

    return response.json();
  };

  const onSubmit = async (data: HorseFormData) => {
    if (!user) return;

    setLoading(true);

    try {
      // Objeto com apenas os campos obrigatórios
      const cavaloData = {
        name: String(data.name || ""),
        breed: String(data.breed || ""),
        birthDate: String(data.birthDate || ""),
        userId: Number(user.id)
      };

      console.log("Enviando dados do cavalo:", cavaloData);
      console.log("JSON para envio:", JSON.stringify(cavaloData));

      // Usar a nova rota diretamente com logs detalhados
      console.log("Iniciando requisição para /api/cavalos/novo");

      try {
        const response = await fetch('/api/cavalos/novo', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'user-id': user.id.toString()
          },
          body: JSON.stringify(cavaloData),
        });

        console.log("Resposta recebida, status:", response.status);
        console.log("Resposta ok:", response.ok);

        const responseText = await response.text();
        console.log("Texto da resposta:", responseText);

        if (!response.ok) {
          let errorData;
          try {
            errorData = JSON.parse(responseText);
          } catch (e) {
            errorData = { message: responseText || "Erro desconhecido" };
          }
          throw new Error(`Erro ${response.status}: ${JSON.stringify(errorData)}`);
        }

        let result;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          result = { message: "Sucesso (resposta não é JSON válido)" };
        }

        console.log("Resultado processado:", result);
      } catch (fetchError) {
        console.error("Erro na requisição fetch:", fetchError);
        throw fetchError;
      }

      toast({
        title: "Cavalo cadastrado",
        description: `${data.name} foi adicionado ao seu estábulo!`,
      });

      navigate("/");
    } catch (error: any) {
      console.error("Erro ao cadastrar cavalo:", error);

      // Logar informações detalhadas para diagnóstico
      console.log("Dados do formulário:", data);
      console.log("Tipo de birthDate:", typeof data.birthDate);
      console.log("Valor de birthDate:", data.birthDate);
      console.log("Tipo de peso:", typeof data.peso);
      console.log("Valor de peso:", data.peso);

      let errorMessage = "Erro ao cadastrar cavalo";

      // Tentar extrair mensagem de erro mais detalhada
      if (error.message) {
        errorMessage = error.message;

        // Se a mensagem contém um JSON, tentar extrair mais detalhes
        if (error.message.includes("{")) {
          try {
            const match = error.message.match(/\{.*\}/);
            if (match) {
              const errorData = JSON.parse(match[0]);
              console.log("Dados de erro parseados:", errorData);

              if (errorData.errors) {
                // Formatar erros de validação
                const validationErrors = Object.entries(errorData.errors)
                  .filter(([key, value]) => key !== "_errors" && (value as any)._errors?.length)
                  .map(([key, value]) => `${key}: ${(value as any)._errors.join(", ")}`)
                  .join("; ");

                if (validationErrors) {
                  errorMessage = `Erro de validação: ${validationErrors}`;
                }
              }

              // Adicionar informações extras se disponíveis
              if (errorData.erro) {
                errorMessage += ` (${errorData.erro})`;
              }
            }
          } catch (e) {
            console.error("Erro ao processar mensagem de erro:", e);
          }
        }
      }

      toast({
        title: "Falha no cadastro",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/");
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <button onClick={handleCancel} className="inline-flex items-center mb-4 text-gray-500 hover:text-gray-700">
          <ChevronLeft className="h-4 w-4 mr-1" />
          Voltar para o Dashboard
        </button>

        <h1 className="text-2xl font-bold text-gray-900">Cadastrar Novo Cavalo</h1>
        <p className="mt-1 text-sm text-gray-500">Adicione um novo cavalo ao seu estábulo.</p>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Campos obrigatórios */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Nome *</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do cavalo" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="breed"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Raça *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a raça" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="arabian">Árabe</SelectItem>
                          <SelectItem value="quarter">Quarto de Milha</SelectItem>
                          <SelectItem value="thoroughbred">Puro-Sangue</SelectItem>
                          <SelectItem value="andalusian">Andaluz</SelectItem>
                          <SelectItem value="friesian">Frisão</SelectItem>
                          <SelectItem value="appaloosa">Appaloosa</SelectItem>
                          <SelectItem value="mangalarga">Mangalarga</SelectItem>
                          <SelectItem value="crioulo">Crioulo</SelectItem>
                          <SelectItem value="other">Outra</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birthDate"
                  render={({ field }) => (
                    <FormItem className="sm:col-span-3">
                      <FormLabel>Data de Nascimento *</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Campos opcionais em abas */}
              <div className="mt-8">
                <Tabs defaultValue="fisico" className="w-full">
                  <TabsList className="mb-4 grid grid-cols-4 w-full">
                    <TabsTrigger value="fisico">Características Físicas</TabsTrigger>
                    <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
                    <TabsTrigger value="status">Status</TabsTrigger>
                    <TabsTrigger value="observacoes">Observações</TabsTrigger>
                  </TabsList>

                  {/* Aba de características físicas */}
                  <TabsContent value="fisico">
                    <Card>
                      <CardHeader>
                        <CardTitle>Características Físicas</CardTitle>
                        <CardDescription>
                          Informações sobre o porte e características físicas do cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="peso"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Peso (kg)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.1"
                                    min="0"
                                    placeholder="Ex: 450.5"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="altura"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Altura (m)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="Ex: 1.68"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="sexo"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Sexo</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o sexo" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="Macho">Macho</SelectItem>
                                    <SelectItem value="Fêmea">Fêmea</SelectItem>
                                    <SelectItem value="Castrado">Castrado</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Aba de genealogia */}
                  <TabsContent value="genealogia">
                    <Card>
                      <CardHeader>
                        <CardTitle>Genealogia</CardTitle>
                        <CardDescription>
                          Informações sobre a linhagem e pedigree do cavalo (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="pai"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Pai</FormLabel>
                                <FormControl>
                                  <Input placeholder="Nome do pai" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="mae"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Mãe</FormLabel>
                                <FormControl>
                                  <Input placeholder="Nome da mãe" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="avoPaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Paterno</FormLabel>
                                <FormControl>
                                  <Input placeholder="Nome do avô paterno" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="avoMaterno"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-3">
                                <FormLabel>Avô Materno</FormLabel>
                                <FormControl>
                                  <Input placeholder="Nome do avô materno" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Aba de status */}
                  <TabsContent value="status">
                    <Card>
                      <CardHeader>
                        <CardTitle>Status</CardTitle>
                        <CardDescription>
                          Situação atual do cavalo no plantel (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-6">
                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Selecione o status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="ativo">Ativo</SelectItem>
                                    <SelectItem value="vendido">Vendido</SelectItem>
                                    <SelectItem value="falecido">Falecido</SelectItem>
                                    <SelectItem value="doado">Doado</SelectItem>
                                    <SelectItem value="emprestado">Emprestado</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="dataEntrada"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Entrada</FormLabel>
                                <FormControl>
                                  <Input
                                    type="date"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="dataSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-2">
                                <FormLabel>Data de Saída</FormLabel>
                                <FormControl>
                                  <Input
                                    type="date"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="motivoSaida"
                            render={({ field }) => (
                              <FormItem className="sm:col-span-6">
                                <FormLabel>Motivo da Saída</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Explique o motivo da saída, se aplicável"
                                    rows={2}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Aba de observações */}
                  <TabsContent value="observacoes">
                    <Card>
                      <CardHeader>
                        <CardTitle>Observações</CardTitle>
                        <CardDescription>
                          Informações adicionais, histórico ou particularidades (opcionais).
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <FormField
                          control={form.control}
                          name="notes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Observações Gerais</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Informações adicionais sobre o cavalo, como histórico médico, comportamento, necessidades especiais, etc."
                                  rows={5}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-between">
                  <p className="text-sm text-gray-500">* Campos obrigatórios</p>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      className="mr-3"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading}
                    >
                      {loading ? "Cadastrando..." : "Cadastrar Cavalo"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default HorseRegistration;
