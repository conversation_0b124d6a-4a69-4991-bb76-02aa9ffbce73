/**
 * Rotas de Relatórios de Nutrição - EquiGestor AI
 * API endpoints para relatórios mensais e indicadores nutricionais
 */

import { Router, Request, Response } from "express";
import { nutritionService } from "../services/nutrition.service";
import { stockService } from "../services/stock.service";
import { authenticateUser } from "../auth";
import { db } from "../db";
import { feedPlanItems, cavalos } from "@shared/schema";
import { eq, and, gte, lte, sql } from "drizzle-orm";
import { z } from "zod";

const router = Router();

// Aplicar autenticação em todas as rotas
router.use(authenticateUser);

/**
 * GET /api/reports/nutrition - Relatório completo de nutrição
 */
router.get("/nutrition", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    
    // Validar parâmetros de data
    const dateSchema = z.object({
      from: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
      to: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional()
    });
    
    const { from, to } = dateSchema.parse(req.query);
    
    // Definir período padrão (último mês)
    const endDate = to || new Date().toISOString().split('T')[0];
    const startDate = from || (() => {
      const date = new Date();
      date.setMonth(date.getMonth() - 1);
      return date.toISOString().split('T')[0];
    })();
    
    // Buscar dados do período
    const [
      feedingPlans,
      wasteStats,
      avgCostPerAnimal,
      horsesData
    ] = await Promise.all([
      // Planos de alimentação do período
      db.select({
        id: feedPlanItems.id,
        animalId: feedPlanItems.animalId,
        forageKg: feedPlanItems.forageKg,
        concentrateKg: feedPlanItems.concentrateKg,
        date: feedPlanItems.date,
        status: feedPlanItems.status,
        leftoverPct: feedPlanItems.leftoverPct,
        horseName: cavalos.name
      })
      .from(feedPlanItems)
      .leftJoin(cavalos, eq(feedPlanItems.animalId, cavalos.id))
      .where(and(
        eq(feedPlanItems.user_id, user_id),
        gte(feedPlanItems.date, startDate),
        lte(feedPlanItems.date, endDate)
      )),
      
      // Estatísticas de desperdício
      nutritionService.getWasteStatistics(user_id, 30),
      
      // Custo médio por animal
      stockService.calculateAverageCostPerAnimal(user_id, 30),
      
      // Dados dos cavalos
      db.select()
        .from(cavalos)
        .where(and(
          eq(cavalos.user_id, user_id),
          eq(cavalos.status, "ativo")
        ))
    ]);
    
    // Calcular métricas
    const totalPlans = feedingPlans.length;
    const completedPlans = feedingPlans.filter(p => p.status === "done").length;
    const plansWithLeftover = feedingPlans.filter(p => p.status === "leftover").length;
    
    const totalForage = feedingPlans.reduce((sum, p) => sum + p.forageKg, 0);
    const totalConcentrate = feedingPlans.reduce((sum, p) => sum + p.concentrateKg, 0);
    
    const avgForagePerAnimal = horsesData.length > 0 ? totalForage / horsesData.length : 0;
    const avgConcentratePerAnimal = horsesData.length > 0 ? totalConcentrate / horsesData.length : 0;
    
    // Calcular escore corporal médio (simulado - seria baseado em avaliações reais)
    const avgBodyScore = 7.5; // Em uma implementação real, viria de avaliações veterinárias
    
    // Dados por animal
    const animalStats = horsesData.map(horse => {
      const horsePlans = feedingPlans.filter(p => p.animalId === horse.id);
      const horseForage = horsePlans.reduce((sum, p) => sum + p.forageKg, 0);
      const horseConcentrate = horsePlans.reduce((sum, p) => sum + p.concentrateKg, 0);
      const horsePlansWithLeftover = horsePlans.filter(p => p.status === "leftover").length;
      
      return {
        id: horse.id,
        name: horse.name,
        totalPlans: horsePlans.length,
        totalForage: +horseForage.toFixed(2),
        totalConcentrate: +horseConcentrate.toFixed(2),
        plansWithLeftover: horsePlansWithLeftover,
        wasteRate: horsePlans.length > 0 ? +(horsePlansWithLeftover / horsePlans.length * 100).toFixed(1) : 0
      };
    });
    
    res.json({
      period: {
        from: startDate,
        to: endDate
      },
      summary: {
        avgCostPerAnimal: +avgCostPerAnimal.toFixed(2),
        avgBodyScore,
        avgWastePct: +wasteStats.avgWastePct.toFixed(1),
        totalAnimals: horsesData.length,
        totalPlans,
        completedPlans,
        plansWithLeftover,
        completionRate: totalPlans > 0 ? +((completedPlans / totalPlans) * 100).toFixed(1) : 0
      },
      consumption: {
        totalForage: +totalForage.toFixed(2),
        totalConcentrate: +totalConcentrate.toFixed(2),
        avgForagePerAnimal: +avgForagePerAnimal.toFixed(2),
        avgConcentratePerAnimal: +avgConcentratePerAnimal.toFixed(2)
      },
      animalStats,
      recommendations: generateRecommendations(wasteStats.avgWastePct, avgCostPerAnimal)
    });
  } catch (error: any) {
    console.error("Erro ao gerar relatório de nutrição:", error);
    
    if (error?.name === 'ZodError') {
      return res.status(400).json({ 
        error: "Formato de data inválido. Use YYYY-MM-DD", 
        details: error.errors 
      });
    }
    
    res.status(500).json({ error: "Erro ao gerar relatório de nutrição" });
  }
});

/**
 * GET /api/reports/nutrition/efficiency - Relatório de eficiência alimentar
 */
router.get("/nutrition/efficiency", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    const days = Number(req.query.days) || 30;
    
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);
    
    // Buscar dados de eficiência
    const efficiency = await db.select({
      date: feedPlanItems.date,
      totalPlans: sql<number>`COUNT(*)`,
      completedPlans: sql<number>`COUNT(CASE WHEN ${feedPlanItems.status} = 'done' THEN 1 END)`,
      plansWithLeftover: sql<number>`COUNT(CASE WHEN ${feedPlanItems.status} = 'leftover' THEN 1 END)`,
      avgLeftover: sql<number>`AVG(COALESCE(${feedPlanItems.leftoverPct}, 0))`,
      totalForage: sql<number>`SUM(${feedPlanItems.forageKg})`,
      totalConcentrate: sql<number>`SUM(${feedPlanItems.concentrateKg})`
    })
    .from(feedPlanItems)
    .where(and(
      eq(feedPlanItems.user_id, user_id),
      gte(feedPlanItems.date, fromDate.toISOString().split('T')[0])
    ))
    .groupBy(feedPlanItems.date)
    .orderBy(feedPlanItems.date);
    
    res.json({
      period: `${days} dias`,
      dailyEfficiency: efficiency,
      trends: analyzeTrends(efficiency)
    });
  } catch (error) {
    console.error("Erro ao gerar relatório de eficiência:", error);
    res.status(500).json({ error: "Erro ao gerar relatório de eficiência" });
  }
});

/**
 * Gera recomendações baseadas nos indicadores
 */
function generateRecommendations(wastePct: number, costPerAnimal: number): string[] {
  const recommendations: string[] = [];
  
  if (wastePct > 15) {
    recommendations.push("Alto índice de sobras detectado. Considere reduzir as porções ou revisar os templates de alimentação.");
  }
  
  if (wastePct > 10) {
    recommendations.push("Monitore os horários de alimentação e verifique a palatabilidade dos alimentos.");
  }
  
  if (costPerAnimal > 25) {
    recommendations.push("Custo por animal elevado. Analise fornecedores alternativos ou ajuste a composição da dieta.");
  }
  
  if (wastePct < 5) {
    recommendations.push("Excelente aproveitamento alimentar! Continue monitorando o escore corporal dos animais.");
  }
  
  return recommendations;
}

/**
 * Analisa tendências nos dados de eficiência
 */
function analyzeTrends(data: any[]): any {
  if (data.length < 7) {
    return { message: "Dados insuficientes para análise de tendência" };
  }
  
  const recent = data.slice(-7); // Últimos 7 dias
  const previous = data.slice(-14, -7); // 7 dias anteriores
  
  const recentAvgWaste = recent.reduce((sum, d) => sum + d.avgLeftover, 0) / recent.length;
  const previousAvgWaste = previous.length > 0 ? 
    previous.reduce((sum, d) => sum + d.avgLeftover, 0) / previous.length : recentAvgWaste;
  
  const wasteTrend = recentAvgWaste - previousAvgWaste;
  
  // The returned object uses `wasteTrend` to report trend data
  return {
    wasteTrend: {
      current: +recentAvgWaste.toFixed(1),
      previous: +previousAvgWaste.toFixed(1),
      change: +wasteTrend.toFixed(1),
      direction: wasteTrend > 1 ? "aumentando" : wasteTrend < -1 ? "diminuindo" : "estável"
    }
  };
}

export default router;