/**
 * Complete Error 500 Monitoring System for EquiGestor AI
 * Comprehensive tracking, analysis, and debugging capabilities
 */

import fs from 'fs';
import path from 'path';

interface ErrorLog {
  id: string;
  timestamp: string;
  module: string;
  endpoint: string;
  error: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  request: {
    method: string;
    url: string;
    user_id?: number;
    userAgent?: string;
    ip?: string;
  };
  context: any;
}

interface ModuleHealth {
  module: string;
  errorCount: number;
  lastError?: string;
  status: 'healthy' | 'warning' | 'critical';
  commonErrors: { [key: string]: number };
}

class ErrorMonitor {
  private errors: ErrorLog[] = [];
  private moduleHealth: Map<string, ModuleHealth> = new Map();
  private logFile: string;

  constructor() {
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    this.logFile = path.join(logDir, 'error-monitor.json');
    this.loadExistingData();
    this.initializeModules();
  }

  recordError(error: any, req: any, module: string): string {
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    
    const errorLog: ErrorLog = {
      id: errorId,
      timestamp: new Date().toISOString(),
      module,
      endpoint: `${req.method} ${req.url}`,
      error: {
        name: error.name || 'UnknownError',
        message: error.message || 'No error message',
        stack: error.stack,
        code: error.code
      },
      request: {
        method: req.method,
        url: req.url,
        user_id: req.headers['user-id'] ? parseInt(req.headers['user-id']) : undefined,
        userAgent: req.headers['user-agent']?.substring(0, 200),
        ip: req.ip
      },
      context: {
        params: req.params,
        query: req.query,
        timestamp: new Date().toISOString()
      }
    };

    this.errors.push(errorLog);
    this.updateModuleHealth(module, errorLog);
    this.maintainErrorLimit();
    this.saveData();

    console.error(`[ERROR MONITOR] ${module} - ${error.name}: ${error.message} (ID: ${errorId})`);
    
    return errorId;
  }

  getHealthReport(): any {
    const moduleHealthArray = Array.from(this.moduleHealth.values());
    const criticalModules = moduleHealthArray.filter(m => m.status === 'critical');
    const recentErrors = this.errors
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 50);

    return {
      summary: {
        totalErrors: this.errors.length,
        criticalModules: criticalModules.length,
        systemStatus: this.calculateSystemStatus(),
        reportTime: new Date().toISOString()
      },
      moduleHealth: moduleHealthArray,
      recentErrors: recentErrors.map(e => ({
        id: e.id,
        timestamp: e.timestamp,
        module: e.module,
        endpoint: e.endpoint,
        error: e.error.name + ': ' + e.error.message
      })),
      criticalIssues: this.identifyCriticalIssues(),
      recommendations: this.generateRecommendations()
    };
  }

  getModuleAnalysis(module: string): any {
    const moduleHealth = this.moduleHealth.get(module);
    const moduleErrors = this.errors.filter(e => e.module === module);
    
    if (!moduleHealth) {
      return { error: 'Module not found' };
    }

    return {
      module,
      health: moduleHealth,
      recentErrors: moduleErrors
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 20)
        .map(e => ({
          id: e.id,
          timestamp: e.timestamp,
          endpoint: e.endpoint,
          error: e.error,
          context: e.context
        })),
      errorPatterns: this.analyzeErrorPatterns(moduleErrors),
      recommendations: this.getModuleRecommendations(module, moduleErrors)
    };
  }

  getErrorDetails(errorId: string): ErrorLog | null {
    return this.errors.find(e => e.id === errorId) || null;
  }

  private initializeModules(): void {
    const modules = ['cavalos', 'nutricao', 'morfologia', 'manejos', 'veterinario', 'agenda', 'auth'];
    
    modules.forEach(module => {
      if (!this.moduleHealth.has(module)) {
        this.moduleHealth.set(module, {
          module,
          errorCount: 0,
          status: 'healthy',
          commonErrors: {}
        });
      }
    });
  }

  private updateModuleHealth(module: string, errorLog: ErrorLog): void {
    let health = this.moduleHealth.get(module);
    
    if (!health) {
      health = {
        module,
        errorCount: 0,
        status: 'healthy',
        commonErrors: {}
      };
      this.moduleHealth.set(module, health);
    }

    health.errorCount++;
    health.lastError = errorLog.timestamp;
    
    const errorKey = errorLog.error.name;
    health.commonErrors[errorKey] = (health.commonErrors[errorKey] || 0) + 1;

    // Update status based on error count
    if (health.errorCount > 20) {
      health.status = 'critical';
    } else if (health.errorCount > 5) {
      health.status = 'warning';
    } else {
      health.status = 'healthy';
    }
  }

  private calculateSystemStatus(): string {
    const healthArray = Array.from(this.moduleHealth.values());
    const criticalCount = healthArray.filter(h => h.status === 'critical').length;
    const warningCount = healthArray.filter(h => h.status === 'warning').length;

    if (criticalCount > 0) return 'critical';
    if (warningCount > 2) return 'warning';
    return 'healthy';
  }

  private identifyCriticalIssues(): any[] {
    const issues = [];
    
    // Database connection issues
    const dbErrors = this.errors.filter(e => 
      e.error.message.includes('database') ||
      e.error.message.includes('connection') ||
      e.error.code?.includes('42703')
    );

    if (dbErrors.length > 3) {
      issues.push({
        type: 'database_issues',
        severity: 'critical',
        count: dbErrors.length,
        description: 'Multiple database-related errors detected',
        recommendation: 'Check database connectivity and schema consistency'
      });
    }

    // Module degradation
    this.moduleHealth.forEach(health => {
      if (health.status === 'critical') {
        issues.push({
          type: 'module_degradation',
          severity: 'high',
          module: health.module,
          count: health.errorCount,
          description: `${health.module} module showing critical error rates`,
          recommendation: `Investigate ${health.module} module functionality`
        });
      }
    });

    return issues;
  }

  private generateRecommendations(): string[] {
    const recommendations = [];
    
    const systemStatus = this.calculateSystemStatus();
    if (systemStatus === 'critical') {
      recommendations.push('Immediate system investigation required');
    }

    const recentErrors = this.errors.filter(e => 
      new Date(e.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
    );

    if (recentErrors.length > 50) {
      recommendations.push('High error frequency detected - review recent changes');
    }

    const dbErrors = this.errors.filter(e => 
      e.error.message.includes('database') || e.error.message.includes('schema')
    );

    if (dbErrors.length > 5) {
      recommendations.push('Database schema validation needed');
    }

    return recommendations;
  }

  private analyzeErrorPatterns(errors: ErrorLog[]): any {
    const patterns = {
      byHour: {} as { [key: number]: number },
      byEndpoint: {} as { [key: string]: number },
      byErrorType: {} as { [key: string]: number }
    };

    errors.forEach(error => {
      const hour = new Date(error.timestamp).getHours();
      patterns.byHour[hour] = (patterns.byHour[hour] || 0) + 1;
      patterns.byEndpoint[error.endpoint] = (patterns.byEndpoint[error.endpoint] || 0) + 1;
      patterns.byErrorType[error.error.name] = (patterns.byErrorType[error.error.name] || 0) + 1;
    });

    return patterns;
  }

  private getModuleRecommendations(module: string, errors: ErrorLog[]): string[] {
    const recommendations = [];
    
    const recentErrors = errors.filter(e => 
      new Date(e.timestamp).getTime() > Date.now() - 6 * 60 * 60 * 1000
    );

    if (recentErrors.length > 10) {
      recommendations.push(`High recent error rate in ${module} - investigate immediately`);
    }

    const dbErrors = errors.filter(e => e.error.message.includes('database'));
    if (dbErrors.length > 3) {
      recommendations.push(`Database issues in ${module} - verify queries and schema`);
    }

    const typeErrors = errors.filter(e => e.error.name === 'TypeError');
    if (typeErrors.length > 2) {
      recommendations.push(`Type validation issues in ${module} - review data handling`);
    }

    return recommendations;
  }

  private loadExistingData(): void {
    try {
      if (fs.existsSync(this.logFile)) {
        const data = JSON.parse(fs.readFileSync(this.logFile, 'utf8'));
        this.errors = data.errors || [];
        
        if (data.moduleHealth) {
          this.moduleHealth = new Map(Object.entries(data.moduleHealth));
        }
      }
    } catch (error) {
      console.warn('Failed to load existing error data:', error);
    }
  }

  private saveData(): void {
    try {
      const data = {
        lastUpdated: new Date().toISOString(),
        errors: this.errors,
        moduleHealth: Object.fromEntries(this.moduleHealth)
      };
      
      fs.writeFileSync(this.logFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.warn('Failed to save error data:', error);
    }
  }

  private maintainErrorLimit(): void {
    if (this.errors.length > 1000) {
      this.errors = this.errors.slice(-1000);
    }
  }
}

export const errorMonitor = new ErrorMonitor();

export function createErrorHandler(module: string) {
  return (error: any, req: any, res: any, next: any) => {
    const errorId = errorMonitor.recordError(error, req, module);
    
    res.setHeader('X-Error-ID', errorId);
    
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Internal Server Error',
        errorId,
        module,
        timestamp: new Date().toISOString()
      });
    }
  };
}

export default errorMonitor;