/**
 * Alertas de Estoque - EquiGestor AI
 * Interface para visualizar e gerenciar alertas de estoque crítico
 */

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { AlertTriangle, Package, Plus, RefreshCw, Clock, TrendingDown } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface StockAlert {
  id: number;
  item: string;
  alertType: 'low_stock' | 'expiring' | 'expired';
  message: string;
  daysRemaining?: number;
  priority: 'low' | 'medium' | 'high';
  resolved: boolean;
  created_at: string;
}

interface StockBatch {
  id: number;
  item: string;
  quantityKg: number;
  unitCost: number;
  expiry?: string;
  supplier?: string;
  batchNumber?: string;
}

export default function StockAlerts() {
  const [addStockDialog, setAddStockDialog] = useState(false);
  const [newBatch, setNewBatch] = useState({
    item: '',
    quantityKg: '',
    unitCost: '',
    expiry: '',
    supplier: '',
    batchNumber: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Buscar alertas de estoque
  const { data: alertsData, isLoading: alertsLoading } = useQuery({
    queryKey: ['/api/stock/alerts'],
    queryFn: () => apiRequest('/api/stock/alerts')
  });

  // Buscar visão geral do estoque
  const { data: stockOverview } = useQuery({
    queryKey: ['/api/stock/overview'],
    queryFn: () => apiRequest('/api/stock/overview')
  });

  // Mutation para adicionar novo lote
  const addBatchMutation = useMutation({
    mutationFn: (batch: any) =>
      apiRequest('/api/stock/batches', {
        method: 'POST',
        body: batch
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/stock/alerts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/stock/overview'] });
      toast({
        title: "Sucesso!",
        description: "Lote adicionado ao estoque",
      });
      setAddStockDialog(false);
      setNewBatch({
        item: '',
        quantityKg: '',
        unitCost: '',
        expiry: '',
        supplier: '',
        batchNumber: ''
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao adicionar lote ao estoque",
        variant: "destructive",
      });
    }
  });

  // Mutation para forçar verificação de alertas
  const checkAlertsMutation = useMutation({
    mutationFn: () => apiRequest('/api/stock/check-alerts', { method: 'POST' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/stock/alerts'] });
      toast({
        title: "Sucesso!",
        description: "Verificação de alertas concluída",
      });
    }
  });

  const alerts = alertsData?.alerts || [];
  const groupedAlerts = alertsData?.grouped || { high: [], medium: [], low: [] };
  const summary = alertsData?.summary || { total: 0, high: 0, medium: 0, low: 0 };

  const handleAddBatch = () => {
    const batch = {
      item: newBatch.item,
      quantityKg: parseFloat(newBatch.quantityKg),
      unitCost: parseFloat(newBatch.unitCost),
      expiry: newBatch.expiry || null,
      supplier: newBatch.supplier || null,
      batchNumber: newBatch.batchNumber || null
    };

    if (!batch.item || !batch.quantityKg || !batch.unitCost) {
      toast({
        title: "Erro",
        description: "Preencha os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }

    addBatchMutation.mutate(batch);
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'low_stock':
        return <TrendingDown className="h-5 w-5" />;
      case 'expiring':
        return <Clock className="h-5 w-5" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5" />;
      default:
        return <Package className="h-5 w-5" />;
    }
  };

  const getAlertColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Alertas de Estoque</h1>
          <p className="text-gray-600 mt-1">Monitore estoque baixo e produtos vencendo</p>
        </div>
        <div className="flex gap-4">
          <Button 
            onClick={() => checkAlertsMutation.mutate()}
            disabled={checkAlertsMutation.isPending}
            variant="outline"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Verificar Alertas
          </Button>
          <Dialog open={addStockDialog} onOpenChange={setAddStockDialog}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Estoque
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Adicionar Novo Lote</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="item">Item *</Label>
                  <Input
                    id="item"
                    value={newBatch.item}
                    onChange={(e) => setNewBatch({ ...newBatch, item: e.target.value })}
                    placeholder="Ex: Feno de Tifton"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quantity">Quantidade (kg) *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={newBatch.quantityKg}
                      onChange={(e) => setNewBatch({ ...newBatch, quantityKg: e.target.value })}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="cost">Custo/kg *</Label>
                    <Input
                      id="cost"
                      type="number"
                      step="0.01"
                      value={newBatch.unitCost}
                      onChange={(e) => setNewBatch({ ...newBatch, unitCost: e.target.value })}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="expiry">Validade</Label>
                  <Input
                    id="expiry"
                    type="date"
                    value={newBatch.expiry}
                    onChange={(e) => setNewBatch({ ...newBatch, expiry: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="supplier">Fornecedor</Label>
                  <Input
                    id="supplier"
                    value={newBatch.supplier}
                    onChange={(e) => setNewBatch({ ...newBatch, supplier: e.target.value })}
                    placeholder="Nome do fornecedor"
                  />
                </div>
                <div>
                  <Label htmlFor="batch">Número do Lote</Label>
                  <Input
                    id="batch"
                    value={newBatch.batchNumber}
                    onChange={(e) => setNewBatch({ ...newBatch, batchNumber: e.target.value })}
                    placeholder="Ex: L001"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setAddStockDialog(false)}
                  >
                    Cancelar
                  </Button>
                  <Button 
                    onClick={handleAddBatch}
                    disabled={addBatchMutation.isPending}
                  >
                    Adicionar
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Resumo de Alertas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Críticos</p>
                <p className="text-2xl font-bold text-gray-900">{summary.high}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Médios</p>
                <p className="text-2xl font-bold text-gray-900">{summary.medium}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Alertas</p>
                <p className="text-2xl font-bold text-gray-900">{summary.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Itens no Estoque</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stockOverview?.summary?.totalItems || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Alertas */}
      <div className="space-y-6">
        {/* Alertas Críticos */}
        {groupedAlerts.high.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Alertas Críticos</CardTitle>
              <CardDescription>Ação imediata necessária</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {groupedAlerts.high.map((alert: StockAlert) => (
                <Alert key={alert.id} className="border-red-200 bg-red-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getAlertIcon(alert.alertType)}
                      <div>
                        <p className="font-medium text-red-800">{alert.item}</p>
                        <p className="text-sm text-red-600">{alert.message}</p>
                      </div>
                    </div>
                    <Badge variant="destructive">Crítico</Badge>
                  </div>
                </Alert>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Alertas Médios */}
        {groupedAlerts.medium.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-orange-600">Alertas de Atenção</CardTitle>
              <CardDescription>Monitore de perto</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {groupedAlerts.medium.map((alert: StockAlert) => (
                <Alert key={alert.id} className="border-orange-200 bg-orange-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getAlertIcon(alert.alertType)}
                      <div>
                        <p className="font-medium text-orange-800">{alert.item}</p>
                        <p className="text-sm text-orange-600">{alert.message}</p>
                      </div>
                    </div>
                    <Badge variant="default">Médio</Badge>
                  </div>
                </Alert>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Alertas Baixos */}
        {groupedAlerts.low.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-600">Alertas Informativos</CardTitle>
              <CardDescription>Para acompanhamento</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {groupedAlerts.low.map((alert: StockAlert) => (
                <Alert key={alert.id} className="border-blue-200 bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getAlertIcon(alert.alertType)}
                      <div>
                        <p className="font-medium text-blue-800">{alert.item}</p>
                        <p className="text-sm text-blue-600">{alert.message}</p>
                      </div>
                    </div>
                    <Badge variant="secondary">Baixo</Badge>
                  </div>
                </Alert>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Sem Alertas */}
        {alerts.length === 0 && !alertsLoading && (
          <Card>
            <CardContent className="p-12 text-center">
              <Package className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Tudo Sob Controle!
              </h3>
              <p className="text-gray-600">
                Não há alertas de estoque no momento. Seu sistema está funcionando perfeitamente.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}