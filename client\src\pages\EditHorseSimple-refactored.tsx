import { useEffect, useState, useMemo } from 'react';
import { useLocation, useRoute } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { usePelagens, usePelagemById } from '@/hooks/use-pelagens';
import { useRacas } from '@/hooks/use-racas';
import { PelagemAutocomplete } from '@/components/pelagem/PelagemAutocomplete';
import { RacaAutocomplete } from '@/components/raca/RacaAutocomplete';
import { updateCavaloSchema } from '@shared/insert-schemas';
import { Cavalo } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { HorseAvatarWithUpload } from '@/components/horse/HorseAvatarWithUpload';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ExpandedGenealogy } from '@/components/genetics/ExpandedGenealogy';
import { GenealogyState, GenealogyLevel, initializeGenealogy, cleanGenealogy } from '@/types/genealogy';
import { isoDate, sexoMapping, sexoDisplayMapping, SexoDisplay } from '@/utils/form-helpers';

// 3.2 Componente GenealogiaSelector refatorado para usar level como prop
interface GenealogySelectorProps {
  level: GenealogyLevel;
  value: any;
  onChange: (level: GenealogyLevel, value: any) => void;
  cavalos: Cavalo[];
  genero?: 'macho' | 'femea';
}

function GenealogySelector({ level, value, onChange, cavalos, genero }: GenealogySelectorProps) {
  const filteredCavalos = cavalos.filter(c => {
    if (!genero) return true;
    return genero === 'macho' 
      ? ['Macho', 'Garanhão', 'Macho (Castrado)'].includes(c.sexo)
      : ['Fêmea', 'Égua'].includes(c.sexo);
  });

  const handleChange = (newValue: any) => {
    onChange(level, newValue);
  };

  return (
    <div className="space-y-2">
      <Label>{level.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</Label>
      <Select value={value?.tipo || 'nenhum'} onValueChange={(tipo) => {
        handleChange({ tipo, cavaloSistemaId: null, cavaloNome: null });
      }}>
        <SelectTrigger>
          <SelectValue placeholder="Selecione uma opção" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="nenhum">Nenhum</SelectItem>
          <SelectItem value="sistema">Do sistema</SelectItem>
          <SelectItem value="externo">Externo</SelectItem>
        </SelectContent>
      </Select>
      
      {value?.tipo === 'sistema' && (
        <Select 
          value={value.cavaloSistemaId || ''} 
          onValueChange={(id) => handleChange({ ...value, cavaloSistemaId: id })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione um cavalo" />
          </SelectTrigger>
          <SelectContent>
            {filteredCavalos.map(cavalo => (
              <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                {cavalo.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
      
      {value?.tipo === 'externo' && (
        <Input
          placeholder="Nome do cavalo externo"
          value={value.cavaloNome || ''}
          onChange={(e) => handleChange({ ...value, cavaloNome: e.target.value })}
        />
      )}
    </div>
  );
}

export default function EditHorseSimple() {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // 1.1 Unificação de roteamento - usar único useLocation e useRoute
  const [location, navigate] = useLocation();
  const [match] = useRoute('/cavalos/:id/editar');
  const horseId = useMemo(() => {
    return match?.id || location.split('/')[2];
  }, [location, match]);

  if (!horseId) {
    navigate('/cavalos');
    return null;
  }

  // 2. Estados refatorados - formulário unificado e genealogia estruturada
  const [genealogy, setGenealogy] = useState<GenealogyState>(initializeGenealogy());
  
  // 5.1 React Hook Form com validação Zod
  const form = useForm({
    resolver: zodResolver(updateCavaloSchema.omit({ id: true })),
    defaultValues: {
      name: '',
      breed: '',
      birth_date: '',
      sexo: 'Fêmea' as SexoDisplay,
      cor: '',
      pelagem_id: null,
      peso: null,
      altura: null,
      numero_registro: '',
      criador: '',
      proprietario: '',
      inspetor: '',
      origem: '',
      notes: '',
      status: 'ativo' as const, // 2.1 Inicializar status como 'ativo'
      data_entrada: '',
      data_saida: '',
      motivo_saida: '',
      valor_compra: null,
      data_compra: '',
      is_external: false
    }
  });

  // Hooks para carregar pelagens e raças
  const { pelagens, isLoading: isPelagensLoading } = usePelagens();
  const { racas } = useRacas();
  const { pelagem: pelagemSelecionada } = usePelagemById(form.watch('pelagem_id') || undefined);

  // 4.1 Query Keys com user?.id para isolamento multi-tenant
  const {
    data: horse,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/cavalos`, horseId, user?.id],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      const result = await apiRequest<Cavalo>(`/api/cavalos/${horseId}`, "GET");
      return result;
    },
  });

  // Buscar todos os cavalos para genealogia
  const {
    data: todosCavalos = [],
    isLoading: isLoadingCavalos,
  } = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    enabled: !!user,
    queryFn: async () => {
      return await apiRequest<Cavalo[]>("/api/cavalos", "GET");
    },
  });

  // Buscar dados de genealogia
  const {
    data: genealogiaData,
    isLoading: isLoadingGenealogia,
  } = useQuery({
    queryKey: [`/api/cavalos/${horseId}/genealogia`, user?.id],
    enabled: !!horseId && !!user,
    queryFn: async () => {
      return await apiRequest(`/api/cavalos/${horseId}/genealogia`, "GET");
    },
  });

  // Inicializar formulário quando dados carregarem
  useEffect(() => {
    if (horse) {
      form.reset({
        name: horse.name || '',
        breed: horse.breed || '',
        birth_date: isoDate(horse.birth_date) || '',
        sexo: sexoDisplayMapping[horse.sexo as keyof typeof sexoDisplayMapping] || 'Fêmea',
        cor: horse.cor || '',
        pelagem_id: horse.pelagem_id,
        peso: horse.peso,
        altura: horse.altura,
        numero_registro: horse.numero_registro || '',
        criador: horse.criador || '',
        proprietario: horse.proprietario || '',
        inspetor: horse.inspetor || '',
        origem: horse.origem || '',
        notes: horse.notes || '',
        status: horse.status || 'ativo',
        data_entrada: isoDate(horse.data_entrada) || '',
        data_saida: isoDate(horse.data_saida) || '',
        motivo_saida: horse.motivo_saida || '',
        valor_compra: horse.valor_compra,
        data_compra: isoDate(horse.data_compra) || '',
        is_external: horse.is_external || false
      });
    }
  }, [horse, form]);

  // 3.1 Inicializar genealogia com dados da API
  useEffect(() => {
    if (genealogiaData && todosCavalos.length > 0) {
      const newGenealogy = { ...genealogy };
      
      // Configurar pais
      if (genealogiaData.pai?.id) {
        newGenealogy.pai = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.pai.id.toString(),
          cavaloNome: null
        };
      }
      
      if (genealogiaData.mae?.id) {
        newGenealogy.mae = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.mae.id.toString(),
          cavaloNome: null
        };
      }

      // Configurar avós
      if (genealogiaData.avoPaterno?.id) {
        newGenealogy.avo_paterno = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.avoPaterno.id.toString(),
          cavaloNome: null
        };
      }

      if (genealogiaData.avoPaterna?.id) {
        newGenealogy.avo_paterna = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.avoPaterna.id.toString(),
          cavaloNome: null
        };
      }

      if (genealogiaData.avoMaterno?.id) {
        newGenealogy.avo_materno = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.avoMaterno.id.toString(),
          cavaloNome: null
        };
      }

      if (genealogiaData.avoMaterna?.id) {
        newGenealogy.avo_materna = {
          tipo: 'sistema',
          cavaloSistemaId: genealogiaData.avoMaterna.id.toString(),
          cavaloNome: null
        };
      }

      setGenealogy(newGenealogy);
    }
  }, [genealogiaData, todosCavalos]);

  // 3.2 Handler para atualizar genealogia
  const handleGenealogyChange = (level: GenealogyLevel, value: any) => {
    setGenealogy(prev => ({
      ...prev,
      [level]: value
    }));
  };

  // 6.1 Mutação segura para atualização
  const updateMutation = useMutation({
    mutationFn: async (data: any) => {
      // 2.4 Converter datas para ISO antes do PUT
      const payload = {
        ...data,
        id: parseInt(horseId),
        birth_date: data.birth_date ? isoDate(data.birth_date) : null,
        data_entrada: data.data_entrada ? isoDate(data.data_entrada) : null,
        data_saida: data.data_saida ? isoDate(data.data_saida) : null,
        data_compra: data.data_compra ? isoDate(data.data_compra) : null,
        // 2.3 EnumSexo: usar valores snake_case
        sexo: sexoMapping[data.sexo as SexoDisplay] || data.sexo,
        // 2.2 Remover cor se pelagem_id presente
        cor: data.pelagem_id ? undefined : data.cor,
        // 3.3 Genealogia limpa (remover entradas com tipo 'nenhum')
        genealogy: cleanGenealogy(genealogy)
      };

      return await apiRequest(`/api/cavalos/${horseId}`, "PUT", payload);
    },
    onSuccess: (data) => {
      // 4.2 Atualizar cache com queryClient.setQueryData
      queryClient.setQueryData([`/api/cavalos`, horseId, user?.id], data);
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos`, user?.id] });
      
      toast({
        title: "Sucesso",
        description: "Cavalo atualizado com sucesso!",
      });
      
      navigate('/cavalos');
    },
    onError: (error: any) => {
      // 6.2 Tratar 409 CONFLICT
      if (error.status === 409) {
        toast({
          title: "Erro",
          description: "Registro duplicado - já existe um cavalo com este número de registro.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Erro",
          description: "Erro ao atualizar cavalo. Tente novamente.",
          variant: "destructive",
        });
      }
    },
  });

  // 6.1 Submissão segura
  const onSubmit = (data: any) => {
    if (!updateMutation.isPending) {
      updateMutation.mutate(data);
    }
  };

  if (isLoading || isLoadingCavalos || isLoadingGenealogia) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-red-500">Erro ao carregar dados do cavalo.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => navigate('/cavalos')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <h1 className="text-2xl font-bold">Editar Cavalo: {horse?.name}</h1>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="dados-basicos" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dados-basicos">Dados Básicos</TabsTrigger>
              <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
              <TabsTrigger value="extras">Informações Extras</TabsTrigger>
            </TabsList>

            <TabsContent value="dados-basicos">
              <Card>
                <CardHeader>
                  <CardTitle>Informações Básicas</CardTitle>
                  <CardDescription>Dados principais do cavalo</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome *</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="breed"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Raça</FormLabel>
                          <FormControl>
                            <RacaAutocomplete
                              value={field.value}
                              onValueChange={field.onChange}
                              racas={racas}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birth_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data de Nascimento</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sexo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sexo</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o sexo" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Macho">Macho</SelectItem>
                              <SelectItem value="Fêmea">Fêmea</SelectItem>
                              <SelectItem value="Macho (Castrado)">Macho (Castrado)</SelectItem>
                              <SelectItem value="Garanhão">Garanhão</SelectItem>
                              <SelectItem value="Égua">Égua</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pelagem_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pelagem</FormLabel>
                          <FormControl>
                            <PelagemAutocomplete
                              value={field.value}
                              onValueChange={field.onChange}
                              pelagens={pelagens}
                              isLoading={isPelagensLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="ativo">Ativo</SelectItem>
                              <SelectItem value="inativo">Inativo</SelectItem>
                              <SelectItem value="vendido">Vendido</SelectItem>
                              <SelectItem value="falecido">Falecido</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="genealogia">
              <Card>
                <CardHeader>
                  <CardTitle>Genealogia</CardTitle>
                  <CardDescription>Configure os dados de genealogia do cavalo</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <GenealogySelector
                      level="pai"
                      value={genealogy.pai}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="macho"
                    />
                    <GenealogySelector
                      level="mae"
                      value={genealogy.mae}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="femea"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <GenealogySelector
                      level="avo_paterno"
                      value={genealogy.avo_paterno}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="macho"
                    />
                    <GenealogySelector
                      level="avo_paterna"
                      value={genealogy.avo_paterna}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="femea"
                    />
                    <GenealogySelector
                      level="avo_materno"
                      value={genealogy.avo_materno}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="macho"
                    />
                    <GenealogySelector
                      level="avo_materna"
                      value={genealogy.avo_materna}
                      onChange={handleGenealogyChange}
                      cavalos={todosCavalos}
                      genero="femea"
                    />
                  </div>

                  {horseId && <ExpandedGenealogy cavaloId={parseInt(horseId)} />}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="extras">
              <Card>
                <CardHeader>
                  <CardTitle>Informações Extras</CardTitle>
                  <CardDescription>Dados complementares do cavalo</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="numero_registro"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Número de Registro</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="peso"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Peso (kg)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : null)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="altura"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Altura (cm)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              {...field} 
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : null)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="valor_compra"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Valor de Compra (R$)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01"
                              {...field} 
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : null)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Observações</FormLabel>
                        <FormControl>
                          <Textarea {...field} rows={4} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => navigate('/cavalos')}>
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={updateMutation.isPending}
              className="min-w-[120px]"
            >
              {updateMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}