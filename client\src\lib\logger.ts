/**
 * Sistema de log centralizado para EquiGestor
 * Fornece funcionalidades para registro de eventos, erros e depuração
 */

// Níveis de log disponíveis
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

// Configuração global de log
const LOG_CONFIG = {
  // Nível mínimo para exibir logs (pode ser alterado em tempo de execução)
  minLevel: process.env.NODE_ENV === 'production' ? LogLevel.ERROR : LogLevel.DEBUG,
  
  // Opções de saída
  console: true,  // Exibir no console
  storage: true,  // Armazenar no localStorage
  remote: false,  // Enviar para servidor remoto (não implementado)
  // Endpoint para envio remoto
  remoteEndpoint: '/api/logs',
  
  // Prefixo para localStorage
  storagePrefix: 'equigestor_log_',
  
  // Limite de logs armazenados no localStorage
  storageLimit: 100,
  
  // Módulos para os quais o log está habilitado
  enabledModules: ['all', 'ui', 'api', 'auth', 'data', 'forms', 'components'],
}

// Interface para entradas de log
interface LogEntry {
  timestamp: number;
  level: LogLevel;
  module: string;
  message: string;
  data?: any;
  context?: string;
  error?: Error;
}

/**
 * Envia uma entrada de log para o endpoint remoto.
 */
export async function postLogEntry(entry: LogEntry): Promise<void> {
  try {
    await fetch(LOG_CONFIG.remoteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(entry)
    });
  } catch (error) {
    console.error('Erro ao enviar log para o servidor:', error);
  }
}

/**
 * Classe principal de log
 */
class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private sessionId: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.loadLogsFromStorage();
    
    // Registrar informações de inicialização
    this.info('logger', 'Sistema de log inicializado', { 
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });
    
    // Capturar erros não tratados
    window.addEventListener('error', (event) => {
      this.error('global', 'Erro não tratado', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      }, event.error);
    });
    
    // Capturar rejeições de promessas não tratadas
    window.addEventListener('unhandledrejection', (event) => {
      this.error('global', 'Promessa rejeitada não tratada', {
        reason: event.reason
      });
    });
  }

  /**
   * Obtém a instância única do logger (padrão Singleton)
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Gera um ID de sessão único
   */
  private generateSessionId(): string {
    return 'sess_' + Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Carrega logs previamente salvos do localStorage
   */
  private loadLogsFromStorage(): void {
    if (!LOG_CONFIG.storage) return;
    
    try {
      const storedLogs = localStorage.getItem(`${LOG_CONFIG.storagePrefix}entries`);
      if (storedLogs) {
        this.logs = JSON.parse(storedLogs);
      }
    } catch (error) {
      console.error('Erro ao carregar logs do localStorage:', error);
    }
  }

  /**
   * Salva os logs atuais no localStorage
   */
  private saveLogsToStorage(): void {
    if (!LOG_CONFIG.storage) return;
    
    try {
      // Limitar número de logs armazenados
      const logsToStore = this.logs.slice(-LOG_CONFIG.storageLimit);
      localStorage.setItem(`${LOG_CONFIG.storagePrefix}entries`, JSON.stringify(logsToStore));
    } catch (error) {
      console.error('Erro ao salvar logs no localStorage:', error);
    }
  }

  /**
   * Registra uma entrada de log
   */
  private log(level: LogLevel, module: string, message: string, data?: any, error?: Error): void {
    // Verificar se o nível de log é suficiente
    const levels = Object.values(LogLevel);
    const currentLevelIndex = levels.indexOf(level);
    const minLevelIndex = levels.indexOf(LOG_CONFIG.minLevel);
    
    if (currentLevelIndex > minLevelIndex) return;
    
    // Verificar se o módulo está habilitado
    if (!LOG_CONFIG.enabledModules.includes('all') && 
        !LOG_CONFIG.enabledModules.includes(module)) {
      return;
    }
    
    // Criar entrada de log
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      module,
      message,
      data,
      context: this.getContext(),
      error
    };
    
    // Armazenar log
    this.logs.push(entry);
    
    // Salvar no localStorage
    if (LOG_CONFIG.storage) {
      this.saveLogsToStorage();
    }
    
    // Exibir no console
    if (LOG_CONFIG.console) {
      this.logToConsole(entry);
    }
    
    // Enviar para servidor remoto se habilitado
    if (LOG_CONFIG.remote) {
      postLogEntry(entry).catch((err) => {
        console.error('Erro ao registrar log remotamente:', err);
      });
    }
  }

  /**
   * Obtém informações contextuais (URL atual, etc)
   */
  private getContext(): string {
    return window.location.pathname;
  }

  /**
   * Exibe log no console do navegador
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.module}]:`;
    
    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(prefix, entry.message, entry.data || '', entry.error || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.data || '');
        break;
    }
  }


  /**
   * Registra um erro
   */
  public error(module: string, message: string, data?: any, error?: Error): void {
    this.log(LogLevel.ERROR, module, message, data, error);
  }

  /**
   * Registra um aviso
   */
  public warn(module: string, message: string, data?: any): void {
    this.log(LogLevel.WARN, module, message, data);
  }

  /**
   * Registra uma informação
   */
  public info(module: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, module, message, data);
  }

  /**
   * Registra uma mensagem de depuração
   */
  public debug(module: string, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, module, message, data);
  }

  /**
   * Limpa todos os logs armazenados
   */
  public clearLogs(): void {
    this.logs = [];
    
    if (LOG_CONFIG.storage) {
      localStorage.removeItem(`${LOG_CONFIG.storagePrefix}entries`);
    }
    
    this.info('logger', 'Logs limpos');
  }

  /**
   * Obtém todos os logs armazenados
   */
  public getAllLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Filtra logs por nível
   */
  public getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(entry => entry.level === level);
  }

  /**
   * Filtra logs por módulo
   */
  public getLogsByModule(module: string): LogEntry[] {
    return this.logs.filter(entry => entry.module === module);
  }

  /**
   * Exporta logs para JSON
   */
  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Altera o nível mínimo de log
   */
  public setMinLogLevel(level: LogLevel): void {
    LOG_CONFIG.minLevel = level;
    this.info('logger', `Nível mínimo de log alterado para ${level}`);
  }
}

// Exporta a instância única do logger
export const logger = Logger.getInstance();

// Atalhos para facilitar o uso
export const logError = (module: string, message: string, data?: any, error?: Error) => 
  logger.error(module, message, data, error);

export const logWarn = (module: string, message: string, data?: any) => 
  logger.warn(module, message, data);

export const logInfo = (module: string, message: string, data?: any) => 
  logger.info(module, message, data);

export const logDebug = (module: string, message: string, data?: any) => 
  logger.debug(module, message, data);
