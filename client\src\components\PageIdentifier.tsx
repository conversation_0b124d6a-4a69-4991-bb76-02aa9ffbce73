import { useEffect } from 'react';
import { useAccessControl } from '@/hooks/use-access-control';

interface PageIdentifierProps {
  pageId: string;
  pageName: string;
  showInConsole?: boolean;
}

/**
 * Componente para identificar páginas do sistema
 * Adiciona um ID único no DOM e opcionalmente loga no console
 * Exibe campo ID visível apenas para usuários administradores
 */
export function PageIdentifier({ pageId, pageName, showInConsole = true }: PageIdentifierProps) {
  const { isAdmin } = useAccessControl();

  useEffect(() => {
    // Adicionar ID no body para identificação
    document.body.setAttribute('data-page-id', pageId);
    document.body.setAttribute('data-page-name', pageName);
    
    // Log no console para debugging
    if (showInConsole) {
      console.log(`📄 Página atual: ${pageName} (ID: ${pageId})`);
    }
    
    // Cleanup quando sair da página
    return () => {
      document.body.removeAttribute('data-page-id');
      document.body.removeAttribute('data-page-name');
    };
  }, [pageId, pageName, showInConsole]);

  // Render visível do ID da página apenas para administradores
  if (!isAdmin()) {
    return null;
  }

  return (
    <div 
      id={pageId} 
      data-page-name={pageName}
      className="fixed top-16 right-4 z-50 bg-black/80 text-white px-3 py-1 rounded-md text-xs font-mono shadow-lg"
      title={pageName}
    >
      ID: {pageId}
    </div>
  );
}

// Hook para acessar informações da página atual
export function useCurrentPage() {
  return {
    pageId: document.body.getAttribute('data-page-id') || 'unknown',
    pageName: document.body.getAttribute('data-page-name') || 'Página Desconhecida'
  };
}