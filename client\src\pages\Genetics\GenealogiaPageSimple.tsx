import { useGeneticsContext } from "@/contexts/GeneticsContext";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, TreePine } from "lucide-react";
import { ExpandedGenealogy } from "@/components/genetics/ExpandedGenealogy";

export default function GenealogiaPageSimple() {
  const { selectedHorse } = useGeneticsContext();

  if (!selectedHorse) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <TreePine className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground">
                Nenhum cavalo selecionado
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                Selecione um cavalo para visualizar sua genealogia
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">
          Genealogia - {selectedHorse.name}
        </h2>
        <p className="text-muted-foreground">
          Árvore genealógica expandida
        </p>
      </div>

      {/* Árvore Genealógica */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TreePine className="h-5 w-5" />
            Árvore Genealógica
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ExpandedGenealogy 
            cavaloId={selectedHorse.id}
          />
        </CardContent>
      </Card>
    </div>
  );
}