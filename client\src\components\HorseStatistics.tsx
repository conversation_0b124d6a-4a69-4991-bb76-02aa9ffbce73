import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Bar<PERSON>hart, XAxis, YAxis, Bar, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend, LineChart, Line, CartesianGrid } from 'recharts';
import { 
  <PERSON><PERSON>hart as BarChartIcon, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  Activity, 
  Calendar, 
  AlertCircle, 
  Clock, 
  CheckCircle2, 
  Scale,
  Ruler,
  Thermometer
} from 'lucide-react';
import { format, differenceInMonths, parseISO } from 'date-fns';
import { pt } from 'date-fns/locale';

interface HorseStatisticsProps {
  horse_id: number;
  horseName?: string;
  horseData: {
    id: number;
    name: string;
    birth_date?: string | null;
    peso?: number | null;
    altura?: number | null;
    sexo?: string | null;
    status?: string | null;
    data_entrada?: string | null;
  };
}

/**
 * Componente HorseStatistics
 * 
 * Exibe estatísticas detalhadas sobre o cavalo, incluindo:
 * - Distribuição de manejos por tipo
 * - Histórico de peso/altura (quando disponível)
 * - Estatísticas de saúde
 */
export function HorseStatistics({ horse_id, horseName, horseData }: HorseStatisticsProps) {
  const [manejos, setManejos] = useState<any[]>([]);
  const [procedimentosVet, setProcedimentosVet] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const { toast } = useToast();

  // Dados simulados para histórico de peso e altura
  // Em um sistema real, esses dados viriam do backend
  const [pesoHistorico, setPesoHistorico] = useState<any[]>([]);
  const [alturaHistorico, setAlturaHistorico] = useState<any[]>([]);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [manejosData, procedimentosData] = await Promise.all([
          apiRequest<any[]>(`/api/cavalos/${horse_id}/manejos`, 'GET'),
          apiRequest<any[]>(`/api/cavalos/${horse_id}/procedimentos-veterinarios`, 'GET')
        ]);
        
        setManejos(manejosData || []);
        setProcedimentosVet(procedimentosData || []);
        
        // Gerar dados simulados para histórico de métricas
        // Em um sistema real, esses dados viriam do banco de dados
        generateSampleData();
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        toast({
          title: 'Erro',
          description: 'Não foi possível carregar as estatísticas',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (horse_id) {
      loadData();
    }
  }, [horse_id]);

  // Função para gerar dados simulados para visualização de estatísticas
  // Em um sistema real, esses dados seriam carregados do banco de dados
  const generateSampleData = () => {
    // Verificar se o cavalo tem dados de peso e altura
    if (horseData.peso || horseData.altura) {
      // Gerar histórico de peso e altura
      const hoje = new Date();
      const dataNascimento = horseData.birth_date ? new Date(horseData.birth_date) : new Date(hoje.getFullYear() - 3, 0, 1);
      const meses = differenceInMonths(hoje, dataNascimento);
      const limiteMeses = Math.min(meses, 24); // Limitar a 24 meses de histórico
      
      // Gerar dados para peso
      if (horseData.peso) {
        const pesoDados = [];
        const pesoBase = horseData.peso;
        
        for (let i = 0; i < limiteMeses; i++) {
          const data = new Date(hoje.getFullYear(), hoje.getMonth() - i, 1);
          const variacao = (Math.random() - 0.5) * 20; // Variação aleatória de peso
          pesoDados.push({
            data: format(data, 'MMM/yyyy', { locale: pt }),
            value: Math.round((pesoBase + variacao) * 10) / 10,
            mes: i
          });
        }
        
        setPesoHistorico(pesoDados.reverse());
      }
      
      // Gerar dados para altura
      if (horseData.altura) {
        const alturaDados = [];
        const alturaBase = horseData.altura;
        
        for (let i = 0; i < limiteMeses; i++) {
          const data = new Date(hoje.getFullYear(), hoje.getMonth() - i, 1);
          const variacao = (Math.random() - 0.5) * 0.05; // Variação aleatória de altura
          alturaDados.push({
            data: format(data, 'MMM/yyyy', { locale: pt }),
            value: Math.round((alturaBase + variacao) * 100) / 100,
            mes: i
          });
        }
        
        setAlturaHistorico(alturaDados.reverse());
      }
    }
  };

  // Função para contar manejos por tipo
  const getManejosPorTipo = () => {
    const contagem: Record<string, number> = {};
    
    manejos.forEach(manejo => {
      contagem[manejo.tipo] = (contagem[manejo.tipo] || 0) + 1;
    });
    
    return Object.entries(contagem).map(([tipo, quantidade]) => ({
      tipo: getTipoManejoNome(tipo),
      quantidade
    }));
  };

  // Função para contar procedimentos por tipo
  const getProcedimentosPorTipo = () => {
    const contagem: Record<string, number> = {};
    
    procedimentosVet.forEach(proc => {
      contagem[proc.tipo] = (contagem[proc.tipo] || 0) + 1;
    });
    
    return Object.entries(contagem).map(([tipo, quantidade]) => ({
      tipo,
      quantidade
    }));
  };

  // Função para obter manejos por status
  const getManejosPorStatus = () => {
    const contagem: Record<string, number> = {
      'pendente': 0,
      'concluido': 0,
      'atrasado': 0,
      'cancelado': 0
    };
    
    manejos.forEach(manejo => {
      const status = manejo.status || 'pendente';
      contagem[status] = (contagem[status] || 0) + 1;
    });
    
    return Object.entries(contagem).map(([status, quantidade]) => ({
      status: getStatusNome(status),
      quantidade
    }));
  };

  // Função para obter nome legível do tipo de manejo
  const getTipoManejoNome = (tipo: string) => {
    const tipos: Record<string, string> = {
      'veterinary': 'Veterinário',
      'farrier': 'Ferrageamento',
      'vaccination': 'Vacinação',
      'deworming': 'Vermifugação',
      'dental': 'Dentário',
      'training': 'Treinamento',
      'other': 'Outro'
    };
    
    return tipos[tipo] || tipo;
  };

  // Função para obter nome legível do status
  const getStatusNome = (status: string) => {
    const statusMap: Record<string, string> = {
      'pendente': 'Pendente',
      'concluido': 'Concluído',
      'atrasado': 'Atrasado',
      'cancelado': 'Cancelado'
    };
    
    return statusMap[status] || status;
  };

  // Função para calcular idade do cavalo em meses
  const getIdadeMeses = () => {
    if (!horseData.birth_date) return 0;
    
    const nascimento = new Date(horseData.birth_date);
    const hoje = new Date();
    return differenceInMonths(hoje, nascimento);
  };

  // Cores para gráficos
  const CORES_MANEJOS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#0088fe', '#00C49F'];
  const CORES_STATUS = {
    'Pendente': '#ffc658',
    'Concluído': '#82ca9d',
    'Atrasado': '#ff7300',
    'Cancelado': '#d0d0d0'
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Card com total de manejos */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Activity className="h-5 w-5 mr-2 text-blue-500" />
              Total de Manejos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{manejos.length}</div>
            <p className="text-sm text-muted-foreground mt-1">
              Registros de cuidados e tratamentos
            </p>
          </CardContent>
        </Card>

        {/* Card com procedimentos veterinários */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Thermometer className="h-5 w-5 mr-2 text-rose-500" />
              Atendimentos Veterinários
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{procedimentosVet.length}</div>
            <p className="text-sm text-muted-foreground mt-1">
              Consultas e procedimentos médicos
            </p>
          </CardContent>
        </Card>

        {/* Card com idade do cavalo */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-amber-500" />
              Idade
            </CardTitle>
          </CardHeader>
          <CardContent>
            {horseData.birth_date ? (
              <>
                <div className="text-3xl font-bold">
                  {Math.floor(getIdadeMeses() / 12)} anos
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {getIdadeMeses()} meses de idade
                </p>
              </>
            ) : (
              <div className="text-muted">Data de nascimento não cadastrada</div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="manejos">
        <TabsList className="mb-4">
          <TabsTrigger value="manejos" className="flex items-center">
            <BarChartIcon className="h-4 w-4 mr-2" />
            Manejos
          </TabsTrigger>
          <TabsTrigger value="biometria" className="flex items-center">
            <Scale className="h-4 w-4 mr-2" />
            Biometria
          </TabsTrigger>
          <TabsTrigger value="saude" className="flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            Saúde
          </TabsTrigger>
        </TabsList>

        <TabsContent value="manejos">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Gráfico de barras - Manejos por tipo */}
            <Card>
              <CardHeader>
                <CardTitle>Manejos por Tipo</CardTitle>
                <CardDescription>
                  Distribuição de manejos por categoria
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {manejos.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getManejosPorTipo()}
                      margin={{ top: 10, right: 10, left: 10, bottom: 40 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="tipo" 
                        angle={-45} 
                        textAnchor="end"
                        tick={{ fontSize: 12 }}
                        height={60}
                      />
                      <YAxis />
                      <RechartsTooltip formatter={(value) => [`${value} manejos`, 'Quantidade']} />
                      <Bar dataKey="quantidade" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Nenhum manejo cadastrado</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Gráfico de pizza - Status de manejos */}
            <Card>
              <CardHeader>
                <CardTitle>Status dos Manejos</CardTitle>
                <CardDescription>
                  Distribuição por estado de conclusão
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {manejos.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={getManejosPorStatus()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="quantidade"
                        nameKey="status"
                        label={({ status, quantidade }) => 
                          quantidade > 0 ? `${status}: ${quantidade}` : null
                        }
                      >
                        {getManejosPorStatus().map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={CORES_STATUS[entry.status as keyof typeof CORES_STATUS] || CORES_MANEJOS[index % CORES_MANEJOS.length]} 
                          />
                        ))}
                      </Pie>
                      <Legend verticalAlign="bottom" height={36} />
                      <RechartsTooltip formatter={(value) => [`${value} manejos`, 'Quantidade']} />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Nenhum manejo cadastrado</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="biometria">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Gráfico de histórico de peso */}
            <Card>
              <CardHeader>
                <CardTitle>Histórico de Peso</CardTitle>
                <CardDescription>
                  Variação de peso ao longo do tempo
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {horseData.peso && pesoHistorico.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={pesoHistorico}
                      margin={{ top: 10, right: 10, left: 10, bottom: 40 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="data" 
                        angle={-45} 
                        textAnchor="end"
                        tick={{ fontSize: 12 }}
                        height={60}
                      />
                      <YAxis 
                        domain={['dataMin - 20', 'dataMax + 20']}
                        label={{ value: 'Peso (kg)', angle: -90, position: 'insideLeft' }}
                      />
                      <RechartsTooltip formatter={(value) => [`${value} kg`, 'Peso']} />
                      <Line type="monotone" dataKey="value" stroke="#8884d8" activeDot={{ r: 8 }} name="Peso" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Scale className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">
                        {horseData.peso 
                          ? 'Histórico de peso não disponível' 
                          : 'Peso não cadastrado'}
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Peso atual: {horseData.peso ? `${horseData.peso} kg` : 'Não informado'}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Gráfico de histórico de altura */}
            <Card>
              <CardHeader>
                <CardTitle>Histórico de Altura</CardTitle>
                <CardDescription>
                  Crescimento ao longo do tempo
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {horseData.altura && alturaHistorico.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={alturaHistorico}
                      margin={{ top: 10, right: 10, left: 10, bottom: 40 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="data" 
                        angle={-45} 
                        textAnchor="end"
                        tick={{ fontSize: 12 }}
                        height={60}
                      />
                      <YAxis 
                        domain={['dataMin - 0.05', 'dataMax + 0.05']}
                        label={{ value: 'Altura (m)', angle: -90, position: 'insideLeft' }}
                      />
                      <RechartsTooltip formatter={(value) => [`${value} m`, 'Altura']} />
                      <Line type="monotone" dataKey="value" stroke="#82ca9d" activeDot={{ r: 8 }} name="Altura" />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Ruler className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">
                        {horseData.altura 
                          ? 'Histórico de altura não disponível' 
                          : 'Altura não cadastrada'}
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Altura atual: {horseData.altura ? `${horseData.altura} m` : 'Não informada'}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="saude">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Procedimentos veterinários por tipo */}
            <Card>
              <CardHeader>
                <CardTitle>Procedimentos Veterinários</CardTitle>
                <CardDescription>
                  Distribuição por tipo de procedimento
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {procedimentosVet.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={getProcedimentosPorTipo()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="quantidade"
                        nameKey="tipo"
                        label={({ tipo, quantidade }) => 
                          quantidade > 0 ? `${tipo}: ${quantidade}` : null
                        }
                      >
                        {getProcedimentosPorTipo().map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={CORES_MANEJOS[index % CORES_MANEJOS.length]} 
                          />
                        ))}
                      </Pie>
                      <Legend verticalAlign="bottom" height={36} />
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">Nenhum procedimento veterinário cadastrado</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Indicadores de Saúde e Bem-estar */}
            <Card>
              <CardHeader>
                <CardTitle>Indicadores de Saúde</CardTitle>
                <CardDescription>
                  Resumo dos principais indicadores
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Última consulta veterinária */}
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-50 rounded-md p-2">
                      <Clock className="h-5 w-5 text-blue-500" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Última consulta veterinária</h4>
                      {procedimentosVet.length > 0 ? (
                        <p className="text-sm mt-1">
                          {format(new Date(procedimentosVet[0].data), 'dd/MM/yyyy', { locale: pt })}
                          {procedimentosVet[0].tipo && ` - ${procedimentosVet[0].tipo}`}
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground mt-1">Nenhum registro encontrado</p>
                      )}
                    </div>
                  </div>

                  {/* Última vermifugação */}
                  <div className="flex items-start space-x-3">
                    <div className="bg-green-50 rounded-md p-2">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Última vermifugação</h4>
                      {manejos.find(m => m.tipo === 'deworming') ? (
                        <p className="text-sm mt-1">
                          {format(
                            new Date(manejos.find(m => m.tipo === 'deworming')?.data), 
                            'dd/MM/yyyy', 
                            { locale: pt }
                          )}
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground mt-1">Nenhum registro encontrado</p>
                      )}
                    </div>
                  </div>

                  {/* Último ferrageamento */}
                  <div className="flex items-start space-x-3">
                    <div className="bg-amber-50 rounded-md p-2">
                      <CheckCircle2 className="h-5 w-5 text-amber-500" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Último ferrageamento</h4>
                      {manejos.find(m => m.tipo === 'farrier') ? (
                        <p className="text-sm mt-1">
                          {format(
                            new Date(manejos.find(m => m.tipo === 'farrier')?.data), 
                            'dd/MM/yyyy', 
                            { locale: pt }
                          )}
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground mt-1">Nenhum registro encontrado</p>
                      )}
                    </div>
                  </div>

                  {/* Último tratamento dentário */}
                  <div className="flex items-start space-x-3">
                    <div className="bg-rose-50 rounded-md p-2">
                      <CheckCircle2 className="h-5 w-5 text-rose-500" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Último tratamento dentário</h4>
                      {manejos.find(m => m.tipo === 'dental') ? (
                        <p className="text-sm mt-1">
                          {format(
                            new Date(manejos.find(m => m.tipo === 'dental')?.data), 
                            'dd/MM/yyyy', 
                            { locale: pt }
                          )}
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground mt-1">Nenhum registro encontrado</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}