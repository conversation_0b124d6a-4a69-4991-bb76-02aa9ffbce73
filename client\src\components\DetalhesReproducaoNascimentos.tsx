import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, FileText, Edit } from 'lucide-react';
import { Reproducao, Cavalo } from '@shared/schema';

/**
 * Componente de Detalhes de Nascimentos
 * Exibe informações detalhadas sobre nascimentos de potros
 */
export function DetalhesReproducaoNascimentos() {
  // Consultar dados de reprodução
  const { data: reproducoes = [], isLoading: loadingReproducoes } = useQuery<Reproducao[]>({
    queryKey: ['/api/reproducao'],
  });
  
  // Consultar dados de cavalos 
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });
  
  // Filtrar apenas reproduções com nascimentos
  const nascimentos = reproducoes.filter(rep => 
    rep.resultado === 'parto_realizado' || 
    rep.observacoes?.toLowerCase().includes('parto') ||
    rep.observacoes?.toLowerCase().includes('nasceu') ||
    rep.observacoes?.toLowerCase().includes('potro')
  );

  // Função para obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    const cavalo = cavalos.find(c => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };
  
  // Função para formatar data
  const formatarData = (dataString: string | null) => {
    if (!dataString) return 'Não informada';
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  };

  // Função para obter o sexo do potro a partir das observações
  const getSexoPotro = (observacoes: string | null): string => {
    if (!observacoes) return 'Não informado';
    
    const textLower = observacoes.toLowerCase();
    if (textLower.includes('potro macho') || textLower.includes('potro: macho')) return 'macho';
    if (textLower.includes('potra') || textLower.includes('fêmea') || textLower.includes('potro: fêmea')) return 'femea';
    
    return 'Não informado';
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Registro de Nascimentos</h1>
        <p className="text-muted-foreground">
          Histórico completo dos potros nascidos
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader className="bg-blue-50">
          <CardTitle>Resumo</CardTitle>
          <CardDescription>Estatísticas de nascimentos</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
            <div className="flex flex-col p-4 bg-blue-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Total de Nascimentos</span>
              <span className="text-2xl font-bold">{nascimentos.length}</span>
            </div>
            <div className="flex flex-col p-4 bg-green-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Machos</span>
              <span className="text-2xl font-bold">
                {nascimentos.filter(n => getSexoPotro(n.observacoes) === 'macho').length}
              </span>
            </div>
            <div className="flex flex-col p-4 bg-pink-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Fêmeas</span>
              <span className="text-2xl font-bold">
                {nascimentos.filter(n => getSexoPotro(n.observacoes) === 'femea').length}
              </span>
            </div>
            <div className="flex flex-col p-4 bg-amber-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Não catalogados</span>
              <span className="text-2xl font-bold">
                {nascimentos.filter(n => getSexoPotro(n.observacoes) === 'Não informado').length}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Potros Nascidos</CardTitle>
          <CardDescription>Registro detalhado de todos os nascimentos</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingReproducoes || loadingCavalos ? (
            <div className="text-center py-8">Carregando informações...</div>
          ) : nascimentos.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Nome</TableHead>
                  <TableHead>Mãe</TableHead>
                  <TableHead>Pai</TableHead>
                  <TableHead>Sexo</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {nascimentos.map((nascimento) => (
                  <TableRow key={nascimento.id}>
                    <TableCell>{formatarData(nascimento.data_parto_real || nascimento.data_cobertura)}</TableCell>
                    <TableCell>
                      {extrairNomePotro(nascimento.observacoes) || 'Não registrado'}
                    </TableCell>
                    <TableCell>{getCavaloName(nascimento.egua_id)}</TableCell>
                    <TableCell>
                      {nascimento.garanhao_id ? getCavaloName(nascimento.garanhao_id) : 'Não informado'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={getSexoPotro(nascimento.observacoes) === 'macho' ? 'default' : 'secondary'}>
                        {getSexoPotro(nascimento.observacoes)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusPotro(nascimento.observacoes)}
                    </TableCell>
                    <TableCell className="text-right space-x-1">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <FileText className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhum nascimento registrado</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Função auxiliar para tentar extrair o nome do potro das observações
function extrairNomePotro(observacoes: string | null): string | null {
  if (!observacoes) return null;
  
  // Padrões comuns em anotações de nascimento
  const padroes = [
    /potro(?:\(a\))?\s+(?:chamado|nomeado)?\s*[":]\s*([^,.;]+)/i,
    /nasceu\s+(?:o|a)\s+potro(?:\(a\))?\s+([^,.;]+)/i,
    /nome\s+(?:do|da)\s+potro(?:\(a\))?\s*[":]\s*([^,.;]+)/i,
    /potro(?:\(a\))?\s+([^,.;]+)/i
  ];
  
  for (const padrao of padroes) {
    const match = observacoes.match(padrao);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  return null;
}

// Função para determinar o status do potro
function getStatusPotro(observacoes: string | null): JSX.Element {
  if (!observacoes) return <Badge variant="outline">Desconhecido</Badge>;
  
  const obsLower = observacoes.toLowerCase();
  
  if (obsLower.includes('faleceu') || obsLower.includes('morreu') || obsLower.includes('óbito')) {
    return <Badge variant="destructive">Falecido</Badge>;
  }
  
  if (obsLower.includes('saudável') || obsLower.includes('bem') || obsLower.includes('normal')) {
    return <Badge variant="default">Saudável</Badge>;
  }
  
  if (obsLower.includes('complicação') || obsLower.includes('tratamento') || obsLower.includes('problema')) {
    return <Badge variant="destructive">Em tratamento</Badge>;
  }
  
  return <Badge variant="secondary">Registrado</Badge>;
}