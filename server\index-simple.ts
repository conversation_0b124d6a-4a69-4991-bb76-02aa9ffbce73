import express from "express";
import { addApiRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { createServer } from "http";

const app = express();

// Middlewares básicos
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Adicionar rotas da API
addApiRoutes(app);

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'EquiGestor AI'
  });
});

const server = createServer(app);

(async () => {
  // Configurar Vite para desenvolvimento
  const nodeEnv = process.env.NODE_ENV || "development";
  if (nodeEnv === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  const port = 5000;
  
  // Iniciar servidor
  server.listen(port, "0.0.0.0", () => {
    console.log(`✅ EquiGestor AI iniciado na porta ${port}`);
    log(`serving on port ${port}`);
  });

  server.on('error', (error) => {
    console.error('Erro no servidor:', error);
  });
})();