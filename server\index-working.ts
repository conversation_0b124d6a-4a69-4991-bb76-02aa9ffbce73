import express from "express";
import cors from "cors";
import { createServer } from "http";
import { addApiRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();

// Basic CORS and body parsing
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'user-id']
}));

app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Simple request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'EquiGestor AI Online!',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime())
  });
});

// Port detection for different environments
function getPort() {
  if (process.env.REPL_ID || process.env.REPLIT_DB_URL) {
    return Number(process.env.PORT) || Number(process.env.REPL_PORT) || 80;
  }
  return Number(process.env.PORT) || 5000;
}

const port = getPort();
const host = '0.0.0.0';

const server = createServer(app);

(async () => {
  try {
    console.log('Starting EquiGestor AI server...');
    
    // Add API routes
    console.log('Configuring API routes...');
    await addApiRoutes(app);
    
    // Setup Vite for development
    const nodeEnv = process.env.NODE_ENV || "development";
    if (nodeEnv === "development") {
      console.log('Setting up Vite development server...');
      await setupVite(app, server);
    } else {
      console.log('Serving static files for production...');
      serveStatic(app);
    }

    // Start server
    server.listen(port, host, () => {
      console.log(`🎉 EquiGestor AI running on port ${port}`);
      console.log(`Host: ${host}`);
      console.log(`Environment: ${nodeEnv}`);
      log(`serving on ${host}:${port}`);
    });

    // Basic error handling
    server.on('error', (error) => {
      console.error('Server error:', error);
    });

  } catch (error) {
    console.error('Critical startup error:', error);
    process.exit(1);
  }
})();