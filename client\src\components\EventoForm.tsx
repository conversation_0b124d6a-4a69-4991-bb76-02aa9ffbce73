import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { <PERSON><PERSON>lo, insertEventoSchema } from '../../../shared/schema';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Calendar, Clock, Save, X } from 'lucide-react';

// Schema para validação do formulário, estendendo o schema base de eventos
const eventoFormSchema = insertEventoSchema.extend({
  titulo: z.string().min(3, 'O título deve ter pelo menos 3 caracteres').max(100),
  tipo: z.string().min(1, 'Selecione um tipo de evento'),
  data: z.string().min(1, 'Selecione uma data'),
  horaInicio: z.string().optional(),
  horaFim: z.string().optional(),
  descricao: z.string().optional(),
  horse_id: z.number().optional().nullable(),
  prioridade: z.enum(['baixa', 'media', 'alta']).default('media'),
  status: z.enum(['pendente', 'concluido']).default('pendente'),
});

type EventoFormData = z.infer<typeof eventoFormSchema>;

interface EventoFormProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: Date;
  eventoId?: number;
  onSuccess?: () => void;
}

export function EventoForm({ 
  isOpen, 
  onClose, 
  selectedDate = new Date(),
  eventoId,
  onSuccess
}: EventoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  // Estado para armazenar o usuario logado
  const [user] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (e) {
        return null;
      }
    }
    return null;
  });
  
  // Buscar dados do evento se estiver editando
  const { data: evento } = useQuery({
    queryKey: [`/api/eventos/${eventoId}`],
    enabled: !!eventoId && !!user,
    queryFn: async () => {
      try {
        if (!eventoId) return null;
        return await apiRequest(`/api/eventos/${eventoId}`, 'GET');
      } catch (error) {
        console.error("Erro ao buscar evento:", error);
        return null;
      }
    }
  });
  
  // Buscar lista de cavalos para o select
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        return await apiRequest('/api/cavalos', 'GET') || [];
      } catch (error) {
        console.error("Erro ao buscar cavalos:", error);
        return [];
      }
    }
  });
  
  // Inicialização do formulário com react-hook-form
  const form = useForm<EventoFormData>({
    resolver: zodResolver(eventoFormSchema),
    defaultValues: {
      titulo: evento?.titulo || '',
      tipo: evento?.tipo || 'outro',
      data: evento?.data || format(selectedDate, 'yyyy-MM-dd'),
      horaInicio: evento?.horaInicio || '',
      horaFim: evento?.horaFim || '',
      descricao: evento?.descricao || '',
      horse_id: evento?.horse_id || undefined,
      prioridade: evento?.prioridade || 'media',
      status: evento?.status || 'pendente',
    }
  });
  
  // Quando o evento é carregado, atualizar o formulário
  useState(() => {
    if (evento) {
      form.reset({
        titulo: evento.titulo,
        tipo: evento.tipo,
        data: evento.data,
        horaInicio: evento.horaInicio || '',
        horaFim: evento.horaFim || '',
        descricao: evento.descricao || '',
        horse_id: evento.horse_id,
        prioridade: evento.prioridade || 'media',
        status: evento.status || 'pendente',
      });
    } else if (selectedDate) {
      form.setValue('data', format(selectedDate, 'yyyy-MM-dd'));
    }
  });
  
  // Função para marcar o evento como concluído
  const handleMarkAsCompleted = async () => {
    if (!user || !eventoId) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Chamar a rota específica de conclusão
      await apiRequest(`/api/eventos/${eventoId}/concluir`, 'PUT');
      
      toast({
        title: "Sucesso",
        description: "Evento marcado como concluído",
      });
      
      // Invalidar cache de eventos
      queryClient.invalidateQueries({ queryKey: ['/api/eventos'] });
      
      // Callback de sucesso, se fornecido
      if (onSuccess) {
        onSuccess();
      }
      
      // Fechar diálogo
      onClose();
    } catch (error) {
      console.error("Erro ao concluir evento:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao concluir o evento. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para salvar o evento
  const handleSubmit = async (data: EventoFormData) => {
    if (!user) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para criar eventos",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Adicionar user_id dos dados
      const eventoData = {
        ...data,
        user_id: user.id
      };
      
      if (eventoId) {
        // Editar evento existente
        await apiRequest(`/api/eventos/${eventoId}`, 'PATCH', eventoData);
        
        toast({
          title: "Sucesso",
          description: "Evento atualizado com sucesso",
        });
      } else {
        // Criar novo evento
        await apiRequest('/api/eventos', 'POST', eventoData);
        
        toast({
          title: "Sucesso",
          description: "Evento criado com sucesso",
        });
      }
      
      // Invalidar cache de eventos
      queryClient.invalidateQueries({ queryKey: ['/api/eventos'] });
      
      // Callback de sucesso, se fornecido
      if (onSuccess) {
        onSuccess();
      }
      
      // Fechar diálogo
      onClose();
    } catch (error) {
      console.error("Erro ao salvar evento:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar o evento. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {eventoId ? "Editar Evento" : "Novo Evento"}
          </DialogTitle>
          <DialogDescription>
            {eventoId 
              ? "Edite os detalhes do evento selecionado" 
              : "Adicione um novo evento à agenda"
            }
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="titulo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título do Evento</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Consulta Veterinária" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="tipo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Evento</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="veterinario">Consulta Veterinária</SelectItem>
                        <SelectItem value="ferrador">Ferrageamento</SelectItem>
                        <SelectItem value="treinamento">Treinamento</SelectItem>
                        <SelectItem value="competicao">Competição</SelectItem>
                        <SelectItem value="reprodutivo">Reprodutivo</SelectItem>
                        <SelectItem value="outro">Outro</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="prioridade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prioridade</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a prioridade" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="baixa">Baixa</SelectItem>
                        <SelectItem value="media">Média</SelectItem>
                        <SelectItem value="alta">Alta</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="data"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input 
                          type="date" 
                          {...field} 
                          className="pl-9"
                        />
                      </FormControl>
                      <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="horse_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Animal (opcional)</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value ? Number(value) : null)}
                      value={field.value?.toString() || ''}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um animal" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="nenhum">Nenhum animal</SelectItem>
                        {cavalos.map((cavalo: Cavalo) => (
                          <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                            {cavalo.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione um animal relacionado a este evento
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="horaInicio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Horário Início (opcional)</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input 
                          type="time" 
                          {...field} 
                          className="pl-9"
                        />
                      </FormControl>
                      <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="horaFim"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Horário Fim (opcional)</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input 
                          type="time" 
                          {...field} 
                          className="pl-9"
                        />
                      </FormControl>
                      <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {eventoId && (
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Status do evento" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="pendente">Pendente</SelectItem>
                        <SelectItem value="concluido">Concluído</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            
            <FormField
              control={form.control}
              name="descricao"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição (opcional)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Descreva os detalhes do evento..."
                      className="resize-none min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
              
            <DialogFooter className="pt-4">
              <div className="flex flex-wrap gap-2 justify-end w-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  <X className="mr-2 h-4 w-4" />
                  Cancelar
                </Button>
                
                {eventoId && form.getValues('status') !== 'concluido' && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handleMarkAsCompleted}
                    disabled={isSubmitting}
                    className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                  >
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      className="mr-2 h-4 w-4" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M5 13l4 4L19 7" 
                      />
                    </svg>
                    Marcar como Concluído
                  </Button>
                )}
                
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isSubmitting ? "Salvando..." : "Salvar Evento"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default EventoForm;