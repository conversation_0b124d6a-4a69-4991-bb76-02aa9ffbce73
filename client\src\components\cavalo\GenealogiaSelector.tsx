import React, { useEffect, useState, useMemo } from 'react';
import {
  FormField,
  FormLabel,
  FormItem,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Cavalo } from '@shared/schema';
import { Control } from 'react-hook-form';

import { EntradaGenealogica, TipoEntradaGenealogica } from '@/types/genealogy';

export const createDefaultEntradaGenealogica = (): EntradaGenealogica => ({
  tipo: 'nenhum',
  cavaloSistemaId: undefined,
  cavaloNome: undefined,
});

// Propriedades do componente
interface GenealogiaSelectorProps {
  cavalos: Cavalo[];
  value?: EntradaGenealogica;
  onChange: (value: EntradaGenealogica) => void;
  label: string;
  sexoFiltro?: 'macho' | 'femea' | 'Castrado' | 'garanhao' | 'femea';
  error?: string;
  disabled?: boolean;
  className?: string;
  name?: string;
  description?: string;
  control?: Control<any>;
  required?: boolean;
}

// Mapeamento de aliases para sexos - otimizado para reduzir redundância
const sexoAliases: Record<string, string[]> = {
  Macho: ['macho', 'garanhao', 'Castrado'],
  Fêmea: ['femea', 'femea'],
  Garanhão: ['macho', 'garanhao', 'Castrado'],
  Égua: ['femea', 'femea'],
  Castrado: ['macho', 'garanhao', 'Castrado'],
};

export const GenealogiaSelector: React.FC<GenealogiaSelectorProps> = ({
  cavalos = [],
  value = { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null },
  onChange,
  label,
  sexoFiltro,
  error,
  disabled = false,
  className = '',
  name,
  description,
  control,
  required = false,
}) => {
  // Estado interno
  const [tipoEntrada, setTipoEntrada] = useState<TipoEntradaGenealogica>((value?.tipo as TipoEntradaGenealogica) || 'nenhum');
  const [cavaloId, setCavaloId] = useState<string | null>(value.cavaloSistemaId ?? null);
  const [nomeExterno, setNomeExterno] = useState<string>(value.cavaloNome ?? '');

  // Filtragem otimizada de cavalos
  const cavalosFiltrados = useMemo(() => {
    // Aplicar filtro de sexo
    let resultado = cavalos;
    if (sexoFiltro) {
      const validSexos = sexoAliases[sexoFiltro] || [sexoFiltro];
      resultado = cavalos.filter((c) => c.sexo && validSexos.includes(c.sexo));
    }
    
    // Agora separamos os cavalos do plantel dos externos
    // Para o dropdown "Do Meu Plantel", só mostrar cavalos com is_external=false
    console.log(`[GenealogiaSelector] Filtrando ${resultado.length} cavalos para o plantel`);
    const cavalosPlantel = resultado.filter(c => c.is_external !== true);
    console.log(`[GenealogiaSelector] Encontrados ${cavalosPlantel.length} cavalos do plantel`);
    
    return cavalosPlantel;
  }, [cavalos, sexoFiltro]);
  
  // Filtragem para cavalos externos já com ID
  const cavalosExternosFiltrados = useMemo(() => {
    // Aplicar filtro de sexo
    let resultado = cavalos;
    if (sexoFiltro) {
      const validSexos = sexoAliases[sexoFiltro] || [sexoFiltro];
      resultado = cavalos.filter((c) => c.sexo && validSexos.includes(c.sexo));
    }
    
    console.log(`[GenealogiaSelector] Filtrando ${resultado.length} cavalos para externos`);
    
    // Filtrar apenas os cavalos externos (que têm is_external=true)
    const cavalosExternos = resultado.filter(c => c.is_external === true);
    console.log(`[GenealogiaSelector] Encontrados ${cavalosExternos.length} cavalos externos`);
    
    // Debug para ver o que exatamente está na lista
    cavalosExternos.forEach(c => {
      console.log(`[GenealogiaSelector] Cavalo externo: id=${c.id}, nome=${c.name}, is_external=${c.is_external}`);
    });
    
    return cavalosExternos;
  }, [cavalos, sexoFiltro]);

  // Sincroniza estado com value
  useEffect(() => {
    setTipoEntrada((value?.tipo as TipoEntradaGenealogica) || 'nenhum');
    setCavaloId(value.cavaloSistemaId ?? null);
    setNomeExterno(value.cavaloNome ?? '');
  }, [value]);

  // Dispara onChange
  useEffect(() => {
    console.log('[GenealogiaSelector] Atualizando valores: tipo:', tipoEntrada, 'cavaloId:', cavaloId, 'nomeExterno:', nomeExterno);
    
    const novoValor: EntradaGenealogica = {
      tipo: tipoEntrada,
      cavaloSistemaId: null,
      cavaloNome: null,
    };

    if (tipoEntrada === 'sistema' && cavaloId) {
      // Cavalo do plantel
      const cavaloSelecionado = cavalosFiltrados.find((c) => c.id.toString() === cavaloId);
      if (cavaloSelecionado) {
        console.log(`[GenealogiaSelector] Selecionado cavalo do plantel: ${cavaloSelecionado.name} (ID: ${cavaloSelecionado.id})`);
        novoValor.cavaloSistemaId = cavaloSelecionado.id.toString();
        novoValor.cavaloNome = cavaloSelecionado.name;
      } else {
        console.log(`[GenealogiaSelector] ID ${cavaloId} não encontrado na lista de cavalos do plantel`);
        setCavaloId(null); // Reset if invalid ID
      }
    } else if (tipoEntrada === 'externo') {
      // Para cavalos externos agora temos duas possibilidades:
      // 1. Um cavalo externo selecionado da lista (tem ID e nome)
      // 2. Um novo cavalo externo (só tem nome)
      
      if (cavaloId) {
        // Selecionado da lista de cavalos externos existentes
        console.log(`[GenealogiaSelector] Buscando cavalo externo com ID: ${cavaloId}`);
        const cavaloSelecionado = cavalosExternosFiltrados.find((c) => c.id.toString() === cavaloId);
        
        if (cavaloSelecionado) {
          console.log(`[GenealogiaSelector] Selecionado cavalo externo: ${cavaloSelecionado.name} (ID: ${cavaloSelecionado.id})`);
          novoValor.cavaloSistemaId = cavaloSelecionado.id.toString();
          novoValor.cavaloNome = cavaloSelecionado.name;
        } else {
          console.log(`[GenealogiaSelector] ID ${cavaloId} não encontrado na lista de cavalos externos`);
          
          // Se o ID não for encontrado mas temos um nome externo, usamos o nome
          if (nomeExterno && nomeExterno.trim()) {
            console.log(`[GenealogiaSelector] Usando nome externo com ID não encontrado: ${nomeExterno} (ID: ${cavaloId})`);
            novoValor.cavaloSistemaId = cavaloId;
            novoValor.cavaloNome = nomeExterno.trim();
          } else {
            console.log(`[GenealogiaSelector] Resetando ID de cavalo externo não encontrado`);
            setCavaloId(null); // Reset if invalid ID
          }
        }
      } else if (nomeExterno.trim()) {
        // Novo cavalo externo sem ID
        const trimmedNome = nomeExterno.trim();
        if (trimmedNome.length <= 80) {
          console.log(`[GenealogiaSelector] Novo cavalo externo sem ID: ${trimmedNome}`);
          novoValor.cavaloNome = trimmedNome;
          // cavaloSistemaId fica null e será gerado ao salvar
        }
      }
    }

    console.log('[GenealogiaSelector] Valor final:', novoValor);
    onChange(novoValor);
  }, [tipoEntrada, cavaloId, nomeExterno, cavalosFiltrados, cavalosExternosFiltrados, onChange]);

  // Handler para troca de tipo
  const handleTipoChange = (newTipo: TipoEntradaGenealogica) => {
    setTipoEntrada(newTipo);
    if (newTipo === 'sistema') {
      setNomeExterno('');
      if (!cavaloId && cavalosFiltrados.length > 0) {
        setCavaloId(cavalosFiltrados[0].id.toString());
      }
    } else if (newTipo === 'externo') {
      setCavaloId(null);
    } else {
      setCavaloId(null);
      setNomeExterno('');
    }
  };

  // ID único para acessibilidade
  const fieldId = `genealogia-${(label || 'campo').toLowerCase().replace(/\s+/g, '-')}`;

  // Componente renderizado
  const renderField = () => (
    <FormItem>
      <FormLabel htmlFor={fieldId}>
        {label}{required && <span className="text-red-500 ml-1">*</span>}
      </FormLabel>
      {description && <FormDescription>{description}</FormDescription>}
      <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-end">
        <Select
          value={tipoEntrada}
          onValueChange={(val) => handleTipoChange(val as TipoEntradaGenealogica)}
          disabled={disabled}
        >
          <FormControl>
            <SelectTrigger
              id={fieldId}
              className="w-36"
              aria-label={`Selecionar tipo de ${label}`}
              aria-required={required}
            >
              <SelectValue placeholder="Tipo" />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            <SelectItem value="nenhum">Não informado</SelectItem>
            <SelectItem value="sistema">Do Meu Plantel</SelectItem>
            <SelectItem value="externo">Externo/ABCCC</SelectItem>
          </SelectContent>
        </Select>

        {tipoEntrada === 'sistema' && (
          <Select
            value={cavaloId ?? ''}
            onValueChange={(val) => setCavaloId(val || null)}
            disabled={disabled || !cavalosFiltrados.length}
          >
            <FormControl>
              <SelectTrigger
                aria-describedby={error ? `${fieldId}-error` : undefined}
                className="min-w-[250px]"
                id={`${fieldId}-sistema-select`}
                aria-required={required}
              >
                <SelectValue placeholder={`Selecione um ${sexoFiltro || 'cavalo'}`} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {cavalosFiltrados.length > 0 ? (
                cavalosFiltrados.map((c) => (
                  <SelectItem key={c.id} value={c.id.toString()}>
                    {c.name} {c.breed && <>({c.breed})</>}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="empty" disabled>
                  Nenhum cavalo disponível
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}

        {tipoEntrada === 'externo' && (
          <div className="flex flex-col gap-2">
            {/* Primeiro, verificamos se há cavalos externos já cadastrados no sistema */}
            {cavalosExternosFiltrados.length > 0 && (
              <div className="flex flex-col sm:flex-row gap-2 items-start">
                <Select
                  value={cavaloId ?? ''}
                  onValueChange={(val) => {
                    setCavaloId(val || null);
                    // Se um cavalo externo foi selecionado, também atualizar o nome
                    if (val) {
                      const cavaloSelecionado = cavalosExternosFiltrados.find(c => c.id.toString() === val);
                      if (cavaloSelecionado) {
                        setNomeExterno(cavaloSelecionado.name);
                      }
                    }
                  }}
                  disabled={disabled}
                >
                  <FormControl>
                    <SelectTrigger
                      aria-describedby={error ? `${fieldId}-error` : undefined}
                      className="min-w-[250px]"
                      id={`${fieldId}-externo-select`}
                      aria-required={required}
                    >
                      <SelectValue placeholder="Selecione um cavalo externo" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="outro">-- Outro (informar nome) --</SelectItem>
                    {cavalosExternosFiltrados.map((c) => (
                      <SelectItem key={c.id} value={c.id.toString()}>
                        {c.name} {c.breed && <>({c.breed})</>}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground italic mt-1">
                  Cavalos externos já cadastrados anteriormente
                </div>
              </div>
            )}
            
            {/* Campo para informar o nome do cavalo externo */}
            <div className="flex flex-col sm:flex-row gap-2 items-start">
              <FormControl>
                <Input
                  className="min-w-[250px]"
                  value={nomeExterno}
                  onChange={(e) => {
                    setNomeExterno(e.target.value);
                    // Se o usuário digitar um nome manualmente, limpar o ID selecionado
                    if (e.target.value !== nomeExterno) {
                      setCavaloId(null);
                    }
                  }}
                  placeholder={`Nome do ${sexoFiltro || 'cavalo'} externo`}
                  aria-describedby={error ? `${fieldId}-error` : undefined}
                  id={`${fieldId}-externo-input`}
                  disabled={disabled}
                  maxLength={80}
                  autoComplete="off"
                  aria-required={required}
                />
              </FormControl>
              <div className="text-xs text-muted-foreground italic mt-1">
                {cavalosExternosFiltrados.length > 0
                  ? 'Ou informe o nome de um novo cavalo externo'
                  : 'Cavalos do registro ABCCC ou externos que não fazem parte do seu plantel'}
              </div>
            </div>
          </div>
        )}
      </div>
      {error && (
        <FormMessage id={`${fieldId}-error`} role="alert">
          {error}
        </FormMessage>
      )}
    </FormItem>
  );

  return (
    <div className={`space-y-2 ${className}`}>
      {control && name ? (
        <FormField
          control={control}
          name={name}
          render={({ field }) => {
            // Garantir que field.value é EntradaGenealogica
            const fieldValue: EntradaGenealogica =
              field.value && typeof field.value === 'object'
                ? (field.value as EntradaGenealogica)
                : { tipo: 'nenhum', cavaloSistemaId: null, cavaloNome: null };

            // Verificar se o tipo é válido, caso contrário usar 'nenhum'
            const tipoValido: TipoEntradaGenealogica = 
              (fieldValue.tipo === 'sistema' || fieldValue.tipo === 'externo' || fieldValue.tipo === 'nenhum')
                ? fieldValue.tipo
                : 'nenhum';

            // Sincronizar estado interno apenas se necessário
            if (JSON.stringify(fieldValue) !== JSON.stringify(value)) {
              setTipoEntrada(tipoValido);
              setCavaloId(fieldValue.cavaloSistemaId ?? null);
              setNomeExterno(fieldValue.cavaloNome ?? '');
            }

            // Combinar onChange do componente com o do react-hook-form
            const handleCombinedChange = (val: EntradaGenealogica) => {
              field.onChange(val);
              onChange(val);
            };

            return React.cloneElement(renderField(), {
              value: fieldValue,
              onChange: handleCombinedChange,
            });
          }}
        />
      ) : (
        renderField()
      )}
    </div>
  );
};

export default GenealogiaSelector;