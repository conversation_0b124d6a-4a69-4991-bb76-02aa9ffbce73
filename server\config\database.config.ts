// Database configuration management
import { DatabaseConfig } from '../types';

export const getDatabaseConfig = (): DatabaseConfig => {
  // Check for Neon database URL first (production)
  const neonUrl = process.env.DATABASE_URL;
  if (neonUrl) {
    console.log('Using Neon database configuration');
    return parseConnectionString(neonUrl);
  }

  // Fallback to local PostgreSQL configuration
  console.log('Using local PostgreSQL configuration');
  return {
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432'),
    database: process.env.PGDATABASE || 'equigestor',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || 'postgres',
    ssl: process.env.NODE_ENV === 'production'
  };
};

const parseConnectionString = (connectionString: string): DatabaseConfig => {
  try {
    const url = new URL(connectionString);
    
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.slice(1), // Remove leading slash
      user: url.username,
      password: url.password,
      ssl: url.searchParams.get('sslmode') !== 'disable'
    };
  } catch (error) {
    console.error('Failed to parse database connection string:', error);
    throw new Error('Invalid database connection string');
  }
};

export const getConnectionString = (config: DatabaseConfig): string => {
  const { host, port, database, user, password, ssl } = config;
  const sslParam = ssl ? '?sslmode=require' : '';
  return `postgresql://${user}:${password}@${host}:${port}/${database}${sslParam}`;
};