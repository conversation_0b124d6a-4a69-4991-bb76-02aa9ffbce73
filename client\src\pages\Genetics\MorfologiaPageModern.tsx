import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, Edit, Eye, Plus, Ruler, TrendingUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useGeneticsContext } from '@/contexts/GeneticsContext';
import { useAuth } from '@/context/AuthContext';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

// Schema de validação
const morfologiaSchema = z.object({
  data_medicao: z.string().min(1, 'Data da medição é obrigatória'),
  responsavel_medicao: z.string().optional(),
  altura_cernelha: z.number().min(0).optional(),
  altura_dorso: z.number().min(0).optional(),
  altura_garupa: z.number().min(0).optional(),
  comprimento_corpo: z.number().min(0).optional(),
  comprimento_pescoco: z.number().min(0).optional(),
  largura_peito: z.number().min(0).optional(),
  perimetro_toracico: z.number().min(0).optional(),
  perimetro_canela: z.number().min(0).optional(),
  pontuacao_cabeca: z.number().min(0).max(10).optional(),
  pontuacao_pescoco: z.number().min(0).max(10).optional(),
  pontuacao_espalda: z.number().min(0).max(10).optional(),
  pontuacao_dorso: z.number().min(0).max(10).optional(),
  pontuacao_garupa: z.number().min(0).max(10).optional(),
  pontuacao_membros: z.number().min(0).max(10).optional(),
  pontuacao_aprumos: z.number().min(0).max(10).optional(),
  pontuacao_andamento: z.number().min(0).max(10).optional(),
  observacoes: z.string().optional(),
});

type MorfologiaFormData = z.infer<typeof morfologiaSchema>;

interface MorfologiaRecord {
  id: number;
  cavalo_id: number;
  data_medicao: string;
  responsavel_medicao?: string;
  altura_cernelha?: number;
  altura_dorso?: number;
  altura_garupa?: number;
  comprimento_corpo?: number;
  comprimento_pescoco?: number;
  largura_peito?: number;
  perimetro_toracico?: number;
  perimetro_canela?: number;
  pontuacao_cabeca?: number;
  pontuacao_pescoco?: number;
  pontuacao_espalda?: number;
  pontuacao_dorso?: number;
  pontuacao_garupa?: number;
  pontuacao_membros?: number;
  pontuacao_aprumos?: number;
  pontuacao_andamento?: number;
  pontuacao_total?: number;
  observacoes?: string;
  created_at: string;
}

export default function MorfologiaPageModern() {
  const { selectedHorse } = useGeneticsContext();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<MorfologiaRecord | null>(null);
  const [viewingRecord, setViewingRecord] = useState<MorfologiaRecord | null>(null);

  const form = useForm<MorfologiaFormData>({
    resolver: zodResolver(morfologiaSchema),
    defaultValues: {
      data_medicao: new Date().toISOString().split('T')[0],
      responsavel_medicao: '',
      observacoes: '',
    } as MorfologiaFormData,
  });

  // Query para buscar dados de morfologia
  const { data: morfologiaData = [], refetch: refetchMorfologia, isLoading } = useQuery<MorfologiaRecord[]>({
    queryKey: [`/api/morfologia/horse/${selectedHorse?.id}`],
    enabled: !!selectedHorse?.id,
    staleTime: 0,
    gcTime: 0,
  });

  // Mutation para criar registro
  const createMutation = useMutation({
    mutationFn: async (data: MorfologiaFormData) => {
      const response = await fetch('/api/morfologia', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': user?.id?.toString() || '',
        },
        body: JSON.stringify({
          ...data,
          cavalo_id: selectedHorse?.id,
        }),
      });
      if (!response.ok) throw new Error('Falha ao criar registro');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Sucesso",
        description: "Avaliação morfológica cadastrada com sucesso!",
      });
      setIsCreateDialogOpen(false);
      form.reset();
      // Recarregar dados
      setTimeout(() => {
        refetchMorfologia();
      }, 100);
    },
    onError: (error) => {
      console.error('Erro ao criar morfologia:', error);
      toast({
        title: "Erro",
        description: "Falha ao cadastrar avaliação morfológica.",
        variant: "destructive",
      });
    },
  });

  // Mutation para editar registro
  const editMutation = useMutation({
    mutationFn: async (data: MorfologiaFormData) => {
      if (!editingRecord) throw new Error('Nenhum registro para editar');
      
      const response = await fetch(`/api/morfologia/${editingRecord.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'user-id': user?.id?.toString() || '',
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Falha ao editar registro');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Sucesso",
        description: "Avaliação morfológica atualizada com sucesso!",
      });
      setIsEditDialogOpen(false);
      setEditingRecord(null);
      form.reset();
      // Recarregar dados
      setTimeout(() => {
        refetchMorfologia();
      }, 100);
    },
    onError: (error) => {
      console.error('Erro ao editar morfologia:', error);
      toast({
        title: "Erro",
        description: "Falha ao atualizar avaliação morfológica.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (data: MorfologiaFormData) => {
    if (editingRecord) {
      // Modo edição
      editMutation.mutate(data);
    } else {
      // Modo criação
      if (!selectedHorse) {
        toast({
          title: "Erro",
          description: "Selecione um cavalo primeiro.",
          variant: "destructive",
        });
        return;
      }
      createMutation.mutate(data);
    }
  };

  const handleEditRecord = (record: MorfologiaRecord) => {
    setEditingRecord(record);
    // Preencher o formulário com os dados do registro
    form.reset({
      data_medicao: record.data_medicao,
      responsavel_medicao: record.responsavel_medicao || '',
      altura_cernelha: record.altura_cernelha || undefined,
      altura_dorso: record.altura_dorso || undefined,
      altura_garupa: record.altura_garupa || undefined,
      comprimento_corpo: record.comprimento_corpo || undefined,
      comprimento_pescoco: record.comprimento_pescoco || undefined,
      largura_peito: record.largura_peito || undefined,
      perimetro_toracico: record.perimetro_toracico || undefined,
      perimetro_canela: record.perimetro_canela || undefined,
      pontuacao_cabeca: record.pontuacao_cabeca || undefined,
      pontuacao_pescoco: record.pontuacao_pescoco || undefined,
      pontuacao_espalda: record.pontuacao_espalda || undefined,
      pontuacao_dorso: record.pontuacao_dorso || undefined,
      pontuacao_garupa: record.pontuacao_garupa || undefined,
      pontuacao_membros: record.pontuacao_membros || undefined,
      pontuacao_aprumos: record.pontuacao_aprumos || undefined,
      pontuacao_andamento: record.pontuacao_andamento || undefined,
      observacoes: record.observacoes || '',
    });
    setIsEditDialogOpen(true);
  };

  const handleViewRecord = (record: MorfologiaRecord) => {
    setViewingRecord(record);
  };

  const calculatePontuacaoTotal = (record: MorfologiaRecord) => {
    const pontuacoes = [
      record.pontuacao_cabeca,
      record.pontuacao_pescoco,
      record.pontuacao_espalda,
      record.pontuacao_dorso,
      record.pontuacao_garupa,
      record.pontuacao_membros,
      record.pontuacao_aprumos,
      record.pontuacao_andamento,
    ].filter(p => p !== null && p !== undefined) as number[];
    
    return pontuacoes.length > 0 ? pontuacoes.reduce((a, b) => a + b, 0) : 0;
  };

  if (!selectedHorse) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Ruler className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground">
            Selecione um cavalo para visualizar dados morfológicos
          </h3>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Carregando dados morfológicos...</p>
        </div>
      </div>
    );
  }

  // Estatísticas resumidas
  const totalRegistros = morfologiaData?.length || 0;
  const ultimaAvaliacao = morfologiaData?.length > 0 ? 
    morfologiaData.sort((a, b) => new Date(b.data_medicao).getTime() - new Date(a.data_medicao).getTime())[0] : null;
  const mediaPontuacao = morfologiaData?.length > 0 ?
    morfologiaData.reduce((sum, record) => sum + calculatePontuacaoTotal(record), 0) / morfologiaData.length : 0;

  return (
    <div className="space-y-6">
      {/* Header com informações do cavalo */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Morfologia</h2>
          <p className="text-muted-foreground">
            Avaliações morfológicas de {selectedHorse.name}
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Nova Avaliação
        </Button>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total de Avaliações
            </CardTitle>
            <Ruler className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRegistros}</div>
            <p className="text-xs text-muted-foreground">
              registros morfológicos
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Última Avaliação
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {ultimaAvaliacao ? format(new Date(ultimaAvaliacao.data_medicao), 'dd/MM/yyyy', { locale: pt }) : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              data da medição
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pontuação Média
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mediaPontuacao.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              de 80 pontos
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Registros */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Avaliações</CardTitle>
          <CardDescription>
            Registros morfológicos organizados por data
          </CardDescription>
        </CardHeader>
        <CardContent>
          {totalRegistros === 0 ? (
            <div className="text-center py-8">
              <Ruler className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                Nenhuma avaliação encontrada
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Adicione a primeira avaliação morfológica deste cavalo
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Primeira Avaliação
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {morfologiaData.map((record) => (
                <div
                  key={record.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Badge variant="outline">
                        {format(new Date(record.data_medicao), 'dd/MM/yyyy', { locale: pt })}
                      </Badge>
                      {record.responsavel_medicao && (
                        <span className="text-sm text-muted-foreground">
                          por {record.responsavel_medicao}
                        </span>
                      )}
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      {record.altura_cernelha && (
                        <span>Cernelha: {record.altura_cernelha}cm</span>
                      )}
                      {record.perimetro_toracico && (
                        <span>P. Torácico: {record.perimetro_toracico}cm</span>
                      )}
                      <span>Pontuação: {calculatePontuacaoTotal(record)}/80</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditRecord(record)}
                      className="gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Editar
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewRecord(record)}
                      className="gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      Ver Detalhes
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog para Nova Avaliação */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] flex flex-col p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle>Nova Avaliação Morfológica</DialogTitle>
            <DialogDescription>
              Registrar nova avaliação morfológica para {selectedHorse?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-y-auto px-6 py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="data_medicao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data da Medição</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="responsavel_medicao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Responsável</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do responsável" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Medidas Corporais */}
              <div className="space-y-4">
                <h4 className="font-medium">Medidas Corporais (cm)</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="altura_cernelha"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Cernelha</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="altura_dorso"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Dorso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="altura_garupa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Garupa</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="comprimento_corpo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comprimento Corpo</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="perimetro_toracico"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Perímetro Torácico</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="perimetro_canela"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Perímetro Canela</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Pontuações */}
              <div className="space-y-4">
                <h4 className="font-medium">Pontuações (0-10)</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="pontuacao_cabeca"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cabeça</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_pescoco"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pescoço</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_espalda"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Espalda</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_dorso"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dorso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_garupa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Garupa</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_membros"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Membros</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_aprumos"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Aprumos</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_andamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Andamento</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={form.control}
                name="observacoes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Observações sobre a avaliação morfológica..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-3 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  className="flex-1"
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={createMutation.isPending} className="flex-1">
                  {createMutation.isPending ? 'Salvando...' : 'Salvar Avaliação'}
                </Button>
              </div>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog para Visualizar Registro */}
      <Dialog open={!!viewingRecord} onOpenChange={() => setViewingRecord(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalhes da Avaliação Morfológica</DialogTitle>
            <DialogDescription>
              {viewingRecord && format(new Date(viewingRecord.data_medicao), 'dd/MM/yyyy', { locale: pt })}
            </DialogDescription>
          </DialogHeader>

          {viewingRecord && (
            <div className="space-y-6">
              {/* Informações Gerais */}
              <div className="space-y-2">
                <h4 className="font-medium">Informações Gerais</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Data:</span> {format(new Date(viewingRecord.data_medicao), 'dd/MM/yyyy', { locale: pt })}
                  </div>
                  {viewingRecord.responsavel_medicao && (
                    <div>
                      <span className="font-medium">Responsável:</span> {viewingRecord.responsavel_medicao}
                    </div>
                  )}
                </div>
              </div>

              {/* Medidas */}
              <div className="space-y-2">
                <h4 className="font-medium">Medidas Corporais</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  {viewingRecord.altura_cernelha && (
                    <div>Altura Cernelha: {viewingRecord.altura_cernelha} cm</div>
                  )}
                  {viewingRecord.altura_dorso && (
                    <div>Altura Dorso: {viewingRecord.altura_dorso} cm</div>
                  )}
                  {viewingRecord.altura_garupa && (
                    <div>Altura Garupa: {viewingRecord.altura_garupa} cm</div>
                  )}
                  {viewingRecord.comprimento_corpo && (
                    <div>Comprimento Corpo: {viewingRecord.comprimento_corpo} cm</div>
                  )}
                  {viewingRecord.perimetro_toracico && (
                    <div>Perímetro Torácico: {viewingRecord.perimetro_toracico} cm</div>
                  )}
                  {viewingRecord.perimetro_canela && (
                    <div>Perímetro Canela: {viewingRecord.perimetro_canela} cm</div>
                  )}
                </div>
              </div>

              {/* Pontuações */}
              <div className="space-y-2">
                <h4 className="font-medium">Pontuações</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  {viewingRecord.pontuacao_cabeca !== undefined && (
                    <div>Cabeça: {viewingRecord.pontuacao_cabeca}/10</div>
                  )}
                  {viewingRecord.pontuacao_pescoco !== undefined && (
                    <div>Pescoço: {viewingRecord.pontuacao_pescoco}/10</div>
                  )}
                  {viewingRecord.pontuacao_espalda !== undefined && (
                    <div>Espalda: {viewingRecord.pontuacao_espalda}/10</div>
                  )}
                  {viewingRecord.pontuacao_dorso !== undefined && (
                    <div>Dorso: {viewingRecord.pontuacao_dorso}/10</div>
                  )}
                  {viewingRecord.pontuacao_garupa !== undefined && (
                    <div>Garupa: {viewingRecord.pontuacao_garupa}/10</div>
                  )}
                  {viewingRecord.pontuacao_membros !== undefined && (
                    <div>Membros: {viewingRecord.pontuacao_membros}/10</div>
                  )}
                  {viewingRecord.pontuacao_aprumos !== undefined && (
                    <div>Aprumos: {viewingRecord.pontuacao_aprumos}/10</div>
                  )}
                  {viewingRecord.pontuacao_andamento !== undefined && (
                    <div>Andamento: {viewingRecord.pontuacao_andamento}/10</div>
                  )}
                </div>
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <div className="font-medium">
                    Pontuação Total: {calculatePontuacaoTotal(viewingRecord)}/80
                  </div>
                </div>
              </div>

              {/* Observações */}
              {viewingRecord.observacoes && (
                <div className="space-y-2">
                  <h4 className="font-medium">Observações</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">
                    {viewingRecord.observacoes}
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setViewingRecord(null)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog para Editar Avaliação */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl h-[90vh] flex flex-col p-0">
          <div className="p-6 pb-4 border-b flex-shrink-0">
            <DialogHeader>
              <DialogTitle>Editar Avaliação Morfológica</DialogTitle>
              <DialogDescription>
                Editar avaliação morfológica de {editingRecord?.data_medicao ? format(new Date(editingRecord.data_medicao), 'dd/MM/yyyy', { locale: pt }) : ''}
              </DialogDescription>
            </DialogHeader>
          </div>

          <div className="flex-1 overflow-y-auto p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="data_medicao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data da Medição</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="responsavel_medicao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Responsável pela Medição</FormLabel>
                      <FormControl>
                        <Input placeholder="Nome do responsável" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Medidas Corporais */}
              <div className="space-y-4">
                <h4 className="font-medium">Medidas Corporais (cm)</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="altura_cernelha"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Cernelha</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="altura_dorso"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Dorso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="altura_garupa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Altura Garupa</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="comprimento_corpo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comprimento Corpo</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="comprimento_pescoco"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Comprimento Pescoço</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="largura_peito"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Largura Peito</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="perimetro_toracico"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Perímetro Torácico</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="perimetro_canela"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Perímetro Canela</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1" 
                            placeholder="0.0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Pontuações */}
              <div className="space-y-4">
                <h4 className="font-medium">Pontuações (0-10)</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="pontuacao_cabeca"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cabeça</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_pescoco"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Pescoço</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_espalda"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Espalda</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_dorso"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dorso</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_garupa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Garupa</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_membros"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Membros</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_aprumos"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Aprumos</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="pontuacao_andamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Andamento</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="0" 
                            max="10"
                            step="0.1"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={form.control}
                name="observacoes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Observações sobre a avaliação morfológica..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              </form>
            </Form>
          </div>

          <div className="p-6 pt-4 border-t flex-shrink-0">
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingRecord(null);
                  form.reset();
                }}
              >
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={editMutation.isPending}
                onClick={form.handleSubmit(handleSubmit)}
              >
                {editMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}