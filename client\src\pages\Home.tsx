import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { 
  Loader2, 
  Eye, 
  EyeOff, 
  Activity, 
  Heart, 
  BarChart3, 
  Users, 
  Shield, 
  Smartphone, 
  Zap,
  CheckCircle,
  ArrowRight,
  Play,
  Star,
  Calendar,
  PawPrint,
  ChevronLeft,
  ChevronRight,
  Award,
  TrendingUp,
  FileText
} from "lucide-react";

export default function Home() {
  // Hero slider state
  const [currentSlide, setCurrentSlide] = useState(0);
  
  // Login form state
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Registration form state
  const [registerForm, setRegisterForm] = useState({
    name: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: ""
  });
  const [isRegistering, setIsRegistering] = useState(false);
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [, setLocation] = useLocation();
  const { login } = useAuth();
  const { toast } = useToast();

  // Hero slides data
  const heroSlides = [
    {
      image: "https://images.unsplash.com/photo-1553284965-83fd3e82fa5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80",
      title: "Gestão Profissional",
      subtitle: "Controle total do seu plantel com tecnologia de ponta"
    },
  ];

  // Auto-advance slides
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    if (!username || !password) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos",
        variant: "destructive",
      });
      return;
    }
    setIsLoading(true);
    try {
      await login(username, password);
      toast({
        title: "Sucesso",
        description: "Login realizado com sucesso!",
      });
      setLocation("/dashboard");
    } catch (error) {
      console.error("Erro no login:", error);
      toast({
        title: "Erro no login",
        description: "Credenciais inválidas. Verifique seu nome de usuário e senha.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegisterSubmit = async (e) => {
    e.preventDefault();
    if (!registerForm.name || !registerForm.username || !registerForm.email || !registerForm.password) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios",
        variant: "destructive",
      });
      return;
    }
    if (registerForm.password !== registerForm.confirmPassword) {
      toast({
        title: "Erro",
        description: "As senhas não coincidem",
        variant: "destructive",
      });
      return;
    }
    if (registerForm.password.length < 6) {
      toast({
        title: "Erro",
        description: "A senha deve ter pelo menos 6 caracteres",
        variant: "destructive",
      });
      return;
    }
    setIsRegistering(true);
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: registerForm.name,
          username: registerForm.username,
          email: registerForm.email,
          password: registerForm.password
        }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao criar conta');
      }
      toast({
        title: "Sucesso",
        description: "Conta criada com sucesso! Redirecionando para login...",
      });
      setRegisterForm({
        name: "",
        username: "",
        email: "",
        password: "",
        confirmPassword: ""
      });
      setTimeout(() => {
        const loginTab = document.querySelector('[data-value="login"]');
        if (loginTab) {
          loginTab.click();
          setTimeout(() => {
            const usernameInput = document.querySelector('input[name="username"]');
            if (usernameInput) {
              usernameInput.focus();
            }
          }, 100);
        }
      }, 2000);
    } catch (error) {
      console.error("Erro no cadastro:", error);
      toast({
        title: "Erro no cadastro",
        description: error.message || "Erro ao criar conta. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const handleRegisterInputChange = (field, value) => {
    setRegisterForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const scrollToAuth = () => {
    document.getElementById('auth-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const features = [
    {
      icon: <PawPrint className="h-8 w-8" />,
      title: "Gestão de Plantel",
      description: "Cadastro completo de cavalos com genealogia, características físicas e histórico reprodutivo."
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: "Saúde Veterinária",
      description: "Controle de vacinas, medicamentos, exames e procedimentos veterinários."
    },
    {
      icon: <Activity className="h-8 w-8" />,
      title: "Manejo Inteligente",
      description: "Agendamento e controle de atividades como doma, ferração e cuidados diários."
    },
    {
      icon: <BarChart3 className="h-8 w-8" />,
      title: "Análise Genética",
      description: "Análise de pedigree, coeficiente de consanguinidade e sugestões de cruzamento."
    },
    {
      icon: <TrendingUp className="h-8 w-8" />,
      title: "Relatórios Avançados",
      description: "Dashboards e relatórios detalhados para análise de performance do plantel."
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Segurança Total",
      description: "Sistema seguro com backup automático e controle de acesso por usuário."
    }
  ];

  const stats = [
    { number: "200+", label: "Criadores Ativos" },
    { number: "8.000+", label: "Cavalos Registrados" },
    { number: "15.000+", label: "Procedimentos Realizados" },
    { number: "99.9%", label: "Uptime Garantido" }
  ];

  const benefits = [
    "Redução de 80% no tempo de gestão do plantel",
    "Controle completo da saúde dos animais",
    "Decisões baseadas em dados genéticos",
    "Alertas automáticos para procedimentos",
    "Relatórios detalhados de performance",
    "Backup automático na nuvem"
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm z-50 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="overflow-hidden">
                <img 
                  src="https://images.unsplash.com/photo-1553284965-83fd3e82fa5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=64&q=80"
                  alt="Logo RS Horse"
                  className="h-8 w-8 object-cover rounded-xl"
                />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                RS HORSE
              </span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-600 hover:text-blue-600 font-medium">Recursos</a>
              <a href="#about" className="text-gray-600 hover:text-blue-600 font-medium">Sobre</a>
              <a href="#auth-section" className="text-gray-600 hover:text-blue-600 font-medium">Login</a>
            </div>
            <Button 
              onClick={scrollToAuth}
              className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold"
            >
              Começar Agora
            </Button>
          </div>
        </div>
      </nav>
      {/* Hero Slider */}
      <section className="relative h-screen overflow-hidden">
        <div className="absolute inset-0">
          {heroSlides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/40"></div>
            </div>
          ))}
        </div>
        
        {/* Hero Content */}
        <div className="relative z-10 h-full flex items-center justify-center">
          <div className="text-center text-white px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <div className="inline-flex items-center space-x-4 p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
                <div className="overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1553284965-83fd3e82fa5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=128&q=80"
                    alt="Cavalo no campo"
                    className="h-16 w-16 object-cover rounded-2xl"
                  />
                </div>
                <div className="text-left">
                  <h1 className="text-3xl font-bold text-white">
                    RS HORSE
                  </h1>
                  <p className="text-sm text-white/90 font-medium">
                    Sistema Profissional de Gestão Equina
                  </p>
                </div>
              </div>
            </div>
            
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              {heroSlides[currentSlide].title}
            </h2>
            
            <p className="text-xl sm:text-2xl mb-12 max-w-3xl mx-auto leading-relaxed text-white/90">
              {heroSlides[currentSlide].subtitle}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                onClick={scrollToAuth}
                size="lg" 
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-10 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Começar Agora
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="px-10 py-4 text-lg border-2 border-white/30 hover:bg-white/10 text-white rounded-full font-semibold backdrop-blur-sm"
              >
                <Play className="mr-2 h-5 w-5" />
                Ver Demonstração
              </Button>
            </div>
          </div>
        </div>

        {/* Slider Controls */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full transition-all duration-300"
        >
          <ChevronLeft className="h-6 w-6 text-white" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full transition-all duration-300"
        >
          <ChevronRight className="h-6 w-6 text-white" />
        </button>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide 
                  ? 'bg-white shadow-lg' 
                  : 'bg-white/50 hover:bg-white/70'
              }`}
            />
          ))}
        </div>
      </section>
      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Recursos Profissionais
            </h3>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Ferramentas completas para gestão moderna de haras e criações
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-xl hover:shadow-2xl transition-all duration-500 bg-white dark:bg-gray-800 hover:scale-105 group">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="p-4 bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900 dark:to-green-900 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      <div className="text-blue-600 dark:text-blue-400">
                        {feature.icon}
                      </div>
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    {feature.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* About Section */}
      <section id="about" className="py-20 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Por que escolher o RS HORSE?
              </h3>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Desenvolvido por especialistas em equinocultura e tecnologia, 
                oferece controle total e profissional para criadores sérios.
              </p>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-500 flex-shrink-0" />
                    {benefit}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <img 
                  src="https://images.unsplash.com/photo-1553284965-83fd3e82fa5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Cavalos no campo"
                  className="rounded-lg shadow-lg"
                />
                <img 
                  src="https://images.unsplash.com/photo-1574270275652-ac7c13ca
