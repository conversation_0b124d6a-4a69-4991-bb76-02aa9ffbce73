import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { GitBranchPlus, Heart, User, TreePine, Edit } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import { Cavalo } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { extractCleanName } from '@/utils/genealogy';

interface ExpandedGenealogyProps {
  cavaloId: number;
  editable?: boolean;
}

interface HorseInfo {
  id: number | null;
  nome: string;
  tipo: 'sistema' | 'externo' | 'nenhum' | 'link';
  sexo?: string | null;
  breed?: string | null;
  cor?: string | null;
}

export function ExpandedGenealogy({ cavaloId, editable = false }: ExpandedGenealogyProps) {
  const [cavalo, setCavalo] = useState<Cavalo | null>(null);
  const [pai, setPai] = useState<Cavalo | null>(null);
  const [mae, setMae] = useState<Cavalo | null>(null);
  const [avo_paterno_data, setAvo_paterno_data] = useState<Cavalo | null>(null);
  const [avo_materno_data, setAvo_materno_data] = useState<Cavalo | null>(null);
  const [avoPaternaData, setAvoPaternaData] = useState<Cavalo | null>(null);
  const [avoMaternaData, setAvoMaternaData] = useState<Cavalo | null>(null);
  const [genealogiaData, setGenealogiaData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  const handleEditClick = (tipo: string) => {
    if (tipo === 'genealogia') {
      setLocation(`/cavalo/${cavaloId}/genealogia/editar`);
    } else {
      toast({
        title: "Funcionalidade em desenvolvimento",
        description: `A funcionalidade de ${tipo} será implementada em breve.`,
        duration: 4000,
      });
    }
  };

  // Função para obter informações do pai
  const getPaiInfo = (cavalo: Cavalo | null): HorseInfo => {
    if (!cavalo) return { id: null, nome: 'Não informado', tipo: 'nenhum' };
    
    // Usar pai_id do cavalos diretamente
    if (cavalo.pai_id && pai) {
      return { 
        id: cavalo.pai_id,
        nome: pai.name, 
        tipo: 'sistema',
        sexo: pai.sexo,
        breed: pai.breed,
        cor: pai.cor
      };
    }
    
    // Se tem dados de genealogia e nome do pai
    if (genealogiaData?.pai) {
      return { 
        id: null,
        nome: extractCleanName(genealogiaData.pai), 
        tipo: 'externo'
      };
    }
    
    return { id: null, nome: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações da mãe
  const getMaeInfo = (cavalo: Cavalo | null): HorseInfo => {
    if (!cavalo) return { id: null, nome: 'Não informado', tipo: 'nenhum' };
    
    // Usar mae_id do cavalos diretamente
    if (cavalo.mae_id && mae) {
      return { 
        id: cavalo.mae_id,
        nome: mae.name, 
        tipo: 'sistema',
        sexo: mae.sexo,
        breed: mae.breed,
        cor: mae.cor
      };
    }
    
    // Se tem dados de genealogia e nome da mãe
    if (genealogiaData?.mae) {
      return { 
        id: null,
        nome: extractCleanName(genealogiaData.mae), 
        tipo: 'externo'
      };
    }
    
    return { id: null, nome: 'Não informado', tipo: 'nenhum' };
  };

  // Função para obter informações dos avós
  const getAvoInfo = (tipo: 'paterno' | 'materno' | 'paterna' | 'materna'): HorseInfo => {
    // Se não tem dados de genealogia, tentar buscar pelos pais diretos
    if (!genealogiaData) {
      return { id: null, nome: 'Dados não disponíveis', tipo: 'nenhum' };
    }

    switch (tipo) {
      case 'paterno':
        // Primeiro: verificar se tem ID e dados do sistema
        if (genealogiaData.avo_paterno_id && avo_paterno_data) {
          return {
            id: genealogiaData.avo_paterno_id,
            nome: avo_paterno_data.name,
            tipo: 'sistema',
            sexo: avo_paterno_data.sexo,
            breed: avo_paterno_data.breed,
            cor: avo_paterno_data.cor
          };
        }
        // Segundo: verificar se tem nome externo
        if (genealogiaData.avo_paterno) {
          return {
            id: null,
            nome: extractCleanName(genealogiaData.avo_paterno),
            tipo: 'externo'
          };
        }
        // Terceiro: tentar buscar pelo pai do pai (se pai existe no sistema)
        if (pai && pai.pai_id) {
          return {
            id: null,
            nome: 'Ver pai do pai',
            tipo: 'link'
          };
        }
        break;
        
      case 'materno':
        if (genealogiaData.avo_materno_id && avo_materno_data) {
          return {
            id: genealogiaData.avo_materno_id,
            nome: avo_materno_data.name,
            tipo: 'sistema',
            sexo: avo_materno_data.sexo,
            breed: avo_materno_data.breed,
            cor: avo_materno_data.cor
          };
        }
        if (genealogiaData.avo_materno) {
          return {
            id: null,
            nome: extractCleanName(genealogiaData.avo_materno),
            tipo: 'externo'
          };
        }
        // Tentar buscar pelo pai da mãe
        if (mae && mae.pai_id) {
          return {
            id: null,
            nome: 'Ver pai da mãe',
            tipo: 'link'
          };
        }
        break;
        
      case 'paterna':
        if (genealogiaData.avo_paterna_id && avoPaternaData) {
          return {
            id: genealogiaData.avo_paterna_id,
            nome: avoPaternaData.name,
            tipo: 'sistema',
            sexo: avoPaternaData.sexo,
            breed: avoPaternaData.breed,
            cor: avoPaternaData.cor
          };
        }
        if (genealogiaData.avo_paterna) {
          return {
            id: null,
            nome: extractCleanName(genealogiaData.avo_paterna),
            tipo: 'externo'
          };
        }
        // Tentar buscar pela mãe do pai
        if (pai && pai.mae_id) {
          return {
            id: null,
            nome: 'Ver mãe do pai',
            tipo: 'link'
          };
        }
        break;
        
      case 'materna':
        if (genealogiaData.avo_materna_id && avoMaternaData) {
          return {
            id: genealogiaData.avo_materna_id,
            nome: avoMaternaData.name,
            tipo: 'sistema',
            sexo: avoMaternaData.sexo,
            breed: avoMaternaData.breed,
            cor: avoMaternaData.cor
          };
        }
        if (genealogiaData.avo_materna) {
          return {
            id: null,
            nome: extractCleanName(genealogiaData.avo_materna),
            tipo: 'externo'
          };
        }
        // Tentar buscar pela mãe da mãe
        if (mae && mae.mae_id) {
          return {
            id: null,
            nome: 'Ver mãe da mãe',
            tipo: 'link'
          };
        }
        break;
    }

    return { id: null, nome: 'Não informado', tipo: 'nenhum' };
  };

  // Carregar dados
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const userJson = localStorage.getItem('user');
        const user_id = userJson ? JSON.parse(userJson).id : null;
        
        if (!user_id) {
          setLoading(false);
          return;
        }
        
        const options = {
          headers: {
            'user-id': user_id.toString(),
            'Content-Type': 'application/json'
          }
        };
        
        // Buscar cavalo principal
        const cavaloData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloId}`, 'GET', undefined, options);
        setCavalo(cavaloData);
        
        // Buscar dados de genealogia
        try {
          const genealogiaResponse = await apiRequest<any>(`/api/cavalos/${cavaloId}/genealogia`, 'GET', undefined, options);
          console.log("Dados de genealogia recebidos:", genealogiaResponse);
          
          // Os dados vêm dentro de genealogiaResponse.genealogia
          const genealogia = genealogiaResponse.genealogia || genealogiaResponse;
          setGenealogiaData(genealogia);
          
          // Buscar avós se existirem IDs
          if (genealogia.avo_paterno_id) {
            try {
              const avo_paterno = await apiRequest<Cavalo>(`/api/cavalos/${genealogia.avo_paterno_id}`, 'GET', undefined, options);
              setAvo_paterno_data(avo_paterno);
              console.log("Avô paterno carregado:", avo_paterno);
            } catch (error) {
              console.log('Avô paterno não encontrado:', error);
            }
          }
          
          if (genealogia.avo_materno_id) {
            try {
              const avo_materno = await apiRequest<Cavalo>(`/api/cavalos/${genealogia.avo_materno_id}`, 'GET', undefined, options);
              setAvo_materno_data(avo_materno);
              console.log("Avô materno carregado:", avo_materno);
            } catch (error) {
              console.log('Avô materno não encontrado:', error);
            }
          }
          
          if (genealogia.avo_paterna_id) {
            try {
              const avoPaterna = await apiRequest<Cavalo>(`/api/cavalos/${genealogia.avo_paterna_id}`, 'GET', undefined, options);
              setAvoPaternaData(avoPaterna);
              console.log("Avó paterna carregada:", avoPaterna);
            } catch (error) {
              console.log('Avó paterna não encontrada:', error);
            }
          }
          
          if (genealogia.avo_materna_id) {
            try {
              const avoMaterna = await apiRequest<Cavalo>(`/api/cavalos/${genealogia.avo_materna_id}`, 'GET', undefined, options);
              setAvoMaternaData(avoMaterna);
              console.log("Avó materna carregada:", avoMaterna);
            } catch (error) {
              console.log('Avó materna não encontrada:', error);
            }
          }
          
        } catch (error) {
          console.log('Genealogia não encontrada para este cavalo');
        }
        
        // Buscar pai e mãe se existirem IDs
        if (cavaloData.pai_id) {
          try {
            const paiData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloData.pai_id}`, 'GET', undefined, options);
            setPai(paiData);
          } catch (error) {
            console.log('Pai não encontrado');
          }
        }
        
        if (cavaloData.mae_id) {
          try {
            const maeData = await apiRequest<Cavalo>(`/api/cavalos/${cavaloData.mae_id}`, 'GET', undefined, options);
            setMae(maeData);
          } catch (error) {
            console.log('Mãe não encontrada');
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, [cavaloId]);

  // Obter dados dos ancestrais
  const paiInfo = getPaiInfo(cavalo);
  const maeInfo = getMaeInfo(cavalo);
  const avo_paterno = getAvoInfo('paterno');
  const avo_materno = getAvoInfo('materno');
  const avoPaterna = getAvoInfo('paterna');
  const avoMaterna = getAvoInfo('materna');

  // Debug dos dados de genealogia
  console.log("ExpandedGenealogy Debug:", {
    cavaloId,
    genealogiaData,
    avo_paterno,
    avo_materno,
    avoPaterna,
    avoMaterna,
    avo_paterno_data,
    avo_materno_data,
    avoPaternaData,
    avoMaternaData
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header com ações */}
      {editable && (
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <TreePine className="h-6 w-6" />
            Árvore Genealógica Expandida
          </h2>
          <Button
            onClick={() => handleEditClick('genealogia')}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Editar Genealogia
          </Button>
        </div>
      )}

      {/* Árvore Genealógica - Layout Simplificado */}
      <div className="space-y-8">
        {/* Linha dos Avós */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Avô Paterno */}
          <Card className="border-blue-300">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-xs text-gray-500 mb-2">Avô Paterno</div>
                <User className="h-5 w-5 mx-auto mb-2 text-blue-600" />
                <div className="text-sm font-medium mb-2">
                  {avo_paterno.nome}
                </div>
                <Badge variant={avo_paterno.tipo === 'sistema' ? 'default' : 'outline'} className="text-xs">
                  ♂ {avo_paterno.tipo === 'sistema' ? 'Plantel' : avo_paterno.tipo === 'externo' ? 'Externo' : avo_paterno.tipo === 'link' ? 'Navegar' : 'Não informado'}
                </Badge>
                {avo_paterno.cor && (
                  <div className="text-xs text-gray-500 mt-1">{avo_paterno.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Avó Paterna */}
          <Card className="border-pink-300">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-xs text-gray-500 mb-2">Avó Paterna</div>
                <User className="h-5 w-5 mx-auto mb-2 text-pink-600" />
                <div className="text-sm font-medium mb-2">
                  {avoPaterna.nome}
                </div>
                <Badge variant={avoPaterna.tipo === 'sistema' ? 'default' : 'outline'} className="text-xs">
                  ♀ {avoPaterna.tipo === 'sistema' ? 'Sistema' : avoPaterna.tipo === 'externo' ? 'Externo' : avoPaterna.tipo === 'link' ? 'Navegar' : 'Não informado'}
                </Badge>
                {avoPaterna.cor && (
                  <div className="text-xs text-gray-500 mt-1">{avoPaterna.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Avô Materno */}
          <Card className="border-blue-300">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-xs text-gray-500 mb-2">Avô Materno</div>
                <User className="h-5 w-5 mx-auto mb-2 text-blue-600" />
                <div className="text-sm font-medium mb-2">
                  {avo_materno.nome}
                </div>
                <Badge variant={avo_materno.tipo === 'sistema' ? 'default' : 'outline'} className="text-xs">
                  ♂ {avo_materno.tipo === 'sistema' ? 'Sistema' : avo_materno.tipo === 'externo' ? 'Externo' : avo_materno.tipo === 'link' ? 'Navegar' : 'Não informado'}
                </Badge>
                {avo_materno.cor && (
                  <div className="text-xs text-gray-500 mt-1">{avo_materno.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Avó Materna */}
          <Card className="border-pink-300">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="text-xs text-gray-500 mb-2">Avó Materna</div>
                <User className="h-5 w-5 mx-auto mb-2 text-pink-600" />
                <div className="text-sm font-medium mb-2">
                  {avoMaterna.nome}
                </div>
                <Badge variant={avoMaterna.tipo === 'sistema' ? 'default' : 'outline'} className="text-xs">
                  ♀ {avoMaterna.tipo === 'sistema' ? 'Sistema' : 'Externo'}
                </Badge>
                {avoMaterna.cor && (
                  <div className="text-xs text-gray-500 mt-1">{avoMaterna.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Linhas conectoras */}
        <div className="flex justify-center">
          <div className="w-3/4 border-t-2 border-gray-300"></div>
        </div>

        {/* Linha dos Pais */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Pai */}
          <Card className="border-blue-400">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-sm text-gray-500 mb-2">Pai</div>
                <User className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <div className="text-lg font-semibold mb-2">
                  {paiInfo.nome}
                </div>
                <Badge variant={paiInfo.tipo === 'sistema' ? 'default' : 'outline'} className="text-sm">
                  ♂ {paiInfo.tipo === 'sistema' ? 'Sistema' : 'Externo'}
                </Badge>
                {paiInfo.cor && (
                  <div className="text-sm text-gray-600 mt-2">{paiInfo.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Mãe */}
          <Card className="border-pink-400">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-sm text-gray-500 mb-2">Mãe</div>
                <User className="h-8 w-8 mx-auto mb-3 text-pink-500" />
                <div className="text-lg font-semibold mb-2">
                  {maeInfo.nome}
                </div>
                <Badge variant={maeInfo.tipo === 'sistema' ? 'default' : 'outline'} className="text-sm">
                  ♀ {maeInfo.tipo === 'sistema' ? 'Sistema' : 'Externo'}
                </Badge>
                {maeInfo.cor && (
                  <div className="text-sm text-gray-600 mt-2">{maeInfo.cor}</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Linha conectora */}
        <div className="flex justify-center">
          <div className="w-1/2 border-t-2 border-gray-300"></div>
        </div>

        {/* Animal Principal */}
        <div className="flex justify-center">
          <div className="w-full max-w-md">
            <Card className="border-yellow-400 bg-yellow-50">
              <CardContent className="p-8">
                <div className="text-center">
                  <div className="text-sm text-gray-600 mb-2">Animal Principal</div>
                  <Heart className="h-10 w-10 mx-auto mb-4 text-yellow-600" />
                  <div className="text-xl font-bold mb-3">
                    {cavalo?.name}
                  </div>
                  <Badge variant="default" className="text-sm bg-yellow-600 mb-2">
                    {cavalo?.sexo === 'Macho' || cavalo?.sexo === 'Garanhão' || cavalo?.sexo === 'Macho (Castrado)' ? '♂' : '♀'} {cavalo?.sexo}
                  </Badge>
                  {cavalo?.cor && (
                    <div className="text-sm text-gray-700 font-medium">{cavalo.cor}</div>
                  )}
                  {cavalo?.breed && (
                    <div className="text-sm text-gray-600 mt-1">{cavalo.breed}</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Informações Adicionais */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <GitBranchPlus className="h-4 w-4" />
              Estatísticas da Genealogia
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Animais no Sistema:</span>
                <span className="font-medium">
                  {[paiInfo, maeInfo, avo_paterno, avo_materno, avoPaterna, avoMaterna]
                    .filter(info => info.tipo === 'sistema').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Animais Externos:</span>
                <span className="font-medium">
                  {[paiInfo, maeInfo, avo_paterno, avo_materno, avoPaterna, avoMaterna]
                    .filter(info => info.tipo === 'externo').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Completude dos Dados:</span>
                <span className="font-medium">
                  {Math.round(([paiInfo, maeInfo, avo_paterno, avo_materno, avoPaterna, avoMaterna]
                    .filter(info => info.tipo !== 'nenhum').length / 6) * 100)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Informações do Animal
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Nome:</span>
                <span className="font-medium">{cavalo?.name || 'Não informado'}</span>
              </div>
              <div className="flex justify-between">
                <span>Sexo:</span>
                <span className="font-medium">{cavalo?.sexo || 'Não informado'}</span>
              </div>
              <div className="flex justify-between">
                <span>Raça:</span>
                <span className="font-medium">{cavalo?.breed || 'Não informado'}</span>
              </div>
              <div className="flex justify-between">
                <span>Cor:</span>
                <span className="font-medium">{cavalo?.cor || 'Não informado'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}