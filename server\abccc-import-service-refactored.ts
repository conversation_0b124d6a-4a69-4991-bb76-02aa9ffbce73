/**
 * Serviço de Importação ABCCC Refatorado
 * Implementa o novo sistema de pelagens com slug normalizado
 * Substitui lógicas manuais por resolvePelagemId()
 */

import path from "path";
import * as fs from "fs";
import { db } from "./db";
import { cavalos, genealogia, pelagens } from "../shared/schema";
import { eq, sql, ilike, or, and } from "drizzle-orm";
import {
  CavaloInfo,
  GenealogiaInfo,
  processarPdfABCCC,
} from "./abccc-pdf-parser";
import { processarPdfComOpenAI } from "./openai-pdf-service";
import {
  ABCCCScrapedData,
  getCriouloDataByRegistro,
} from "./abccc-scraper-service";
import { Cavalo, InsertCavalo } from "../shared/schema";
import { getModuleLogger } from "./logger";
import { updateGenealogySync, updateParentRelationshipSync } from "./genealogy-sync-service";
import { resolvePelagemId } from "./pelagem-resolver-service";

const logger = getModuleLogger("abccc-import");

/**
 * Cria novo cavalo com pelagem resolvida automaticamente
 */
async function criarNovoCavalo(
  info: CavaloInfo,
  user_id: number,
): Promise<Cavalo> {
  try {
    // Resolver pelagem usando o novo sistema de slug normalizado
    let pelagemId = null;

    if (info.pelagem) {
      try {
        logger.debug(`Resolvendo pelagem: "${info.pelagem}"`);
        pelagemId = await resolvePelagemId(info.pelagem, 'ABCCC Import');
        logger.info(`Pelagem resolvida: ${info.pelagem} (ID=${pelagemId})`);
      } catch (error) {
        logger.error(`Erro ao resolver pelagem "${info.pelagem}":`, error.message);
        // Continue sem pelagem_id, usando cor como fallback
      }
    }

    // Preparar dados do cavalo sem definir cor diretamente (conforme regra ESLint)
    const dadosCavalo: any = {
      name: info.nome || "Cavalo Importado",
      breed: info.raca || "Crioulo",
      birth_date: info.dataNascimento ? new Date(info.dataNascimento) : null,
      sexo: info.sexo || null,
      status: "ativo",
      user_id: user_id,
      numero_registro: info.numero_registro || null,
      criador: info.criador || null,
      proprietario: info.proprietario || null,
      inspetor: info.inspetor || null,
      is_external: false,
      pelagem_id: pelagemId,
    };

    // Usar campo 'cor' apenas como fallback legível quando não há pelagem_id
    if (!pelagemId && info.pelagem) {
      dadosCavalo.cor = info.pelagem;
    }

    logger.info(`Criando novo cavalo: ${info.nome} (Registro: ${info.numero_registro})`);
    
    const novoCavalo = await db
      .insert(cavalos)
      .values(dadosCavalo)
      .returning();

    if (novoCavalo && novoCavalo.length > 0) {
      logger.info(`Cavalo criado com sucesso: ID=${novoCavalo[0].id}`);
      return novoCavalo[0];
    } else {
      throw new Error("Falha ao criar cavalo: nenhum registro retornado");
    }
  } catch (error) {
    logger.error("Erro ao criar novo cavalo:", error);
    throw new Error(`Erro ao criar cavalo: ${error.message}`);
  }
}

/**
 * Atualiza cavalo existente com pelagem resolvida automaticamente
 */
async function atualizarCavaloExistente(
  id: number,
  info: CavaloInfo,
  user_id: number,
): Promise<Cavalo> {
  try {
    // Resolver pelagem usando o novo sistema de slug normalizado
    let pelagemId = null;

    if (info.pelagem) {
      try {
        logger.debug(`Resolvendo pelagem: "${info.pelagem}"`);
        pelagemId = await resolvePelagemId(info.pelagem, 'ABCCC Import');
        logger.info(`Pelagem resolvida: ${info.pelagem} (ID=${pelagemId})`);
      } catch (error) {
        logger.error(`Erro ao resolver pelagem "${info.pelagem}":`, error.message);
        // Continue sem pelagem_id, usando cor como fallback
      }
    }

    // Preparar dados de atualização
    const dadosAtualizacao: any = {};

    if (info.nome) dadosAtualizacao.name = info.nome;
    if (info.raca) dadosAtualizacao.breed = info.raca;
    if (info.dataNascimento) dadosAtualizacao.birth_date = new Date(info.dataNascimento);
    if (info.sexo) dadosAtualizacao.sexo = info.sexo;
    if (info.numero_registro) dadosAtualizacao.numero_registro = info.numero_registro;
    if (info.criador) dadosAtualizacao.criador = info.criador;
    if (info.proprietario) dadosAtualizacao.proprietario = info.proprietario;
    if (info.inspetor) dadosAtualizacao.inspetor = info.inspetor;
    
    // Sempre salvar pelagem_id se resolvida
    if (pelagemId) {
      dadosAtualizacao.pelagem_id = pelagemId;
    }

    // Usar campo 'cor' apenas como fallback legível quando não há pelagem_id
    if (!pelagemId && info.pelagem) {
      dadosAtualizacao.cor = info.pelagem;
    }

    // Validação para prevenir update vazio
    if (Object.keys(dadosAtualizacao).length === 0) {
      logger.warn(`Nenhum campo para atualizar no cavalo ID=${id}`);
      // Buscar cavalo atual para retorno
      const cavaloAtual = await db
        .select()
        .from(cavalos)
        .where(eq(cavalos.id, id))
        .limit(1);
      
      if (cavaloAtual && cavaloAtual.length > 0) {
        return cavaloAtual[0];
      } else {
        throw new Error(`Cavalo com ID=${id} não encontrado`);
      }
    }

    logger.info(`Atualizando cavalo ID=${id} com ${Object.keys(dadosAtualizacao).length} campos`);
    
    const cavaloAtualizado = await db
      .update(cavalos)
      .set(dadosAtualizacao)
      .where(eq(cavalos.id, id))
      .returning();

    if (cavaloAtualizado && cavaloAtualizado.length > 0) {
      logger.info(`Cavalo atualizado com sucesso: ID=${id}`);
      return cavaloAtualizado[0];
    } else {
      throw new Error(`Falha ao atualizar cavalo: ID=${id} não encontrado`);
    }
  } catch (error) {
    logger.error(`Erro ao atualizar cavalo ID=${id}:`, error);
    throw new Error(`Erro ao atualizar cavalo: ${error.message}`);
  }
}

// Exportar as funções refatoradas
export {
  criarNovoCavalo,
  atualizarCavaloExistente,
};