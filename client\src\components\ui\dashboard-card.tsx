import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

/**
 * DashboardCard
 * 
 * Componente de card especializado para dashboards com responsividade aprimorada.
 * Otimizado para visualização de dados estatísticos e métricas.
 */

const DashboardCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      ref={ref}
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        "hover:shadow-md transition-all overflow-hidden",
        isMobile ? "p-4" : "p-6",
        className
      )}
      {...props}
    />
  );
});

DashboardCard.displayName = "DashboardCard";

const DashboardCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col space-y-1.5",
        isMobile ? "pb-3" : "pb-4",
        className
      )}
      {...props}
    />
  );
});

DashboardCardHeader.displayName = "DashboardCardHeader";

const DashboardCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <h3
      ref={ref}
      className={cn(
        "font-semibold leading-none tracking-tight",
        isMobile ? "text-lg" : "text-xl",
        className
      )}
      {...props}
    />
  );
});

DashboardCardTitle.displayName = "DashboardCardTitle";

const DashboardCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <p
      ref={ref}
      className={cn(
        "text-muted-foreground",
        isMobile ? "text-xs" : "text-sm",
        className
      )}
      {...props}
    />
  );
});

DashboardCardDescription.displayName = "DashboardCardDescription";

const DashboardCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      ref={ref}
      className={cn(
        isMobile ? "pt-2" : "pt-0",
        className
      )}
      {...props}
    />
  );
});

DashboardCardContent.displayName = "DashboardCardContent";

const DashboardCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center",
        isMobile ? "pt-3 flex-wrap gap-2" : "pt-4",
        className
      )}
      {...props}
    />
  );
});

DashboardCardFooter.displayName = "DashboardCardFooter";

// Componente especializado para exibir métricas
const DashboardCardMetric = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value: string | number;
    label: string;
    trend?: "up" | "down" | "neutral";
    trendValue?: string | number;
    icon?: React.ReactNode;
  }
>(({ className, value, label, trend, trendValue, icon, ...props }, ref) => {
  const isMobile = useIsMobile();
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col",
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-2">
        {icon && (
          <div className={cn(
            "rounded-full p-2 bg-primary/10",
            isMobile ? "w-8 h-8" : "w-10 h-10",
            "flex items-center justify-center"
          )}>
            {icon}
          </div>
        )}
        <div>
          <div className={cn(
            "font-bold",
            isMobile ? "text-xl" : "text-2xl"
          )}>
            {value}
          </div>
          <div className={cn(
            "text-muted-foreground",
            isMobile ? "text-xs" : "text-sm"
          )}>
            {label}
          </div>
        </div>
      </div>
      {trend && trendValue && (
        <div className={cn(
          "flex items-center mt-2",
          trend === "up" && "text-green-600",
          trend === "down" && "text-red-600",
          trend === "neutral" && "text-gray-600"
        )}>
          {trend === "up" && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586l3.293-3.293A1 1 0 0112 7z" clipRule="evenodd" />
            </svg>
          )}
          {trend === "down" && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414l3.293 3.293A1 1 0 0012 13z" clipRule="evenodd" />
            </svg>
          )}
          {trend === "neutral" && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a1 1 0 01-1 1H3a1 1 0 110-2h14a1 1 0 011 1z" clipRule="evenodd" />
            </svg>
          )}
          <span className={isMobile ? "text-xs" : "text-sm"}>
            {trendValue}
          </span>
        </div>
      )}
    </div>
  );
});

DashboardCardMetric.displayName = "DashboardCardMetric";

export {
  DashboardCard,
  DashboardCardHeader,
  DashboardCardFooter,
  DashboardCardTitle,
  DashboardCardDescription,
  DashboardCardContent,
  DashboardCardMetric,
};