import { pgTable, text, serial, integer, timestamp, real, date, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { horses } from './cavalos';
import { 
  nomeSchema,
  observacoesSchema,
  optionalDateSchema,
  UserContext,
  AuditFields 
} from './core';

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const morfologia = pgTable("morfologia", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  
  // Data da medição
  dataMedicao: date("data_medicao").notNull(),
  responsavelMedicao: text("responsavel_medicao"),
  
  // Medidas corporais (cm)
  alturaCernelha: real("altura_cernelha"),
  alturaDorso: real("altura_dorso"),
  alturaGarupa: real("altura_garupa"),
  comprimentoCorpo: real("comprimento_corpo"),
  comprimentoPescoco: real("comprimento_pescoco"),
  larguraPeito: real("largura_peito"),
  perimetroPeito: real("perimetro_peito"),
  perimetroCanela: real("perimetro_canela"),
  
  // Pontuações morfológicas (1-10)
  pontuacaoCabeca: real("pontuacao_cabeca"),
  pontuacaoPescoco: real("pontuacao_pescoco"),
  pontuacaoEspadua: real("pontuacao_espadua"),
  pontuacaoTronco: real("pontuacao_tronco"),
  pontuacaoGarupa: real("pontuacao_garupa"),
  pontuacaoMembrosDianteiros: real("pontuacao_membros_dianteiros"),
  pontuacaoMembrosPosteriores: real("pontuacao_membros_posteriores"),
  pontuacaoAprumos: real("pontuacao_aprumos"),
  pontuacaoMovimentacao: real("pontuacao_movimentacao"),
  pontuacaoGeral: real("pontuacao_geral"),
  
  // Arquivos relacionados
  arquivoIds: text("arquivo_ids").array(),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const genealogia = pgTable("genealogia", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  
  // Ascendência direta
  paiId: integer("pai_id"),
  maeId: integer("mae_id"),
  
  // Avós paternos
  avoPaterno: json("avo_paterno").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  avoPaterna: json("avo_paterna").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  
  // Avós maternos
  avoMaterno: json("avo_materno").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  avoMaterna: json("avo_materna").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  
  // Bisavós paternos
  bisavoPaterno: json("bisavo_paterno").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  bisavoPaterna: json("bisavo_paterna").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  
  // Bisavós maternos
  bisavoMaterno: json("bisavo_materno").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  bisavoMaterna: json("bisavo_materna").$type<{
    id?: number;
    nome: string;
    registro?: string;
  }>(),
  
  // Metadados
  completude: real("completude"), // Percentual de completude da árvore genealógica
  verificado: timestamp("verificado"), // Data da última verificação
  verificadoPor: text("verificado_por"),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const cruzamentos = pgTable("cruzamentos", {
  id: serial("id").primaryKey(),
  
  // Cavalos envolvidos
  garanhaoId: integer("garanhao_id").notNull(),
  eguaId: integer("egua_id").notNull(),
  
  // Análise genética
  coeficienteConsanguinidade: real("coeficiente_consanguinidade"),
  compatibilidadeGenetica: real("compatibilidade_genetica"), // Score 0-1
  
  // Predições
  predicaoAltura: json("predicao_altura").$type<{
    minima: number;
    maxima: number;
    media: number;
    confianca: number;
  }>(),
  
  predicaoDesempenho: json("predicao_desempenho").$type<{
    velocidade: number;
    resistencia: number;
    agilidade: number;
    temperamento: number;
  }>(),
  
  // Riscos identificados
  riscosGeneticos: text("riscos_geneticos").array(),
  recomendacoes: text("recomendacoes").array(),
  
  // Status do cruzamento
  status: text("status"), // "planejado", "realizado", "descartado"
  dataAnalise: date("data_analise").notNull(),
  analisadoPor: text("analisado_por"),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const morfologiaRelations = relations(morfologia, ({ one }) => ({
  horse: one(horses, {
    fields: [morfologia.horse_id],
    references: [horses.id],
  }),
}));

export const genealogiaRelations = relations(genealogia, ({ one }) => ({
  horse: one(horses, {
    fields: [genealogia.horse_id],
    references: [horses.id],
  }),
  pai: one(horses, {
    fields: [genealogia.paiId],
    references: [horses.id],
    relationName: "pai_genealogia"
  }),
  mae: one(horses, {
    fields: [genealogia.maeId],
    references: [horses.id],
    relationName: "mae_genealogia"
  }),
}));

export const cruzamentosRelations = relations(cruzamentos, ({ one }) => ({
  garanhao: one(horses, {
    fields: [cruzamentos.garanhaoId],
    references: [horses.id],
    relationName: "garanhao_cruzamento"
  }),
  egua: one(horses, {
    fields: [cruzamentos.eguaId],
    references: [horses.id],
    relationName: "egua_cruzamento"
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Ancestor schema for genealogy
const ancestorSchema = z.object({
  id: z.number().int().positive().optional(),
  nome: nomeSchema,
  registro: z.string().max(50).optional(),
});

// Morfologia validation
export const morfologiaSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  dataMedicao: z.string().datetime().or(z.date()),
  responsavelMedicao: z.string().max(100).optional().nullable(),
  
  // Physical measurements validation
  alturaCernelha: z.number().positive().max(300).optional().nullable(),
  alturaDorso: z.number().positive().max(300).optional().nullable(),
  alturaGarupa: z.number().positive().max(300).optional().nullable(),
  comprimentoCorpo: z.number().positive().max(500).optional().nullable(),
  comprimentoPescoco: z.number().positive().max(200).optional().nullable(),
  larguraPeito: z.number().positive().max(200).optional().nullable(),
  perimetroPeito: z.number().positive().max(400).optional().nullable(),
  perimetroCanela: z.number().positive().max(50).optional().nullable(),
  
  // Score validation (1-10)
  pontuacaoCabeca: z.number().min(1).max(10).optional().nullable(),
  pontuacaoPescoco: z.number().min(1).max(10).optional().nullable(),
  pontuacaoEspadua: z.number().min(1).max(10).optional().nullable(),
  pontuacaoTronco: z.number().min(1).max(10).optional().nullable(),
  pontuacaoGarupa: z.number().min(1).max(10).optional().nullable(),
  pontuacaoMembrosDianteiros: z.number().min(1).max(10).optional().nullable(),
  pontuacaoMembrosPosteriores: z.number().min(1).max(10).optional().nullable(),
  pontuacaoAprumos: z.number().min(1).max(10).optional().nullable(),
  pontuacaoMovimentacao: z.number().min(1).max(10).optional().nullable(),
  pontuacaoGeral: z.number().min(1).max(10).optional().nullable(),
  
  arquivoIds: z.array(z.string()).optional().nullable(),
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: garupa não pode ser maior que cernelha
  if (data.alturaCernelha && data.alturaGarupa) {
    return data.alturaGarupa <= data.alturaCernelha;
  }
  return true;
}, {
  message: "Altura da garupa não pode ser maior que a altura na cernelha",
  path: ["alturaGarupa"]
});

// Genealogia validation
export const genealogiaSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  paiId: z.number().int().positive().optional().nullable(),
  maeId: z.number().int().positive().optional().nullable(),
  
  avoPaterno: ancestorSchema.optional().nullable(),
  avoPaterna: ancestorSchema.optional().nullable(),
  avoMaterno: ancestorSchema.optional().nullable(),
  avoMaterna: ancestorSchema.optional().nullable(),
  
  bisavoPaterno: ancestorSchema.optional().nullable(),
  bisavoPaterna: ancestorSchema.optional().nullable(),
  bisavoMaterno: ancestorSchema.optional().nullable(),
  bisavoMaterna: ancestorSchema.optional().nullable(),
  
  completude: z.number().min(0).max(100).optional().nullable(),
  verificado: optionalDateSchema,
  verificadoPor: z.string().max(100).optional().nullable(),
  
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: pai e mãe não podem ser o mesmo cavalo
  if (data.paiId && data.maeId) {
    return data.paiId !== data.maeId;
  }
  return true;
}, {
  message: "Pai e mãe não podem ser o mesmo cavalo",
  path: ["maeId"]
});

// Cruzamentos validation
export const cruzamentosSchema = z.object({
  id: z.number().int().positive().optional(),
  garanhaoId: z.number().int().positive(),
  eguaId: z.number().int().positive(),
  
  coeficienteConsanguinidade: z.number().min(0).max(1).optional().nullable(),
  compatibilidadeGenetica: z.number().min(0).max(1).optional().nullable(),
  
  predicaoAltura: z.object({
    minima: z.number().positive(),
    maxima: z.number().positive(),
    media: z.number().positive(),
    confianca: z.number().min(0).max(1),
  }).optional().nullable(),
  
  predicaoDesempenho: z.object({
    velocidade: z.number().min(0).max(10),
    resistencia: z.number().min(0).max(10),
    agilidade: z.number().min(0).max(10),
    temperamento: z.number().min(0).max(10),
  }).optional().nullable(),
  
  riscosGeneticos: z.array(z.string()).optional().nullable(),
  recomendacoes: z.array(z.string()).optional().nullable(),
  
  status: z.enum(['planejado', 'realizado', 'descartado']).default('planejado'),
  dataAnalise: z.string().datetime().or(z.date()),
  analisadoPor: z.string().max(100).optional().nullable(),
  
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: garanhão e égua não podem ser o mesmo animal
  return data.garanhaoId !== data.eguaId;
}, {
  message: "Garanhão e égua devem ser animais diferentes",
  path: ["eguaId"]
}).refine((data) => {
  // Business rule: predição de altura deve ser consistente
  if (data.predicaoAltura) {
    const { minima, maxima, media } = data.predicaoAltura;
    return minima <= media && media <= maxima && minima < maxima;
  }
  return true;
}, {
  message: "Predição de altura deve ser consistente (mínima ≤ média ≤ máxima)",
  path: ["predicaoAltura"]
});

// Insert schemas
export const insertMorfologiaSchema = createInsertSchema(morfologia).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertGenealogiaSchema = createInsertSchema(genealogia).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertCruzamentosSchema = createInsertSchema(cruzamentos).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type Morfologia = typeof morfologia.$inferSelect;
export type NewMorfologia = typeof morfologia.$inferInsert;
export type MorfologiaData = z.infer<typeof morfologiaSchema>;

export type Genealogia = typeof genealogia.$inferSelect;
export type NewGenealogia = typeof genealogia.$inferInsert;
export type GenealogiaData = z.infer<typeof genealogiaSchema>;

export type Cruzamentos = typeof cruzamentos.$inferSelect;
export type NewCruzamentos = typeof cruzamentos.$inferInsert;
export type CruzamentosData = z.infer<typeof cruzamentosSchema>;

export type AncestorData = z.infer<typeof ancestorSchema>;

// ============================================================================
// BUSINESS LOGIC
// ============================================================================

/**
 * Calculates genealogical completeness percentage
 */
export function calcularComplitudeGenealogia(genealogia: GenealogiaData): number {
  let total = 0;
  let preenchidos = 0;
  
  // Direct parents (weight: 4 each)
  total += 8;
  if (genealogia.paiId) preenchidos += 4;
  if (genealogia.maeId) preenchidos += 4;
  
  // Grandparents (weight: 2 each)
  total += 8;
  if (genealogia.avo_paterno?.nome) preenchidos += 2;
  if (genealogia.avo_paterna?.nome) preenchidos += 2;
  if (genealogia.avo_materno?.nome) preenchidos += 2;
  if (genealogia.avo_materna?.nome) preenchidos += 2;
  
  // Great-grandparents (weight: 1 each)
  total += 8;
  if (genealogia.bisavoPaterno?.nome) preenchidos += 1;
  if (genealogia.bisavoPaterna?.nome) preenchidos += 1;
  if (genealogia.bisavoMaterno?.nome) preenchidos += 1;
  if (genealogia.bisavoMaterna?.nome) preenchidos += 1;
  
  return Math.round((preenchidos / total) * 100);
}

/**
 * Calculates morphological score
 */
export function calcularPontuacaoMorfologica(morfologia: MorfologiaData): number | null {
  const pontuacoes = [
    morfologia.pontuacaoCabeca,
    morfologia.pontuacaoPescoco,
    morfologia.pontuacaoEspadua,
    morfologia.pontuacaoTronco,
    morfologia.pontuacaoGarupa,
    morfologia.pontuacaoMembrosDianteiros,
    morfologia.pontuacaoMembrosPosteriores,
    morfologia.pontuacaoAprumos,
    morfologia.pontuacaoMovimentacao,
  ].filter(p => p !== null && p !== undefined) as number[];
  
  if (pontuacoes.length === 0) return null;
  
  const soma = pontuacoes.reduce((acc, p) => acc + p, 0);
  return Math.round((soma / pontuacoes.length) * 100) / 100;
}

/**
 * Validates business rules for breeding analysis
 */
export function validarRegrasCruzamento(
  garanhao: { id: number; sexo?: string; birthDate?: string },
  egua: { id: number; sexo?: string; birthDate?: string }
): string[] {
  const errors: string[] = [];
  
  // Gender validation
  if (garanhao.sexo && !['macho', 'garanhao'].includes(garanhao.sexo)) {
    errors.push("Garanhão deve ser do sexo masculino");
  }
  
  if (egua.sexo && !['femea', 'femea'].includes(egua.sexo)) {
    errors.push("Égua deve ser do sexo feminino");
  }
  
  // Age validation
  if (garanhao.birthDate) {
    const idade = calcularIdadeEmAnos(garanhao.birthDate);
    if (idade < 2) {
      errors.push("Garanhão muito jovem para reprodução (mínimo 2 anos)");
    }
    if (idade > 25) {
      errors.push("Garanhão pode estar muito idoso para reprodução");
    }
  }
  
  if (egua.birthDate) {
    const idade = calcularIdadeEmAnos(egua.birthDate);
    if (idade < 3) {
      errors.push("Égua muito jovem para reprodução (mínimo 3 anos)");
    }
    if (idade > 20) {
      errors.push("Égua pode estar muito idosa para reprodução");
    }
  }
  
  return errors;
}

function calcularIdadeEmAnos(birthDate: string): number {
  const birth = new Date(birthDate);
  const today = new Date();
  return (today.getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
}