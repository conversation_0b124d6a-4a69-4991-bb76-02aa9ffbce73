import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search } from 'lucide-react';

interface FilterControlsProps {
  title?: string;
  description?: string;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterRaca: string;
  setFilterRaca: (raca: string) => void;
  racasOptions: string[];
  filterSexo: string;
  setFilterSexo: (sexo: string) => void;
  sexosOptions: string[];
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  statusOptions: string[];
}

/**
 * Componente de controles de filtro para a lista de cavalos
 * 
 * Este componente permite filtrar a lista de cavalos por nome, raça, sexo e status
 */
export function FilterControls({
  title = "Filtros",
  description = "Filtre a lista de cavalos",
  searchTerm,
  setSearchTerm,
  filterRaca,
  setFilterRaca,
  racasOptions = [],
  filterSexo,
  setFilterSexo,
  sexosOptions = [],
  filterStatus,
  setFilterStatus,
  statusOptions = []
}: FilterControlsProps) {
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleRacaChange = (value: string) => {
    setFilterRaca(value === "todas" ? "" : value);
  };

  const handleSexoChange = (value: string) => {
    setFilterSexo(value === "todos" ? "" : value);
  };

  const handleStatusChange = (value: string) => {
    setFilterStatus(value === "todos" ? "" : value);
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          {/* Filtro por nome (search) */}
          <div className="space-y-2">
            <Label htmlFor="search">Nome</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Buscar por nome..."
                value={searchTerm}
                onChange={handleSearchInput}
                className="pl-8"
              />
            </div>
          </div>

          {/* Filtro por raça */}
          <div className="space-y-2">
            <Label htmlFor="raca">Raça</Label>
            <Select value={filterRaca || "todas"} onValueChange={handleRacaChange}>
              <SelectTrigger id="raca">
                <SelectValue placeholder="Todas as raças" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todas">Todas as raças</SelectItem>
                {racasOptions.map((raca) => (
                  <SelectItem key={raca} value={raca}>
                    {raca}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por sexo */}
          <div className="space-y-2">
            <Label htmlFor="sexo">Sexo</Label>
            <Select value={filterSexo || "todos"} onValueChange={handleSexoChange}>
              <SelectTrigger id="sexo">
                <SelectValue placeholder="Todos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos</SelectItem>
                {sexosOptions.map((sexo) => (
                  <SelectItem key={sexo} value={sexo}>
                    {sexo}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={filterStatus || "todos"} onValueChange={handleStatusChange}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Todos os status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os status</SelectItem>
                {statusOptions.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}