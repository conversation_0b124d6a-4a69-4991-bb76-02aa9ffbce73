import express, { Request, Response } from 'express';
import { eq, and, asc, desc, or, like, sql } from "drizzle-orm";
import { z } from "zod";
import * as dotenv from 'dotenv';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { storage, IStorage } from './storage';
import { pool, db } from './db';
import { enriquecerDadosComSite } from './abccc-scraper-service';
import { 
  insertUserSchema, 
  insertTaskSchema, 
  insertArquivoSchema,
  insertEventoSchema,
  insertVeterinarioSchema,
  insertProcedimentoVetSchema,
  insertMedidaFisicaSchema,
  insertDesempenhoHistoricoSchema,
  insertSugestoesCruzamentoSchema
} from '../shared/schema';
import {
  insertCavaloSchema,
  insertManejoSchema,
  insertNutricaoSchema,
  insertGenealogiaSchema,
  insertMorfologiaSchema,
  insertReproducaoSchema,
  updateCavaloSchema,
  updateManejoSchema,
  updateNutricaoSchema,
  updateGenealogiaSchema,
  updateMorfologiaSchema
} from '../shared/insert-schemas';
import { authenticateUser, setupAuth } from './auth';
import { importarRegistroABCCC, previsualizarRegistroABCCC } from './abccc-import-service';
import { updateGenealogySync, validateGenealogyConsistency, getCompleteGenealogyData } from './genealogy-sync-service';
import { comprehensiveErrorSystem } from './comprehensive-error-system';
import { logger } from './error-logger';
import { sanitizeAndValidateHorseData } from './utils/sanitize-horse-data';
import { 
  intelligentRateLimit, 
  intelligentCacheMiddleware,
  photoOptimizationMiddleware,
  requestCoordinationMiddleware,
  QueryOptimizer,
  CacheInvalidator,
  PerformanceMonitor
} from './production-stabilizer';

// Configuração do Multer para upload de arquivos
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Diretório específico para PDFs da ABCCC
const abcccPdfsDir = path.join(process.cwd(), 'uploads', 'abccc_pdfs');
if (!fs.existsSync(abcccPdfsDir)) {
  fs.mkdirSync(abcccPdfsDir, { recursive: true });
}

// Diretório para logs de debug
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const storage_config = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Gerar um nome de arquivo único
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExt = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + fileExt);
  }
});

// Configuração específica para PDFs da ABCCC
const abccc_storage_config = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, abcccPdfsDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, 'abccc-' + uniqueSuffix + extension);
  }
});

const upload = multer({ 
  storage: storage_config,
  limits: { fileSize: 10 * 1024 * 1024 }, // Limite de 10MB
  fileFilter: function(req, file, cb) {
    // Verificar tipos de arquivo permitidos
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'video/mp4'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não suportado') as any, false);
    }
  }
});

// Upload específico para PDFs da ABCCC
const uploadAbcccPdf = multer({
  storage: abccc_storage_config,
  limits: { fileSize: 10 * 1024 * 1024 }, // Limite de 10MB
  fileFilter: function(req, file, cb) {
    // Apenas aceita PDFs
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos PDF são aceitos para importação ABCCC') as any, false);
    }
  }
});

export async function addApiRoutes(app: express.Express) {
  // Middleware de logging de erros
  app.use(logger.expressMiddleware());

  // Endpoints para visualizar logs de erro
  app.get("/api/error-logs", authenticateUser, async (req, res) => {
    try {
      const { level, module, limit = 50, user_id } = req.query;
      
      const logs = logger.getLogs({
        level: level as string,
        module: module as string,
        limit: parseInt(limit as string),
        user_id: user_id ? parseInt(user_id as string) : 1
      });

      res.json({ logs, stats: logger.getStats() });
    } catch (error) {
      logger.error('ERROR_LOGS_API', 'Erro ao buscar logs', { error, user_id: (req as any).user?.id });
      res.status(500).json({ error: 'Erro ao buscar logs' });
    }
  });

  app.get("/api/error-logs/:id", authenticateUser, async (req, res) => {
    try {
      const log = logger.getLogById(req.params.id as string);
      
      if (!log) {
        return res.status(404).json({ error: 'Log não encontrado' });
      }

      res.json(log);
    } catch (error) {
      logger.error('ERROR_LOGS_API', 'Erro ao buscar log específico', { error, logId: req.params.id as string });
      res.status(500).json({ error: 'Erro ao buscar log' });
    }
  });

  // User Management API Routes
  
  // Get current user profile
  app.get("/api/users/profile", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user?.id;
      const user = await storage.getUserById(user_id);
      
      if (!user) {
        return res.status(404).json({ message: "Usuário não encontrado" });
      }

      const { password, password_hash, ...userProfile } = user;
      res.json(userProfile);
    } catch (error) {
      console.error("Erro ao buscar perfil:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Update user profile
  app.put("/api/users/profile", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user?.id;
      const { name, email, avatar_url, currentPassword, newPassword } = req.body;

      // Validate current password if changing password
      if (newPassword && currentPassword) {
        const user = await storage.getUserById(user_id);
        if (!user) {
          return res.status(404).json({ message: "Usuário não encontrado" });
        }

        const bcrypt = await import('bcrypt');
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash || user.password);
        if (!isValidPassword) {
          return res.status(400).json({ message: "Senha atual incorreta" });
        }
      }

      // Update user data
      const updateData: any = { name, email, avatar_url };
      
      if (newPassword) {
        const bcrypt = await import('bcrypt');
        updateData.password_hash = await bcrypt.hash(newPassword, 10);
      }

      await storage.updateUser(user_id, updateData);
      
      res.json({ message: "Perfil atualizado com sucesso" });
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Admin routes - Get all users
  app.get("/api/admin/users", authenticateUser, async (req, res) => {
    try {
      const currentUser = await storage.getUserById((req as any).user?.id);
      
      if (!currentUser || currentUser.role !== 'ADMIN') {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const users = await storage.getAllUsers();
      const usersWithoutPasswords = users.map(({ password, password_hash, ...user }) => user);
      
      res.json(usersWithoutPasswords);
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Admin routes - Get user statistics
  app.get("/api/admin/stats", authenticateUser, async (req, res) => {
    try {
      const currentUser = await storage.getUserById((req as any).user?.id);
      
      if (!currentUser || currentUser.role !== 'ADMIN') {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const users = await storage.getAllUsers();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const stats = {
        totalUsers: users.length,
        activeUsers: users.length, // For simplicity, considering all users as active
        adminUsers: users.filter(u => u.role === 'ADMIN').length,
        recentRegistrations: users.filter(u => 
          new Date(u.created_at) > thirtyDaysAgo
        ).length
      };
      
      res.json(stats);
    } catch (error) {
      console.error("Erro ao buscar estatísticas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Admin routes - Update user
  app.put("/api/admin/users/:userId", authenticateUser, async (req, res) => {
    try {
      const currentUser = await storage.getUserById((req as any).user?.id);
      
      if (!currentUser || currentUser.role !== 'ADMIN') {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const { userId } = req.params;
      const { name, email, role, flags } = req.body;

      await storage.updateUser(userId, { name, email, role, flags });
      
      res.json({ message: "Usuário atualizado com sucesso" });
    } catch (error) {
      console.error("Erro ao atualizar usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Admin routes - Delete user
  app.delete("/api/admin/users/:userId", authenticateUser, async (req, res) => {
    try {
      const currentUser = await storage.getUserById((req as any).user?.id);
      
      if (!currentUser || currentUser.role !== 'ADMIN') {
        return res.status(403).json({ message: "Acesso negado" });
      }

      const { userId } = req.params;
      
      // Prevent admin from deleting themselves
      if (userId === currentUser.id.toString()) {
        return res.status(400).json({ message: "Não é possível excluir sua própria conta" });
      }

      await storage.deleteUser(userId);
      
      res.json({ message: "Usuário removido com sucesso" });
    } catch (error) {
      console.error("Erro ao remover usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });
  
  // Auth routes are handled by auth-routes.ts, loaded in server/index.ts
  // setupAuth(app); // REMOVED: Conflicts with JWT authentication in auth-routes.ts
  
  // Endpoint de pelagens removido - novas rotas baseadas em slug estão definidas abaixo
  
  // Rota de verificação de conexão com a API e o banco de dados
  app.get("/api/health", async (req, res) => {
    try {
      // Retorna informações básicas sobre o sistema, sem expor detalhes de implementação
      res.json({
        status: "online",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Erro na verificação de status:", error);
      res.status(500).json({ message: "Serviço indisponível" });
    }
  });

  // CAVALOS DO PLANTEL - EXCLUSIVO (SEM GENEALOGIA EXTERNA)
  app.get("/api/cavalos", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user_id;
      console.log(`[CAVALOS] Buscando cavalos do plantel para user_id: ${user_id}`);
      
      if (!user_id) {
        console.error("[CAVALOS] user_id não encontrado na requisição");
        return res.status(401).json({ error: "Usuário não autenticado" });
      }
      
      const cavalos = await storage.getCavalos(user_id);
      console.log(`[CAVALOS] Encontrados ${cavalos.length} cavalos do plantel`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(cavalos);
    } catch (error) {
      console.error("Erro ao buscar cavalos:", error);
      res.status(500).json({ error: "Erro ao buscar cavalos do plantel" });
    }
  });

  // CAVALOS INATIVOS DO PLANTEL
  app.get("/api/cavalos-inativos", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user_id;
      console.log(`[CAVALOS-INATIVOS] Buscando cavalos inativos para user_id: ${user_id}`);
      
      if (!user_id) {
        console.error("[CAVALOS-INATIVOS] user_id não encontrado na requisição");
        return res.status(401).json({ error: "Usuário não autenticado" });
      }
      
      // Use direct SQL query to get inactive horses
      const { dbHandler } = await import('./db-connection-handler');
      
      if (!dbHandler.isConnected()) {
        return res.status(503).json({ error: 'Database not available' });
      }
      
      const pool = dbHandler.getPool();
      const result = await pool.query(
        'SELECT * FROM cavalos WHERE "user_id" = $1 AND ("is_external" = false OR "is_external" IS NULL) AND status = $2 ORDER BY name ASC', 
        [user_id, 'inativo']
      );
      
      const cavalosInativos = result.rows || [];
      
      console.log(`[CAVALOS-INATIVOS] Encontrados ${cavalosInativos.length} cavalos inativos`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(cavalosInativos);
    } catch (error) {
      console.error("Erro ao buscar cavalos inativos:", error);
      res.status(500).json({ error: "Erro ao buscar cavalos inativos" });
    }
  });
  
  // Simplified test endpoint for genealogy debugging
  app.get("/api/test-genealogy", async (req, res) => {
    try {
      console.log('[TEST] Endpoint /api/test-genealogy chamado');
      return res.json({ message: "Test endpoint working", timestamp: new Date().toISOString() });
    } catch (error) {
      console.error('[TEST] Erro no test endpoint:', error);
      return res.status(500).json({ error: "Test endpoint failed" });
    }
  });

  // Rota para buscar todos os cavalos, incluindo os externos (para seleção de genealogia)
  app.get("/api/cavalos-genealogia", authenticateUser, async (req, res) => {
    console.log('[DEBUG] Iniciando GET /api/cavalos-genealogia');
    
    try {
      // Get user ID from authenticated request
      const user_id = (req as any).user_id;
      console.log(`[DEBUG] Buscando cavalos para user_id: ${user_id}`);
      
      if (!user_id) {
        console.error("[CAVALOS-GENEALOGIA] user_id não encontrado na requisição");
        return res.status(401).json({ error: "Usuário não autenticado" });
      }
      
      const cavalos = await storage.getCavalos(user_id);
      console.log(`[DEBUG] Encontrados ${cavalos.length} cavalos`);
      
      // Return the horses with minimal processing
      res.json(cavalos);
    } catch (error) {
      console.error("[ERROR] Erro na genealogia:", error);
      res.status(500).json({ 
        message: "Erro ao buscar cavalos para genealogia",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });


  app.get("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user_id;
      
      console.log(`GET /api/cavalos/${id}: Buscando cavalo para usuário ${user_id}`);
      
      if (!id || isNaN(id)) {
        console.error(`GET /api/cavalos/${id}: ID do cavalo inválido`);
        return res.status(400).json({ message: "ID do cavalo inválido" });
      }
      
      // Use direct SQL query like the working cavalos list endpoint
      const result = await pool.query(
        'SELECT * FROM cavalos WHERE id = $1 AND "user_id" = $2 AND status = $3', 
        [id, user_id, 'ativo']
      );
      
      if (result.rows.length === 0) {
        console.warn(`GET /api/cavalos/${id}: Cavalo não encontrado para usuário ${user_id}`);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      const cavalo = result.rows[0];
      
      console.log(`GET /api/cavalos/${id}: Cavalo encontrado:`, {
        id: cavalo.id,
        name: cavalo.name,
        user_id: cavalo.user_id
      });
      
      res.json(cavalo);
    } catch (error) {
      comprehensiveErrorSystem.captureError(error, req, 'cavalos');
      console.error(`GET /api/cavalos/${req.params.id as string}: Erro:`, error);
      res.status(500).json({ message: "Erro ao buscar cavalo" });
    }
  });

  app.post("/api/cavalos", authenticateUser, async (req, res) => {
    try {
      console.log('POST /api/cavalos: Dados recebidos:', JSON.stringify(req.body, null, 2));
      
      const user_id = req.body.user_id;
      if (!user_id) {
        return res.status(400).json({ message: "ID do usuário não fornecido" });
      }

      // Sanitize and validate horse data before schema validation
      const sanitizedData = sanitizeAndValidateHorseData(req.body);
      console.log('POST /api/cavalos: Dados sanitizados:', JSON.stringify(sanitizedData, null, 2));

      // Validar dados do cavalo com schema
      const cavaloData = insertCavaloSchema.parse(sanitizedData);
      console.log('POST /api/cavalos: Dados validados:', JSON.stringify(cavaloData, null, 2));
      
      // Criar cavalo na tabela cavalos
      const cavalo = await storage.createCavalo(cavaloData);
      console.log('POST /api/cavalos: Cavalo criado com ID:', cavalo.id);
      
      // Se houver dados de genealogia, processar separadamente
      if (req.body.genealogia && Object.keys(req.body.genealogia).length > 0) {
        console.log('POST /api/cavalos: Processando dados de genealogia:', req.body.genealogia);
        
        try {
          const genealogiaData = {
            horse_id: cavalo.id,
            ...req.body.genealogia
          };
          
          // Validar dados de genealogia
          const validatedGenealogia = insertGenealogiaSchema.parse(genealogiaData);
          
          // Inserir na tabela genealogia
          const insertQuery = `
            INSERT INTO genealogia (horse_id, pai, mae, avo_paterno_id, avo_paterno, avo_paterna_id, avo_paterna, 
                                  avo_materno_id, avo_materno, avo_materna_id, avo_materna, 
                                  bisavo_paterno_paterno, bisavo_paterno_paterno_id, bisavo_paterna_paterno, 
                                  bisavo_paterna_paterno_id, bisavo_materno_paterno, bisavo_materno_paterno_id, 
                                  bisavo_materna_paterno, bisavo_materna_paterno_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
            RETURNING *
          `;
          
          const genealogiaValues = [
            validatedGenealogia.horse_id,
            validatedGenealogia.pai || null,
            validatedGenealogia.mae || null,
            validatedGenealogia.avo_paterno_id || null,
            validatedGenealogia.avo_paterno || null,
            validatedGenealogia.avo_paterna_id || null,
            validatedGenealogia.avo_paterna || null,
            validatedGenealogia.avo_materno_id || null,
            validatedGenealogia.avo_materno || null,
            validatedGenealogia.avo_materna_id || null,
            validatedGenealogia.avo_materna || null,
            validatedGenealogia.bisavo_paterno_paterno || null,
            validatedGenealogia.bisavo_paterno_paterno_id || null,
            validatedGenealogia.bisavo_paterna_paterno || null,
            validatedGenealogia.bisavo_paterna_paterno_id || null,
            validatedGenealogia.bisavo_materno_paterno || null,
            validatedGenealogia.bisavo_materno_paterno_id || null,
            validatedGenealogia.bisavo_materna_paterno || null,
            validatedGenealogia.bisavo_materna_paterno_id || null
          ];
          
          const genealogiaResult = await pool.query(insertQuery, genealogiaValues);
          console.log('POST /api/cavalos: Genealogia criada:', genealogiaResult.rows[0]?.id);
        } catch (genealogiaError) {
          console.warn('POST /api/cavalos: Erro ao criar genealogia (cavalo já foi criado):', genealogiaError);
          // Não falhar a operação se a genealogia der erro
        }
      }

      res.status(201).json(cavalo);
    } catch (error) {
      console.error('POST /api/cavalos: Erro:', error);
      
      if (error instanceof z.ZodError) {
        console.log('POST /api/cavalos: Erro de validação Zod:', error.errors);
        return res.status(400).json({ 
          errors: error.format(),
          message: "Dados inválidos para cadastro do cavalo"
        });
      }

      res.status(500).json({ 
        message: "Erro interno do servidor ao criar cavalo",
        error: (error as Error).message
      });
    }
  });

  app.put("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user_id; // user_id do middleware de autenticação
      
      // Validar se temos um user_id válido
      if (!user_id || isNaN(user_id)) {
        console.error(`PUT /api/cavalos/${id}: user_id inválido ou não fornecido:`, { user_id, reqUserId: (req as any).user_id, bodyUserId: req.body.user_id });
        return res.status(401).json({ message: "Usuário não autenticado" });
      }
      
      // Remove o user_id e genealogy do objeto de atualização para não sobrescrever o dono
      const { user_id: _, genealogy, ...rawUpdateData } = req.body;
      
      // Sanitize update data before processing
      const updateData = sanitizeAndValidateHorseData({ ...rawUpdateData, user_id });
      delete updateData.user_id; // Remove user_id after sanitization to avoid overwriting owner
      
      // Fazer log dos dados recebidos para depuração
      console.log(`PUT /api/cavalos/${id}: Dados sanitizados para usuário ${user_id}:`, JSON.stringify(updateData, null, 2));
      
      try {
        const cavalo = await storage.updateCavalo(id, user_id, updateData);
        
        if (!cavalo) {
          return res.status(404).json({ message: "Cavalo não encontrado" });
        }
        
        res.json(cavalo);
      } catch (updateError: any) {
        console.error(`Erro ao atualizar cavalo ${id}:`, updateError);
        return res.status(500).json({ message: `Erro ao atualizar cavalo: ${updateError.message || 'Erro desconhecido'}` });
      }
    } catch (error) {
      console.error("Erro ao processar requisição:", error);
      res.status(500).json({ message: "Erro ao atualizar cavalo" });
    }
  });

  app.patch("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização para não sobrescrever o dono
      const { user_id: _, ...rawUpdateData } = req.body;
      
      // Sanitize update data before processing
      const updateData = sanitizeAndValidateHorseData({ ...rawUpdateData, user_id });
      delete updateData.user_id; // Remove user_id after sanitization to avoid overwriting owner
      
      // Fazer log dos dados recebidos para depuração
      console.log(`PATCH /api/cavalos/${id}: Dados sanitizados:`, JSON.stringify(updateData, null, 2));
      
      try {
        const cavalo = await storage.updateCavalo(id, user_id, updateData);
        
        if (!cavalo) {
          return res.status(404).json({ message: "Cavalo não encontrado" });
        }
        
        res.json(cavalo);
      } catch (updateError: any) {
        console.error(`Erro ao atualizar cavalo ${id}:`, updateError);
        return res.status(500).json({ message: `Erro ao atualizar cavalo: ${updateError.message || 'Erro desconhecido'}` });
      }
    } catch (error) {
      console.error("Erro ao processar requisição:", error);
      res.status(500).json({ message: "Erro ao atualizar cavalo" });
    }
  });

  app.delete("/api/cavalos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user_id; // from authentication middleware
      
      console.log(`🔄 DELETE Request - Horse ID: ${id}, User ID: ${user_id}`);
      console.log(`📋 Headers:`, req.headers);
      console.log(`📋 Params:`, req.params);
      
      if (!id || isNaN(id)) {
        console.error(`❌ Invalid horse ID: ${req.params.id as string}`);
        return res.status(400).json({ message: "ID do cavalo inválido" });
      }
      
      if (!user_id || isNaN(user_id)) {
        console.error(`❌ Invalid user ID: ${user_id}`);
        return res.status(401).json({ message: "Usuário não autenticado" });
      }
      
      console.log(`🔄 Attempting to delete horse ${id} for user ${user_id}`);
      
      const success = await storage.deleteCavalo(id, user_id);
      
      if (!success) {
        console.warn(`❌ DELETE Failed - Horse ${id} not found or already inactive for user ${user_id}`);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      console.log(`✅ DELETE Success - Horse ${id} marked as inactive`);
      res.status(200).json({ 
        message: "Cavalo removido com sucesso",
        action: "soft_delete",
        status: "inativo"
      });
    } catch (error) {
      console.error(`❌ DELETE Error for horse ${req.params.id as string}:`, error);
      comprehensiveErrorSystem.captureError(error, req, 'cavalos');
      res.status(500).json({ message: "Erro ao excluir cavalo" });
    }
  });

  // Rotas de manejos (Horse care tasks) - corrigido com SQL direto
  app.get("/api/manejos", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      // Query SQL direta para evitar problemas de schema
      const result = await pool.query(
        'SELECT * FROM manejos WHERE user_id = $1 ORDER BY data_execucao DESC', 
        [user_id]
      );
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(result.rows || []);
    } catch (error) {
      console.error("Erro ao buscar manejos:", error);
      res.status(500).json({ message: "Erro ao buscar manejos" });
    }
  });

  app.get("/api/cavalos/:horse_id/manejos", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const manejos = await storage.getManejosByHorse(horse_id, user_id);
      res.json(manejos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar manejos do cavalo" });
    }
  });

  app.get("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const manejo = await storage.getManejo(id, user_id);
      
      if (!manejo) {
        return res.status(404).json({ message: "Manejo não encontrado" });
      }
      
      res.json(manejo);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar manejo" });
    }
  });

  app.post("/api/manejos", authenticateUser, async (req, res) => {
    try {
      // Extract user_id from authenticated request
      const user_id = req.body.user_id;
      
      // Add user_id to the request data for validation
      const manejoDataWithUser = {
        ...req.body,
        user_id: user_id
      };
      
      const manejoData = insertManejoSchema.parse(manejoDataWithUser);
      const manejo = await storage.createManejo(manejoData);
      res.status(201).json(manejo);
    } catch (error) {
      console.error("Erro ao criar manejo:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar manejo" });
    }
  });

  app.put("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização para não sobrescrever o dono
      const { user_id: _, ...updateData } = req.body;
      
      const manejo = await storage.updateManejo(id, user_id, updateData);
      
      if (!manejo) {
        return res.status(404).json({ message: "Manejo não encontrado" });
      }
      
      res.json(manejo);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar manejo" });
    }
  });

  app.delete("/api/manejos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      console.log(`[DELETE MANEJO] Tentando excluir manejo ID: ${id} para user_id: ${user_id}`);
      
      const success = await storage.deleteManejo(id, user_id);
      
      if (!success) {
        console.log(`[DELETE MANEJO] Manejo ID: ${id} não encontrado ou já foi excluído`);
        return res.status(404).json({ message: "Manejo não encontrado ou já foi excluído" });
      }
      
      console.log(`[DELETE MANEJO] Manejo ID: ${id} excluído com sucesso`);
      res.status(204).end();
    } catch (error) {
      console.error(`[DELETE MANEJO] Erro ao excluir manejo ID: ${req.params.id as string}:`, error);
      res.status(500).json({ message: "Erro ao excluir manejo" });
    }
  });

  // Rotas de arquivos
  app.get("/api/arquivos", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      const arquivos = await storage.getArquivos(user_id);
      res.json(arquivos);
    } catch (error) {
      console.error("Erro ao buscar arquivos:", error);
      res.status(500).json({ message: "Erro ao buscar arquivos" });
    }
  });

  app.get("/api/cavalos/:horse_id/arquivos", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const arquivos = await storage.getArquivosByHorse(horse_id, user_id);
      res.json(arquivos);
    } catch (error) {
      console.error("Erro ao buscar arquivos do cavalo:", error);
      res.status(500).json({ message: "Erro ao buscar arquivos do cavalo" });
    }
  });

  app.get("/api/arquivos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const arquivo = await storage.getArquivo(id, user_id);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      res.json(arquivo);
    } catch (error) {
      console.error("Erro ao buscar arquivo:", error);
      res.status(500).json({ message: "Erro ao buscar arquivo" });
    }
  });
  
  // Rota para download/visualização de arquivos
  app.get("/api/arquivos/:id/download", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const arquivo = await storage.getArquivo(id, user_id);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      // Verificar se o arquivo existe no sistema
      if (!fs.existsSync(arquivo.filePath)) {
        return res.status(404).json({ message: "Arquivo físico não encontrado no servidor" });
      }
      
      // Verificar o tipo de arquivo para configurar os headers apropriados
      let contentType = 'application/octet-stream'; // padrão para download
      
      if (arquivo.fileType === 'image') {
        const ext = path.extname(arquivo.fileName).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        }
      } else if (arquivo.fileType === 'pdf') {
        contentType = 'application/pdf';
      } else if (arquivo.fileType === 'video') {
        contentType = 'video/mp4';
      }
      
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${arquivo.fileName}"`);
      
      // Enviar o arquivo como stream
      const fileStream = fs.createReadStream(arquivo.filePath);
      fileStream.pipe(res);
      
    } catch (error) {
      console.error("Erro ao baixar arquivo:", error);
      res.status(500).json({ message: "Erro ao baixar arquivo" });
    }
  });
  
  // Rota para upload de arquivo
  app.post("/api/arquivos", authenticateUser, upload.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo enviado" });
      }
      
      const file = req.file;
      const { horse_id, description, fileType } = req.body;
      const user_id = req.body.user_id;
      
      if (!horse_id || !user_id) {
        // Se o arquivo foi salvo, mas os dados estão inválidos, remover o arquivo
        fs.unlinkSync(file.path);
        return res.status(400).json({ message: "Dados de arquivo inválidos" });
      }
      
      // Verificar se o cavalo existe e pertence ao usuário
      const cavalo = await storage.getCavalo(parseInt(horse_id), parseInt(user_id));
      if (!cavalo) {
        // Remover arquivo se o cavalo não for encontrado
        fs.unlinkSync(file.path);
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      const arquivoData = {
        fileName: file.originalname,
        filePath: file.path,
        fileType: fileType || 'other', // Tipo padrão se não especificado
        description: description || '',
        cavalo_id: parseInt(horse_id),
        user_id: parseInt(user_id)
      };
      
      const arquivo = await storage.createArquivo(arquivoData);
      res.status(201).json(arquivo);
      
    } catch (error) {
      console.error("Erro ao fazer upload do arquivo:", error);
      // Se houve erro ao salvar no banco, mas o arquivo foi salvo, deveria remover o arquivo
      if (req.file && fs.existsSync(req.file.path)) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (e) {
          console.error("Erro ao remover arquivo após falha:", e);
        }
      }
      res.status(500).json({ message: "Erro ao fazer upload do arquivo" });
    }
  });
  
  // Rota para pré-visualização de PDF da ABCCC (sem salvar no banco)
  app.post("/api/importar-pdf-crioulo/preview", authenticateUser, uploadAbcccPdf.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo PDF enviado" });
      }
      
      const user_id = parseInt(req.body.user_id);
      if (!user_id) {
        return res.status(400).json({ message: "ID de usuário não fornecido" });
      }
      
      // Log detalhado do início do processo
      console.log(`Iniciando pré-visualização de PDF ABCCC: ${req.file.originalname} (${req.file.size} bytes) por usuário ${user_id}`);
      
      // Pré-processar o PDF sem salvar no banco
      const resultado = await previsualizarRegistroABCCC(req.file.path, user_id);
      
      // Retorna os dados processados para pré-visualização
      res.status(200).json({
        sucesso: true,
        mensagem: "Registro pré-visualizado com sucesso",
        dados: resultado
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      console.error(`Erro ao pré-visualizar PDF ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao processar o arquivo para pré-visualização: ${mensagem}`,
        detalhes: error instanceof Error ? error.stack : undefined
      });
    }
  });

  // Rota para importação direta ABCCC (para testes)
  app.post("/api/abccc/import", authenticateUser, async (req, res) => {
    try {
      const { registrationNumber, user_id } = req.body;
      
      if (!registrationNumber) {
        return res.status(400).json({ message: "Número de registro é obrigatório" });
      }
      
      if (!user_id) {
        return res.status(400).json({ message: "ID de usuário é obrigatório" });
      }
      
      console.log(`Testando importação ABCCC para registro: ${registrationNumber}`);
      
      // Dados de teste para verificar se a correção da coluna birth_date funciona
      const dadosTeste = {
        name: "Cavalo Teste",
        sexo: "Macho",
        birth_date: new Date().toISOString().split('T')[0], // Usando birth_date em snake_case
        cor: "Tordilho",
        numero_registro: registrationNumber,
        user_id: user_id
      };
      
      // Validar se conseguimos inserir no banco com a coluna birth_date corrigida
      const cavaloData = insertCavaloSchema.parse(dadosTeste);
      const cavalo = await storage.createCavalo(cavaloData);
      
      res.status(200).json({
        sucesso: true,
        mensagem: "Teste de importação ABCCC realizado com sucesso - coluna birth_date corrigida",
        dados: cavalo
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      console.error(`Erro no teste de importação ABCCC: ${mensagem}`);
      
      // Verificar se é erro de coluna birth_date
      if (mensagem.includes('birth_date') || mensagem.includes('does not exist')) {
        return res.status(500).json({
          sucesso: false,
          mensagem: "Erro de coluna birth_date ainda não resolvido",
          erro: mensagem
        });
      }
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro no teste: ${mensagem}`,
        detalhes: error instanceof Error ? error.stack : undefined
      });
    }
  });

  // Rota para importação de PDF da ABCCC
  app.post("/api/importar-pdf-crioulo", authenticateUser, uploadAbcccPdf.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Nenhum arquivo PDF enviado" });
      }
      
      const user_id = parseInt(req.body.user_id);
      if (!user_id) {
        return res.status(400).json({ message: "ID de usuário não fornecido" });
      }
      
      // Log detalhado do início do processo
      console.log(`Iniciando importação de PDF ABCCC: ${req.file.originalname} (${req.file.size} bytes) por usuário ${user_id}`);
      
      // Processar o PDF e importar os dados
      const resultado = await importarRegistroABCCC(req.file.path, user_id);
      
      // Retorna os dados processados
      res.status(200).json({
        sucesso: true,
        mensagem: "Registro processado com sucesso",
        dados: resultado
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      console.error(`Erro ao importar PDF ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao processar o arquivo: ${mensagem}`,
        detalhes: error instanceof Error ? error.stack : undefined
      });
    }
  });
  
  // Rota para obter logs de debug da importação ABCCC
  app.get("/api/importar-pdf-crioulo/logs", authenticateUser, async (req, res) => {
    try {
      // Verificar se o usuário tem permissão de admin (simplificado)
      const user_id = parseInt(req.query.user_id as string || '0');
      
      // Listar os arquivos de log disponíveis
      const logsDir = path.join(process.cwd(), 'logs');
      const arquivos = fs.readdirSync(logsDir)
        .filter(file => file.startsWith('debug_pdf_'))
        .sort()
        .reverse() // Mais recentes primeiro
        .slice(0, 20); // Limitar a 20 logs
      
      res.status(200).json({
        sucesso: true,
        logs: arquivos.map(arquivo => ({
          nome: arquivo,
          caminho: `/api/importar-pdf-crioulo/logs/${arquivo}`,
          data: new Date(parseInt(arquivo.replace(/\D/g, ''))).toISOString()
        }))
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      console.error(`Erro ao listar logs de importação ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao listar logs: ${mensagem}`
      });
    }
  });
  
  // Rota para visualizar um log específico
  app.get("/api/importar-pdf-crioulo/logs/:nome", authenticateUser, async (req, res) => {
    try {
      const nomeArquivo = req.params.nome as string;
      if (!nomeArquivo || !nomeArquivo.startsWith('debug_pdf_')) {
        return res.status(400).json({
          sucesso: false,
          mensagem: "Nome de arquivo de log inválido"
        });
      }
      
      const caminhoArquivo = path.join(process.cwd(), 'logs', nomeArquivo);
      if (!fs.existsSync(caminhoArquivo)) {
        return res.status(404).json({
          sucesso: false,
          mensagem: "Arquivo de log não encontrado"
        });
      }
      
      // Ler o conteúdo do arquivo
      const conteudo = JSON.parse(fs.readFileSync(caminhoArquivo, 'utf8'));
      
      res.status(200).json({
        sucesso: true,
        conteudo
      });
      
    } catch (error) {
      const mensagem = error instanceof Error ? error.message : String(error);
      console.error(`Erro ao ler log de importação ABCCC: ${mensagem}`);
      
      res.status(500).json({
        sucesso: false,
        mensagem: `Erro ao ler log: ${mensagem}`
      });
    }
  });

  app.delete("/api/arquivos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Primeiro buscar o arquivo para ter o caminho do arquivo físico
      const arquivo = await storage.getArquivo(id, user_id);
      
      if (!arquivo) {
        return res.status(404).json({ message: "Arquivo não encontrado" });
      }
      
      // Excluir o registro do banco de dados
      const success = await storage.deleteArquivo(id, user_id);
      
      if (!success) {
        return res.status(500).json({ message: "Erro ao excluir arquivo do banco de dados" });
      }
      
      // Excluir o arquivo físico (se existir)
      if (fs.existsSync(arquivo.filePath)) {
        fs.unlinkSync(arquivo.filePath);
      }
      
      res.status(204).end();
    } catch (error) {
      console.error("Erro ao excluir arquivo:", error);
      res.status(500).json({ message: "Erro ao excluir arquivo" });
    }
  });

  // Rotas de Eventos (Agenda)
  app.get("/api/eventos", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      const eventos = await storage.getEventos(user_id);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos" });
    }
  });

  app.get("/api/eventos/data/:data", authenticateUser, async (req, res) => {
    try {
      const data = req.params.data as string; // Formato esperado: YYYY-MM-DD
      const user_id = req.body.user_id;
      const eventos = await storage.getEventosByDate(data, user_id);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos por data" });
    }
  });

  app.get("/api/cavalos/:horse_id/eventos", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const eventos = await storage.getEventosByHorse(horse_id, user_id);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos do cavalo" });
    }
  });

  app.get("/api/manejos/:manejoId/eventos", authenticateUser, async (req, res) => {
    try {
      const manejoId = parseInt(req.params.manejoId as string);
      const user_id = req.body.user_id;
      const eventos = await storage.getEventosByManejo(manejoId, user_id);
      res.json(eventos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar eventos do manejo" });
    }
  });

  app.get("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const evento = await storage.getEvento(id, user_id);
      
      if (!evento) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.json(evento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar evento" });
    }
  });

  app.post("/api/eventos", authenticateUser, async (req, res) => {
    try {
      const eventoData = insertEventoSchema.parse(req.body);
      const evento = await storage.createEvento(eventoData);
      res.status(201).json(evento);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar evento" });
    }
  });

  app.put("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização
      const { user_id: _, ...updateData } = req.body;
      
      const evento = await storage.updateEvento(id, user_id, updateData);
      
      if (!evento) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.json(evento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar evento" });
    }
  });

  app.delete("/api/eventos/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteEvento(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Evento não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir evento" });
    }
  });

  // Rotas de Procedimentos Veterinários
  app.get("/api/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user?.id;
      
      console.log(`[VETERINARIO] Buscando dados para user_id: ${user_id}`);
      
      // Use storage interface for consistent database access
      const procedimentos = await storage.getProcedimentosVet(user_id);
      
      console.log(`[VETERINARIO] Encontrados ${procedimentos?.length || 0} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(procedimentos || []);
    } catch (error) {
      console.error("Erro ao buscar procedimentos veterinários:", error);
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários" });
    }
  });

  app.get("/api/cavalos/:horse_id/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = (req as any).user?.id;
      const procedimentos = await storage.getProcedimentosVetByHorse(horse_id, user_id);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do cavalo" });
    }
  });

  app.get("/api/manejos/:manejoId/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const manejoId = parseInt(req.params.manejoId as string);
      const user_id = (req as any).user?.id;
      const procedimentos = await storage.getProcedimentosVetByManejo(manejoId, user_id);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do manejo" });
    }
  });

  app.get("/api/eventos/:eventoId/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const eventoId = parseInt(req.params.eventoId as string);
      const user_id = (req as any).user?.id;
      const procedimentos = await storage.getProcedimentosVetByEvento(eventoId, user_id);
      res.json(procedimentos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimentos veterinários do evento" });
    }
  });

  app.get("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      const procedimento = await storage.getProcedimentoVet(id, user_id);
      
      if (!procedimento) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.json(procedimento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar procedimento veterinário" });
    }
  });

  app.post("/api/procedimentos-vet", authenticateUser, async (req, res) => {
    try {
      const procedimentoData = insertVeterinarioSchema.parse(req.body);
      const procedimento = await storage.createProcedimentoVet(procedimentoData);
      res.status(201).json(procedimento);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar procedimento veterinário" });
    }
  });

  app.put("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      
      // Remove o user_id do objeto de atualização
      const { user_id: _, ...updateData } = req.body;
      
      const procedimento = await storage.updateProcedimentoVet(id, user_id, updateData);
      
      if (!procedimento) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.json(procedimento);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar procedimento veterinário" });
    }
  });

  app.delete("/api/procedimentos-vet/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      const success = await storage.deleteProcedimentoVet(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Procedimento veterinário não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir procedimento veterinário" });
    }
  });
  
  // Rotas de Reprodução
  app.get("/api/reproducao", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      console.log(`[REPRODUCAO] Buscando dados para user_id: ${user_id}`);
      
      // Query SQL direta para evitar problemas de schema
      const result = await pool.query(
        'SELECT * FROM reproducao WHERE "user_id" = $1 ORDER BY "data_cobertura" DESC', 
        [user_id]
      );
      
      console.log(`[REPRODUCAO] Encontrados ${result.rows?.length || 0} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(result.rows || []);
    } catch (error) {
      console.error("Erro ao buscar reprodução:", error);
      res.status(500).json({ message: "Erro ao buscar registros de reprodução" });
    }
  });

  app.get("/api/cavalos/:horse_id/reproducao", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const reproducoes = await storage.getReproducoesByHorse(horse_id, user_id);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do cavalo" });
    }
  });

  app.get("/api/procedimentos-vet/:procedimentoId/reproducao", authenticateUser, async (req, res) => {
    try {
      const procedimentoId = parseInt(req.params.procedimentoId as string);
      const user_id = req.body.user_id;
      const reproducoes = await storage.getReproducoesByProcedimento(procedimentoId, user_id);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do procedimento" });
    }
  });

  app.get("/api/eventos/:eventoId/reproducao", authenticateUser, async (req, res) => {
    try {
      const eventoId = parseInt(req.params.eventoId as string);
      const user_id = req.body.user_id;
      const reproducoes = await storage.getReproducoesByEvento(eventoId, user_id);
      res.json(reproducoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de reprodução do evento" });
    }
  });

  app.get("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const reproducao = await storage.getReproducao(id, user_id);
      
      if (!reproducao) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.json(reproducao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de reprodução" });
    }
  });

  app.post("/api/reproducao", authenticateUser, async (req, res) => {
    try {
      const reproducaoData = insertReproducaoSchema.parse(req.body);
      const reproducao = await storage.createReproducao(reproducaoData);
      res.status(201).json(reproducao);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de reprodução" });
    }
  });

  app.put("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização
      const { user_id: _, ...updateData } = req.body;
      
      const reproducao = await storage.updateReproducao(id, user_id, updateData);
      
      if (!reproducao) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.json(reproducao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de reprodução" });
    }
  });

  app.delete("/api/reproducao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteReproducao(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de reprodução não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de reprodução" });
    }
  });
  
  // Rotas para Medidas Físicas
  app.get("/api/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      const medidas = await storage.getMedidasFisicas(user_id);
      res.json(medidas);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medidas físicas" });
    }
  });

  app.get("/api/cavalos/:horse_id/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const medidas = await storage.getMedidasFisicasByHorse(horse_id, user_id);
      res.json(medidas);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medidas físicas do cavalo" });
    }
  });

  app.get("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const medida = await storage.getMedidaFisica(id, user_id);
      
      if (!medida) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.json(medida);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar medida física" });
    }
  });

  app.post("/api/medidas-fisicas", authenticateUser, async (req, res) => {
    try {
      const medidaData = insertMedidaFisicaSchema.parse(req.body);
      const medida = await storage.createMedidaFisica(medidaData);
      res.status(201).json(medida);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar medida física" });
    }
  });

  app.put("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização
      const { user_id: _, ...updateData } = req.body;
      
      const medida = await storage.updateMedidaFisica(id, user_id, updateData);
      
      if (!medida) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.json(medida);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar medida física" });
    }
  });

  app.delete("/api/medidas-fisicas/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteMedidaFisica(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Medida física não encontrada" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir medida física" });
    }
  });
  
  // Rotas para Nutrição - usando pool direto para evitar storage issues
  app.get("/api/nutricao", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      console.log(`[NUTRICAO] Buscando dados para user_id: ${user_id}`);
      
      // Using pool.query directly to bypass storage issues
      const result = await pool.query(
        'SELECT * FROM nutricao WHERE "user_id" = $1 ORDER BY created_at DESC', 
        [user_id]
      );
      
      console.log(`[NUTRICAO] Encontrados ${result.rows?.length || 0} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(result.rows || []);
    } catch (error) {
      console.error("Erro detalhado ao buscar nutrição:", error);
      res.status(500).json({ message: "Erro ao buscar registros de nutrição" });
    }
  });

  app.get("/api/nutricao/horse/:horse_id", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = (req as any).user?.id;
      const nutricoes = await storage.getNutricoesByHorse(horse_id, user_id);
      res.json(nutricoes);
    } catch (error) {
      console.error("Erro ao buscar nutrição por cavalo:", error);
      res.status(500).json({ message: "Erro ao buscar registros de nutrição do cavalo" });
    }
  });

  app.get("/api/cavalos/:horse_id/nutricao", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = (req as any).user?.id;
      const nutricoes = await storage.getNutricoesByHorse(horse_id, user_id);
      res.json(nutricoes);
    } catch (error) {
      console.error("Erro ao buscar nutrição por cavalo (rota alternativa):", error);
      res.status(500).json({ message: "Erro ao buscar registros de nutrição do cavalo" });
    }
  });

  app.get("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      const nutricao = await storage.getNutricao(id, user_id);
      
      if (!nutricao) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      res.json(nutricao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de nutrição" });
    }
  });

  app.post("/api/nutricao", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user?.id;
      
      console.log("[NUTRIÇÃO DEBUG] user_id:", user_id);
      console.log("[NUTRIÇÃO DEBUG] req.body:", req.body);
      console.log("[NUTRIÇÃO DEBUG] req.user:", (req as any).user);
      
      if (!user_id) {
        console.error("[NUTRIÇÃO ERROR] user_id não encontrado na requisição");
        return res.status(401).json({ 
          success: false,
          error: "Usuário não autenticado",
          details: "user_id não encontrado na sessão"
        });
      }
      
      const dataToValidate = {
        ...req.body,
        user_id: Number(user_id)
      };
      
      console.log("[NUTRIÇÃO DEBUG] Dados para validação:", dataToValidate);
      
      const nutricaoData = insertNutricaoSchema.parse(dataToValidate);
      
      console.log("[NUTRIÇÃO DEBUG] Dados validados:", nutricaoData);
      
      const nutricao = await storage.createNutricao(nutricaoData);
      
      console.log("[NUTRIÇÃO SUCCESS] Registro criado:", nutricao);
      
      return res.status(201).json({
        success: true,
        data: nutricao,
        message: "Registro de nutrição criado com sucesso"
      });
    } catch (error) {
      console.error("[NUTRIÇÃO ERROR] Erro detalhado:", error);
      
      if (error instanceof z.ZodError) {
        console.error("[NUTRIÇÃO VALIDATION ERROR] Erros de validação:", error.errors);
        return res.status(400).json({ 
          success: false,
          error: "Dados inválidos",
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            received: (err as any).received
          })),
          originalErrors: error.format()
        });
      }
      
      return res.status(500).json({ 
        success: false,
        error: "Erro interno do servidor",
        message: "Erro ao criar registro de nutrição",
        details: (error as Error).message
      });
    }
  });

  app.put("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      
      // Remove o user_id do objeto de atualização se estiver presente
      const { user_id: _, ...updateData } = req.body;
      
      const nutricao = await storage.updateNutricao(id, user_id, updateData);
      
      if (!nutricao) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      return res.json(nutricao);
    } catch (error) {
      console.error("Erro ao atualizar registro de nutrição:", error);
      return res.status(500).json({ message: "Erro ao atualizar registro de nutrição" });
    }
  });

  app.delete("/api/nutricao/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      const success = await storage.deleteNutricao(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de nutrição não encontrado" });
      }
      
      return res.status(204).end();
    } catch (error) {
      console.error("Erro ao excluir registro de nutrição:", error);
      return res.status(500).json({ message: "Erro ao excluir registro de nutrição" });
    }
  });
  
  // Rotas para Morfologia - usando pool direto para evitar storage issues
  app.get("/api/morfologia", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      console.log(`[MORFOLOGIA] Buscando dados para user_id: ${user_id}`);
      
      // Using pool.query directly to bypass storage issues
      const result = await pool.query(
        'SELECT * FROM medidas_morfologicas WHERE "user_id" = $1 ORDER BY "created_at" DESC', 
        [user_id]
      );
      
      console.log(`[MORFOLOGIA] Encontrados ${result.rows?.length || 0} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(result.rows || []);
    } catch (error) {
      comprehensiveErrorSystem.captureError(error, req, 'morfologia');
      console.error("Erro detalhado ao buscar morfologia:", error);
      res.status(500).json({ message: "Erro ao buscar registros de morfologia" });
    }
  });

  app.get("/api/cavalos/:horse_id/morfologia", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const morfologias = await storage.getMorfologiasByHorse(horse_id, user_id);
      res.json(morfologias);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de morfologia do cavalo" });
    }
  });

  app.get("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const morfologia = await storage.getMorfologia(id, user_id);
      
      if (!morfologia) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.json(morfologia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de morfologia" });
    }
  });

  app.post("/api/morfologia", authenticateUser, async (req, res) => {
    try {
      const user_id = (req as any).user?.id;
      
      console.log("=== MORFOLOGIA DEBUG ===");
      console.log("Original request body:", JSON.stringify(req.body, null, 2));
      
      // Add user_id to the request data
      const morfologiaDataWithUser = {
        ...req.body,
        user_id
      };
      
      console.log("Data with user_id:", JSON.stringify(morfologiaDataWithUser, null, 2));
      
      const morfologiaData = insertMorfologiaSchema.parse(morfologiaDataWithUser);
      
      console.log("Transformed data after schema:", JSON.stringify(morfologiaData, null, 2));
      
      const morfologia = await storage.createMorfologia(morfologiaData);
      
      res.status(201).json({
        success: true,
        data: morfologia,
        message: "Registro de morfologia criado com sucesso"
      });
    } catch (error) {
      console.error("Erro ao criar morfologia:", error);
      
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          success: false,
          error: "Dados inválidos",
          details: error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message,
            code: issue.code,
            received: 'received' in issue ? issue.received : undefined,
            expected: 'expected' in issue ? issue.expected : undefined
          }))
        });
      }
      
      return res.status(500).json({ 
        success: false,
        error: "Erro interno do servidor",
        message: "Erro ao criar registro de morfologia",
        details: (error as Error).message
      });
    }
  });

  app.put("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      
      console.log("=== MORFOLOGIA UPDATE DEBUG ===");
      console.log("ID:", id);
      console.log("User ID:", user_id);
      console.log("Request body:", JSON.stringify(req.body, null, 2));
      
      if (!user_id) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }
      
      // Remove o user_id do objeto de atualização se presente e valide dados
      const { user_id: _userId, ...rawData } = req.body;
      const parsed = updateMorfologiaSchema.parse({ ...rawData, id });
      const { id: _id, ...updateData } = parsed;

      const morfologia = await storage.updateMorfologia(id, user_id, updateData);
      
      if (!morfologia) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.json(morfologia);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de morfologia" });
    }
  });

  app.delete("/api/morfologia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = (req as any).user?.id;
      
      if (!user_id) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }
      
      const success = await storage.deleteMorfologia(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de morfologia não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de morfologia" });
    }
  });
  
  // Rotas para Desempenho
  app.get("/api/desempenho", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      
      console.log(`[DESEMPENHO] Buscando dados para user_id: ${user_id}`);
      
      // Tabela desempenho_historico não existe - retorna array vazio
      // Futuro: implementar quando tabela for criada no banco
      const desempenhoData: any[] = [];
      
      console.log(`[DESEMPENHO] Encontrados ${desempenhoData.length} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(desempenhoData);
    } catch (error) {
      console.error("Erro ao buscar desempenho:", error);
      res.status(500).json({ message: "Erro ao buscar registros de desempenho" });
    }
  });

  app.get("/api/cavalos/:horse_id/desempenho", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const desempenhos = await storage.getDesempenhoHistoricoByHorse(horse_id, user_id);
      res.json(desempenhos);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de desempenho do cavalo" });
    }
  });

  app.get("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const desempenho = await storage.getDesempenho(id, user_id);
      
      if (!desempenho) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.json(desempenho);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de desempenho" });
    }
  });

  app.post("/api/desempenho", authenticateUser, async (req, res) => {
    try {
      const desempenhoData = insertDesempenhoHistoricoSchema.parse(req.body);
      const desempenho = await storage.createDesempenhoHistorico(desempenhoData);
      res.status(201).json(desempenho);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar registro de desempenho" });
    }
  });

  app.put("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização
      const { user_id: _, ...updateData } = req.body;
      
      const desempenho = await storage.updateDesempenho(id, user_id, updateData);
      
      if (!desempenho) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.json(desempenho);
    } catch (error) {
      res.status(500).json({ message: "Erro ao atualizar registro de desempenho" });
    }
  });

  app.delete("/api/desempenho/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteDesempenho(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de desempenho não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de desempenho" });
    }
  });
  
  // Rotas para Genealogia
  app.get("/api/genealogia", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      const genealogias = await storage.getGenealogias(user_id);
      res.json(genealogias);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registros de genealogia" });
    }
  });

  app.get("/api/cavalos/:horse_id/genealogia", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      
      // Use synchronized genealogy service to get complete data
      const genealogiaCompleta = await getCompleteGenealogyData(horse_id);
      
      if (!genealogiaCompleta.cavalo) {
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }
      
      res.json(genealogiaCompleta);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar registro de genealogia do cavalo" });
    }
  });

  // New synchronized genealogy update endpoint
  app.put("/api/cavalos/:horse_id/genealogia/sync", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      
      const updateData = {
        horse_id,
        ...req.body
      };
      
      // Validate genealogy data consistency before update
      const validationResult = await validateGenealogyConsistency(horse_id);
      
      if (!validationResult.isConsistent) {
        console.warn(`Genealogy inconsistency detected for horse ${horse_id}:`, validationResult.issues);
      }
      
      // Perform synchronized update across both tables
      await updateGenealogySync(updateData);
      
      // Return updated complete data
      const updatedData = await getCompleteGenealogyData(horse_id);
      
      res.json({
        success: true,
        message: "Genealogia atualizada com sincronização entre tabelas",
        data: updatedData,
        validation: validationResult
      });
      
    } catch (error) {
      console.error("Erro ao atualizar genealogia sincronizada:", error);
      res.status(500).json({ 
        success: false,
        message: "Erro ao atualizar genealogia sincronizada",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Genealogy consistency validation endpoint
  app.get("/api/cavalos/:horse_id/genealogia/validate", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const validationResult = await validateGenealogyConsistency(horse_id);
      
      res.json({
        horse_id,
        ...validationResult,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      res.status(500).json({ 
        message: "Erro ao validar consistência da genealogia",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.post("/api/genealogia", authenticateUser, async (req, res) => {
    try {
      console.log('POST /api/genealogia - Dados recebidos:', JSON.stringify(req.body, null, 2));
      const user_id = req.body.user_id;
      
      // Validação simples dos campos obrigatórios
      if (!req.body.horse_id) {
        return res.status(400).json({ message: "horse_id é obrigatório" });
      }
      
      // Preparar dados para inserção (user_id removed - table doesn't have this column)
      const genealogiaData = {
        ...req.body
      };
      delete genealogiaData.user_id; // Remove user_id as it doesn't exist in table
      
      console.log('POST /api/genealogia - Dados preparados:', JSON.stringify(genealogiaData, null, 2));
      const genealogia = await storage.createGenealogia(genealogiaData);
      console.log('POST /api/genealogia - Genealogia criada:', JSON.stringify(genealogia, null, 2));
      res.status(201).json(genealogia);
    } catch (error) {
      console.error('POST /api/genealogia - Erro:', error);
      res.status(500).json({ message: "Erro ao criar registro de genealogia", error: (error as Error).message });
    }
  });

  app.put("/api/genealogia/:id", authenticateUser, async (req, res) => {
    try {
      console.log('PUT /api/genealogia/:id - Dados recebidos:', JSON.stringify(req.body, null, 2));
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      
      // Remove o user_id do objeto de atualização (table doesn't have this column)
      const { user_id: _, ...updateData } = req.body;
      
      console.log('PUT /api/genealogia/:id - Dados preparados:', JSON.stringify(updateData, null, 2));
      const genealogia = await storage.updateGenealogia(id, user_id, updateData);
      
      if (!genealogia) {
        return res.status(404).json({ message: "Registro de genealogia não encontrado" });
      }
      
      console.log('PUT /api/genealogia/:id - Genealogia atualizada:', JSON.stringify(genealogia, null, 2));
      res.json(genealogia);
    } catch (error) {
      console.error('PUT /api/genealogia/:id - Erro:', error);
      res.status(500).json({ message: "Erro ao atualizar registro de genealogia", error: (error as Error).message });
    }
  });

  app.delete("/api/genealogia/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteGenealogia(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Registro de genealogia não encontrado" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir registro de genealogia" });
    }
  });

  // Rota para salvar/atualizar genealogia de um cavalo específico
  app.post("/api/genealogia/:cavaloId", authenticateUser, async (req, res) => {
    try {
      const cavaloId = parseInt(req.params.cavaloId as string);
      const user_id = req.headers['user-id'] as string;
      
      if (!user_id) {
        return res.status(401).json({ message: "ID do usuário não fornecido" });
      }

      console.log(`POST /api/genealogia/${cavaloId}: Salvando genealogia para usuário ${user_id}`);
      console.log('Dados recebidos:', JSON.stringify(req.body, null, 2));

      // Verificar se o cavalo existe e pertence ao usuário
      const cavaloResult = await pool.query(`
        SELECT id FROM cavalos WHERE id = $1 AND user_id = $2
      `, [cavaloId, user_id]);
      
      if (cavaloResult.rows.length === 0) {
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }

      const {
        pai_nome,
        mae_nome,
        avo_paterno_nome,
        avo_paterna_nome,
        avo_materno_nome,
        avo_materna_nome
      } = req.body;

      // Verificar se já existe registro de genealogia
      const existingGenealogia = await pool.query(`
        SELECT id FROM genealogia WHERE horse_id = $1
      `, [cavaloId]);

      let genealogiaResult;
      
      if (existingGenealogia.rows.length > 0) {
        // Atualizar registro existente
        genealogiaResult = await pool.query(`
          UPDATE genealogia 
          SET 
            pai = $2,
            mae = $3,
            avo_paterno = $4,
            avo_paterna = $5,
            avo_materno = $6,
            avo_materna = $7
          WHERE horse_id = $1 
          RETURNING *
        `, [cavaloId, pai_nome, mae_nome, avo_paterno_nome, avo_paterna_nome, avo_materno_nome, avo_materna_nome]);
        
        console.log(`Genealogia atualizada para cavalo ${cavaloId}`);
      } else {
        // Criar novo registro
        genealogiaResult = await pool.query(`
          INSERT INTO genealogia (
            horse_id, pai, mae, avo_paterno, avo_paterna, 
            avo_materno, avo_materna
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING *
        `, [cavaloId, pai_nome, mae_nome, avo_paterno_nome, avo_paterna_nome, avo_materno_nome, avo_materna_nome]);
        
        console.log(`Nova genealogia criada para cavalo ${cavaloId}`);
      }

      res.json({
        success: true,
        message: "Genealogia salva com sucesso",
        genealogia: genealogiaResult.rows[0]
      });

    } catch (error) {
      console.error(`Erro ao salvar genealogia:`, error);
      res.status(500).json({ message: "Erro ao salvar genealogia" });
    }
  });

  // Rota para buscar dados de genealogia de um cavalo específico
  app.get("/api/genealogia/:cavaloId", authenticateUser, async (req, res) => {
    try {
      const cavaloId = parseInt(req.params.cavaloId as string);
      const user_id = req.headers['user-id'] as string;
      
      if (!user_id) {
        return res.status(401).json({ message: "ID do usuário não fornecido" });
      }

      console.log(`GET /api/genealogia/${cavaloId}: Buscando genealogia para usuário ${user_id}`);

      // Verificar se o cavalo existe e pertence ao usuário
      const cavaloResult = await pool.query(`
        SELECT id FROM cavalos WHERE id = $1 AND user_id = $2
      `, [cavaloId, user_id]);
      
      if (cavaloResult.rows.length === 0) {
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }

      // Buscar dados de genealogia na tabela genealogia
      const genealogiaResult = await pool.query(`
        SELECT * FROM genealogia WHERE horse_id = $1
      `, [cavaloId]);
      
      if (genealogiaResult.rows.length === 0) {
        return res.status(404).json({ message: "Dados de genealogia não encontrados para este cavalo" });
      }

      const genealogia = genealogiaResult.rows[0];
      
      res.json({
        genealogia: genealogia,
        cavaloId: cavaloId
      });
      
    } catch (error) {
      console.error(`Erro ao buscar genealogia do cavalo ${req.params.cavaloId}:`, error);
      res.status(500).json({ message: "Erro ao buscar dados de genealogia" });
    }
  });

  // Rota para calcular coeficiente de consanguinidade
  app.get("/api/genealogia/:cavaloId/coeficiente", authenticateUser, async (req, res) => {
    try {
      const cavaloId = parseInt(req.params.cavaloId as string);
      const user_id = req.headers['user-id'] as string;
      
      if (!user_id) {
        return res.status(401).json({ message: "ID do usuário não fornecido" });
      }

      console.log(`GET /api/genealogia/${cavaloId}/coeficiente: Calculando coeficiente para usuário ${user_id}`);

      // Verificar se o cavalo existe e pertence ao usuário
      const cavaloResult = await pool.query(`
        SELECT id FROM cavalos WHERE id = $1 AND user_id = $2
      `, [cavaloId, user_id]);
      
      if (cavaloResult.rows.length === 0) {
        return res.status(404).json({ message: "Cavalo não encontrado" });
      }

      // Buscar dados de genealogia
      const genealogiaResult = await pool.query(`
        SELECT * FROM genealogia WHERE horse_id = $1
      `, [cavaloId]);

      if (genealogiaResult.rows.length === 0) {
        return res.json({
          valor: 0,
          interpretacao: "Sem dados de genealogia disponíveis",
          recomendacao: "Cadastre as informações de genealogia para calcular o coeficiente"
        });
      }

      // Algoritmo simplificado de cálculo de coeficiente de consanguinidade
      // Em um sistema real, seria necessário um algoritmo mais complexo
      const genealogia = genealogiaResult.rows[0];
      let coeficiente = 0;

      // Verificar se há ancestrais comuns conhecidos (lógica simplificada)
      const ancestrais = [
        genealogia.pai,
        genealogia.mae,
        genealogia.avo_paterno,
        genealogia.avo_paterna,
        genealogia.avo_materno,
        genealogia.avo_materna
      ].filter(Boolean);

      // Detectar nomes duplicados (ancestrais comuns)
      const nomesUnicos = new Set(ancestrais);
      const duplicatas = ancestrais.length - nomesUnicos.size;
      
      if (duplicatas > 0) {
        coeficiente = duplicatas * 6.25; // Cada duplicata contribui 6.25%
      }

      // Buscar por IDs duplicados na genealogia mais profunda
      const idsAncestros = [
        genealogia.avo_paterno_id,
        genealogia.avo_paterna_id,
        genealogia.avo_materno_id,
        genealogia.avo_materna_id
      ].filter(Boolean);

      const idsUnicos = new Set(idsAncestros);
      const duplicatasId = idsAncestros.length - idsUnicos.size;
      
      if (duplicatasId > 0) {
        coeficiente += duplicatasId * 12.5; // IDs duplicados têm maior peso
      }

      // Limitar o coeficiente máximo
      coeficiente = Math.min(coeficiente, 50);

      let interpretacao, recomendacao;
      
      if (coeficiente === 0) {
        interpretacao = "Nenhuma consanguinidade detectada com os dados disponíveis";
        recomendacao = "Excelente diversidade genética. Cruzamento recomendado.";
      } else if (coeficiente <= 6.25) {
        interpretacao = "Consanguinidade baixa - aceitável";
        recomendacao = "Nível seguro de consanguinidade. Cruzamento pode ser realizado.";
      } else if (coeficiente <= 12.5) {
        interpretacao = "Consanguinidade moderada - atenção necessária";
        recomendacao = "Monitorar descendentes para problemas genéticos.";
      } else {
        interpretacao = "Consanguinidade alta - risco elevado";
        recomendacao = "Evitar este cruzamento. Buscar parceiros com linhagens diferentes.";
      }

      res.json({
        valor: coeficiente,
        interpretacao,
        recomendacao
      });

    } catch (error) {
      console.error(`Erro ao calcular coeficiente:`, error);
      res.status(500).json({ message: "Erro ao calcular coeficiente de consanguinidade" });
    }
  });

  app.get("/api/cavalos/:horse_id/consanguinidade", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const coeficiente = await storage.calcularConsanguinidade(horse_id, user_id);
      res.json({ coeficiente });
    } catch (error) {
      res.status(500).json({ message: "Erro ao calcular coeficiente de consanguinidade" });
    }
  });

  // Rota principal veterinário - MVP crítico
  app.get("/api/veterinario", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      console.log(`[VETERINARIO] Buscando dados para user_id: ${user_id}`);
      
      // Using pool.query directly for consistency with other endpoints
      const result = await pool.query(
        'SELECT * FROM veterinario WHERE "user_id" = $1 ORDER BY "data_procedimento" DESC', 
        [user_id]
      );
      
      console.log(`[VETERINARIO] Encontrados ${result.rows?.length || 0} registros`);
      
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      });
      
      res.json(result.rows || []);
    } catch (error) {
      console.error("Erro detalhado ao buscar procedimentos veterinários:", error);
      res.status(500).json({ 
        message: "Erro ao buscar registros veterinários",
        error: error.message 
      });
    }
  });

  // Status do sistema para administradores
  app.get("/api/system-status", async (req, res) => {
    try {
      const user_id = parseInt(req.headers["user-id"] as string) || 1;
      
      // Verificar se é admin (ID 1 para admin)
      if (user_id !== 1) {
        return res.status(403).json({ message: "Acesso negado. Somente administradores." });
      }

      // Buscar estatísticas do sistema
      const systemStats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM cavalos) as total_cavalos,
          (SELECT COUNT(DISTINCT user_id) FROM cavalos) as total_usuarios,
          (SELECT COUNT(*) FROM manejos WHERE created_at >= NOW() - INTERVAL '24 hours') as manejos_hoje,
          (SELECT COUNT(*) FROM veterinario WHERE created_at >= NOW() - INTERVAL '24 hours') as veterinario_hoje,
          (SELECT COUNT(*) FROM nutricao WHERE created_at >= NOW() - INTERVAL '24 hours') as nutricao_hoje,
          (SELECT COUNT(*) FROM reproducao WHERE created_at >= NOW() - INTERVAL '24 hours') as reproducao_hoje
      `);

      const moduleHealth = await Promise.all([
        pool.query('SELECT COUNT(*) FROM cavalos LIMIT 1').then(() => ({ module: 'Cavalos', status: 'online' })).catch(() => ({ module: 'Cavalos', status: 'error' })),
        pool.query('SELECT COUNT(*) FROM manejos LIMIT 1').then(() => ({ module: 'Manejos', status: 'online' })).catch(() => ({ module: 'Manejos', status: 'error' })),
        pool.query('SELECT COUNT(*) FROM nutricao LIMIT 1').then(() => ({ module: 'Nutrição', status: 'online' })).catch(() => ({ module: 'Nutrição', status: 'error' })),
        pool.query('SELECT COUNT(*) FROM veterinario LIMIT 1').then(() => ({ module: 'Veterinário', status: 'online' })).catch(() => ({ module: 'Veterinário', status: 'error' })),
        pool.query('SELECT COUNT(*) FROM reproducao LIMIT 1').then(() => ({ module: 'Reprodução', status: 'online' })).catch(() => ({ module: 'Reprodução', status: 'error' })),
        pool.query('SELECT COUNT(*) FROM medidas_morfologicas LIMIT 1').then(() => ({ module: 'Morfologia', status: 'online' })).catch(() => ({ module: 'Morfologia', status: 'error' }))
      ]);

      const stats = systemStats.rows[0];
      
      res.json({
        status: 'operational',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: 'connected',
        statistics: {
          totalCavalos: parseInt(stats.total_cavalos),
          totalUsuarios: parseInt(stats.total_usuarios),
          atividadeHoje: {
            manejos: parseInt(stats.manejos_hoje),
            veterinario: parseInt(stats.veterinario_hoje),
            nutricao: parseInt(stats.nutricao_hoje),
            reproducao: parseInt(stats.reproducao_hoje)
          }
        },
        modules: moduleHealth,
        mvpStatus: '95% completo',
        health: 'good'
      });
    } catch (error) {
      console.error("Erro ao buscar status do sistema:", error);
      res.status(500).json({ 
        status: 'error',
        message: "Erro ao buscar status do sistema",
        error: error.message 
      });
    }
  });
  
  // Rotas para Sugestões de Cruzamento
  app.get("/api/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const user_id = req.body.user_id;
      const sugestoes = await storage.getSugestoesCruzamento(user_id);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestões de cruzamento" });
    }
  });

  app.get("/api/cavalos/:horse_id/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const sugestoes = await storage.getSugestoesCruzamentoByHorse(horse_id, user_id);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestões de cruzamento para o cavalo" });
    }
  });

  app.get("/api/sugestoes-cruzamento/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const sugestao = await storage.getSugestoesCruzamentoById(id, user_id);
      
      if (!sugestao) {
        return res.status(404).json({ message: "Sugestão de cruzamento não encontrada" });
      }
      
      res.json(sugestao);
    } catch (error) {
      res.status(500).json({ message: "Erro ao buscar sugestão de cruzamento" });
    }
  });

  app.post("/api/sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const sugestaoData = insertSugestoesCruzamentoSchema.parse(req.body);
      const sugestao = await storage.createSugestaoCruzamento(sugestaoData);
      res.status(201).json(sugestao);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.format() });
      }
      res.status(500).json({ message: "Erro ao criar sugestão de cruzamento" });
    }
  });

  app.delete("/api/sugestoes-cruzamento/:id", authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      const user_id = req.body.user_id;
      const success = await storage.deleteSugestaoCruzamento(id, user_id);
      
      if (!success) {
        return res.status(404).json({ message: "Sugestão de cruzamento não encontrada" });
      }
      
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Erro ao excluir sugestão de cruzamento" });
    }
  });

  app.post("/api/cavalos/:horse_id/gerar-sugestoes-cruzamento", authenticateUser, async (req, res) => {
    try {
      const horse_id = parseInt(req.params.horse_id as string);
      const user_id = req.body.user_id;
      const { objetivo } = req.body;
      
      if (!objetivo) {
        return res.status(400).json({ message: "Objetivo do cruzamento é obrigatório" });
      }
      
      const sugestoes = await storage.gerarSugestoesCruzamento(horse_id, objetivo, user_id);
      res.json(sugestoes);
    } catch (error) {
      res.status(500).json({ message: "Erro ao gerar sugestões de cruzamento" });
    }
  });

  // Rota de teste temporária para depuração da busca por registro
  app.post("/api/testar-busca-registro", authenticateUser, async (req, res) => {
    try {
      const { registro } = req.body;
      console.log("Testando busca por registro:", registro);
      
      // Importando a função do serviço de importação ABCCC
      const { buscarCavaloPorRegistro } = require('./abccc-import-service');
      
      const resultado = await buscarCavaloPorRegistro(registro);
      console.log("Resultado da busca:", resultado ? "Encontrado" : "Não encontrado");
      
      return res.json({ 
        sucesso: true, 
        resultado,
        encontrado: !!resultado,
        detalhes: resultado ? {
          id: resultado.id,
          nome: resultado.name,
          numero_registro: resultado.numero_registro
        } : null
      });
    } catch (error) {
      console.error("Erro na busca por registro:", error);
      return res.status(500).json({ 
        sucesso: false, 
        erro: (error as Error).message,
        stack: (error as Error).stack
      });
    }
  });
  
  // API para raças de cavalos
  app.get('/api/racas', authenticateUser, async (req: any, res: any) => {
    try {
      const result = await pool.query('SELECT * FROM racas ORDER BY nome');
      res.json(result.rows);
    } catch (error) {
      console.error('Erro ao buscar raças:', error);
      res.status(500).json({ error: 'Erro ao buscar raças' });
    }
  });
  
  // API para buscar dados de cavalos da ABCCC pelo registro
  app.get('/api/abccc/cavalo/:registro', authenticateUser, async (req: express.Request, res: express.Response) => {
    // Obter o registro do parâmetro da URL
    const registroParam = req.params.registro as string;
    
    try {
      // Validação do registro
      if (!registroParam || registroParam.trim() === '') {
        return res.status(400).json({ 
          error: 'Parâmetro inválido', 
          message: 'Número de registro é obrigatório' 
        });
      }
      
      // Formatação do registro (mantém apenas letras e números)
      const registroFormatado = registroParam.trim().toUpperCase();
      
      // Log detalhado da requisição
      const user_id = req.body?.user_id || 'anônimo';
      console.log(`Solicitação para buscar dados da ABCCC - Registro: ${registroFormatado}, Usuário: ${user_id}`);
      
      // Removendo a lista de registros problemáticos específicos
      // pois implementamos uma solução robusta que funciona com qualquer registro

      // BUSCA AUTOMÁTICA NO SITE TEMPORARIAMENTE DESABILITADA
      // Devido a problemas de certificado SSL no site da ABCCC
      console.log(`Busca automática desabilitada para registro ${registroFormatado} - Use importação via PDF`);
      
      return res.status(503).json({
        error: 'Busca automática temporariamente indisponível',
        message: 'A busca automática no site da ABCCC está temporariamente desabilitada devido a problemas técnicos. Use a funcionalidade "Importar PDF da ABCCC" para cadastrar cavalos.',
        registro: registroFormatado,
        alternativa: {
          metodo: 'Importação via PDF',
          descricao: 'Faça o download do PDF do cavalo no site da ABCCC e use a opção "Importar PDF" no sistema',
          localizacao: 'Página de cadastro de cavalos → Botão "Importar PDF da ABCCC"'
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Erro não tratado na rota ABCCC: ${errorMessage}`);
      
      // Log do stack trace para depuração
      if (error instanceof Error && error.stack) {
        console.log(`Stack trace: ${error.stack}`);
      }
      
      return res.status(500).json({ 
        error: 'Erro interno do servidor', 
        message: 'Ocorreu um erro ao tentar buscar os dados do cavalo. Por favor, tente novamente mais tarde.'
      });
    }
  });

  // === NUTRITION LOGISTICS ROUTES ===
  
  // Temporarily mount inline routes until dynamic imports are fixed
  app.use('/api/feeding', (await import('./routes/feeding.js')).default);
  app.use('/api/stock', (await import('./routes/stock.js')).default);
  app.use('/api/reports', (await import('./routes/nutrition-reports.js')).default);
  
  // Endpoint específico para foto de perfil do cavalo - OTIMIZADO
  app.get('/api/cavalos/:horse_id/photo', 
    photoOptimizationMiddleware,
    intelligentRateLimit,
    (req, res) => {
      // Cache agressivo para 404s evitando requisições desnecessárias
      res.set({
        'Cache-Control': 'public, max-age=3600', // 1 hora de cache para 404s
        'X-Photo-Cache': 'optimized'
      });
      res.status(404).send();
    }
  );

  // Endpoint para buscar pelagens com autocomplete
  app.get('/api/pelagens', async (req, res) => {
    try {
      const search = req.query.search as string || '';
      const limit = parseInt(req.query.limit as string) || 20;
      
      const { searchPelagens } = await import('./pelagem-resolver-service');
      const pelagens = await searchPelagens(search, limit);
      
      res.json({ success: true, data: pelagens });
    } catch (error) {
      console.error('Erro ao buscar pelagens:', error);
      res.status(500).json({ success: false, error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para buscar pelagem por ID
  app.get('/api/pelagens/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id as string);
      
      if (!id || isNaN(id)) {
        return res.status(400).json({ success: false, error: 'ID inválido' });
      }
      
      const result = await pool.query(
        'SELECT id, nome, descricao, fonte FROM pelagens WHERE id = $1',
        [id]
      );
      
      if (result.rows.length === 0) {
        return res.status(404).json({ success: false, error: 'Pelagem não encontrada' });
      }
      
      res.json({ success: true, data: result.rows[0] });
    } catch (error) {
      console.error('Erro ao buscar pelagem por ID:', error);
      res.status(500).json({ success: false, error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para criar nova pelagem
  app.post('/api/pelagens', async (req, res) => {
    try {
      const { nome } = req.body;
      
      if (!nome || nome.trim() === '') {
        return res.status(400).json({ success: false, error: 'Nome da pelagem é obrigatório' });
      }
      
      const { resolvePelagemId } = await import('./pelagem-resolver-service');
      const pelagemId = await resolvePelagemId(nome.trim(), 'Manual');
      
      // Buscar pelagem criada para retornar dados completos
      const result = await pool.query(
        'SELECT id, nome FROM pelagens WHERE id = $1',
        [pelagemId]
      );
      
      if (result.rows.length > 0) {
        res.json({ success: true, data: result.rows[0] });
      } else {
        res.status(500).json({ success: false, error: 'Erro ao recuperar pelagem criada' });
      }
    } catch (error) {
      console.error('Erro ao criar pelagem:', error);
      res.status(500).json({ success: false, error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para estatísticas de pelagens
  app.get('/api/pelagens/stats', async (req, res) => {
    try {
      const { getPelagemStats } = await import('./pelagem-resolver-service');
      const stats = await getPelagemStats();
      
      res.json({ success: true, data: stats });
    } catch (error) {
      console.error('Erro ao buscar estatísticas de pelagens:', error);
      res.status(500).json({ success: false, error: 'Erro interno do servidor' });
    }
  });
  

  // Rota do Assistente IA
  app.post("/api/chat", authenticateUser, async (req, res) => {
    try {
      const { messages, horse_id } = req.body;
      const user_id = req.body.user_id;
      
      console.log("[CHAT] Processando mensagem do assistente:", { user_id, horse_id, messageCount: messages?.length });
      
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        return res.status(400).json({ 
          error: "Mensagens são obrigatórias",
          message: "Forneça um array de mensagens válido"
        });
      }
      
      // Verificar se a chave OpenAI está configurada
      if (!process.env.OPENAI_API_KEY) {
        return res.status(503).json({
          error: "Serviço indisponível",
          message: "Assistente IA não configurado. Configure OPENAI_API_KEY."
        });
      }
      
      // Importar e usar o serviço OpenAI
      const { generateChatResponse } = await import('./services/openai');
      
      const horseIdNum = horse_id ? parseInt(horse_id) : undefined;
      const response = await generateChatResponse(messages, horseIdNum, user_id);
      
      console.log("[CHAT] Resposta gerada com sucesso");
      res.json({ content: response });
      
    } catch (error) {
      console.error('[CHAT] Erro ao processar mensagem:', error);
      res.status(500).json({ 
        error: "Erro interno",
        message: "Não foi possível processar sua mensagem. Tente novamente."
      });
    }
  });


  // === STATISTICS MODULE ROUTES ===
  
  app.get('/api/estatisticas', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = req.body.user_id;
      console.log(`[ESTATISTICAS] Buscando dados para user_id: ${user_id}`);
      
      // Buscar dados básicos do plantel
      const cavalos = await storage.getCavalos(user_id);
      const manejos = await storage.getManejos(user_id);
      const procedimentosVet = await storage.getProcedimentosVet(user_id);
      
      // Calcular estatísticas básicas
      const totalCavalos = cavalos.length;
      const cavalosAtivos = cavalos.filter(c => c.status === 'ativo').length;
      const cavalosInativos = totalCavalos - cavalosAtivos;
      
      // Estatísticas por sexo
      const estatisticasSexo = cavalos.reduce((acc, cavalo) => {
        const sexo = cavalo.sexo || 'Não informado';
        acc[sexo] = (acc[sexo] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      // Estatísticas por raça
      const estatisticasRaca = cavalos.reduce((acc, cavalo) => {
        const raca = cavalo.breed || 'Não informada';
        acc[raca] = (acc[raca] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      // Estatísticas por idade
      const agora = new Date();
      const estatisticasIdade = cavalos.reduce((acc, cavalo) => {
        if (cavalo.birth_date) {
          const idade = Math.floor((agora.getTime() - new Date(cavalo.birth_date).getTime()) / (1000 * 60 * 60 * 24 * 365.25));
          const faixaEtaria = idade < 5 ? 'Jovem (0-4 anos)' : 
                            idade < 15 ? 'Adulto (5-14 anos)' : 
                            'Idoso (15+ anos)';
          acc[faixaEtaria] = (acc[faixaEtaria] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);
      
      // Estatísticas de manejos
      const totalManejos = manejos.length;
      const manejosRecentes = manejos.filter(m => {
        const dataExecucao = new Date(m.data_execucao);
        const umMesAtras = new Date();
        umMesAtras.setMonth(umMesAtras.getMonth() - 1);
        return dataExecucao >= umMesAtras;
      }).length;
      
      // Estatísticas veterinárias
      const totalProcedimentosVet = procedimentosVet.length;
      const vacinas = procedimentosVet.filter(p => p.tipo === 'vacinacao').length;
      const vermifugacoes = procedimentosVet.filter(p => p.tipo === 'vermifugacao').length;
      const consultas = procedimentosVet.filter(p => p.tipo === 'consulta').length;
      
      const estatisticas = {
        plantel: {
          total: totalCavalos,
          ativos: cavalosAtivos,
          inativos: cavalosInativos,
          porSexo: estatisticasSexo,
          porRaca: estatisticasRaca,
          porIdade: estatisticasIdade
        },
        manejos: {
          total: totalManejos,
          recentes: manejosRecentes
        },
        veterinario: {
          total: totalProcedimentosVet,
          vacinas,
          vermifugacoes,
          consultas
        },
        resumo: {
          dataAtualizacao: new Date().toISOString(),
          periodoAnalise: '30 dias'
        }
      };
      
      console.log(`[ESTATISTICAS] Geradas estatísticas: ${totalCavalos} cavalos, ${totalManejos} manejos`);
      res.json(estatisticas);
      
    } catch (error) {
      console.error('[ESTATISTICAS] Erro:', error);
      res.status(500).json({ 
        error: 'Erro ao gerar estatísticas',
        message: 'Não foi possível calcular as estatísticas do sistema'
      });
    }
  });
  
  // === FINANCIAL MODULE ROUTES ===
  
  // Integrar rotas financeiras refatoradas diretamente
  const financeiroRouter = express.Router();
  
  // Importar serviço financeiro inline para evitar problemas de module
  const { financeiroService } = require('./services/financeiro.service');
  
  // Rotas de categorias
  financeiroRouter.get('/categorias', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      const categorias = await financeiroService.getCategorias(user_id);
      res.json(categorias);
    } catch (error) {
      console.error('Erro ao buscar categorias:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  financeiroRouter.post('/categorias', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      const data = { ...req.body, user_id };
      const categoria = await financeiroService.createCategoria(data);
      res.status(201).json(categoria);
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  // Rotas de lançamentos
  financeiroRouter.get('/lancamentos', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      const lancamentos = await financeiroService.getLancamentos(user_id, req.query as any);
      res.json(lancamentos);
    } catch (error) {
      console.error('Erro ao buscar lançamentos:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  financeiroRouter.post('/lancamentos', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      const data = { ...req.body, user_id };
      const lancamento = await financeiroService.createLancamento(data);
      res.status(201).json(lancamento);
    } catch (error) {
      console.error('Erro ao criar lançamento:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  // Rota de relatório mensal
  financeiroRouter.get('/relatorio-mensal', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      const { mes, ano } = req.query as { mes: string; ano: string };
      const relatorio = await financeiroService.getRelatorioMensal(user_id, parseInt(mes), parseInt(ano));
      res.json(relatorio);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  // Rota para inicializar categorias padrão
  financeiroRouter.post('/init-categorias', authenticateUser, async (req: Request, res: Response) => {
    try {
      const user_id = (req as any).user.id;
      await financeiroService.initializeCategoriasPadrao(user_id);
      res.json({ message: 'Categorias padrão inicializadas com sucesso' });
    } catch (error) {
      console.error('Erro ao inicializar categorias:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });
  
  app.use('/api/financeiro', financeiroRouter);
  console.log('✅ Rotas financeiras integradas com sucesso');

  // Rota para download do backup do banco de dados
  app.get('/api/backup/download', (req: express.Request, res: express.Response) => {
    try {
      const backupFiles = fs.readdirSync('.').filter(file => file.startsWith('backup_equigestor_') && file.endsWith('.sql'));
      
      if (backupFiles.length === 0) {
        return res.status(404).json({ error: 'Nenhum backup encontrado' });
      }
      
      // Pegar o backup mais recente
      const latestBackup = backupFiles.sort().reverse()[0];
      const filePath = path.join(process.cwd(), latestBackup as string);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'Arquivo de backup não encontrado' });
      }
      
      res.setHeader('Content-Disposition', `attachment; filename="${latestBackup}"`);
      res.setHeader('Content-Type', 'application/sql');
      
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
      
      console.log(`Backup baixado: ${latestBackup}`);
    } catch (error) {
      console.error('Erro ao baixar backup:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // === CONFIGURAÇÕES E PERFIL DE USUÁRIO ===


  // Endpoint para alterar senha do usuário
  app.put('/api/users/change-password', authenticateUser, async (req: express.Request, res: express.Response) => {
    try {
      const userId = (req as any).user_id;
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({ 
          error: 'Senha atual e nova senha são obrigatórias' 
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({ 
          error: 'A nova senha deve ter pelo menos 6 caracteres' 
        });
      }

      // Buscar usuário atual
      const userQuery = await pool.query(
        'SELECT password_hash, password FROM users WHERE id = $1',
        [userId]
      );

      if (userQuery.rows.length === 0) {
        return res.status(404).json({ error: 'Usuário não encontrado' });
      }

      const user = userQuery.rows[0];
      let isCurrentPasswordValid = false;

      // Verificar senha atual (suporte a ambos os formatos)
      if (user.password_hash) {
        // Senha com hash bcrypt
        const bcrypt = require('bcrypt');
        isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      } else if (user.password) {
        // Senha em texto plano (legacy)
        isCurrentPasswordValid = currentPassword === user.password;
      }

      if (!isCurrentPasswordValid) {
        return res.status(400).json({ 
          error: 'Senha atual incorreta' 
        });
      }

      // Gerar hash da nova senha
      const bcrypt = require('bcrypt');
      const newPasswordHash = await bcrypt.hash(newPassword, 10);

      // Atualizar senha no banco
      await pool.query(
        'UPDATE users SET password_hash = $1, password = NULL WHERE id = $2',
        [newPasswordHash, userId]
      );

      logger.info(`[PERFIL] Senha alterada para usuário ${userId}`);

      res.json({
        message: 'Senha alterada com sucesso'
      });

    } catch (error) {
      console.error('[PERFIL] Erro ao alterar senha:', error);
      res.status(500).json({ 
        error: 'Erro interno do servidor ao alterar senha'
      });
    }
  });

  // Endpoint para upload de avatar (simulado - sem multer por simplicidade)
  app.post('/api/upload/avatar', authenticateUser, async (req: express.Request, res: express.Response) => {
    try {
      // Por enquanto, retornamos uma URL simulada
      // Em produção, seria integrado com multer e storage (AWS S3, etc.)
      const avatarUrl = '/uploads/avatars/default-avatar.png';
      
      console.log('[UPLOAD] Upload de avatar simulado');
      
      res.json({
        message: 'Avatar enviado com sucesso',
        url: avatarUrl
      });

    } catch (error) {
      console.error('[UPLOAD] Erro no upload do avatar:', error);
      res.status(500).json({ 
        error: 'Erro interno do servidor no upload'
      });
    }
  });

  // ===== ROTAS FINANCEIRAS =====
  // Implementação inline devido a incompatibilidades de módulos ES/CommonJS
  console.log('📊 Configurando rotas financeiras inline...');

  // Endpoint para listar categorias financeiras
  app.get('/api/financeiro/categorias', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      
      const categorias = await db
        .select()
        .from(categorias_financeiras)
        .where(eq(categorias_financeiras.user_id, user_id))
        .orderBy(asc(categorias_financeiras.nome));
      
      res.json(categorias);
    } catch (error) {
      console.error('Erro ao buscar categorias:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para criar categoria financeira
  app.post('/api/financeiro/categorias', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      const { nome, tipo, descricao = '', ativo = true } = req.body;
      
      if (!nome || !tipo) {
        return res.status(400).json({ error: 'Nome e tipo são obrigatórios' });
      }
      
      if (!['receita', 'despesa'].includes(tipo)) {
        return res.status(400).json({ error: 'Tipo deve ser receita ou despesa' });
      }
      
      const [categoria] = await db
        .insert(categorias_financeiras)
        .values({ nome, tipo, descricao, ativo, user_id })
        .returning();
      
      res.status(201).json(categoria);
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para atualizar categoria financeira
  app.put('/api/financeiro/categorias/:id', authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const user_id = req.user?.id || 1;
      const { nome, tipo, descricao, ativo } = req.body;
      
      if (isNaN(id)) {
        return res.status(400).json({ error: 'ID inválido' });
      }
      
      const [categoria] = await db
        .update(categorias_financeiras)
        .set({ nome, tipo, descricao, ativo })
        .where(and(
          eq(categorias_financeiras.id, id),
          eq(categorias_financeiras.user_id, user_id)
        ))
        .returning();
      
      if (!categoria) {
        return res.status(404).json({ error: 'Categoria não encontrada' });
      }
      
      res.json(categoria);
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para excluir categoria financeira
  app.delete('/api/financeiro/categorias/:id', authenticateUser, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const user_id = req.user?.id || 1;
      
      if (isNaN(id)) {
        return res.status(400).json({ error: 'ID inválido' });
      }
      
      // Verificar se há lançamentos vinculados
      const lancamentosVinculados = await db
        .select({ count: count() })
        .from(lancamentos_financeiros)
        .where(eq(lancamentos_financeiros.categoria_id, id));
      
      if (lancamentosVinculados[0]?.count > 0) {
        return res.status(400).json({ 
          error: 'Não é possível excluir categoria com lançamentos vinculados' 
        });
      }
      
      const [categoria] = await db
        .delete(categorias_financeiras)
        .where(and(
          eq(categorias_financeiras.id, id),
          eq(categorias_financeiras.user_id, user_id)
        ))
        .returning();
      
      if (!categoria) {
        return res.status(404).json({ error: 'Categoria não encontrada' });
      }
      
      res.status(204).end();
    } catch (error) {
      console.error('Erro ao excluir categoria:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para listar lançamentos financeiros
  app.get('/api/financeiro/lancamentos', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      const { dataInicio, dataFim, tipo, categoria_id, cavalo_id } = req.query;
      
      let query = db
        .select({
          id: lancamentos_financeiros.id,
          data: lancamentos_financeiros.data,
          tipo: lancamentos_financeiros.tipo,
          categoria_id: lancamentos_financeiros.categoria_id,
          categoria_nome: categorias_financeiras.nome,
          descricao: lancamentos_financeiros.descricao,
          valor: lancamentos_financeiros.valor,
          cavalo_id: lancamentos_financeiros.cavalo_id,
          cavalo_nome: cavalos.name,
          observacoes: lancamentos_financeiros.observacoes,
          created_at: lancamentos_financeiros.created_at
        })
        .from(lancamentos_financeiros)
        .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
        .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
        .where(eq(lancamentos_financeiros.user_id, user_id));
      
      // Adicionar filtros opcionais
      const conditions = [eq(lancamentos_financeiros.user_id, user_id)];
      
      if (dataInicio) {
        conditions.push(gte(lancamentos_financeiros.data, dataInicio as string));
      }
      
      if (dataFim) {
        conditions.push(lte(lancamentos_financeiros.data, dataFim as string));
      }
      
      if (tipo) {
        conditions.push(eq(lancamentos_financeiros.tipo, tipo as 'receita' | 'despesa'));
      }
      
      if (categoria_id) {
        conditions.push(eq(lancamentos_financeiros.categoria_id, parseInt(categoria_id as string)));
      }
      
      if (cavalo_id) {
        conditions.push(eq(lancamentos_financeiros.cavalo_id, parseInt(cavalo_id as string)));
      }
      
      const lancamentos = await query
        .where(and(...conditions))
        .orderBy(desc(lancamentos_financeiros.data), desc(lancamentos_financeiros.created_at));
      
      res.json(lancamentos);
    } catch (error) {
      console.error('Erro ao buscar lançamentos:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para criar lançamento financeiro
  app.post('/api/financeiro/lancamentos', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      const { data, tipo, categoria_id, descricao, valor, cavalo_id, observacoes } = req.body;
      
      if (!data || !tipo || !categoria_id || !descricao || valor === undefined) {
        return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
      }
      
      if (!['receita', 'despesa'].includes(tipo)) {
        return res.status(400).json({ error: 'Tipo deve ser receita ou despesa' });
      }
      
      const [lancamento] = await db
        .insert(lancamentos_financeiros)
        .values({
          data,
          tipo,
          categoria_id: parseInt(categoria_id),
          descricao,
          valor: parseFloat(valor),
          cavalo_id: cavalo_id ? parseInt(cavalo_id) : null,
          observacoes,
          user_id
        })
        .returning();
      
      res.status(201).json(lancamento);
    } catch (error) {
      console.error('Erro ao criar lançamento:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para relatório mensal
  app.get('/api/financeiro/relatorio-mensal', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      const { mes, ano } = req.query;
      
      if (!mes || !ano) {
        return res.status(400).json({ error: 'Mês e ano são obrigatórios' });
      }
      
      const mesInt = parseInt(mes as string);
      const anoInt = parseInt(ano as string);
      
      if (mesInt < 1 || mesInt > 12) {
        return res.status(400).json({ error: 'Mês deve estar entre 1 e 12' });
      }
      
      const dataInicio = `${anoInt}-${mesInt.toString().padStart(2, '0')}-01`;
      const dataFim = `${anoInt}-${mesInt.toString().padStart(2, '0')}-31`;
      
      // Totais por tipo
      const totaisReceitas = await db
        .select({ total: sum(lancamentos_financeiros.valor) })
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          eq(lancamentos_financeiros.tipo, 'receita'),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim)
        ));
      
      const totaisDespesas = await db
        .select({ total: sum(lancamentos_financeiros.valor) })
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          eq(lancamentos_financeiros.tipo, 'despesa'),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim)
        ));
      
      const receitas = Number(totaisReceitas[0]?.total || 0);
      const despesas = Number(totaisDespesas[0]?.total || 0);
      const saldo = receitas - despesas;
      
      const relatorio = {
        periodo: { mes: mesInt, ano: anoInt },
        resumo: { receitas, despesas, saldo },
        porCategoria: [], // Implementação simplificada
        porCavalo: [] // Implementação simplificada
      };
      
      res.json(relatorio);
    } catch (error) {
      console.error('Erro ao gerar relatório mensal:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  // Endpoint para inicializar categorias padrão
  app.post('/api/financeiro/init-categorias', authenticateUser, async (req, res) => {
    try {
      const user_id = req.user?.id || 1;
      
      const categoriasPadrao = [
        // Receitas
        { nome: 'Pensão', tipo: 'receita' as const, descricao: 'Receitas de pensão de cavalos', user_id, ativo: true },
        { nome: 'Serviços', tipo: 'receita' as const, descricao: 'Receitas de serviços prestados', user_id, ativo: true },
        { nome: 'Vendas', tipo: 'receita' as const, descricao: 'Vendas de cavalos e produtos', user_id, ativo: true },
        
        // Despesas
        { nome: 'Alimentação', tipo: 'despesa' as const, descricao: 'Ração, feno, suplementos', user_id, ativo: true },
        { nome: 'Medicamentos', tipo: 'despesa' as const, descricao: 'Medicamentos e vacinas', user_id, ativo: true },
        { nome: 'Veterinário', tipo: 'despesa' as const, descricao: 'Consultas e tratamentos veterinários', user_id, ativo: true },
        { nome: 'Ferrageamento', tipo: 'despesa' as const, descricao: 'Serviços de ferrageamento', user_id, ativo: true },
        { nome: 'Manutenção', tipo: 'despesa' as const, descricao: 'Manutenção de instalações', user_id, ativo: true }
      ];
      
      // Verificar categorias existentes
      const existentes = await db
        .select({ nome: categorias_financeiras.nome })
        .from(categorias_financeiras)
        .where(eq(categorias_financeiras.user_id, user_id));
      
      const nomesExistentes = existentes.map(c => c.nome);
      const novasCategorias = categoriasPadrao.filter(c => !nomesExistentes.includes(c.nome));
      
      if (novasCategorias.length > 0) {
        await db.insert(categorias_financeiras).values(novasCategorias);
      }
      
      res.json({ 
        message: 'Categorias padrão inicializadas com sucesso',
        criadas: novasCategorias.length,
        existentes: nomesExistentes.length
      });
    } catch (error) {
      console.error('Erro ao inicializar categorias padrão:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  });

  console.log('✅ Rotas financeiras configuradas inline com sucesso');
}
