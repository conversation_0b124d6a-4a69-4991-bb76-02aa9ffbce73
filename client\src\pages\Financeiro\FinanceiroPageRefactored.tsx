import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  PieChart,
  BarChart3,
  Plus,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Settings
} from 'lucide-react';
import { formatMoney } from '@/utils/finance';
import { useTitle } from '@/hooks/use-title';
import { 
  useCategorias, 
  useLancamentos, 
  useRelatorioMensal,
  useInitCategoriasPadrao 
} from '@/hooks/use-financeiro';
import LancamentosPageRefactored from './LancamentosPageRefactored';
import CategoriasPageRefactored from './CategoriasPageRefactored';
import RelatoriosPageRefactored from './RelatoriosPageRefactored';

/**
 * Página principal do módulo financeiro refatorada
 * 
 * Aplicando princípios de engenharia de software sênior:
 * - Separação de responsabilidades
 * - Hooks personalizados para lógica de negócio
 * - Componentes modulares e reutilizáveis
 * - Gerenciamento de estado centralizado
 * - Tratamento robusto de erros
 * - Interface responsiva e acessível
 */
export default function FinanceiroPageRefactored() {
  useTitle('Financeiro - EquiGestor');
  
  const [activeTab, setActiveTab] = useState('dashboard');
  const [mesAtual] = useState(new Date().getMonth() + 1);
  const [anoAtual] = useState(new Date().getFullYear());

  // Hooks para dados financeiros
  const { data: categorias, isLoading: loadingCategorias, error: errorCategorias } = useCategorias();
  const { data: lancamentos, isLoading: loadingLancamentos, error: errorLancamentos } = useLancamentos();
  const { data: relatorio, isLoading: loadingRelatorio, error: errorRelatorio } = useRelatorioMensal(mesAtual, anoAtual);
  const initCategorias = useInitCategoriasPadrao();

  // Estados derivados para o dashboard
  const hasData = categorias && categorias.length > 0;
  const totalReceitas = relatorio?.resumo.receitas || 0;
  const totalDespesas = relatorio?.resumo.despesas || 0;
  const saldoMensal = relatorio?.resumo.saldo || 0;
  const isPositive = saldoMensal >= 0;

  // Função para inicializar categorias padrão
  const handleInitCategorias = async () => {
    try {
      await initCategorias.mutateAsync();
    } catch (error) {
      console.error('Erro ao inicializar categorias:', error);
    }
  };

  // Renderização do componente de inicialização
  const renderInitializationCard = () => (
    <Card className="border-amber-200 bg-amber-50">
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-amber-700 font-semibold">
            <Settings className="h-5 w-5" />
            Configure as categorias padrão para começar
          </div>
          <p className="text-sm text-gray-600">
            Crie categorias básicas como Alimentação, Medicamentos, Serviços Veterinários, 
            Pensão e outras essenciais para gestão equina.
          </p>
          <Button 
            onClick={handleInitCategorias} 
            disabled={initCategorias.isPending}
            className="w-full"
          >
            {initCategorias.isPending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Inicializando...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Criar Categorias Padrão
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  // Renderização dos cards de resumo
  const renderSummaryCards = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6">
      <Card className="border-green-200 bg-green-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm sm:text-base text-green-700 flex items-center">
            <TrendingUp className="h-4 w-4 mr-1.5" />
            Receitas
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-lg sm:text-xl font-bold text-green-600">
            {formatMoney(totalReceitas)}
          </div>
          {(relatorio?.porCategoria.filter(c => c.tipo === 'receita').length ?? 0) > 0 && (
            <p className="text-sm text-green-600 mt-2">
              {relatorio?.porCategoria.filter(c => c.tipo === 'receita').length} categorias
            </p>
          )}
        </CardContent>
      </Card>
      
      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm sm:text-base text-red-700 flex items-center">
            <TrendingDown className="h-4 w-4 mr-1.5" />
            Despesas
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-lg sm:text-xl font-bold text-red-600">
            {formatMoney(totalDespesas)}
          </div>
          {(relatorio?.porCategoria.filter(c => c.tipo === 'despesa').length ?? 0) > 0 && (
            <p className="text-sm text-red-600 mt-2">
              {relatorio?.porCategoria.filter(c => c.tipo === 'despesa').length} categorias
            </p>
          )}
        </CardContent>
      </Card>
      
      <Card className={`border-2 ${isPositive ? 'border-blue-200 bg-blue-50' : 'border-orange-200 bg-orange-50'} sm:col-span-2 lg:col-span-1`}>
        <CardHeader className="pb-2">
          <CardTitle className={`text-sm sm:text-base flex items-center ${isPositive ? 'text-blue-700' : 'text-orange-700'}`}>
            <DollarSign className="h-4 w-4 mr-1.5" />
            Saldo
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className={`text-lg sm:text-xl font-bold ${isPositive ? 'text-blue-600' : 'text-orange-600'}`}>
            {formatMoney(saldoMensal)}
          </div>
          <div className="mt-2 flex items-center">
            <Badge variant={isPositive ? 'default' : 'destructive'} className="text-xs">
              {isPositive ? 'Positivo' : 'Negativo'}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // Renderização da visualização de categorias
  const renderCategoriesOverview = () => {
    if (!relatorio?.porCategoria.length) return null;

    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <PieChart className="h-4 w-4" />
            Resumo por Categoria
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {relatorio?.porCategoria.slice(0, 5).map((categoria, index) => (
              <div key={categoria.categoria_nome + index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge 
                    variant={categoria.tipo === 'receita' ? 'default' : 'destructive'}
                    className="w-20 justify-center"
                  >
                    {categoria.tipo === 'receita' ? 'Receita' : 'Despesa'}
                  </Badge>
                  <span className="font-medium">{categoria.categoria_nome}</span>
                </div>
                <span className={`font-bold ${categoria.tipo === 'receita' ? 'text-green-600' : 'text-red-600'}`}>
                  {formatMoney(categoria.total)}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Renderização de estados de erro
  const renderErrorState = () => (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Erro ao carregar dados financeiros. Verifique sua conexão e tente novamente.
      </AlertDescription>
    </Alert>
  );

  // Renderização de estado de carregamento
  const renderLoadingState = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-none">
      {/* Tratamento de erros */}
      {(errorCategorias || errorLancamentos || errorRelatorio) && renderErrorState()}

      {/* Conteúdo principal */}
      {loadingCategorias || loadingLancamentos || loadingRelatorio ? (
        renderLoadingState()
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
            <TabsTrigger value="dashboard" className="text-xs sm:text-sm p-2">Dashboard</TabsTrigger>
            <TabsTrigger value="lancamentos" className="text-xs sm:text-sm p-2">Lançamentos</TabsTrigger>
            <TabsTrigger value="categorias" className="text-xs sm:text-sm p-2">Categorias</TabsTrigger>
            <TabsTrigger value="relatorios" className="text-xs sm:text-sm p-2">Relatórios</TabsTrigger>
          </TabsList>
            
            <TabsContent value="dashboard" className="space-y-4 mt-3">
              {!hasData ? (
                renderInitializationCard()
              ) : (
                <>
                  {renderSummaryCards()}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {renderCategoriesOverview()}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2 text-base">
                          <BarChart3 className="h-4 w-4" />
                          Ações Rápidas
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <Button 
                          onClick={() => setActiveTab('lancamentos')}
                          className="w-full justify-start text-sm sm:text-base"
                          variant="outline"
                        >
                          <Plus className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                          Novo Lançamento
                        </Button>
                        <Button 
                          onClick={() => setActiveTab('categorias')}
                          className="w-full justify-start text-sm sm:text-base"
                          variant="outline"
                        >
                          <Settings className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                          Gerenciar Categorias
                        </Button>
                        <Button 
                          onClick={() => setActiveTab('relatorios')}
                          className="w-full justify-start"
                          variant="outline"
                        >
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Ver Relatórios
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </>
              )}
            </TabsContent>
            
            <TabsContent value="lancamentos" className="mt-3">
              <LancamentosPageRefactored />
            </TabsContent>
            
            <TabsContent value="categorias" className="mt-3">
              <CategoriasPageRefactored />
            </TabsContent>
            
            <TabsContent value="relatorios" className="mt-3">
              <RelatoriosPageRefactored />
            </TabsContent>
        </Tabs>
      )}
    </div>
  );
}