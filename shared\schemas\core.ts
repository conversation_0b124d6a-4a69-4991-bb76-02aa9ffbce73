import { pgEnum } from "drizzle-orm/pg-core";
import { z } from "zod";

// ============================================================================
// CORE ENUMS - Shared across all domains
// ============================================================================

export const horseStatusEnum = pgEnum('horse_status', [
  'Ativo', 'Vendido', 'Falecido', 'Doado', 'nao_informado',
  'Reprodução', 'Competição', 'Tratamento', 'Aposentado'
]);

export const horseSexoEnum = pgEnum('horse_sexo', [
  'macho', 'femea', 'macho_castrado'
]);

export const tipoCoberturaEnum = pgEnum('tipo_cobertura', [
  'monta_natural', 'inseminacao', 'transferencia_embriao'
]);

export const estadoReproducaoEnum = pgEnum('estado_reproducao', [
  'pre_cobertura', 'em_cobertura', 'prenhez_confirmada', 
  'parto_realizado', 'abortado', 'nao_prenhe'
]);

// ============================================================================
// CORE VALIDATION SCHEMAS
// ============================================================================

// Base ID validation
export const idSchema = z.number().int().positive();

// Date validation - accepts ISO string and converts to Date
export const dateSchema = z.string().datetime().or(z.date()).transform((val) => {
  return typeof val === 'string' ? new Date(val) : val;
});

// Optional date for forms
export const optionalDateSchema = z.string().optional().nullable().transform((val) => {
  if (!val) return null;
  return new Date(val);
});

// Status validation
export const statusSchema = z.enum([
  'Ativo', 'Vendido', 'Falecido', 'Doado', 'nao_informado',
  'Reprodução', 'Competição', 'Tratamento', 'Aposentado'
]);

// Sexo validation
export const sexoSchema = z.enum(['macho', 'femea', 'macho_castrado']);

// Currency validation (for prices)
export const currencySchema = z.number().nonnegative().multipleOf(0.01);

// Peso validation (kg)
export const pesoSchema = z.number().positive().max(2000); // Max 2000kg

// Altura validation (cm)
export const alturaSchema = z.number().positive().max(300); // Max 300cm

// ============================================================================
// COMMON FIELD SCHEMAS
// ============================================================================

export const nomeSchema = z.string()
  .min(1, 'Nome é obrigatório')
  .max(100, 'Nome deve ter no máximo 100 caracteres')
  .regex(/^[a-zA-ZÀ-ÿ\s\-'\.]+$/, 'Nome deve conter apenas letras, espaços, hífens e apostrofes');

export const observacoesSchema = z.string()
  .max(1000, 'Observações devem ter no máximo 1000 caracteres')
  .optional()
  .nullable();

export const numeroRegistroSchema = z.string()
  .regex(/^[A-Z0-9\-]+$/, 'Número de registro deve conter apenas letras maiúsculas, números e hífens')
  .optional()
  .nullable();

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type HorseStatus = typeof horseStatusEnum.enumValues[number];
export type HorseSexo = typeof horseSexoEnum.enumValues[number];
export type TipoCobertura = typeof tipoCoberturaEnum.enumValues[number];
export type EstadoReproducao = typeof estadoReproducaoEnum.enumValues[number];

// Base audit fields
export interface AuditFields {
  created_at: Date;
  updatedAt: Date;
}

// User context
export interface UserContext {
  user_id: number;
}