import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { LayoutWrapper } from '@/components/Layout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Calendar, ArrowLeftToLine, ArrowRightFromLine, ChevronDown, ChevronUp } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Interface para definir a estrutura de um registro de saída/retorno de animal
 */
interface MovimentacaoAnimal {
  id: number;
  tipo: 'saida' | 'retorno';
  cavaloId: number;
  cavaloNome: string;
  dataMovimentacao: string;
  motivoMovimentacao: string;
  destino?: string;
  origem?: string;
  previsaoRetorno?: string;
  data_saida?: string;
  observacoes?: string;
  responsavel: string;
  status: 'ativo' | 'finalizado' | 'cancelado';
}

/**
 * Componente principal da página de Saída e Retorno de Animais
 */
export function SaidaRetornoPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('todos');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showAddSaida, setShowAddSaida] = useState(false);
  const [showAddRetorno, setShowAddRetorno] = useState(false);

  // Estados para o formulário de nova saída
  const [formSaida, setFormSaida] = useState({
    cavaloId: '',
    dataMovimentacao: format(new Date(), 'yyyy-MM-dd'),
    motivoMovimentacao: '',
    destino: '',
    previsaoRetorno: '',
    observacoes: '',
    responsavel: ''
  });

  // Estados para o formulário de novo retorno
  const [formRetorno, setFormRetorno] = useState({
    cavaloId: '',
    dataMovimentacao: format(new Date(), 'yyyy-MM-dd'),
    motivoMovimentacao: '',
    origem: '',
    data_saida: '',
    observacoes: '',
    responsavel: ''
  });

  // Função para ordenar a tabela
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Função para renderizar o ícone de ordenação
  const renderSortIcon = (column: string) => {
    if (sortColumn !== column) {
      return null;
    }
    return sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  // Função para formatar a data no padrão brasileiro
  const formatarData = (dataString: string) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (error) {
      return dataString;
    }
  };

  // Filtragem e ordenação dos dados
  const filteredMovimentacoes = movimentacoesData
    .filter(mov => 
      (selectedTab === 'todos' || selectedTab === mov.tipo) &&
      (mov.cavaloNome.toLowerCase().includes(searchTerm.toLowerCase()) ||
       mov.motivoMovimentacao.toLowerCase().includes(searchTerm.toLowerCase()) ||
       (mov.destino && mov.destino.toLowerCase().includes(searchTerm.toLowerCase())) ||
       (mov.origem && mov.origem.toLowerCase().includes(searchTerm.toLowerCase())))
    )
    .sort((a, b) => {
      if (!sortColumn) return 0;
      
      let valA: any = a[sortColumn as keyof MovimentacaoAnimal];
      let valB: any = b[sortColumn as keyof MovimentacaoAnimal];
      
      if (typeof valA === 'string') {
        valA = valA.toLowerCase();
        valB = valB.toLowerCase();
      }
      
      if (valA < valB) return sortDirection === 'asc' ? -1 : 1;
      if (valA > valB) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  // Handler para adicionar nova saída
  const handleAddSaida = () => {
    console.log('Nova saída:', formSaida);
    setShowAddSaida(false);
    // Reset do formulário
    setFormSaida({
      cavaloId: '',
      dataMovimentacao: format(new Date(), 'yyyy-MM-dd'),
      motivoMovimentacao: '',
      destino: '',
      previsaoRetorno: '',
      observacoes: '',
      responsavel: ''
    });
  };

  // Handler para adicionar novo retorno
  const handleAddRetorno = () => {
    console.log('Novo retorno:', formRetorno);
    setShowAddRetorno(false);
    // Reset do formulário
    setFormRetorno({
      cavaloId: '',
      dataMovimentacao: format(new Date(), 'yyyy-MM-dd'),
      motivoMovimentacao: '',
      origem: '',
      data_saida: '',
      observacoes: '',
      responsavel: ''
    });
  };

  return (
    <LayoutWrapper pageTitle="Saída e Retorno de Animais">
      <div className="space-y-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Dialog open={showAddSaida} onOpenChange={setShowAddSaida}>
              <DialogTrigger asChild>
                <Button className="gap-1">
                  <ArrowRightFromLine className="h-4 w-4" />
                  Registrar Saída
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Registrar Saída de Animal</DialogTitle>
                  <DialogDescription>
                    Preencha os dados para registrar a saída do animal.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label htmlFor="cavaloId" className="block text-sm font-medium mb-1">Animal</label>
                      <Select value={formSaida.cavaloId} onValueChange={value => setFormSaida({...formSaida, cavaloId: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o animal" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">Trovão</SelectItem>
                          <SelectItem value="2">Pegasus</SelectItem>
                          <SelectItem value="3">Tempestade</SelectItem>
                          <SelectItem value="4">Relâmpago</SelectItem>
                          <SelectItem value="5">Luna</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label htmlFor="dataMovimentacao" className="block text-sm font-medium mb-1">Data da Saída</label>
                      <Input 
                        type="date" 
                        id="dataMovimentacao" 
                        value={formSaida.dataMovimentacao}
                        onChange={e => setFormSaida({...formSaida, dataMovimentacao: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="motivoMovimentacao" className="block text-sm font-medium mb-1">Motivo da Saída</label>
                      <Select value={formSaida.motivoMovimentacao} onValueChange={value => setFormSaida({...formSaida, motivoMovimentacao: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o motivo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Competição">Competição</SelectItem>
                          <SelectItem value="Tratamento">Tratamento médico/veterinário</SelectItem>
                          <SelectItem value="Venda">Venda</SelectItem>
                          <SelectItem value="Empréstimo">Empréstimo</SelectItem>
                          <SelectItem value="Reprodução">Reprodução</SelectItem>
                          <SelectItem value="Eventos">Eventos</SelectItem>
                          <SelectItem value="Outros">Outros</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label htmlFor="destino" className="block text-sm font-medium mb-1">Destino</label>
                      <Input 
                        id="destino" 
                        placeholder="Para onde o animal está indo" 
                        value={formSaida.destino}
                        onChange={e => setFormSaida({...formSaida, destino: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="previsaoRetorno" className="block text-sm font-medium mb-1">Previsão de Retorno</label>
                      <Input 
                        type="date" 
                        id="previsaoRetorno" 
                        value={formSaida.previsaoRetorno}
                        onChange={e => setFormSaida({...formSaida, previsaoRetorno: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="responsavel" className="block text-sm font-medium mb-1">Responsável</label>
                      <Input 
                        id="responsavel" 
                        placeholder="Nome do responsável" 
                        value={formSaida.responsavel}
                        onChange={e => setFormSaida({...formSaida, responsavel: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="observacoes" className="block text-sm font-medium mb-1">Observações</label>
                      <Textarea 
                        id="observacoes" 
                        placeholder="Observações adicionais" 
                        value={formSaida.observacoes}
                        onChange={e => setFormSaida({...formSaida, observacoes: e.target.value})}
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddSaida(false)}>Cancelar</Button>
                  <Button onClick={handleAddSaida}>Registrar Saída</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={showAddRetorno} onOpenChange={setShowAddRetorno}>
              <DialogTrigger asChild>
                <Button className="gap-1" variant="secondary">
                  <ArrowLeftToLine className="h-4 w-4" />
                  Registrar Retorno
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Registrar Retorno de Animal</DialogTitle>
                  <DialogDescription>
                    Preencha os dados para registrar o retorno do animal.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label htmlFor="cavaloId" className="block text-sm font-medium mb-1">Animal</label>
                      <Select value={formRetorno.cavaloId} onValueChange={value => setFormRetorno({...formRetorno, cavaloId: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o animal" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">Trovão</SelectItem>
                          <SelectItem value="2">Pegasus</SelectItem>
                          <SelectItem value="3">Tempestade</SelectItem>
                          <SelectItem value="4">Relâmpago</SelectItem>
                          <SelectItem value="5">Luna</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label htmlFor="dataMovimentacao" className="block text-sm font-medium mb-1">Data do Retorno</label>
                      <Input 
                        type="date" 
                        id="dataMovimentacao" 
                        value={formRetorno.dataMovimentacao}
                        onChange={e => setFormRetorno({...formRetorno, dataMovimentacao: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="motivoMovimentacao" className="block text-sm font-medium mb-1">Motivo do Retorno</label>
                      <Select value={formRetorno.motivoMovimentacao} onValueChange={value => setFormRetorno({...formRetorno, motivoMovimentacao: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o motivo" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Fim de Competição">Fim de Competição</SelectItem>
                          <SelectItem value="Alta médica">Alta médica</SelectItem>
                          <SelectItem value="Fim de Empréstimo">Fim de Empréstimo</SelectItem>
                          <SelectItem value="Fim de Reprodução">Fim de Reprodução</SelectItem>
                          <SelectItem value="Fim de Evento">Fim de Evento</SelectItem>
                          <SelectItem value="Compra">Compra</SelectItem>
                          <SelectItem value="Outros">Outros</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label htmlFor="origem" className="block text-sm font-medium mb-1">Origem</label>
                      <Input 
                        id="origem" 
                        placeholder="De onde o animal está retornando" 
                        value={formRetorno.origem}
                        onChange={e => setFormRetorno({...formRetorno, origem: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="data_saida" className="block text-sm font-medium mb-1">Data da Saída Original</label>
                      <Input 
                        type="date" 
                        id="data_saida" 
                        value={formRetorno.data_saida}
                        onChange={e => setFormRetorno({...formRetorno, data_saida: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="responsavel" className="block text-sm font-medium mb-1">Responsável</label>
                      <Input 
                        id="responsavel" 
                        placeholder="Nome do responsável" 
                        value={formRetorno.responsavel}
                        onChange={e => setFormRetorno({...formRetorno, responsavel: e.target.value})}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="observacoes" className="block text-sm font-medium mb-1">Observações</label>
                      <Textarea 
                        id="observacoes" 
                        placeholder="Observações adicionais" 
                        value={formRetorno.observacoes}
                        onChange={e => setFormRetorno({...formRetorno, observacoes: e.target.value})}
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddRetorno(false)}>Cancelar</Button>
                  <Button onClick={handleAddRetorno}>Registrar Retorno</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        <Tabs defaultValue="todos" value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-3 max-w-md">
            <TabsTrigger value="todos">Todos</TabsTrigger>
            <TabsTrigger value="saida">Saídas</TabsTrigger>
            <TabsTrigger value="retorno">Retornos</TabsTrigger>
          </TabsList>
          
          <TabsContent value="todos" className="mt-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Saídas e Retornos de Animais</CardTitle>
                <CardDescription>
                  Visualize e gerencie os registros de saídas e retornos dos animais.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px] cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID {renderSortIcon('id')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('tipo')}>
                          <div className="flex items-center">
                            Tipo {renderSortIcon('tipo')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('cavaloNome')}>
                          <div className="flex items-center">
                            Animal {renderSortIcon('cavaloNome')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('dataMovimentacao')}>
                          <div className="flex items-center">
                            Data {renderSortIcon('dataMovimentacao')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('motivoMovimentacao')}>
                          <div className="flex items-center">
                            Motivo {renderSortIcon('motivoMovimentacao')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('destino')}>
                          <div className="flex items-center">
                            Destino/Origem {renderSortIcon('destino')}
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                          <div className="flex items-center">
                            Status {renderSortIcon('status')}
                          </div>
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMovimentacoes.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            Nenhum registro de saída ou retorno encontrado.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredMovimentacoes.map((movimentacao) => (
                          <TableRow key={movimentacao.id}>
                            <TableCell className="font-medium">{movimentacao.id}</TableCell>
                            <TableCell>
                              <Badge variant={movimentacao.tipo === 'saida' ? 'destructive' : 'default'}>
                                {movimentacao.tipo === 'saida' ? 'Saída' : 'Retorno'}
                              </Badge>
                            </TableCell>
                            <TableCell>{movimentacao.cavaloNome}</TableCell>
                            <TableCell className="whitespace-nowrap">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                {formatarData(movimentacao.dataMovimentacao)}
                              </div>
                            </TableCell>
                            <TableCell>{movimentacao.motivoMovimentacao}</TableCell>
                            <TableCell>
                              {movimentacao.tipo === 'saida' ? movimentacao.destino : movimentacao.origem}
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                movimentacao.status === 'ativo' ? 'outline' : 
                                movimentacao.status === 'finalizado' ? 'secondary' : 'destructive'
                              }>
                                {movimentacao.status === 'ativo' ? 'Ativo' : 
                                 movimentacao.status === 'finalizado' ? 'Finalizado' : 'Cancelado'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="saida" className="mt-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Saídas de Animais</CardTitle>
                <CardDescription>
                  Visualize e gerencie os registros de saídas dos animais.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px]">ID</TableHead>
                        <TableHead>Animal</TableHead>
                        <TableHead>Data da Saída</TableHead>
                        <TableHead>Motivo</TableHead>
                        <TableHead>Destino</TableHead>
                        <TableHead>Previsão de Retorno</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMovimentacoes.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            Nenhum registro de saída encontrado.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredMovimentacoes.map((movimentacao) => (
                          <TableRow key={movimentacao.id}>
                            <TableCell className="font-medium">{movimentacao.id}</TableCell>
                            <TableCell>{movimentacao.cavaloNome}</TableCell>
                            <TableCell className="whitespace-nowrap">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                {formatarData(movimentacao.dataMovimentacao)}
                              </div>
                            </TableCell>
                            <TableCell>{movimentacao.motivoMovimentacao}</TableCell>
                            <TableCell>{movimentacao.destino}</TableCell>
                            <TableCell>
                              {movimentacao.previsaoRetorno ? formatarData(movimentacao.previsaoRetorno) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                movimentacao.status === 'ativo' ? 'outline' : 
                                movimentacao.status === 'finalizado' ? 'secondary' : 'destructive'
                              }>
                                {movimentacao.status === 'ativo' ? 'Ativo' : 
                                 movimentacao.status === 'finalizado' ? 'Finalizado' : 'Cancelado'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="retorno" className="mt-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Retornos de Animais</CardTitle>
                <CardDescription>
                  Visualize e gerencie os registros de retornos dos animais.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px]">ID</TableHead>
                        <TableHead>Animal</TableHead>
                        <TableHead>Data do Retorno</TableHead>
                        <TableHead>Motivo</TableHead>
                        <TableHead>Origem</TableHead>
                        <TableHead>Data da Saída</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMovimentacoes.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            Nenhum registro de retorno encontrado.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredMovimentacoes.map((movimentacao) => (
                          <TableRow key={movimentacao.id}>
                            <TableCell className="font-medium">{movimentacao.id}</TableCell>
                            <TableCell>{movimentacao.cavaloNome}</TableCell>
                            <TableCell className="whitespace-nowrap">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                {formatarData(movimentacao.dataMovimentacao)}
                              </div>
                            </TableCell>
                            <TableCell>{movimentacao.motivoMovimentacao}</TableCell>
                            <TableCell>{movimentacao.origem}</TableCell>
                            <TableCell>
                              {movimentacao.data_saida ? formatarData(movimentacao.data_saida) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                movimentacao.status === 'ativo' ? 'outline' : 
                                movimentacao.status === 'finalizado' ? 'secondary' : 'destructive'
                              }>
                                {movimentacao.status === 'ativo' ? 'Ativo' : 
                                 movimentacao.status === 'finalizado' ? 'Finalizado' : 'Cancelado'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}

// Dados de exemplo para movimentações
const movimentacoesData: MovimentacaoAnimal[] = [
  {
    id: 1,
    tipo: 'saida',
    cavaloId: 1,
    cavaloNome: 'Trovão',
    dataMovimentacao: '2025-03-15',
    motivoMovimentacao: 'Competição',
    destino: 'Haras São João - São Paulo',
    previsaoRetorno: '2025-03-20',
    observacoes: 'Participação no Campeonato Brasileiro de Hipismo',
    responsavel: 'Carlos Silva',
    status: 'finalizado'
  },
  {
    id: 2,
    tipo: 'retorno',
    cavaloId: 1,
    cavaloNome: 'Trovão',
    dataMovimentacao: '2025-03-20',
    motivoMovimentacao: 'Fim de Competição',
    origem: 'Haras São João - São Paulo',
    data_saida: '2025-03-15',
    observacoes: 'Conquistou o 2º lugar no Campeonato Brasileiro de Hipismo',
    responsavel: 'Carlos Silva',
    status: 'finalizado'
  },
  {
    id: 3,
    tipo: 'saida',
    cavaloId: 2,
    cavaloNome: 'Pegasus',
    dataMovimentacao: '2025-03-22',
    motivoMovimentacao: 'Tratamento',
    destino: 'Clínica Veterinária EqüiSaúde',
    previsaoRetorno: '2025-03-25',
    observacoes: 'Tratamento de lesão no posterior direito',
    responsavel: 'Dr. Marcos Oliveira',
    status: 'finalizado'
  },
  {
    id: 4,
    tipo: 'retorno',
    cavaloId: 2,
    cavaloNome: 'Pegasus',
    dataMovimentacao: '2025-03-26',
    motivoMovimentacao: 'Alta médica',
    origem: 'Clínica Veterinária EqüiSaúde',
    data_saida: '2025-03-22',
    observacoes: 'Completou tratamento com sucesso. Recomendação de repouso por mais 5 dias.',
    responsavel: 'Dr. Marcos Oliveira',
    status: 'finalizado'
  },
  {
    id: 5,
    tipo: 'saida',
    cavaloId: 3,
    cavaloNome: 'Tempestade',
    dataMovimentacao: '2025-03-28',
    motivoMovimentacao: 'Reprodução',
    destino: 'Central de Reprodução Equina',
    previsaoRetorno: '2025-04-10',
    observacoes: 'Coleta de material genético para inseminação artificial',
    responsavel: 'Dra. Amanda Soares',
    status: 'ativo'
  },
  {
    id: 6,
    tipo: 'saida',
    cavaloId: 4,
    cavaloNome: 'Relâmpago',
    dataMovimentacao: '2025-03-29',
    motivoMovimentacao: 'Empréstimo',
    destino: 'Haras Montana',
    previsaoRetorno: '2025-05-01',
    observacoes: 'Empréstimo para treinamento de jovens cavaleiros',
    responsavel: 'João Mendes',
    status: 'ativo'
  },
  {
    id: 7,
    tipo: 'saida',
    cavaloId: 5,
    cavaloNome: 'Luna',
    dataMovimentacao: '2025-03-10',
    motivoMovimentacao: 'Venda',
    destino: 'Haras Alvorada',
    responsavel: 'Maria Santos',
    status: 'finalizado'
  }
];

export default SaidaRetornoPage;