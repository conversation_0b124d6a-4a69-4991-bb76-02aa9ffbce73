/**
 * Serviço de Importação de Registros Genealógicos da ABCCC - FIXED VERSION
 * Corrigido para resolver "syntax error at or near 'where'"
 */

import path from "path";
import * as fs from "fs";
import { db } from "./db";
import { cavalos, genealogia, pelagens } from "../shared/schema";
import { eq, ilike } from "drizzle-orm";
import {
  CavaloInfo,
  GenealogiaInfo,
  processarPdfABCCC,
} from "./abccc-pdf-parser";
import { processarPdfComOpenAI } from "./openai-pdf-service";
import {
  ABCCCScrapedData,
  buscarDadosABCCCPorRegistro,
  enriquecerDadosComSite,
} from "./abccc-scraper-service";
import { updateGenealogySync } from "./genealogy-sync-service";
import { InsertCavalo } from "../shared/insert-schemas";
import { <PERSON>ava<PERSON> } from "../shared/schema";
import { getModuleLogger } from "./logger";

const logger = getModuleLogger("abccc-import");

// Função auxiliar para normalizar valores de sexo
function normalizarSexo(sexo: string): string {
  const sexoNormalizado = sexo.toLowerCase().trim();
  
  if (sexoNormalizado.includes("macho") && sexoNormalizado.includes("castrado")) {
    return "Macho (Castrado)";
  } else if (sexoNormalizado.includes("garanhão") || sexoNormalizado.includes("garanhao")) {
    return "Garanhão";
  } else if (sexoNormalizado.includes("égua") || sexoNormalizado.includes("egua")) {
    return "Égua";
  } else if (sexoNormalizado.includes("macho")) {
    return "Macho";
  } else if (sexoNormalizado.includes("fêmea") || sexoNormalizado.includes("femea")) {
    return "Fêmea";
  }
  
  return "Macho"; // Default fallback
}

/**
 * Atualiza um cavalo existente com informações do ABCCC
 * FIXED: Removed unsafe SQL interpolation and multiple where clauses
 */
async function atualizarCavaloExistente(
  id: number,
  info: CavaloInfo,
  user_id: number,
): Promise<Cavalo> {
  try {
    // Verificar se a pelagem corresponde a uma entrada na tabela de pelagens
    let pelagemId = null;

    if (info.pelagem) {
      try {
        // Tentar encontrar a pelagem na tabela
        logger.debug(
          `Buscando pelagem correspondente a "${info.pelagem}" na tabela de pelagens`,
        );
        const pelagemEncontrada = await db
          .select()
          .from(pelagens)
          .where(eq(pelagens.nome, info.pelagem))
          .limit(1);

        if (pelagemEncontrada && pelagemEncontrada.length > 0) {
          logger.info(
            `Pelagem encontrada no banco: ${info.pelagem} (ID=${pelagemEncontrada[0].id})`,
          );
          pelagemId = pelagemEncontrada[0].id;
        } else {
          // FIXED: Use ilike instead of unsafe SQL interpolation
          const pelagemSimilar = await db
            .select()
            .from(pelagens)
            .where(ilike(pelagens.nome, info.pelagem))
            .limit(1);

          if (pelagemSimilar && pelagemSimilar.length > 0) {
            logger.info(
              `Pelagem encontrada no banco (case insensitive): ${info.pelagem} (ID=${pelagemSimilar[0].id})`,
            );
            pelagemId = pelagemSimilar[0].id;
          } else {
            // Se a pelagem não for encontrada, registrá-la automaticamente na tabela de pelagens
            try {
              logger.info(
                `Pelagem "${info.pelagem}" não encontrada na tabela. Criando novo registro de pelagem.`,
              );
              const novaPelagem = await db
                .insert(pelagens)
                .values({
                  nome: info.pelagem,
                  fonte: 'ABCCC Import',
                } as any)
                .returning();

              if (novaPelagem && novaPelagem.length > 0) {
                logger.info(
                  `Nova pelagem criada com sucesso: "${info.pelagem}" (ID=${novaPelagem[0].id})`,
                );
                pelagemId = novaPelagem[0].id;
              } else {
                logger.warn(
                  `Falha ao criar pelagem: "${info.pelagem}". Será usada apenas como texto.`,
                );
              }
            } catch (createErr: any) {
              logger.warn(
                `Erro ao criar pelagem: ${createErr.message}. Será usada apenas como texto no campo cor`,
              );
            }
          }
        }
      } catch (err: any) {
        logger.warn(
          `Erro ao buscar pelagem na tabela: ${err.message}. Usando apenas o texto da pelagem.`,
        );
      }
    }

    // FIXED: Always ensure at least one field to update
    const atualizacoes: Partial<InsertCavalo> = {};
    
    // Always include name if provided
    if (info.nome && info.nome.trim() !== '') {
      atualizacoes.name = info.nome.trim();
    }

    // Add optional fields if present
    if (info.registro && info.registro.trim()) atualizacoes.numero_registro = info.registro.trim();
    if (info.sexo && info.sexo.trim()) atualizacoes.sexo = normalizarSexo(info.sexo) as any;
    if (info.nascimento && info.nascimento.trim()) {
      atualizacoes.birth_date = info.nascimento.includes('/') ? 
        info.nascimento.split('/').reverse().join('-') : 
        info.nascimento;
    }
    if (info.pelagem && info.pelagem.trim()) atualizacoes.cor = info.pelagem.trim();
    if (info.criador && info.criador.trim()) atualizacoes.criador = info.criador.trim();
    if (info.proprietario && info.proprietario.trim()) atualizacoes.proprietario = info.proprietario.trim();
    if (info.inspetor && info.inspetor.trim()) atualizacoes.inspetor = info.inspetor.trim();
    if (info.origem && info.origem.trim()) atualizacoes.origem = info.origem.trim();
    if (pelagemId !== null && pelagemId !== undefined) atualizacoes.pelagem_id = pelagemId;

    // Store titles and observations in notes field
    if (info.titulos || info.observacoes) {
      const notesArray = [];
      if (info.titulos) notesArray.push(`Títulos: ${info.titulos.join(", ")}`);
      if (info.observacoes) notesArray.push(info.observacoes);
      atualizacoes.notes = notesArray.join("\n");
    }

    // FIXED: Validate there are updates to apply
    if (Object.keys(atualizacoes).length === 0) {
      logger.warn(`Nenhuma atualização fornecida para cavalo ${id}, retornando cavalo existente`);
      const cavaloExistente = await db
        .select()
        .from(cavalos)
        .where(eq(cavalos.id, id))
        .limit(1);
      
      if (cavaloExistente.length === 0) {
        throw new Error(`Cavalo com ID ${id} não encontrado no banco de dados`);
      }
      
      return cavaloExistente[0] as any;
    }

    // Update in database with proper logging
    logger.debug(`Atualizando cavalo ${id} com campos:`, Object.keys(atualizacoes));
    const resultado = await db
      .update(cavalos)
      .set(atualizacoes)
      .where(eq(cavalos.id, id))
      .returning();

    if (resultado.length === 0) {
      throw new Error(`Falha ao atualizar cavalo ${id} - nenhuma linha afetada`);
    }

    return resultado[0] as any;
  } catch (error: any) {
    logger.error(`Erro ao atualizar cavalo ${id}: ${error.message}`);
    throw error;
  }
}

// Export the fixed function
export { atualizarCavaloExistente };