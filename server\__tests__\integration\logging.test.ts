/**
 * Testes de integração para o sistema de logging
 */
import express from 'express';
import request from 'supertest';
import { traceMiddleware, TRACE_ID_KEY } from '../../middleware/tracing';
import { logger } from '../../logger';

describe('Sistema de Logging - Integração', () => {
  let app: express.Express;

  beforeEach(() => {
    // Configura uma aplicação Express para testes
    app = express();
    
    // Adiciona o middleware de tracing
    app.use(traceMiddleware);
    
    // Adiciona uma rota de teste
    app.get('/api/test', (req, res) => {
      // Usa o logger da requisição
      (req as any).logger.info({
        msg: 'Teste de log na rota',
        data: { test: true }
      });
      
      res.json({ success: true, traceId: req.headers[TRACE_ID_KEY] });
    });
    
    // Adiciona uma rota que gera erro
    app.get('/api/error', (_req, _res) => {
      throw new Error('Erro de teste');
    });
    
    // Middleware de tratamento de erros
    app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
      res.status(500).json({ error: err.message });
    });
  });

  test('deve adicionar traceId à resposta', async () => {
    // Espiona o método info do logger
    const infoSpy = jest.spyOn(logger, 'info');
    
    const response = await request(app)
      .get('/api/test')
      .set('user-id', '123');
    
    // Verifica se a resposta contém o traceId
    expect(response.status).toBe(200);
    expect(response.body.traceId).toBeDefined();
    expect(typeof response.body.traceId).toBe('string');
    
    // Verifica se o header contém o traceId
    expect(response.headers[TRACE_ID_KEY.toLowerCase()]).toBeDefined();
    expect(response.headers[TRACE_ID_KEY.toLowerCase()]).toBe(response.body.traceId);
    
    // Verifica se o log foi registrado
    expect(infoSpy).toHaveBeenCalled();
  });

  test('deve capturar e registrar erros', async () => {
    // Espiona o método error do logger
    const errorSpy = jest.spyOn(logger, 'error');
    
    const response = await request(app)
      .get('/api/error')
      .set('user-id', '123');
    
    // Verifica se a resposta contém o erro
    expect(response.status).toBe(500);
    expect(response.body.error).toBe('Erro de teste');
    
    // Verifica se o header contém o traceId
    expect(response.headers[TRACE_ID_KEY.toLowerCase()]).toBeDefined();
    
    // Verifica se o erro foi registrado
    expect(errorSpy).toHaveBeenCalled();
  });
});
