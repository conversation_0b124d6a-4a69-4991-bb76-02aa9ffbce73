-- Migration: Convert all database columns to snake_case
-- Date: 2025-06-13
-- Description: Rename all camelCase columns to snake_case and create new enum with snake_case values

BEGIN;

-- Create new enum with snake_case values
CREATE TYPE horse_sexo_v2 AS ENUM ('macho', 'femea', 'macho_castrado', 'garanhao', 'egua');

-- Rename columns in users table
ALTER TABLE users RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in genealogia table
ALTER TABLE genealogia RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE genealogia RENAME COLUMN "avoPaternoId" TO avo_paterno_id;
ALTER TABLE genealogia RENAME COLUMN "avoPaterno" TO avo_paterno;
ALTER TABLE genealogia RENAME COLUMN "avoPaternaId" TO avo_paterna_id;
ALTER TABLE genealogia RENAME COLUMN "avoPaterna" TO avo_paterna;
ALTER TABLE genealogia RENAME COLUMN "avoMaternoId" TO avo_materno_id;
ALTER TABLE genealogia RENAME COLUMN "avoMaterno" TO avo_materno;
ALTER TABLE genealogia RENAME COLUMN "avoMaternaId" TO avo_materna_id;
ALTER TABLE genealogia RENAME COLUMN "avoMaterna" TO avo_materna;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternoPaterno" TO bisavo_paterno_paterno;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternoPaterno_id" TO bisavo_paterno_paterno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternaPaterno" TO bisavo_paterna_paterno;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternaPaterno_id" TO bisavo_paterna_paterno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternoPaterno" TO bisavo_materno_paterno;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternoPaterno_id" TO bisavo_materno_paterno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternaPaterno" TO bisavo_materna_paterno;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternaPaterno_id" TO bisavo_materna_paterno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternoMaterno" TO bisavo_paterno_materno;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternoMaterno_id" TO bisavo_paterno_materno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternaMaterno" TO bisavo_paterna_materno;
ALTER TABLE genealogia RENAME COLUMN "bisavoPaternaMaterno_id" TO bisavo_paterna_materno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternoMaterno" TO bisavo_materno_materno;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternoMaterno_id" TO bisavo_materno_materno_id;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternaMaterno" TO bisavo_materna_materno;
ALTER TABLE genealogia RENAME COLUMN "bisavoMaternaMaterno_id" TO bisavo_materna_materno_id;
ALTER TABLE genealogia RENAME COLUMN "coeficienteConsanguinidade" TO coeficiente_consanguinidade;
ALTER TABLE genealogia RENAME COLUMN "userId" TO user_id;
ALTER TABLE genealogia RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in manejos table (HIGH PRIORITY - causing SQL errors)
ALTER TABLE manejos RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE manejos RENAME COLUMN "userId" TO user_id;
ALTER TABLE manejos RENAME COLUMN "createdAt" TO created_at;
ALTER TABLE manejos RENAME COLUMN "dataVencimento" TO data_vencimento;

-- Rename columns in tasks table
ALTER TABLE tasks RENAME COLUMN "dueDate" TO due_date;
ALTER TABLE tasks RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE tasks RENAME COLUMN "userId" TO user_id;
ALTER TABLE tasks RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in arquivos table
ALTER TABLE arquivos RENAME COLUMN "fileName" TO file_name;
ALTER TABLE arquivos RENAME COLUMN "filePath" TO file_path;
ALTER TABLE arquivos RENAME COLUMN "fileType" TO file_type;
ALTER TABLE arquivos RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE arquivos RENAME COLUMN "userId" TO user_id;
ALTER TABLE arquivos RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in eventos table
ALTER TABLE eventos RENAME COLUMN "horaInicio" TO hora_inicio;
ALTER TABLE eventos RENAME COLUMN "horaFim" TO hora_fim;
ALTER TABLE eventos RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE eventos RENAME COLUMN "userId" TO user_id;
ALTER TABLE eventos RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in procedimentos_vet table
ALTER TABLE procedimentos_vet RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE procedimentos_vet RENAME COLUMN "userId" TO user_id;
ALTER TABLE procedimentos_vet RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in reproducao table
ALTER TABLE reproducao RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE reproducao RENAME COLUMN "padreiroId" TO padreiro_id;
ALTER TABLE reproducao RENAME COLUMN "dataCobertura" TO data_cobertura;
ALTER TABLE reproducao RENAME COLUMN "tipoCobertura" TO tipo_cobertura;
ALTER TABLE reproducao RENAME COLUMN "userId" TO user_id;
ALTER TABLE reproducao RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in nutricao table
ALTER TABLE nutricao RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE nutricao RENAME COLUMN "tipoAlimentacao" TO tipo_alimentacao;
ALTER TABLE nutricao RENAME COLUMN "nomeAlimento" TO nome_alimento;
ALTER TABLE nutricao RENAME COLUMN "unidadeMedida" TO unidade_medida;
ALTER TABLE nutricao RENAME COLUMN "frequenciaDiaria" TO frequencia_diaria;
ALTER TABLE nutricao RENAME COLUMN "custoUnitario" TO custo_unitario;
ALTER TABLE nutricao RENAME COLUMN "custoMensal" TO custo_mensal;
ALTER TABLE nutricao RENAME COLUMN "userId" TO user_id;
ALTER TABLE nutricao RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in morfologia table
ALTER TABLE morfologia RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE morfologia RENAME COLUMN "dataMedicao" TO data_medicao;
ALTER TABLE morfologia RENAME COLUMN "alturaCernelha" TO altura_cernelha;
ALTER TABLE morfologia RENAME COLUMN "alturaDorso" TO altura_dorso;
ALTER TABLE morfologia RENAME COLUMN "alturaGarupa" TO altura_garupa;
ALTER TABLE morfologia RENAME COLUMN "comprimentoCorpo" TO comprimento_corpo;
ALTER TABLE morfologia RENAME COLUMN "comprimentoPescoco" TO comprimento_pescoco;
ALTER TABLE morfologia RENAME COLUMN "larguraPeito" TO largura_peito;
ALTER TABLE morfologia RENAME COLUMN "perimetroToracico" TO perimetro_toracico;
ALTER TABLE morfologia RENAME COLUMN "perimetroPescoco" TO perimetro_pescoco;
ALTER TABLE morfologia RENAME COLUMN "perimetroCanela" TO perimetro_canela;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoCabeca" TO pontuacao_cabeca;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoPescoco" TO pontuacao_pescoco;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoEspalda" TO pontuacao_espalda;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoDorso" TO pontuacao_dorso;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoGarupa" TO pontuacao_garupa;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoMembros" TO pontuacao_membros;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoAprumos" TO pontuacao_aprumos;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoAndamento" TO pontuacao_andamento;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoHarmonia" TO pontuacao_harmonia;
ALTER TABLE morfologia RENAME COLUMN "pontuacaoTotal" TO pontuacao_total;
ALTER TABLE morfologia RENAME COLUMN "responsavelMedicao" TO responsavel_medicao;
ALTER TABLE morfologia RENAME COLUMN "userId" TO user_id;
ALTER TABLE morfologia RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in morfologia_arquivos table
ALTER TABLE morfologia_arquivos RENAME COLUMN "morfologiaId" TO morfologia_id;
ALTER TABLE morfologia_arquivos RENAME COLUMN "arquivoId" TO arquivo_id;
ALTER TABLE morfologia_arquivos RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in desempenho_historico table
ALTER TABLE desempenho_historico RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE desempenho_historico RENAME COLUMN "userId" TO user_id;
ALTER TABLE desempenho_historico RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in sugestoes_cruzamento table
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "padreiroSugerido" TO padreiro_sugerido;
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "probabilidadeCorPelagem" TO probabilidade_cor_pelagem;
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "caracteristicasEsperadas" TO caracteristicas_esperadas;
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "userId" TO user_id;
ALTER TABLE sugestoes_cruzamento RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in pelagens table
ALTER TABLE pelagens RENAME COLUMN "userId" TO user_id;
ALTER TABLE pelagens RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in feed_templates table
ALTER TABLE feed_templates RENAME COLUMN "valorNutricional" TO valor_nutricional;
ALTER TABLE feed_templates RENAME COLUMN "userId" TO user_id;
ALTER TABLE feed_templates RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in feed_plan_items table
ALTER TABLE feed_plan_items RENAME COLUMN "templateId" TO template_id;
ALTER TABLE feed_plan_items RENAME COLUMN "horseId" TO horse_id;
ALTER TABLE feed_plan_items RENAME COLUMN "userId" TO user_id;
ALTER TABLE feed_plan_items RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in stock_batches table
ALTER TABLE stock_batches RENAME COLUMN "dataValidade" TO data_validade;
ALTER TABLE stock_batches RENAME COLUMN "userId" TO user_id;
ALTER TABLE stock_batches RENAME COLUMN "createdAt" TO created_at;

-- Rename columns in stock_alerts table
ALTER TABLE stock_alerts RENAME COLUMN "estoqueMinimo" TO estoque_minimo;
ALTER TABLE stock_alerts RENAME COLUMN "estoqueAtual" TO estoque_atual;
ALTER TABLE stock_alerts RENAME COLUMN "userId" TO user_id;
ALTER TABLE stock_alerts RENAME COLUMN "createdAt" TO created_at;

-- Update sexo enum columns to use new enum (requires conversion)
-- First add temporary column with new enum type
ALTER TABLE cavalos ADD COLUMN sexo_v2 horse_sexo_v2;

-- Convert existing values to new enum values
UPDATE cavalos SET sexo_v2 = 
  CASE 
    WHEN sexo = 'Macho' THEN 'macho'::horse_sexo_v2
    WHEN sexo = 'Fêmea' THEN 'femea'::horse_sexo_v2
    WHEN sexo = 'Macho (Castrado)' THEN 'macho_castrado'::horse_sexo_v2
    WHEN sexo = 'Garanhão' THEN 'garanhao'::horse_sexo_v2
    WHEN sexo = 'Égua' THEN 'femea'::horse_sexo_v2
    ELSE 'macho'::horse_sexo_v2
  END;

-- Drop old column and rename new one
ALTER TABLE cavalos DROP COLUMN sexo;
ALTER TABLE cavalos RENAME COLUMN sexo_v2 TO sexo;

-- Create environment flag to enable new schema
-- This will be checked by application code with SNAKE_V2=true
COMMENT ON SCHEMA public IS 'snake_case_migration_v2_enabled';

COMMIT;