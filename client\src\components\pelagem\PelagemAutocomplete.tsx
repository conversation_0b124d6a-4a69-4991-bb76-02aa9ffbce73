/**
 * Componente de Autocomplete para Pelagens
 * Implementa o novo sistema com slug normalizado e criação automática
 */

import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { usePelagens, useCreatePelagem, Pelagem } from '@/hooks/use-pelagens';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';

interface PelagemAutocompleteProps {
  value?: number;
  onValueChange: (value: number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function PelagemAutocomplete({
  value,
  onValueChange,
  placeholder = "Selecione uma pelagem...",
  disabled = false
}: PelagemAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPelagemName, setNewPelagemName] = useState('');
  const { toast } = useToast();

  const { pelagens, isLoading } = usePelagens(search, 20);
  const createPelagem = useCreatePelagem();

  const selectedPelagem = pelagens.find(p => p.id === value);

  const handleCreatePelagem = async () => {
    if (!newPelagemName.trim()) return;

    try {
      const novaPelagem = await createPelagem.mutateAsync(newPelagemName.trim());
      
      onValueChange(novaPelagem.id);
      setShowCreateDialog(false);
      setNewPelagemName('');
      setOpen(false);
      
      toast({
        title: "Pelagem criada",
        description: `A pelagem "${novaPelagem.nome}" foi criada com sucesso.`,
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível criar a nova pelagem.",
        variant: "destructive",
      });
    }
  };

  const handleSelectPelagem = (pelagem: Pelagem) => {
    onValueChange(pelagem.id);
    setOpen(false);
  };

  const handleCreateNewPelagem = () => {
    setNewPelagemName(search);
    setShowCreateDialog(true);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            {selectedPelagem?.nome || placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput
              placeholder="Buscar pelagem..."
              value={search}
              onValueChange={setSearch}
            />
            <CommandEmpty>
              <div className="p-4 text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  Nenhuma pelagem encontrada para "{search}"
                </p>
                {search.trim() && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCreateNewPelagem}
                    className="w-full"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Criar pelagem "{search}"
                  </Button>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              {pelagens.map((pelagem) => (
                <CommandItem
                  key={pelagem.id}
                  onSelect={() => handleSelectPelagem(pelagem)}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === pelagem.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex flex-col">
                    <span>{pelagem.nome}</span>
                    {pelagem.fonte && (
                      <span className="text-xs text-muted-foreground">
                        Fonte: {pelagem.fonte}
                      </span>
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      <AlertDialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Criar Nova Pelagem</AlertDialogTitle>
            <AlertDialogDescription>
              Você deseja criar uma nova pelagem chamada "{newPelagemName}"?
              Esta pelagem será adicionada ao sistema e ficará disponível para outros usuários.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCreateDialog(false)}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleCreatePelagem}
              disabled={createPelagem.isPending}
            >
              {createPelagem.isPending ? 'Criando...' : 'Criar Pelagem'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}