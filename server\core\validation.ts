// Centralized validation utilities
import { z, ZodError } from 'zod';
import { ValidationError } from './errors';

export const validateRequest = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof ZodError) {
      const details = error.errors.reduce((acc, curr) => {
        acc[curr.path.join('.')] = curr.message;
        return acc;
      }, {} as Record<string, string>);

      throw new ValidationError('Validation failed', details);
    }
    throw error;
  }
};

export const validateId = (id: string | number): number => {
  const numericId = typeof id === 'string' ? parseInt(id) : id;
  
  if (isNaN(numericId) || numericId <= 0) {
    throw new ValidationError('Invalid ID format');
  }
  
  return numericId;
};

export const validateUserId = (userId: string | number): number => {
  const numericUserId = typeof userId === 'string' ? parseInt(userId) : userId;
  
  if (isNaN(numericUserId) || numericUserId <= 0) {
    throw new ValidationError('Invalid user ID format');
  }
  
  return numericUserId;
};

export const validatePagination = (query: any) => {
  const page = parseInt(query.page) || 1;
  const limit = Math.min(parseInt(query.limit) || 10, 100); // Max 100 items per page
  const offset = (page - 1) * limit;

  return { page, limit, offset };
};

export const validateDateRange = (dateFrom?: string, dateTo?: string) => {
  const from = dateFrom ? new Date(dateFrom) : undefined;
  const to = dateTo ? new Date(dateTo) : undefined;

  if (from && isNaN(from.getTime())) {
    throw new ValidationError('Invalid dateFrom format');
  }

  if (to && isNaN(to.getTime())) {
    throw new ValidationError('Invalid dateTo format');
  }

  if (from && to && from > to) {
    throw new ValidationError('dateFrom must be before dateTo');
  }

  return { from, to };
};

// Common validation schemas
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10)
});

export const searchSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  ...paginationSchema.shape
});

export const idSchema = z.object({
  id: z.number().positive()
});

export const userIdSchema = z.object({
  user_id: z.number().positive()
});