import { useEffect, useState } from 'react';
import { Link, useParams } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { usePelagens } from '@/hooks/use-pelagens';
import { Cavalo, Manejo, Arquivo } from '@shared/schema';
import { FileUploader } from '@/components/FileUploader';
import { FilesList } from '@/components/FilesList';
import { HistoricoReprodutivoEgua } from '@/components/HistoricoReprodutivoEgua';
import { Genealogia } from '@/components/Genealogia';
import { GenealogyTree } from '@/components/GenealogyTree';
import { ExpandedGenealogy } from '@/components/genetics/ExpandedGenealogy';
import { Separator } from '@/components/ui/separator';
import { HorseStatistics } from '@/components/HorseStatistics';
import { AcompanhamentoFisico } from '@/components/AcompanhamentoFisico';

import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Importações dos ícones
import { 
  Calendar as CalendarIcon, 
  ChevronLeft, 
  Edit, 
  Trash, 
  Activity, 
  Award, 
  AlertTriangle, 
  Plus, 
  FileImage, 
  FileText, 
  Scale, 
  Ruler, 
  Tag,
  GitBranchPlus,
  Heart, 
  CalendarDays, 
  CalendarX, 
  Badge as BadgeIcon,
  UserCircle, 
  GitFork,
  FolderOpen,
  UserSquare,
  Users,
  Baby,
  BarChart,
  LineChart,
  Calculator,
  ListChecks,
  PawPrint
} from 'lucide-react';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

interface HorseDetailsProps {
  id: string;
}

const HorseDetails = ({ id }: HorseDetailsProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { pelagens } = usePelagens('', 100); // Buscar todas as pelagens
  const [isDeleting, setIsDeleting] = useState(false);

  // Consultar detalhes do cavalo
  const { 
    data: horse, 
    isLoading,
    error
  } = useQuery({
    queryKey: [`/api/cavalos/${id}`],
    enabled: !!user && !!id,
    queryFn: async () => {
      console.log(`Buscando cavalo com ID ${id}`);
      const cavalo = await apiRequest<Cavalo>(`/api/cavalos/${id}`, 'GET');
      console.log("Cavalo recebido:", cavalo);
      return cavalo;
    }
  });

  // Consultar manejos relacionados a este cavalo
  const { 
    data: manejosData = [], 
    isLoading: isLoadingManejos
  } = useQuery({
    queryKey: [`/api/cavalos/${id}/manejos`],
    enabled: !!user && !!id,
    queryFn: async () => {
      try {
        console.log(`Buscando manejos para o cavalo ${id}`);
        const manejos = await apiRequest<Manejo[]>(`/api/cavalos/${id}/manejos`, 'GET');
        console.log("Manejos recebidos:", manejos);
        return manejos || [];
      } catch (error) {
        console.error("Erro ao buscar manejos do cavalo:", error);
        toast({
          title: "Erro",
          description: "Não foi possível buscar os manejos deste cavalo",
          variant: "destructive"
        });
        return [];
      }
    }
  });
  
  // Consultar arquivos relacionados a este cavalo
  const { 
    data: arquivosData = [], 
    isLoading: isLoadingArquivos
  } = useQuery({
    queryKey: [`/api/cavalos/${id}/arquivos`],
    enabled: !!user && !!id,
    queryFn: async () => {
      try {
        console.log(`Buscando arquivos para o cavalo ${id}`);
        const arquivos = await apiRequest<Arquivo[]>(`/api/cavalos/${id}/arquivos`, 'GET');
        console.log("Arquivos recebidos:", arquivos);
        return arquivos || [];
      } catch (error) {
        console.error("Erro ao buscar arquivos do cavalo:", error);
        toast({
          title: "Aviso",
          description: "Não foi possível carregar os arquivos deste cavalo",
          variant: "default"
        });
        return [];
      }
    }
  });
  
  // Ordenar manejos por data (mais recentes primeiro)
  const manejos = [...manejosData].sort((a, b) => {
    // Primeiro ordenar por data do manejo (mais recente primeiro)
    const dateA = new Date(a.data_execucao).getTime();
    const dateB = new Date(b.data_execucao).getTime();
    if (dateB !== dateA) return dateB - dateA;
    
    // Se as datas forem iguais, ordenar pela data de criação
    const createdAtA = a.created_at ? new Date(a.created_at).getTime() : 0;
    const createdAtB = b.created_at ? new Date(b.created_at).getTime() : 0;
    return createdAtB - createdAtA;
  });

  const handleDelete = async () => {
    if (!confirm('Tem certeza que deseja excluir este cavalo?')) return;
    
    setIsDeleting(true);
    try {
      await apiRequest(`/api/cavalos/${id}`, 'DELETE');
      toast({
        title: "Sucesso",
        description: "Cavalo excluído com sucesso",
        variant: "default"
      });
      window.location.href = '/';
    } catch (error) {
      console.error("Erro ao excluir cavalo:", error);
      toast({
        title: "Erro",
        description: "Erro ao excluir o cavalo",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Calcular idade do cavalo
  const calculateAge = (birth_date: string) => {
    if (!birth_date) return 'Idade não disponível';
    
    try {
      let birth: Date;
      
      // Se está no formato DD/MM/YYYY, converter para Date
      if (birth_date.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        const [day, month, year] = birth_date.split('/');
        birth = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      } else {
        birth = new Date(birth_date);
      }
      
      if (isNaN(birth.getTime())) {
        return 'Data de nascimento inválida';
      }
      
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      
      return `${age} ${age === 1 ? 'ano' : 'anos'}`;
    } catch (e) {
      return 'Erro ao calcular idade';
    }
  };

  // Formatar data para exibição
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return 'Data não disponível';
    
    try {
      // Se já está no formato DD/MM/YYYY, retornar como está
      if (typeof dateString === 'string' && dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
        return dateString;
      }
      
      let date: Date;
      if (typeof dateString === 'string') {
        // Se está no formato DD/MM/YYYY, converter corretamente
        if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          const [day, month, year] = dateString.split('/');
          date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        } else {
          date = new Date(dateString);
        }
      } else {
        date = dateString;
      }
      
      if (isNaN(date.getTime())) {
        return 'Data inválida';
      }
      
      return format(date, 'dd/MM/yyyy', { locale: pt });
    } catch (e) {
      return typeof dateString === 'string' ? dateString : 'Formato de data inválido';
    }
  };

  // Obter nome para exibição do tipo de manejo
  const getTaskTypeName = (tipo: string) => {
    const types: Record<string, string> = {
      'veterinary': 'Consulta Veterinária',
      'farrier': 'Ferrageamento',
      'vaccination': 'Vacinação',
      'deworming': 'Vermifugação',
      'dental': 'Tratamento Dentário',
      'training': 'Treinamento',
      'other': 'Outro'
    };
    
    return types[tipo] || tipo.charAt(0).toUpperCase() + tipo.slice(1);
  };
  
  // Obter ícone de acordo com o tipo de arquivo
  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <FileImage className="h-10 w-10 text-blue-500" />;
      case 'video':
        return <FileImage className="h-10 w-10 text-red-500" />;
      case 'pdf':
        return <FileText className="h-10 w-10 text-amber-500" />;
      default:
        return <FileText className="h-10 w-10 text-gray-500" />;
    }
  };
  
  // Traduzir status para texto legível
  const getStatusText = (status: string | null) => {
    if (!status) return 'Ativo';
    
    const statusMap: Record<string, string> = {
      'ativo': 'Ativo',
      'vendido': 'Vendido',
      'falecido': 'Falecido',
      'doado': 'Doado',
      'emprestado': 'Emprestado'
    };
    
    return statusMap[status.toLowerCase()] || status;
  };

  // Buscar nome da pelagem baseado no pelagem_id
  const getPelagemNome = (pelagem_id: number | null) => {
    if (!pelagem_id || !pelagens) {
      console.log('getPelagemNome: pelagem_id ou pelagens não disponível', { pelagem_id, pelagens: pelagens?.length });
      return null;
    }
    const pelagem = pelagens.find(p => p.id === pelagem_id);
    console.log('getPelagemNome: Procurando pelagem', { pelagem_id, pelagem, totalPelagens: pelagens.length });
    return pelagem?.nome || null;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
          <AlertTriangle className="h-6 w-6 text-red-500 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-red-800">Cavalo não encontrado</h3>
            <p className="mt-1 text-sm text-red-700">
              Não foi possível encontrar este cavalo. Ele pode ter sido excluído ou você não tem permissão para acessá-lo.
            </p>
            <Link href="/">
              <Button variant="link" className="mt-2 h-auto p-0 text-red-700">
                Voltar para o Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Link href="/">
          <Button variant="ghost" className="mb-4 -ml-3 text-gray-500 hover:text-gray-700">
            <ChevronLeft className="mr-1 h-4 w-4" />
            Voltar para o Dashboard
          </Button>
        </Link>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{horse.name}</h1>
            <div className="mt-1 flex items-center space-x-2">
              <Badge variant="outline" className="px-2 py-1 text-xs bg-gray-50">
                {horse.breed}
              </Badge>
              {horse.birth_date && (
                <span className="text-sm text-gray-500">
                  {calculateAge(horse.birth_date)}
                </span>
              )}
            </div>
          </div>
          
          <div className="mt-4 sm:mt-0 flex space-x-2">
            <Link href={`/manejos?horse_id=${horse.id}`}>
              <Button variant="default" size="sm" className="flex items-center">
                <Plus className="mr-1 h-4 w-4" />
                Adicionar Manejo
              </Button>
            </Link>
            <Link href={`/cavalo/${horse.id}/editar`}>
              <Button variant="outline" size="sm" className="flex items-center">
                <Edit className="mr-1 h-4 w-4" />
                Editar
              </Button>
            </Link>
            <Button 
              variant="destructive" 
              size="sm" 
              className="flex items-center" 
              onClick={handleDelete}
              disabled={isDeleting}
            >
              <Trash className="mr-1 h-4 w-4" />
              {isDeleting ? 'Excluindo...' : 'Excluir'}
            </Button>
          </div>
        </div>
      </div>
      
      <Tabs defaultValue="info">
        <TabsList className="mb-6 flex flex-wrap justify-start overflow-x-auto">
          <TabsTrigger value="info" className="whitespace-nowrap">
            <Tag className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Informações</span>
            <span className="sm:hidden">Info</span>
          </TabsTrigger>
          <TabsTrigger value="manejos" className="whitespace-nowrap">
            <ListChecks className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Manejos</span>
            <span className="sm:hidden">Manejos</span>
            <span className="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{manejos.length}</span>
          </TabsTrigger>
          <TabsTrigger value="reproducao" className="whitespace-nowrap">
            <Heart className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Reprodução</span>
            <span className="sm:hidden">Reprod.</span>
          </TabsTrigger>
          <TabsTrigger value="genealogia" className="whitespace-nowrap">
            <GitFork className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Genealogia</span>
            <span className="sm:hidden">Geneal.</span>
          </TabsTrigger>
          <TabsTrigger value="arquivos" className="whitespace-nowrap">
            <FolderOpen className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Arquivos</span>
            <span className="sm:hidden">Arquivos</span>
            <span className="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{arquivosData.length}</span>
          </TabsTrigger>
          <TabsTrigger value="estatisticas" className="whitespace-nowrap">
            <BarChart className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Estatísticas</span>
            <span className="sm:hidden">Stats</span>
          </TabsTrigger>
          <TabsTrigger value="saude" className="whitespace-nowrap">
            <Activity className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Saúde</span>
            <span className="sm:hidden">Saúde</span>
          </TabsTrigger>
          <TabsTrigger value="fisico" className="whitespace-nowrap">
            <Scale className="h-4 w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Acompanhamento Físico</span>
            <span className="sm:hidden">Físico</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="info">
          <Card>
            <CardHeader>
              <CardTitle>Detalhes do Cavalo</CardTitle>
              <CardDescription>
                Informações detalhadas sobre {horse.name}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Informações Básicas */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Tag className="mr-2 h-5 w-5 text-primary" />
                  Informações Básicas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Nome</h4>
                    <p className="text-lg font-semibold text-gray-900">{horse.name}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Raça</h4>
                    <p className="text-lg font-semibold text-gray-900">{horse.breed}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">ID Sistema</h4>
                    <p className="text-lg font-semibold text-gray-900">#{horse.id}</p>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-600 mb-1">Data de Nascimento</h4>
                    <p className="text-lg font-semibold text-blue-800">{horse.birth_date ? formatDate(horse.birth_date) : 'Não informada'}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-green-600 mb-1">Idade</h4>
                    <p className="text-lg font-semibold text-green-800">{horse.birth_date ? calculateAge(horse.birth_date) : 'Não calculável'}</p>
                  </div>
                  {horse.numero_registro && (
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-purple-600 mb-1">Nº Registro</h4>
                      <p className="text-lg font-semibold text-purple-800">{horse.numero_registro}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Características Físicas */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Ruler className="mr-2 h-5 w-5 text-primary" />
                  Características Físicas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {horse.sexo && (
                    <div className="bg-indigo-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-indigo-600 mb-1 flex items-center">
                        <UserSquare className="mr-1 h-4 w-4" />
                        Sexo
                      </h4>
                      <p className="text-lg font-semibold text-indigo-800">{horse.sexo}</p>
                    </div>
                  )}
                  
                  {horse.peso && (
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-orange-600 mb-1 flex items-center">
                        <Scale className="mr-1 h-4 w-4" />
                        Peso
                      </h4>
                      <p className="text-lg font-semibold text-orange-800">{horse.peso} kg</p>
                    </div>
                  )}
                  
                  {horse.altura && (
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-yellow-600 mb-1 flex items-center">
                        <Ruler className="mr-1 h-4 w-4" />
                        Altura
                      </h4>
                      <p className="text-lg font-semibold text-yellow-800">{horse.altura} m</p>
                    </div>
                  )}
                  
                  {horse.pelagem_id && (
                    <div className="bg-pink-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-pink-600 mb-1 flex items-center">
                        <Tag className="mr-1 h-4 w-4" />
                        Pelagem
                      </h4>
                      <p className="text-lg font-semibold text-pink-800">
                        {getPelagemNome(horse.pelagem_id) || `ID: ${horse.pelagem_id}`}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Status e Informações de Controle */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <BadgeIcon className="mr-2 h-5 w-5 text-primary" />
                  Status e Controle
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-emerald-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-emerald-600 mb-1">Status</h4>
                    <p className="text-lg font-semibold text-emerald-800">{getStatusText(horse.status)}</p>
                  </div>
                  
                  {horse.data_entrada && (
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-green-600 mb-1 flex items-center">
                        <CalendarDays className="mr-1 h-4 w-4" />
                        Data de Entrada
                      </h4>
                      <p className="text-lg font-semibold text-green-800">{formatDate(horse.data_entrada)}</p>
                    </div>
                  )}
                  
                  {horse.data_saida && (
                    <div className="bg-red-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-red-600 mb-1 flex items-center">
                        <CalendarX className="mr-1 h-4 w-4" />
                        Data de Saída
                      </h4>
                      <p className="text-lg font-semibold text-red-800">{formatDate(horse.data_saida)}</p>
                    </div>
                  )}
                  
                  {horse.data_compra && (
                    <div className="bg-cyan-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-cyan-600 mb-1 flex items-center">
                        <CalendarIcon className="mr-1 h-4 w-4" />
                        Data de Compra
                      </h4>
                      <p className="text-lg font-semibold text-cyan-800">{formatDate(horse.data_compra)}</p>
                    </div>
                  )}
                  
                  {horse.origem && (
                    <div className="bg-violet-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-violet-600 mb-1 flex items-center">
                        <FolderOpen className="mr-1 h-4 w-4" />
                        Origem
                      </h4>
                      <p className="text-lg font-semibold text-violet-800">{horse.origem}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Informações de Responsáveis */}
              {(horse.proprietario || horse.criador || horse.inspetor) && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Users className="mr-2 h-5 w-5 text-primary" />
                    Responsáveis
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {horse.proprietario && (
                      <div className="bg-teal-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-teal-600 mb-1 flex items-center">
                          <UserCircle className="mr-1 h-4 w-4" />
                          Proprietário
                        </h4>
                        <p className="text-lg font-semibold text-teal-800">{horse.proprietario}</p>
                      </div>
                    )}
                    
                    {horse.criador && (
                      <div className="bg-amber-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-amber-600 mb-1 flex items-center">
                          <Baby className="mr-1 h-4 w-4" />
                          Criador
                        </h4>
                        <p className="text-lg font-semibold text-amber-800">{horse.criador}</p>
                      </div>
                    )}
                    
                    {horse.inspetor && (
                      <div className="bg-slate-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-slate-600 mb-1 flex items-center">
                          <UserSquare className="mr-1 h-4 w-4" />
                          Inspetor
                        </h4>
                        <p className="text-lg font-semibold text-slate-800">{horse.inspetor}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Genealogia */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <GitFork className="mr-2 h-5 w-5 text-primary" />
                  Genealogia
                </h3>
                <ExpandedGenealogy cavaloId={horse.id} />
              </div>
              
              {/* Observações e Notas */}
              {(horse.motivo_saida || horse.notes) && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FileText className="mr-2 h-5 w-5 text-primary" />
                    Observações
                  </h3>
                  <div className="space-y-4">
                    {horse.motivo_saida && (
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-red-600 mb-1">Motivo da Saída</h4>
                        <p className="text-sm text-red-800 whitespace-pre-line">{horse.motivo_saida}</p>
                      </div>
                    )}
                    
                    {horse.notes && (
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-blue-600 mb-1">Observações Gerais</h4>
                        <p className="text-sm text-blue-800 whitespace-pre-line">{horse.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Informações de Sistema */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CalendarIcon className="mr-2 h-5 w-5 text-primary" />
                  Informações do Sistema
                </h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Data de Cadastro</h4>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatDate(horse.created_at)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="manejos">
          <div className="mb-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Histórico de Manejos</h2>
              <p className="text-sm text-gray-500">
                {manejos.length === 0 
                  ? 'Nenhum manejo registrado para este cavalo.' 
                  : `${manejos.length} ${manejos.length === 1 ? 'manejo registrado' : 'manejos registrados'}.`}
              </p>
            </div>
            <Link href={`/manejos?horse_id=${horse.id}`}>
              <Button size="sm" className="flex items-center">
                <Plus className="mr-1 h-4 w-4" />
                Adicionar Manejo
              </Button>
            </Link>
          </div>
          
          {isLoadingManejos ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : manejos.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Activity className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Nenhum manejo registrado</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Adicione manejos para acompanhar o histórico de cuidados com este cavalo.
                </p>
                <Link href={`/manejos?horse_id=${horse.id}`}>
                  <Button className="mt-4">Adicionar Primeiro Manejo</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 grid-cols-1">
              {manejos.map((manejo) => (
                <Card key={manejo.id} className="overflow-hidden">
                  <div className="flex flex-col sm:flex-row">
                    <div className="p-4 sm:p-5 bg-gray-50 sm:w-1/3 flex flex-col justify-between">
                      <div>
                        <h3 className="font-semibold text-sm sm:text-base">{getTaskTypeName(manejo.tipo)}</h3>
                        <div className="flex items-center mt-2">
                          <CalendarIcon className="h-4 w-4 text-gray-500 mr-1.5" />
                          <p className="text-sm text-gray-600">{formatDate(manejo.data_execucao)}</p>
                        </div>
                      </div>
                      <div className="mt-4 sm:mt-0">
                        <p className="text-xs text-gray-500">
                          Adicionado em {formatDate(manejo.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="p-4 sm:p-5 sm:flex-1 sm:border-l border-gray-200">
                      {manejo.observacoes ? (
                        <div>
                          <h4 className="text-xs font-medium text-gray-500 uppercase mb-2">Observações</h4>
                          <p className="text-sm text-gray-700 whitespace-pre-line">{manejo.observacoes}</p>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 italic">Nenhuma observação registrada.</p>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
        
        {/* Aba de Genealogia */}

        
        {/* Aba de Estatísticas */}
        <TabsContent value="estatisticas">
          <HorseStatistics 
            horse_id={horse.id} 
            horseName={horse.name}
            horseData={horse}
          />
        </TabsContent>
        
        {/* Aba de Saúde */}
        <TabsContent value="fisico">
          <Card>
            <CardHeader>
              <CardTitle>Acompanhamento Físico</CardTitle>
              <CardDescription>
                Dados de peso, altura e condição corporal de {horse.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AcompanhamentoFisico horse_id={Number(id)} horseName={horse.name} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="saude">
          <Card>
            <CardHeader>
              <CardTitle>Saúde e Bem-estar</CardTitle>
              <CardDescription>
                Informações sobre a saúde e o bem-estar de {horse.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <Heart className="mr-2 h-5 w-5 text-red-500" />
                    Resumo de Saúde
                  </h3>
                  
                  <Card className="mb-5 bg-gray-50 border">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Último Check-up Veterinário</h4>
                          {manejos.some(m => m.tipo === 'veterinary') ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Realizado</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pendente</Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center text-sm">
                          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                          {manejos.filter(m => m.tipo === 'veterinary').length > 0 ? (
                            <span>
                              {formatDate(
                                manejos
                                  .filter(m => m.tipo === 'veterinary')
                                  .sort((a, b) => new Date(b.data_execucao).getTime() - new Date(a.data_execucao).getTime())[0].data_execucao
                              )}
                            </span>
                          ) : (
                            <span className="text-gray-500">Não há registros</span>
                          )}
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Última Vermifugação</h4>
                          {manejos.some(m => m.tipo === 'deworming') ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Realizado</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pendente</Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center text-sm">
                          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                          {manejos.filter(m => m.tipo === 'deworming').length > 0 ? (
                            <span>
                              {formatDate(
                                manejos
                                  .filter(m => m.tipo === 'deworming')
                                  .sort((a, b) => new Date(b.data_execucao).getTime() - new Date(a.data_execucao).getTime())[0].data_execucao
                              )}
                            </span>
                          ) : (
                            <span className="text-gray-500">Não há registros</span>
                          )}
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Última Vacinação</h4>
                          {manejos.some(m => m.tipo === 'vaccination') ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Realizado</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pendente</Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center text-sm">
                          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                          {manejos.filter(m => m.tipo === 'vaccination').length > 0 ? (
                            <span>
                              {formatDate(
                                manejos
                                  .filter(m => m.tipo === 'vaccination')
                                  .sort((a, b) => new Date(b.data_execucao).getTime() - new Date(a.data_execucao).getTime())[0].data_execucao
                              )}
                            </span>
                          ) : (
                            <span className="text-gray-500">Não há registros</span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Peso e Altura */}
                  <div className="p-4 bg-white rounded-lg border border-gray-200 mb-5">
                    <h4 className="text-sm font-semibold mb-3">Acompanhamento Físico</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Peso Atual</div>
                        <div className="flex items-center">
                          <Scale className="mr-2 h-4 w-4 text-amber-500" />
                          <span className="text-lg font-semibold">{horse.peso || '?'} kg</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Altura</div>
                        <div className="flex items-center">
                          <Ruler className="mr-2 h-4 w-4 text-blue-500" />
                          <span className="text-lg font-semibold">{horse.altura || '?'} m</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <ListChecks className="mr-2 h-5 w-5 text-teal-600" />
                    Próximos Procedimentos Recomendados
                  </h3>
                  
                  <Card className="border bg-gray-50">
                    <CardContent className="p-4">
                      <ul className="space-y-4">
                        <li className="p-3 bg-white rounded-lg border border-gray-200">
                          <div className="flex justify-between">
                            <div className="flex items-center">
                              <div className="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                                <Heart className="h-4 w-4 text-amber-600" />
                              </div>
                              <div>
                                <div className="text-sm font-medium">Check-up Veterinário</div>
                                <div className="text-xs text-gray-500 mt-0.5">Recomendado a cada 6 meses</div>
                              </div>
                            </div>
                            <div>
                              <Badge variant={manejos.some(m => 
                                m.tipo === 'veterinary' && 
                                ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 180
                              ) ? "outline" : "secondary"} className="text-xs">
                                {manejos.some(m => 
                                  m.tipo === 'veterinary' && 
                                  ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 180
                                ) ? "Atualizado" : "Agendar"}
                              </Badge>
                            </div>
                          </div>
                        </li>
                        
                        <li className="p-3 bg-white rounded-lg border border-gray-200">
                          <div className="flex justify-between">
                            <div className="flex items-center">
                              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                <Activity className="h-4 w-4 text-purple-600" />
                              </div>
                              <div>
                                <div className="text-sm font-medium">Exame Odontológico</div>
                                <div className="text-xs text-gray-500 mt-0.5">Recomendado anualmente</div>
                              </div>
                            </div>
                            <div>
                              <Badge variant={manejos.some(m => 
                                m.tipo === 'dental' && 
                                ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 365
                              ) ? "outline" : "secondary"} className="text-xs">
                                {manejos.some(m => 
                                  m.tipo === 'dental' && 
                                  ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 365
                                ) ? "Atualizado" : "Agendar"}
                              </Badge>
                            </div>
                          </div>
                        </li>
                        
                        <li className="p-3 bg-white rounded-lg border border-gray-200">
                          <div className="flex justify-between">
                            <div className="flex items-center">
                              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                <Activity className="h-4 w-4 text-green-600" />
                              </div>
                              <div>
                                <div className="text-sm font-medium">Ferrageamento</div>
                                <div className="text-xs text-gray-500 mt-0.5">Recomendado a cada 45 dias</div>
                              </div>
                            </div>
                            <div>
                              <Badge variant={manejos.some(m => 
                                m.tipo === 'farrier' && 
                                ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 45
                              ) ? "outline" : "secondary"} className="text-xs">
                                {manejos.some(m => 
                                  m.tipo === 'farrier' && 
                                  ((new Date().getTime() - new Date(m.data_execucao).getTime()) / (1000 * 60 * 60 * 24)) < 45
                                ) ? "Atualizado" : "Agendar"}
                              </Badge>
                            </div>
                          </div>
                        </li>
                      </ul>
                      
                      <div className="mt-4 flex justify-end">
                        <Link href={`/manejos?horse_id=${horse.id}`}>
                          <Button variant="outline" size="sm">Agendar Procedimento</Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Aba de Reprodução */}
        <TabsContent value="reproducao">
          {/* Verificar se é égua (sexo feminino) para mostrar histórico reprodutivo */}
          {horse.sexo && 
            (horse.sexo.toLowerCase() === 'fêmea' || 
             horse.sexo.toLowerCase() === 'femea' || 
             horse.sexo.toLowerCase() === 'f') ? (
            <HistoricoReprodutivoEgua horse_id={horse.id} horseName={horse.name} />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Reprodução</CardTitle>
                <CardDescription>
                  Histórico reprodutivo de {horse.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Heart className="mx-auto h-12 w-12 text-red-200" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">
                    Histórico Reprodutivo não disponível
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {horse.sexo && 
                      (horse.sexo.toLowerCase() === 'macho' || 
                       horse.sexo.toLowerCase() === 'm') ? (
                      "Este recurso está otimizado para éguas. Para garanhões, registre os procedimentos na seção de Manejos."
                    ) : (
                      "O sexo do animal precisa ser registrado como 'femea' para acessar o histórico reprodutivo."
                    )}
                  </p>
                  {(!horse.sexo || !(horse.sexo.toLowerCase() === 'macho' || horse.sexo.toLowerCase() === 'm')) && (
                    <Link href={`/cavalo/${horse.id}/editar`}>
                      <Button className="mt-4">Atualizar Informações</Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="genealogia">
          <ExpandedGenealogy cavaloId={horse.id} />
        </TabsContent>
        
        <TabsContent value="arquivos">
          <div className="mb-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Arquivos</h2>
              <p className="text-sm text-gray-500">
                {arquivosData.length === 0 
                  ? 'Nenhum arquivo anexado para este cavalo.' 
                  : `${arquivosData.length} ${arquivosData.length === 1 ? 'arquivo anexado' : 'arquivos anexados'}.`}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 gap-8">
            <FileUploader 
              horse_id={parseInt(id!)} 
              onUploadSuccess={() => {
                // Recarregar a consulta de arquivos após o upload
                queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${id}/arquivos`] });
              }}
              className="bg-muted/50"
            />
            
            {isLoadingArquivos ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <FilesList 
                horse_id={parseInt(id!)} 
                onRefresh={() => {
                  // Recarregar a consulta de arquivos após exclusão
                  queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${id}/arquivos`] });
                }}
              />
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HorseDetails;