/**
 * Dashboard de Alimentação - EquiGestor AI
 * Interface principal para gerenciar planos de alimentação diários
 */

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { CalendarDays, CheckCircle, AlertTriangle, TrendingUp, Utensils, Clock } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface FeedPlan {
  id: number;
  animalId: number;
  templateId: number;
  forageKg: number;
  concentrateKg: number;
  date: string;
  status: 'pending' | 'done' | 'leftover';
  leftoverPct?: number;
  horseName: string;
  templateCategory: string;
}

interface FeedingStatistics {
  avgWastePct: number;
  totalPlans: number;
  plansWithWaste: number;
  avgCostPerAnimalPerDay: number;
}

export default function FeedingDashboard() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [leftoverDialog, setLeftoverDialog] = useState<{ open: boolean; planId?: number }>({ open: false });
  const [leftoverPct, setLeftoverPct] = useState<string>('');
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Buscar planos do dia
  const { data: feedingData, isLoading: plansLoading } = useQuery({
    queryKey: ['/api/feeding/today', selectedDate],
    queryFn: () => apiRequest(`/api/feeding/today?date=${selectedDate}`)
  });

  // Buscar estatísticas
  const { data: statistics } = useQuery({
    queryKey: ['/api/feeding/statistics'],
    queryFn: () => apiRequest('/api/feeding/statistics')
  });

  // Mutation para marcar plano como concluído
  const markCompleteMutation = useMutation({
    mutationFn: ({ planId, leftoverPct }: { planId: number; leftoverPct?: number }) =>
      apiRequest(`/api/feeding/${planId}/mark`, {
        method: 'PATCH',
        body: leftoverPct !== undefined ? { leftoverPct } : {}
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/feeding/today'] });
      queryClient.invalidateQueries({ queryKey: ['/api/feeding/statistics'] });
      toast({
        title: "Sucesso!",
        description: "Plano de alimentação atualizado",
      });
      setLeftoverDialog({ open: false });
      setLeftoverPct('');
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao atualizar plano de alimentação",
        variant: "destructive",
      });
    }
  });

  // Mutation para gerar novos planos
  const generatePlansMutation = useMutation({
    mutationFn: (date: string) =>
      apiRequest('/api/feeding/generate', {
        method: 'POST',
        body: { date }
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/feeding/today'] });
      toast({
        title: "Sucesso!",
        description: "Planos de alimentação gerados",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao gerar planos de alimentação",
        variant: "destructive",
      });
    }
  });

  const plans = feedingData?.plans || [];
  const planStats = feedingData?.statistics || { total: 0, pending: 0, completed: 0, withLeftover: 0 };

  const handleMarkComplete = (planId: number) => {
    markCompleteMutation.mutate({ planId });
  };

  const handleMarkWithLeftover = () => {
    if (leftoverDialog.planId && leftoverPct) {
      const pct = parseFloat(leftoverPct);
      if (pct >= 0 && pct <= 100) {
        markCompleteMutation.mutate({ 
          planId: leftoverDialog.planId, 
          leftoverPct: pct 
        });
      } else {
        toast({
          title: "Erro",
          description: "Percentual deve ser entre 0 e 100",
          variant: "destructive",
        });
      }
    }
  };

  const completionRate = planStats.total > 0 ? 
    ((planStats.completed + planStats.withLeftover) / planStats.total) * 100 : 0;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard de Alimentação</h1>
          <p className="text-gray-600 mt-1">Gerencie os planos de alimentação diários</p>
        </div>
        <div className="flex items-center gap-4">
          <Input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-auto"
          />
          <Button 
            onClick={() => generatePlansMutation.mutate(selectedDate)}
            disabled={generatePlansMutation.isPending}
            className="bg-green-600 hover:bg-green-700"
          >
            <Utensils className="w-4 h-4 mr-2" />
            Gerar Planos
          </Button>
        </div>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CalendarDays className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Tratos Pendentes</p>
                <p className="text-2xl font-bold text-gray-900">{planStats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taxa de Conclusão</p>
                <p className="text-2xl font-bold text-gray-900">{completionRate.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Desperdício Médio</p>
                <p className="text-2xl font-bold text-gray-900">
                  {statistics?.avgWastePct?.toFixed(1) || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Custo/Dia/Animal</p>
                <p className="text-2xl font-bold text-gray-900">
                  R$ {statistics?.avgCostPerAnimalPerDay?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progresso do Dia */}
      <Card>
        <CardHeader>
          <CardTitle>Progresso do Dia</CardTitle>
          <CardDescription>
            {planStats.completed + planStats.withLeftover} de {planStats.total} planos concluídos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={completionRate} className="w-full" />
        </CardContent>
      </Card>

      {/* Tabela de Planos */}
      <Card>
        <CardHeader>
          <CardTitle>Planos de Alimentação - {selectedDate}</CardTitle>
          <CardDescription>
            Clique em "Concluído" para marcar um trato como realizado, ou "Com Sobra" se houve desperdício
          </CardDescription>
        </CardHeader>
        <CardContent>
          {plansLoading ? (
            <div className="text-center py-8">Carregando planos...</div>
          ) : plans.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Nenhum plano encontrado para esta data</p>
              <Button 
                onClick={() => generatePlansMutation.mutate(selectedDate)}
                className="mt-4"
                disabled={generatePlansMutation.isPending}
              >
                Gerar Planos para Hoje
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Cavalo</TableHead>
                    <TableHead>Categoria</TableHead>
                    <TableHead>Volumoso (kg)</TableHead>
                    <TableHead>Concentrado (kg)</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sobra (%)</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan: FeedPlan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.horseName}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{plan.templateCategory}</Badge>
                      </TableCell>
                      <TableCell>{plan.forageKg}kg</TableCell>
                      <TableCell>{plan.concentrateKg}kg</TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            plan.status === 'done' ? 'default' : 
                            plan.status === 'leftover' ? 'destructive' : 
                            'secondary'
                          }
                        >
                          {plan.status === 'done' ? 'Concluído' : 
                           plan.status === 'leftover' ? 'Com Sobra' : 
                           'Pendente'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {plan.leftoverPct ? `${plan.leftoverPct}%` : '-'}
                      </TableCell>
                      <TableCell>
                        {plan.status === 'pending' && (
                          <div className="flex gap-2">
                            <Button 
                              size="sm" 
                              onClick={() => handleMarkComplete(plan.id)}
                              disabled={markCompleteMutation.isPending}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              OK
                            </Button>
                            <Dialog 
                              open={leftoverDialog.open && leftoverDialog.planId === plan.id}
                              onOpenChange={(open) => setLeftoverDialog({ open, planId: open ? plan.id : undefined })}
                            >
                              <DialogTrigger asChild>
                                <Button 
                                  size="sm" 
                                  variant="outline"
                                  onClick={() => setLeftoverDialog({ open: true, planId: plan.id })}
                                >
                                  Com Sobra
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Registrar Sobra</DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <p>Cavalo: <strong>{plan.horseName}</strong></p>
                                  <div>
                                    <label className="text-sm font-medium">Percentual de sobra (%)</label>
                                    <Input
                                      type="number"
                                      min="0"
                                      max="100"
                                      value={leftoverPct}
                                      onChange={(e) => setLeftoverPct(e.target.value)}
                                      placeholder="Ex: 15"
                                      className="mt-1"
                                    />
                                  </div>
                                  <div className="flex justify-end gap-2">
                                    <Button 
                                      variant="outline" 
                                      onClick={() => setLeftoverDialog({ open: false })}
                                    >
                                      Cancelar
                                    </Button>
                                    <Button 
                                      onClick={handleMarkWithLeftover}
                                      disabled={markCompleteMutation.isPending}
                                    >
                                      Registrar
                                    </Button>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}