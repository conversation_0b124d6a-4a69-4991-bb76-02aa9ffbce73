import { 
  users, cavalos, manejos
} from "@shared/schema";
import { db } from "./db";
import { eq, and, asc } from "drizzle-orm";
import { getModuleLogger } from './logger';

export interface IStorage {
  // User methods
  getUserByUsername(username: string): Promise<any>;
  
  // Horse methods
  getCavalos(user_id: number): Promise<any[]>;
  getCavalo(id: number, user_id: number): Promise<any>;
  createCavalo(cavalo: any): Promise<any>;
  updateCavalo(id: number, user_id: number, cavalo: any): Promise<any>;
  deleteCavalo(id: number, user_id: number): Promise<boolean>;
  
  // Manejo methods
  getManejos(user_id: number): Promise<any[]>;
  getManejosByHorse(horse_id: number, user_id: number): Promise<any[]>;
  getManejo(id: number, user_id: number): Promise<any>;
  createManejo(manejo: any): Promise<any>;
  updateManejo(id: number, user_id: number, manejo: any): Promise<any>;
  deleteManejo(id: number, user_id: number): Promise<boolean>;

  // Placeholder methods for other routes
  getArquivos?(user_id: number): Promise<any[]>;
  getArquivosByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getArquivo?(id: number, user_id: number): Promise<any>;
  createArquivo?(arquivo: any): Promise<any>;
  deleteArquivo?(id: number, user_id: number): Promise<boolean>;
  
  getEventos?(user_id: number): Promise<any[]>;
  getEventosByDate?(date: string, user_id: number): Promise<any[]>;
  getEventosByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getEventosByManejo?(manejoId: number, user_id: number): Promise<any[]>;
  getEvento?(id: number, user_id: number): Promise<any>;
  createEvento?(evento: any): Promise<any>;
  updateEvento?(id: number, user_id: number, evento: any): Promise<any>;
  deleteEvento?(id: number, user_id: number): Promise<boolean>;

  [key: string]: any; // Allow any other methods for compatibility
}

export class Storage implements IStorage {
  private logger = getModuleLogger('Storage');

  // User methods
  async getUserByUsername(username: string): Promise<any> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.username, username));
      return user || null;
    } catch (error) {
      this.logger.error('Erro ao buscar usuário:', error);
      return null;
    }
  }

  // Horse methods
  async getCavalos(user_id: number): Promise<any[]> {
    try {
      const horses = await db.select()
        .from(cavalos)
        .where(eq(cavalos.user_id, user_id))
        .orderBy(asc(cavalos.name));
      
      return horses.map(horse => ({
        ...horse,
        nome: horse.name, // Compatibility mapping
        pelagem: horse.cor
      }));
    } catch (error) {
      this.logger.error('Erro ao buscar cavalos:', error);
      return [];
    }
  }

  async getCavalo(id: number, user_id: number): Promise<any> {
    try {
      const [horse] = await db.select()
        .from(cavalos)
        .where(and(eq(cavalos.id, id), eq(cavalos.user_id, user_id)));
      
      if (!horse) return null;
      
      return {
        ...horse,
        nome: horse.name,
        pelagem: horse.cor
      };
    } catch (error) {
      this.logger.error('Erro ao buscar cavalo:', error);
      return null;
    }
  }

  async createCavalo(cavalo: any): Promise<any> {
    try {
      const [newHorse] = await db
        .insert(cavalos)
        .values(cavalo)
        .returning();
      return newHorse;
    } catch (error) {
      this.logger.error('Erro ao criar cavalo:', error);
      throw error;
    }
  }

  async updateCavalo(id: number, user_id: number, cavalo: any): Promise<any> {
    try {
      const [updatedHorse] = await db
        .update(cavalos)
        .set(cavalo)
        .where(and(eq(cavalos.id, id), eq(cavalos.user_id, user_id)))
        .returning();
      return updatedHorse;
    } catch (error) {
      this.logger.error('Erro ao atualizar cavalo:', error);
      return null;
    }
  }

  async deleteCavalo(id: number, user_id: number): Promise<boolean> {
    try {
      await db
        .delete(cavalos)
        .where(and(eq(cavalos.id, id), eq(cavalos.user_id, user_id)));
      return true;
    } catch (error) {
      this.logger.error('Erro ao deletar cavalo:', error);
      return false;
    }
  }

  // Manejo methods
  async getManejos(user_id: number): Promise<any[]> {
    try {
      return await db.select()
        .from(manejos)
        .where(eq(manejos.user_id, user_id))
        // Sort by execution date (data_execucao)
        .orderBy(asc(manejos.data_execucao));
    } catch (error) {
      this.logger.error('Erro ao buscar manejos:', error);
      return [];
    }
  }

  async getManejosByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
        return await db.select()
          .from(manejos)
          // Filter by cavalo_id then order by execution date
          .where(and(eq(manejos.cavalo_id, horse_id), eq(manejos.user_id, user_id)))
          // Sort by execution date (data_execucao)
          .orderBy(asc(manejos.data_execucao));
    } catch (error) {
      this.logger.error('Erro ao buscar manejos por cavalo:', error);
      return [];
    }
  }

  async getManejo(id: number, user_id: number): Promise<any> {
    try {
      const [manejo] = await db.select()
        .from(manejos)
        .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)));
      return manejo || null;
    } catch (error) {
      this.logger.error('Erro ao buscar manejo:', error);
      return null;
    }
  }

  async createManejo(manejo: any): Promise<any> {
    try {
      const [newManejo] = await db
        .insert(manejos)
        .values(manejo)
        .returning();
      return newManejo;
    } catch (error) {
      this.logger.error('Erro ao criar manejo:', error);
      throw error;
    }
  }

  async updateManejo(id: number, user_id: number, manejo: any): Promise<any> {
    try {
      const [updatedManejo] = await db
        .update(manejos)
        .set(manejo)
        .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)))
        .returning();
      return updatedManejo;
    } catch (error) {
      this.logger.error('Erro ao atualizar manejo:', error);
      return null;
    }
  }

  async deleteManejo(id: number, user_id: number): Promise<boolean> {
    try {
      await db
        .delete(manejos)
        .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)));
      return true;
    } catch (error) {
      this.logger.error('Erro ao deletar manejo:', error);
      return false;
    }
  }

  // Placeholder methods for compatibility
  async getArquivos(user_id: number): Promise<any[]> { return []; }
  async getArquivosByHorse(horse_id: number, user_id: number): Promise<any[]> { return []; }
  async getArquivo(id: number, user_id: number): Promise<any> { return null; }
  async createArquivo(arquivo: any): Promise<any> { return null; }
  async deleteArquivo(id: number, user_id: number): Promise<boolean> { return true; }
  
  async getEventos(user_id: number): Promise<any[]> { return []; }
  async getEventosByDate(date: string, user_id: number): Promise<any[]> { return []; }
  async getEventosByHorse(horse_id: number, user_id: number): Promise<any[]> { return []; }
  async getEventosByManejo(manejoId: number, user_id: number): Promise<any[]> { return []; }
  async getEvento(id: number, user_id: number): Promise<any> { return null; }
  async createEvento(evento: any): Promise<any> { return null; }
  async updateEvento(id: number, user_id: number, evento: any): Promise<any> { return null; }
  async deleteEvento(id: number, user_id: number): Promise<boolean> { return true; }

  // Handle any other method calls gracefully
  [key: string]: any;
}

export const storage = new Storage();