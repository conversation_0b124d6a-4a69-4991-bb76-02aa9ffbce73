import { eq, sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';
import { db as localDb } from './db';
import { cavalos, manejos, eventos, procedimentosVet, reproducao, medidasFisicas, morfologia, desempenhoHistorico, genealogia } from '@shared/schema';
import { medidasMorfologicas } from '@shared/schema_medidas_morfologicas';

// Carregar variáveis de ambiente
dotenv.config();

// Configurar conexão com o banco de dados online
let onlineDb: any = null; // Objeto Drizzle
let onlineSql: any = null; // Cliente SQL direto
let syncEnabled = false;

// Inicializar conexão com o banco de dados online
export async function initOnlineDb() {
  try {
    if (!process.env.ONLINE_DATABASE_URL) {
      console.warn('ONLINE_DATABASE_URL não está definida. Sincronização desabilitada.');
      return false;
    }

    // Usar o módulo postgres para conectar ao banco de dados online
    onlineSql = postgres(process.env.ONLINE_DATABASE_URL, {
      ssl: { rejectUnauthorized: false },
      max: 5, // reduzir o máximo de conexões simultâneas
      idle_timeout: 30, // aumentar o tempo máximo de inatividade em segundos
      connect_timeout: 30, // aumentar o tempo máximo para conectar em segundos
      connection: {
        application_name: 'equigestor-sync',
        keepalive: true, // manter conexão ativa
        keepaliveInitialDelayMs: 10000 // delay inicial para keepalive
      },
      debug: true, // habilitar logs de debug
      transform: {
        undefined: null, // transformar undefined em null
      }
    });

    // Testar conexão
    await onlineSql`SELECT 1`;

    // Configurar drizzle com o cliente postgres
    onlineDb = drizzle(onlineSql);

    console.log('Conexão com o banco de dados online estabelecida com sucesso.');
    syncEnabled = true;
    return true;
  } catch (error) {
    console.error('Erro ao conectar ao banco de dados online:', error);
    syncEnabled = false;
    return false;
  }
}

// Verificar se a sincronização está habilitada
export function isSyncEnabled() {
  return syncEnabled;
}

// Sincronizar dados do banco local para o online
export async function syncLocalToOnline(user_id: number) {
  if (!syncEnabled || !onlineSql) {
    console.warn('Sincronização desabilitada ou banco de dados online não inicializado.');
    return false;
  }

  try {
    console.log(`Iniciando sincronização de dados locais para online para o usuário ${user_id}...`);

    // Sincronizar cavalos
    const localCavalos = await localDb.select().from(cavalos).where(eq(cavalos.user_id, user_id));
    for (const cavalo of localCavalos) {
      // Verificar se o cavalo já existe no banco online
      const result = await onlineSql`SELECT * FROM cavalos WHERE id = ${cavalo.id}`;

      const existingCavalo = result[0];

      if (existingCavalo) {
        // Atualizar cavalo existente
        await onlineSql`
          UPDATE cavalos 
          SET name = ${cavalo.name}, 
              breed = ${cavalo.breed}, 
              sexo = ${cavalo.sexo}, 
              dataNascimento = ${cavalo.dataNascimento}, 
              cor = ${cavalo.cor}, 
              status = ${cavalo.status}, 
              user_id = ${cavalo.user_id}, 
              updatedAt = ${new Date()} 
          WHERE id = ${cavalo.id}
        `;
      } else {
        // Inserir novo cavalo
        await onlineSql`
          INSERT INTO cavalos (id, name, breed, sexo, dataNascimento, cor, status, user_id, created_at, updatedAt) 
          VALUES (${cavalo.id}, ${cavalo.name}, ${cavalo.breed}, ${cavalo.sexo}, ${cavalo.dataNascimento}, 
                 ${cavalo.cor}, ${cavalo.status}, ${cavalo.user_id}, ${cavalo.created_at}, ${cavalo.updatedAt})
        `;
      }
    }

    // Sincronizar medidas morfológicas
    const localMedidas = await localDb.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.user_id, user_id));

    for (const medida of localMedidas) {
      const result = await onlineSql`SELECT * FROM medidas_morfologicas WHERE id = ${medida.id}`;

      const existingMedida = result[0];

      if (existingMedida) {
        // Atualizar medida existente
        await onlineSql`
          UPDATE medidas_morfologicas 
          SET horse_id = ${medida.horse_id}, 
              user_id = ${medida.user_id}, 
              dataMedicao = ${medida.dataMedicao}, 
              alturaCernelha = ${medida.alturaCernelha}, 
              comprimentoCorpo = ${medida.comprimentoCorpo}, 
              perimetroToracico = ${medida.perimetroToracico}, 
              comprimentoGarupa = ${medida.comprimentoGarupa}, 
              perimetroCanela = ${medida.perimetroCanela}, 
              comprimentoCabeca = ${medida.comprimentoCabeca}, 
              larguraCabeca = ${medida.larguraCabeca}, 
              temperatura = ${medida.temperatura}, 
              indiceRelativo = ${medida.indiceRelativo}, 
              indiceRobustez = ${medida.indiceRobustez}, 
              indiceTorax = ${medida.indiceTorax}, 
              indiceCefalico = ${medida.indiceCefalico}, 
              updatedAt = ${new Date()} 
          WHERE id = ${medida.id}
        `;
      } else {
        // Inserir nova medida
        await onlineSql`
          INSERT INTO medidas_morfologicas (
            id, horse_id, user_id, dataMedicao, alturaCernelha, comprimentoCorpo, 
            perimetroToracico, comprimentoGarupa, perimetroCanela, comprimentoCabeca, 
            larguraCabeca, temperatura, indiceRelativo, indiceRobustez, indiceTorax, 
            indiceCefalico, created_at, updatedAt
          ) 
          VALUES (
            ${medida.id}, ${medida.horse_id}, ${medida.user_id}, ${medida.dataMedicao}, 
            ${medida.alturaCernelha}, ${medida.comprimentoCorpo}, ${medida.perimetroToracico}, 
            ${medida.comprimentoGarupa}, ${medida.perimetroCanela}, ${medida.comprimentoCabeca}, 
            ${medida.larguraCabeca}, ${medida.temperatura}, ${medida.indiceRelativo}, 
            ${medida.indiceRobustez}, ${medida.indiceTorax}, ${medida.indiceCefalico}, 
            ${medida.created_at}, ${medida.updatedAt}
          )
        `;
      }
    }

    // Adicionar sincronização para outras tabelas conforme necessário
    // ...

    console.log('Sincronização local para online concluída com sucesso.');
    return true;
  } catch (error) {
    console.error('Erro durante a sincronização local para online:', error);
    return false;
  }
}

// Sincronizar dados do banco online para o local
export async function syncOnlineToLocal(user_id: number) {
  if (!syncEnabled || !onlineSql) {
    console.warn('Sincronização desabilitada ou banco de dados online não inicializado.');
    return false;
  }

  try {
    console.log(`Iniciando sincronização de dados online para local para o usuário ${user_id}...`);

    // Sincronizar cavalos
    const onlineCavalos = await onlineSql`SELECT * FROM cavalos WHERE user_id = ${user_id}`;

    for (const cavalo of onlineCavalos) {
      // Verificar se o cavalo já existe no banco local
      const [existingCavalo] = await localDb.select()
        .from(cavalos)
        .where(eq(cavalos.id, cavalo.id));

      if (existingCavalo) {
        // Verificar qual registro é mais recente
        if (new Date(cavalo.updated_at) > new Date(existingCavalo.updatedAt)) {
          // Atualizar cavalo local com dados do online
          await localDb.update(cavalos)
            .set({
              name: cavalo.name,
              breed: cavalo.breed,
              sexo: cavalo.sexo,
              birth_date: cavalo.data_nascimento,
              cor: cavalo.cor,
              status: cavalo.status,
              user_id: cavalo.user_id,
              created_at: new Date()
            })
            .where(eq(cavalos.id, cavalo.id));
        }
      } else {
        // Inserir novo cavalo
        await localDb.insert(cavalos).values({
          id: cavalo.id,
          name: cavalo.name,
          breed: cavalo.breed,
          sexo: cavalo.sexo,
          dataNascimento: cavalo.data_nascimento,
          cor: cavalo.cor,
          status: cavalo.status,
          user_id: cavalo.user_id,
          created_at: new Date(cavalo.created_at),
          updatedAt: new Date(cavalo.updated_at)
        });
      }
    }

    // Sincronizar medidas morfológicas
    const onlineMedidas = await onlineSql`SELECT * FROM medidas_morfologicas WHERE user_id = ${user_id}`;

    for (const medida of onlineMedidas) {
      const [existingMedida] = await localDb.select()
        .from(medidasMorfologicas)
        .where(eq(medidasMorfologicas.id, medida.id));

      if (existingMedida) {
        if (new Date(medida.updated_at) > new Date(existingMedida.updatedAt)) {
          await localDb.update(medidasMorfologicas)
            .set({
              horse_id: medida.horse_id,
              user_id: medida.user_id,
              data_medicao: medida.data_medicao,
              altura_cernelha: medida.altura_cernelha,
              comprimento_corpo: medida.comprimento_corpo,
              perimetro_toracico: medida.perimetro_toracico,
              comprimento_garupa: medida.comprimento_garupa,
              perimetro_canela: medida.perimetro_canela,
              comprimento_cabeca: medida.comprimento_cabeca,
              largura_cabeca: medida.largura_cabeca,
              temperatura: medida.temperatura,
              indice_relativo: medida.indice_relativo,
              indice_robustez: medida.indice_robustez,
              indice_torax: medida.indice_torax,
              indice_cefalico: medida.indice_cefalico,
              created_at: new Date()
            })
            .where(eq(medidasMorfologicas.id, medida.id));
        }
      } else {
        await localDb.insert(medidasMorfologicas).values({
          id: medida.id,
          horse_id: medida.horse_id,
          user_id: medida.user_id,
          dataMedicao: medida.data_medicao,
          alturaCernelha: medida.altura_cernelha,
          comprimentoCorpo: medida.comprimento_corpo,
          perimetroToracico: medida.perimetro_toracico,
          comprimentoGarupa: medida.comprimento_garupa,
          perimetroCanela: medida.perimetro_canela,
          comprimentoCabeca: medida.comprimento_cabeca,
          larguraCabeca: medida.largura_cabeca,
          temperatura: medida.temperatura,
          indiceRelativo: medida.indice_relativo,
          indiceRobustez: medida.indice_robustez,
          indiceTorax: medida.indice_torax,
          indiceCefalico: medida.indice_cefalico,
          created_at: new Date(medida.created_at),
          updatedAt: new Date(medida.updated_at)
        });
      }
    }

    // Adicionar sincronização para outras tabelas conforme necessário
    // ...

    console.log('Sincronização online para local concluída com sucesso.');
    return true;
  } catch (error) {
    console.error('Erro durante a sincronização online para local:', error);
    return false;
  }
}

// Iniciar sincronização periódica
export function startPeriodicSync(user_id: number) {
  const syncInterval = process.env.SYNC_INTERVAL ? parseInt(process.env.SYNC_INTERVAL) : 3600000; // 1 hora por padrão

  console.log(`Configurando sincronização periódica a cada ${syncInterval / 1000 / 60} minutos.`);

  // Verificar se a conexão com o banco de dados online está ativa
  if (!syncEnabled || !onlineSql) {
    console.warn('Sincronização periódica não iniciada: banco de dados online não disponível.');
    return;
  }

  // Sincronizar imediatamente ao iniciar
  syncOnlineToLocal(user_id).then(() => {
    return syncLocalToOnline(user_id);
  }).catch(error => {
    console.error('Erro na sincronização inicial:', error);
  });

  // Configurar sincronização periódica
  setInterval(async () => {
    try {
      // Verificar novamente se a sincronização ainda está habilitada
      if (!syncEnabled || !onlineSql) {
        console.warn('Sincronização periódica ignorada: banco de dados online não disponível.');
        return;
      }
      
      await syncOnlineToLocal(user_id);
      await syncLocalToOnline(user_id);
    } catch (error) {
      console.error('Erro na sincronização periódica:', error);
    }
  }, syncInterval);
}
