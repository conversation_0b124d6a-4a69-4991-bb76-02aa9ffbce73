<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend Auth</title>
</head>
<body>
    <h1>Test Frontend Auth Connection</h1>
    <button onclick="testLogin()">Test Login API</button>
    <button onclick="testHealthCheck()">Test Health Check</button>
    <div id="result"></div>

    <script>
        async function testHealthCheck() {
            const resultDiv = document.getElementById('result');
            try {
                resultDiv.innerHTML = 'Testing health check...';
                const response = await fetch('/health');
                const data = await response.json();
                resultDiv.innerHTML = `<h3>Health Check Result:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<h3>Health Check Error:</h3><pre>${error.message}</pre>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('result');
            try {
                resultDiv.innerHTML = 'Testing login API...';
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const contentType = response.headers.get('content-type');
                if (response.ok) {
                    if (contentType && contentType.includes('application/json')) {
                        let data;
                        try {
                            data = await response.json();
                        } catch (e) {
                            throw new Error('Server returned non-JSON response');
                        }
                        resultDiv.innerHTML = `<h3>Login Success:</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    } else {
                        throw new Error('Server returned non-JSON response');
                    }
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<h3>Login Error (${response.status}):</h3><pre>${errorData}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h3>Login Network Error:</h3><pre>${error.message}</pre>`;
                console.error('Login error:', error);
            }
        }
    </script>
</body>
</html>