import express, { Request, Response } from 'express';
import { db<PERSON><PERSON><PERSON> } from './db-connection-handler';

export async function addApiRoutes(app: express.Application) {
  console.log('Setting up minimal API routes...');

  // Database status endpoint
  app.get('/api/status', (req: Request, res: Response) => {
    const dbStatus = dbHandler.getStatus();
    const isConnected = dbHandler.isConnected();
    
    res.json({
      server: 'online',
      database: {
        status: dbStatus,
        connected: isConnected
      },
      timestamp: new Date().toISOString()
    });
  });

  // Test database connectivity
  app.get('/api/db-test', async (req: Request, res: Response) => {
    try {
      const connected = await dbHandler.connect();
      res.json({
        success: connected,
        status: dbHandler.getStatus(),
        message: connected ? 'Database connected successfully' : 'Database connection failed'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message,
        status: dbHandler.getStatus()
      });
    }
  });

  // Initialize database connection attempt
  try {
    console.log('Attempting database connection...');
    await dbHandler.connect();
  } catch (error) {
    console.log('Database connection will be retried in background');
  }

  // Add full routes if database is connected
  if (dbHandler.isConnected()) {
    try {
      console.log('Database connected - loading full API routes...');
      const { addApiRoutes: addFullRoutes } = await import('./routes');
      await addFullRoutes(app);
      console.log('Full API routes loaded successfully');
      return;
    } catch (error) {
      console.error('Failed to load full routes:', error.message);
    }
  }

  console.log('Running with minimal routes - database features unavailable');
  
  // Fallback routes for when database is not available
  app.get('/api/cavalos', (req: Request, res: Response) => {
    res.status(503).json({
      error: 'Database unavailable',
      message: 'Horse management features require database connection',
      status: dbHandler.getStatus()
    });
  });

  // Teste simples para rotas financeiras
  app.get('/api/financeiro/test', (req: Request, res: Response) => {
    res.json({
      status: 'ok',
      message: 'Módulo financeiro funcionando',
      timestamp: new Date().toISOString()
    });
  });

  app.get('/api/manejos', (req: Request, res: Response) => {
    res.status(503).json({
      error: 'Database unavailable',
      message: 'Management features require database connection',
      status: dbHandler.getStatus()
    });
  });

  app.get('/api/genealogia/*', (req: Request, res: Response) => {
    res.status(503).json({
      error: 'Database unavailable',
      message: 'Genealogy features require database connection',
      status: dbHandler.getStatus()
    });
  });

  // Generic fallback for other API routes
  app.use('/api/*', (req: Request, res: Response) => {
    if (!dbHandler.isConnected()) {
      res.status(503).json({
        error: 'Database unavailable',
        message: 'This feature requires database connection',
        status: dbHandler.getStatus(),
        endpoint: req.path
      });
    } else {
      res.status(404).json({
        error: 'Endpoint not found',
        endpoint: req.path
      });
    }
  });

  console.log('Minimal API routes configured');
}