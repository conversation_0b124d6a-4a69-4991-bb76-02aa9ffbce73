// Application constants and configuration
export const APP_CONFIG = {
  name: 'EquiGestor AI',
  version: '2.0.0',
  description: 'Sistema de Gestão Equina Inteligente',
  port: 5000,
  host: '0.0.0.0'
} as const;

export const DATABASE_CONFIG = {
  maxConnections: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
  maxRetries: 3,
  retryDelayMs: 1000
} as const;

export const VALIDATION_RULES = {
  maxNameLength: 255,
  maxDescriptionLength: 1000,
  maxNotesLength: 2000,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxPasswordLength: 128,
  minPasswordLength: 6,
  maxPaginationLimit: 100
} as const;

export const HORSE_STATUS = {
  ACTIVE: 'ativo',
  INACTIVE: 'inativo',
  SOLD: 'vendido',
  DECEASED: 'falecido',
  NOT_INFORMED: 'nao_informado',
  COMPETITION: 'competicao',
  REPRODUCTION: 'reproducao'
} as const;

export const HORSE_SEXO = {
  MALE: 'Macho',
  FEMALE: 'Fêmea',
  CASTRATED_MALE: 'Macho (Castrado)',
  STALLION: 'Garanhão',
  MARE: 'Égua'
} as const;

export const USER_ROLES = {
  ADMIN: 'ADMIN',
  USER: 'USER'
} as const;

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  DATABASE_ERROR: 'DATABASE_ERROR',
  BUSINESS_LOGIC_ERROR: 'BUSINESS_LOGIC_ERROR',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR'
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500
} as const;

export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  HORSE_LIST: 'horse_list',
  HORSE_DETAILS: 'horse_details',
  HORSE_STATISTICS: 'horse_statistics',
  GENEALOGY: 'genealogy'
} as const;

export const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600 // 1 hour
} as const;