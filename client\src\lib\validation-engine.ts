import { z, ZodError } from 'zod';
import { insertCavaloSchema, updateCavaloSchema } from '@shared/insert-schemas';

// Tipos para o sistema de validação
export type ValidationResult = {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
  formatted: Record<string, string>;
};

export type ValidationOptions = {
  strict?: boolean;
  includeWarnings?: boolean;
  customRules?: Record<string, (value: any) => string | null>;
};

/**
 * Sistema de validação inteligente que aplica recursão em vez de loops
 * para processar erros de validação seguindo as melhores práticas
 */
export class ValidationEngine {
  private customRules: Record<string, (value: any) => string | null> = {};
  private warningRules: Record<string, (value: any) => string | null> = {};

  constructor(options?: ValidationOptions) {
    if (options?.customRules) {
      this.customRules = options.customRules;
    }
    this.initializeWarningRules();
  }

  /**
   * Inicializa regras de warning específicas para o domínio equino
   */
  private initializeWarningRules() {
    this.warningRules = {
      birth_date: (value: string) => {
        if (value) {
          const birthDate = new Date(value);
          const currentDate = new Date();
          const age = currentDate.getFullYear() - birthDate.getFullYear();
          
          if (age > 30) {
            return 'Idade muito avançada para um cavalo (>30 anos)';
          }
          if (age < 0) {
            return 'Data de nascimento não pode ser no futuro';
          }
        }
        return null;
      },
      
      peso: (value: string) => {
        if (value) {
          const weight = parseFloat(value);
          if (weight < 200) {
            return 'Peso muito baixo para um cavalo adulto (<200kg)';
          }
          if (weight > 1200) {
            return 'Peso muito alto para um cavalo (>1200kg)';
          }
        }
        return null;
      },
      
      altura: (value: string) => {
        if (value) {
          const height = parseFloat(value);
          if (height < 1.0) {
            return 'Altura muito baixa para um cavalo (<1.0m)';
          }
          if (height > 2.0) {
            return 'Altura muito alta para um cavalo (>2.0m)';
          }
        }
        return null;
      },
      
      numero_registro: (value: string) => {
        if (value && value.length > 20) {
          return 'Número de registro muito longo (>20 caracteres)';
        }
        return null;
      }
    };
  }

  /**
   * Valida dados do cavalo usando recursão para processar erros
   */
  validateCavalo(data: any, isUpdate: boolean = false): ValidationResult {
    const schema = isUpdate ? updateCavaloSchema : insertCavaloSchema;
    
    try {
      schema.parse(data);
      
      // Se passou na validação principal, verificar warnings
      const warnings = this.processWarnings(data);
      
      return {
        isValid: true,
        errors: {},
        warnings,
        formatted: {}
      };
    } catch (error) {
      if (error instanceof ZodError) {
        const errors = this.processZodErrors(error.errors);
        const warnings = this.processWarnings(data);
        
        return {
          isValid: false,
          errors,
          warnings,
          formatted: this.formatErrorsForDisplay(errors)
        };
      }
      
      return {
        isValid: false,
        errors: { general: ['Erro de validação desconhecido'] },
        warnings: {},
        formatted: { general: 'Erro de validação desconhecido' }
      };
    }
  }

  /**
   * Processa erros do Zod usando recursão
   */
  private processZodErrors(errors: z.ZodIssue[], processed: Record<string, string[]> = {}): Record<string, string[]> {
    if (errors.length === 0) {
      return processed;
    }

    const [currentError, ...remainingErrors] = errors;
    const field = currentError.path.join('.');
    const message = this.getCustomErrorMessage(currentError);

    if (!processed[field]) {
      processed[field] = [];
    }
    processed[field].push(message);

    return this.processZodErrors(remainingErrors, processed);
  }

  /**
   * Processa warnings usando recursão
   */
  private processWarnings(data: any, fields: string[] = Object.keys(this.warningRules), warnings: Record<string, string[]> = {}): Record<string, string[]> {
    if (fields.length === 0) {
      return warnings;
    }

    const [currentField, ...remainingFields] = fields;
    const warningRule = this.warningRules[currentField];
    
    if (warningRule && data[currentField] !== undefined) {
      const warning = warningRule(data[currentField]);
      if (warning) {
        if (!warnings[currentField]) {
          warnings[currentField] = [];
        }
        warnings[currentField].push(warning);
      }
    }

    return this.processWarnings(data, remainingFields, warnings);
  }

  /**
   * Gera mensagens de erro customizadas para o contexto equino
   */
  private getCustomErrorMessage(error: z.ZodIssue): string {
    const fieldName = error.path[error.path.length - 1];
    
    const fieldMessages: Record<string, Record<string, string>> = {
      nome: {
        too_small: 'O nome do cavalo deve ter pelo menos 2 caracteres',
        required: 'O nome do cavalo é obrigatório'
      },
      birth_date: {
        required: 'A data de nascimento é obrigatória',
        invalid_date: 'Data de nascimento inválida'
      },
      sexo: {
        required: 'O sexo do cavalo é obrigatório',
        invalid_enum_value: 'Valor inválido para sexo. Use: Macho, Fêmea, Garanhão, Égua ou Macho (Castrado)'
      },
      breed: {
        required: 'A raça do cavalo é obrigatória',
        too_small: 'A raça deve ter pelo menos 2 caracteres'
      },
      peso: {
        invalid_type: 'O peso deve ser um número',
        too_small: 'O peso deve ser maior que 0'
      },
      altura: {
        invalid_type: 'A altura deve ser um número',
        too_small: 'A altura deve ser maior que 0'
      },
      numero_registro: {
        too_big: 'O número de registro não pode ter mais de 50 caracteres'
      }
    };

    const fieldSpecificMessages = fieldMessages[fieldName as string];
    if (fieldSpecificMessages && fieldSpecificMessages[error.code]) {
      return fieldSpecificMessages[error.code];
    }

    // Mensagens genéricas baseadas no tipo de erro
    switch (error.code) {
      case 'too_small':
        return `${fieldName} deve ter pelo menos ${error.minimum} caracteres`;
      case 'too_big':
        return `${fieldName} não pode ter mais de ${error.maximum} caracteres`;
      case 'invalid_type':
        return `${fieldName} deve ser do tipo ${error.expected}`;
      case 'required':
        return `${fieldName} é obrigatório`;
      case 'invalid_enum_value':
        return `Valor inválido para ${fieldName}`;
      default:
        return error.message;
    }
  }

  /**
   * Formata erros para exibição usando recursão
   */
  private formatErrorsForDisplay(errors: Record<string, string[]>, formatted: Record<string, string> = {}, fields: string[] = Object.keys(errors)): Record<string, string> {
    if (fields.length === 0) {
      return formatted;
    }

    const [currentField, ...remainingFields] = fields;
    const fieldErrors = errors[currentField];
    
    if (fieldErrors && fieldErrors.length > 0) {
      formatted[currentField] = fieldErrors.join(', ');
    }

    return this.formatErrorsForDisplay(errors, formatted, remainingFields);
  }

  /**
   * Valida campo específico
   */
  validateField(fieldName: string, value: any, isUpdate: boolean = false): { isValid: boolean; error?: string; warning?: string } {
    const schema = isUpdate ? updateCavaloSchema : insertCavaloSchema;
    
    try {
      // Criar objeto temporário para validação
      const tempData = { [fieldName]: value };
      const fieldSchema = schema.pick({ [fieldName]: true } as any);
      
      fieldSchema.parse(tempData);
      
      // Verificar warnings
      const warningRule = this.warningRules[fieldName];
      const warning = warningRule ? warningRule(value) : null;
      
      return {
        isValid: true,
        warning: warning || undefined
      };
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldError = error.errors[0];
        return {
          isValid: false,
          error: this.getCustomErrorMessage(fieldError)
        };
      }
      
      return {
        isValid: false,
        error: 'Erro de validação'
      };
    }
  }

  /**
   * Adiciona regra de validação customizada
   */
  addCustomRule(fieldName: string, rule: (value: any) => string | null): void {
    this.customRules[fieldName] = rule;
  }

  /**
   * Adiciona regra de warning customizada
   */
  addWarningRule(fieldName: string, rule: (value: any) => string | null): void {
    this.warningRules[fieldName] = rule;
  }

  /**
   * Valida consistência genealógica
   */
  validateGenealogy(data: any): ValidationResult {
    const errors: Record<string, string[]> = {};
    
    // Validar que pai e mãe não sejam o mesmo cavalo
    if (data.pai_id && data.mae_id && data.pai_id === data.mae_id) {
      errors.genealogy = ['Pai e mãe não podem ser o mesmo cavalo'];
    }
    
    // Validar que o cavalo não seja pai de si mesmo
    if (data.pai_id && data.pai_id === data.id) {
      errors.pai_id = ['Cavalo não pode ser pai de si mesmo'];
    }
    
    // Validar que o cavalo não seja mãe de si mesmo
    if (data.mae_id && data.mae_id === data.id) {
      errors.mae_id = ['Cavalo não pode ser mãe de si mesmo'];
    }
    
    const hasErrors = Object.keys(errors).length > 0;
    
    return {
      isValid: !hasErrors,
      errors,
      warnings: {},
      formatted: this.formatErrorsForDisplay(errors)
    };
  }
}

// Instância global do motor de validação
export const validationEngine = new ValidationEngine({
  strict: true,
  includeWarnings: true
});

// Hooks para uso em componentes React
export const useValidation = () => {
  return {
    validateCavalo: validationEngine.validateCavalo.bind(validationEngine),
    validateField: validationEngine.validateField.bind(validationEngine),
    validateGenealogy: validationEngine.validateGenealogy.bind(validationEngine)
  };
};