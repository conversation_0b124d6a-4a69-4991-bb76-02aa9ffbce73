/**
 * Middleware de logging estruturado com Pino
 * Injeta req.id e grava logs JSON estruturados
 */
import pino from 'pino';
import { randomUUID } from 'crypto';
import { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import path from 'path';

// Garantir que o diretório logs existe
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Configurar Pino com destinos múltiplos
const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  formatters: {
    level: (label) => {
      return { level: label };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
}, pino.multistream([
  // Console para desenvolvimento
  {
    level: 'debug',
    stream: process.stdout
  },
  // Arquivo para todos os logs
  {
    level: 'info',
    stream: pino.destination({
      dest: path.join(logsDir, 'app.log'),
      sync: false
    })
  },
  // Arquivo separado para erros
  {
    level: 'error',
    stream: pino.destination({
      dest: path.join(logsDir, 'error.log'),
      sync: false
    })
  }
]));

// Estender Request para incluir id e logger
declare global {
  namespace Express {
    interface Request {
      id: string;
      logger: pino.Logger;
    }
  }
}

/**
 * Middleware que injeta request ID e logger estruturado
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const requestId = randomUUID();
  req.id = requestId;
  
  // Criar logger child com contexto da requisição
  req.logger = logger.child({
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.socket.remoteAddress,
    user_id: (req as any).user?.id || 'anonymous'
  });

  // Log da requisição
  req.logger.info({
    event: 'request_start',
    headers: req.headers,
    query: req.query,
    body: req.method !== 'GET' ? req.body : undefined
  }, 'Request received');

  const startTime = Date.now();

  // Log da resposta quando terminar
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const level = res.statusCode >= 400 ? 'error' : 'info';
    
    req.logger[level]({
      event: 'request_complete',
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('Content-Length')
    }, 'Request completed');
  });

  next();
};

export { logger };