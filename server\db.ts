// Re-export from the connection handler for backward compatibility
export { db<PERSON><PERSON><PERSON> as connectionHandler } from './db-connection-handler';

import { dbHand<PERSON> } from './db-connection-handler';

// Export the actual Drizzle ORM instance
export const db = new Proxy({} as any, {
  get(target, prop) {
    if (dbHandler.isConnected()) {
      const dbInstance = dbHandler.getDB();
      return dbInstance[prop];
    }
    throw new Error(`Database not connected - cannot access property: ${String(prop)}`);
  }
});

// Export the actual PostgreSQL pool instance
export const pool = new Proxy({} as any, {
  get(target, prop) {
    if (dbHandler.isConnected()) {
      const poolInstance = dbHandler.getPool();
      return (poolInstance as any)[prop];
    }
    throw new Error(`Database pool not available - cannot access property: ${String(prop)}`);
  }
});
