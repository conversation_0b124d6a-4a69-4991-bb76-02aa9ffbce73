import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutWrapper } from '@/components/Layout';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  PlusCircle, Search, Edit, Trash2, Package, ShoppingCart, AlertCircle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

/**
 * Interface para definir a estrutura de um insumo
 */
interface Insumo {
  id: number;
  nome: string;
  categoria: string;
  unidade: string;
  quantidadeEstoque: number;
  estoqueMinimo: number;
  valorUnitario: number;
  fornecedor: string | null;
  ultimaCompra: string | null;
  statusEstoque: 'normal' | 'baixo' | 'critico';
}

/**
 * Componente da página de gestão de insumos
 */
export function InsumosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('todos');
  const [statusFilter, setStatusFilter] = useState('todos');

  // Filtrar insumos com base nos filtros aplicados
  const filteredInsumos = insumosData.filter(insumo => {
    const matchesSearch = insumo.nome.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         (insumo.fornecedor && insumo.fornecedor.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'todos' || insumo.categoria === categoryFilter;
    
    const matchesStatus = statusFilter === 'todos' || 
                         (statusFilter === 'baixo' && insumo.statusEstoque === 'baixo') || 
                         (statusFilter === 'critico' && insumo.statusEstoque === 'critico') ||
                         (statusFilter === 'normal' && insumo.statusEstoque === 'normal');
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Função para renderizar o status do estoque
  const renderEstoqueStatus = (insumo: Insumo) => {
    if (insumo.statusEstoque === 'critico') {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Crítico
      </Badge>;
    } else if (insumo.statusEstoque === 'baixo') {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600 flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Baixo
      </Badge>;
    } else {
      return <Badge variant="outline" className="text-green-600 border-green-600">
        Normal
      </Badge>;
    }
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <LayoutWrapper pageTitle="Gestão de Insumos">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Controle de Insumos</h1>
            <p className="text-muted-foreground">
              Gerencie os insumos utilizados nas atividades do haras
            </p>
          </div>
          
          <div className="flex gap-2">
            <Link href="/insumos/novo">
              <Button className="flex items-center gap-2">
                <PlusCircle className="h-4 w-4" />
                <span>Novo Insumo</span>
              </Button>
            </Link>
            <Link href="/insumos/compra">
              <Button variant="outline" className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                <span>Registrar Compra</span>
              </Button>
            </Link>
          </div>
        </div>
        
        {/* Card de filtros e busca */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Filtros e Busca</CardTitle>
            <CardDescription>
              Encontre e filtre insumos de acordo com suas necessidades
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar por nome ou fornecedor..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2">
                <Select 
                  value={categoryFilter} 
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="Categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todas</SelectItem>
                    <SelectItem value="Ração">Ração</SelectItem>
                    <SelectItem value="Medicamento">Medicamento</SelectItem>
                    <SelectItem value="Equipamento">Equipamento</SelectItem>
                    <SelectItem value="Limpeza">Limpeza</SelectItem>
                    <SelectItem value="Material">Material</SelectItem>
                  </SelectContent>
                </Select>
                
                <Tabs 
                  defaultValue="todos" 
                  className="w-full sm:w-auto"
                  onValueChange={setStatusFilter}
                >
                  <TabsList className="grid grid-cols-3 w-full sm:w-auto">
                    <TabsTrigger value="todos">Todos</TabsTrigger>
                    <TabsTrigger value="baixo">Baixo</TabsTrigger>
                    <TabsTrigger value="critico">Crítico</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de insumos */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Lista de Insumos</CardTitle>
            <CardDescription>
              {filteredInsumos.length} insumos encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredInsumos.length === 0 ? (
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Nenhum insumo encontrado</h3>
                <p className="text-muted-foreground">
                  {searchTerm || categoryFilter !== 'todos' || statusFilter !== 'todos' ? 
                    'Tente ajustar os critérios de busca' : 
                    'Adicione insumos ao sistema para começar'}
                </p>
              </div>
            ) : (
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead className="text-right">Quantidade</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Valor Unitário</TableHead>
                      <TableHead className="text-right">Última Compra</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInsumos.map(insumo => (
                      <TableRow key={insumo.id}>
                        <TableCell>
                          <div className="font-medium">{insumo.nome}</div>
                          <div className="text-sm text-muted-foreground">
                            {insumo.fornecedor || 'Fornecedor não especificado'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-normal">
                            {insumo.categoria}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {insumo.quantidadeEstoque} {insumo.unidade}
                          <div className="text-xs text-muted-foreground">
                            Min: {insumo.estoqueMinimo} {insumo.unidade}
                          </div>
                        </TableCell>
                        <TableCell>
                          {renderEstoqueStatus(insumo)}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(insumo.valorUnitario)}
                        </TableCell>
                        <TableCell className="text-right text-sm">
                          {insumo.ultimaCompra ? 
                            new Date(insumo.ultimaCompra).toLocaleDateString('pt-BR') : 
                            'N/A'}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <span className="sr-only">Abrir menu</span>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                  className="h-5 w-5"
                                >
                                  <path d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z"></path>
                                </svg>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <Link href={`/insumos/${insumo.id}`}>
                                <DropdownMenuItem>
                                  <Package className="mr-2 h-4 w-4" />
                                  Ver Detalhes
                                </DropdownMenuItem>
                              </Link>
                              <Link href={`/insumos/${insumo.id}/editar`}>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Editar
                                </DropdownMenuItem>
                              </Link>
                              <Link href={`/insumos/entrada/${insumo.id}`}>
                                <DropdownMenuItem>
                                  <ShoppingCart className="mr-2 h-4 w-4" />
                                  Registrar Entrada
                                </DropdownMenuItem>
                              </Link>
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Insumos com estoque crítico */}
        <Card className="border-red-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-red-600 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Insumos com Estoque Crítico
            </CardTitle>
            <CardDescription>
              Insumos que precisam de reposição urgente
            </CardDescription>
          </CardHeader>
          <CardContent>
            {insumosData.filter(i => i.statusEstoque === 'critico').length === 0 ? (
              <div className="text-center py-4">
                <p className="text-green-600">Não há insumos com estoque crítico no momento.</p>
              </div>
            ) : (
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Categoria</TableHead>
                      <TableHead className="text-right">Quantidade</TableHead>
                      <TableHead className="text-right">Mínimo</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {insumosData
                      .filter(i => i.statusEstoque === 'critico')
                      .map(insumo => (
                        <TableRow key={`critico-${insumo.id}`}>
                          <TableCell className="font-medium">{insumo.nome}</TableCell>
                          <TableCell>{insumo.categoria}</TableCell>
                          <TableCell className="text-right text-red-600 font-medium">
                            {insumo.quantidadeEstoque} {insumo.unidade}
                          </TableCell>
                          <TableCell className="text-right">
                            {insumo.estoqueMinimo} {insumo.unidade}
                          </TableCell>
                          <TableCell className="text-right">
                            <Link href={`/insumos/entrada/${insumo.id}`}>
                              <Button variant="outline" size="sm">
                                <ShoppingCart className="mr-1 h-3 w-3" />
                                <span>Comprar</span>
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}

// Dados de exemplo para insumos (apenas para demonstração)
const insumosData: Insumo[] = [
  {
    id: 1,
    nome: 'Ração Equina Premium',
    categoria: 'Ração',
    unidade: 'kg',
    quantidadeEstoque: 250,
    estoqueMinimo: 100,
    valorUnitario: 5.75,
    fornecedor: 'Nutrição Animal Ltda',
    ultimaCompra: new Date(Date.now() - 86400000 * 15).toISOString(), // 15 dias atrás
    statusEstoque: 'normal'
  },
  {
    id: 2,
    nome: 'Vermífugo Equino',
    categoria: 'Medicamento',
    unidade: 'un',
    quantidadeEstoque: 8,
    estoqueMinimo: 10,
    valorUnitario: 45.99,
    fornecedor: 'Vet Pharma',
    ultimaCompra: new Date(Date.now() - 86400000 * 30).toISOString(), // 30 dias atrás
    statusEstoque: 'baixo'
  },
  {
    id: 3,
    nome: 'Shampoo para Cavalos',
    categoria: 'Limpeza',
    unidade: 'L',
    quantidadeEstoque: 2,
    estoqueMinimo: 5,
    valorUnitario: 28.50,
    fornecedor: 'PetClean',
    ultimaCompra: new Date(Date.now() - 86400000 * 45).toISOString(), // 45 dias atrás
    statusEstoque: 'critico'
  },
  {
    id: 4,
    nome: 'Capa de Chuva para Cavalo',
    categoria: 'Equipamento',
    unidade: 'un',
    quantidadeEstoque: 12,
    estoqueMinimo: 5,
    valorUnitario: 189.90,
    fornecedor: 'Equestre Shop',
    ultimaCompra: new Date(Date.now() - 86400000 * 60).toISOString(), // 60 dias atrás
    statusEstoque: 'normal'
  },
  {
    id: 5,
    nome: 'Feno de Alfafa',
    categoria: 'Ração',
    unidade: 'kg',
    quantidadeEstoque: 320,
    estoqueMinimo: 200,
    valorUnitario: 2.80,
    fornecedor: 'Agro Forrageira',
    ultimaCompra: new Date(Date.now() - 86400000 * 7).toISOString(), // 7 dias atrás
    statusEstoque: 'normal'
  },
  {
    id: 6,
    nome: 'Anti-inflamatório Equino',
    categoria: 'Medicamento',
    unidade: 'un',
    quantidadeEstoque: 3,
    estoqueMinimo: 5,
    valorUnitario: 85.00,
    fornecedor: 'Vet Pharma',
    ultimaCompra: new Date(Date.now() - 86400000 * 20).toISOString(), // 20 dias atrás
    statusEstoque: 'critico'
  },
  {
    id: 7,
    nome: 'Pasta de Cascos',
    categoria: 'Material',
    unidade: 'un',
    quantidadeEstoque: 4,
    estoqueMinimo: 6,
    valorUnitario: 35.90,
    fornecedor: 'Casqueamento Ltda',
    ultimaCompra: new Date(Date.now() - 86400000 * 25).toISOString(), // 25 dias atrás
    statusEstoque: 'baixo'
  },
  {
    id: 8,
    nome: 'Escovas para Cavalos (Kit)',
    categoria: 'Material',
    unidade: 'kit',
    quantidadeEstoque: 15,
    estoqueMinimo: 8,
    valorUnitario: 75.50,
    fornecedor: 'Equestre Shop',
    ultimaCompra: new Date(Date.now() - 86400000 * 40).toISOString(), // 40 dias atrás
    statusEstoque: 'normal'
  },
];

export default InsumosPage;