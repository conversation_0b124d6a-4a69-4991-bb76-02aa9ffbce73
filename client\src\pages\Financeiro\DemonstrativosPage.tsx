import { useState } from 'react';
import { LayoutWrapper } from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  FileDown, Filter, RefreshCw, CalendarRange, 
  BarChart4, <PERSON><PERSON>hart, LineChart, Download, 
  Printer, Mail
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DatePicker } from "@/components/DatePicker";

/**
 * Página de demonstrativos financeiros
 * 
 * Permite gerar relatórios financeiros como balancetes, fluxo de caixa,
 * demonstrativo de resultados, etc.
 */
export default function DemonstrativosPage() {
  const { toast } = useToast();
  const [periodoInicio, setPeriodoInicio] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1) // Primeiro dia do mês atual
  );
  const [periodoFim, setPeriodoFim] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0) // Último dia do mês atual
  );
  const [tipoRelatorio, setTipoRelatorio] = useState<string>("fluxo-caixa");
  const [formatoRelatorio, setFormatoRelatorio] = useState<string>("pdf");
  
  // Tipos de relatórios disponíveis
  const tiposRelatorio = [
    { id: "fluxo-caixa", nome: "Fluxo de Caixa", icone: <LineChart className="h-5 w-5 text-blue-600" /> },
    { id: "balancete", nome: "Balancete", icone: <BarChart4 className="h-5 w-5 text-blue-600" /> },
    { id: "categorias", nome: "Despesas por Categoria", icone: <PieChart className="h-5 w-5 text-blue-600" /> },
    { id: "receitas", nome: "Receitas por Categoria", icone: <PieChart className="h-5 w-5 text-green-600" /> },
    { id: "dre", nome: "Demonstrativo de Resultados", icone: <BarChart4 className="h-5 w-5 text-purple-600" /> },
    { id: "cavalos", nome: "Despesas por Cavalo", icone: <BarChart4 className="h-5 w-5 text-amber-600" /> },
  ];
  
  // Formatos de exportação disponíveis
  const formatosExportacao = [
    { id: "pdf", nome: "PDF" },
    { id: "excel", nome: "Excel" },
    { id: "csv", nome: "CSV" },
  ];
  
  // Limpar filtros
  const limparFiltros = () => {
    setPeriodoInicio(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
    setPeriodoFim(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0));
    setTipoRelatorio("fluxo-caixa");
    setFormatoRelatorio("pdf");
  };
  
  // Gerar relatório
  const gerarRelatorio = () => {
    toast({
      title: "Gerando relatório",
      description: `${tiposRelatorio.find(t => t.id === tipoRelatorio)?.nome} no formato ${formatoRelatorio.toUpperCase()} sendo gerado.`,
    });
    
    // Aqui você implementaria a lógica para gerar o relatório
  };
  
  // Enviar relatório por e-mail
  const enviarEmail = () => {
    toast({
      title: "Enviando relatório por e-mail",
      description: "O relatório será enviado para o e-mail cadastrado.",
    });
    
    // Aqui você implementaria a lógica para enviar o relatório por e-mail
  };
  
  // Imprimir relatório
  const imprimirRelatorio = () => {
    toast({
      title: "Enviando para impressão",
      description: "Preparando relatório para impressão.",
    });
    
    // Aqui você implementaria a lógica para imprimir o relatório
  };
  
  return (
    <LayoutWrapper pageTitle="Demonstrativos Financeiros" showBackButton backUrl="/financeiro">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-blue-700">Demonstrativos Financeiros</h1>
            <p className="text-gray-600 mt-1">
              Gere relatórios financeiros personalizados
            </p>
          </div>
        </div>
        
        {/* Filtros e opções de relatório */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Painel de configuração */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Filter className="h-5 w-5 mr-1.5" />
                Configurações do Relatório
              </CardTitle>
              <CardDescription>
                Defina o período e tipo de relatório
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm">Período</Label>
                <div className="flex flex-col gap-2">
                  <DatePicker
                    date={periodoInicio}
                    onDateChange={setPeriodoInicio}
                    placeholder="Data inicial"
                  />
                  <DatePicker
                    date={periodoFim}
                    onDateChange={setPeriodoFim}
                    placeholder="Data final"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm">Tipo de Relatório</Label>
                <Select value={tipoRelatorio} onValueChange={setTipoRelatorio}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    {tiposRelatorio.map(tipo => (
                      <SelectItem key={tipo.id} value={tipo.id}>
                        <div className="flex items-center">
                          <span className="mr-2">{tipo.icone}</span>
                          {tipo.nome}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm">Formato de Exportação</Label>
                <Select value={formatoRelatorio} onValueChange={setFormatoRelatorio}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o formato" />
                  </SelectTrigger>
                  <SelectContent>
                    {formatosExportacao.map(formato => (
                      <SelectItem key={formato.id} value={formato.id}>
                        {formato.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="pt-4 space-y-3">
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  onClick={gerarRelatorio}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Gerar Relatório
                </Button>
                
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={enviarEmail}
                  >
                    <Mail className="mr-1 h-4 w-4" />
                    E-mail
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={imprimirRelatorio}
                  >
                    <Printer className="mr-1 h-4 w-4" />
                    Imprimir
                  </Button>
                </div>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full"
                  onClick={limparFiltros}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Limpar Filtros
                </Button>
              </div>
            </CardContent>
          </Card>
          
          {/* Prévia do relatório */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <FileDown className="h-5 w-5 mr-1.5" />
                Prévia do Relatório
              </CardTitle>
              <CardDescription>
                Visualize como ficará seu relatório
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue={tipoRelatorio} value={tipoRelatorio}>
                <TabsContent value="fluxo-caixa" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border">
                    <h3 className="text-lg font-semibold text-center mb-4">Fluxo de Caixa</h3>
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>Período: {periodoInicio?.toLocaleDateString()} a {periodoFim?.toLocaleDateString()}</span>
                      <span>Gerado em: {new Date().toLocaleDateString()}</span>
                    </div>
                    <div className="space-y-4">
                      {/* Layout simulado de um relatório de fluxo de caixa */}
                      <div className="border rounded-md overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-100">
                            <tr>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Entradas</th>
                              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Saídas</th>
                              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200 text-sm">
                            <tr>
                              <td className="px-4 py-2">01/03/2025</td>
                              <td className="px-4 py-2">Saldo Inicial</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 5.340,20</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2">05/03/2025</td>
                              <td className="px-4 py-2">Pagamento de Pensão</td>
                              <td className="px-4 py-2 text-right text-green-600">R$ 2.500,00</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 7.840,20</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2">10/03/2025</td>
                              <td className="px-4 py-2">Compra de Ração</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right text-red-600">R$ 850,75</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 6.989,45</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2">15/03/2025</td>
                              <td className="px-4 py-2">Aula de Equitação</td>
                              <td className="px-4 py-2 text-right text-green-600">R$ 150,00</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 7.139,45</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2">20/03/2025</td>
                              <td className="px-4 py-2">Ferrageamento</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right text-red-600">R$ 280,00</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 6.859,45</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2">25/03/2025</td>
                              <td className="px-4 py-2">Medicamentos</td>
                              <td className="px-4 py-2 text-right">-</td>
                              <td className="px-4 py-2 text-right text-red-600">R$ 375,50</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 6.483,95</td>
                            </tr>
                            <tr className="font-medium bg-gray-50">
                              <td className="px-4 py-2">31/03/2025</td>
                              <td className="px-4 py-2">Saldo Final</td>
                              <td className="px-4 py-2 text-right text-green-600">R$ 2.650,00</td>
                              <td className="px-4 py-2 text-right text-red-600">R$ 1.506,25</td>
                              <td className="px-4 py-2 text-right font-medium">R$ 6.483,95</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-center text-sm">
                        <div className="p-3 bg-green-50 rounded-md">
                          <div className="text-green-600 font-semibold">Total Entradas</div>
                          <div className="text-lg font-bold text-green-600">R$ 2.650,00</div>
                        </div>
                        <div className="p-3 bg-red-50 rounded-md">
                          <div className="text-red-600 font-semibold">Total Saídas</div>
                          <div className="text-lg font-bold text-red-600">R$ 1.506,25</div>
                        </div>
                        <div className="p-3 bg-blue-50 rounded-md">
                          <div className="text-blue-600 font-semibold">Saldo Final</div>
                          <div className="text-lg font-bold text-blue-600">R$ 6.483,95</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="balancete" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <BarChart4 className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                      <p>Selecione "Gerar Relatório" para visualizar o Balancete</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="categorias" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <PieChart className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                      <p>Selecione "Gerar Relatório" para visualizar o relatório de Despesas por Categoria</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="receitas" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <PieChart className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                      <p>Selecione "Gerar Relatório" para visualizar o relatório de Receitas por Categoria</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="dre" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <BarChart4 className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                      <p>Selecione "Gerar Relatório" para visualizar o Demonstrativo de Resultados</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="cavalos" className="mt-0">
                  <div className="bg-gray-50 p-4 rounded-md min-h-[400px] border flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <BarChart4 className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                      <p>Selecione "Gerar Relatório" para visualizar o relatório de Despesas por Cavalo</p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
        
        {/* Relatórios Recentes */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <CalendarRange className="h-5 w-5 mr-1.5" />
              Relatórios Recentes
            </CardTitle>
            <CardDescription>
              Histórico de relatórios gerados anteriormente
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-100 text-xs uppercase">
                  <tr>
                    <th className="px-4 py-3 text-left">Data</th>
                    <th className="px-4 py-3 text-left">Tipo</th>
                    <th className="px-4 py-3 text-left">Período</th>
                    <th className="px-4 py-3 text-left">Formato</th>
                    <th className="px-4 py-3 text-center">Ações</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm">28/03/2025 15:30</td>
                    <td className="px-4 py-3 text-sm">Fluxo de Caixa</td>
                    <td className="px-4 py-3 text-sm">01/03/2025 - 31/03/2025</td>
                    <td className="px-4 py-3 text-sm">PDF</td>
                    <td className="px-4 py-3 text-sm text-center">
                      <div className="flex justify-center space-x-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Printer className="h-4 w-4" />
                          <span className="sr-only">Imprimir</span>
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm">25/03/2025 10:15</td>
                    <td className="px-4 py-3 text-sm">Despesas por Categoria</td>
                    <td className="px-4 py-3 text-sm">01/02/2025 - 28/02/2025</td>
                    <td className="px-4 py-3 text-sm">Excel</td>
                    <td className="px-4 py-3 text-sm text-center">
                      <div className="flex justify-center space-x-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Printer className="h-4 w-4" />
                          <span className="sr-only">Imprimir</span>
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm">15/03/2025 08:45</td>
                    <td className="px-4 py-3 text-sm">Demonstrativo de Resultados</td>
                    <td className="px-4 py-3 text-sm">01/01/2025 - 28/02/2025</td>
                    <td className="px-4 py-3 text-sm">PDF</td>
                    <td className="px-4 py-3 text-sm text-center">
                      <div className="flex justify-center space-x-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Printer className="h-4 w-4" />
                          <span className="sr-only">Imprimir</span>
                        </Button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}