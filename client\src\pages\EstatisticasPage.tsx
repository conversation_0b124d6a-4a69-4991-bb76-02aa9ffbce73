import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3,
  PieChart,
  TrendingUp,
  Calendar,
  Target,
  Activity,
  ArrowDown,
  ArrowUp,
  Percent
} from "lucide-react";
import { 
  <PERSON><PERSON><PERSON> as Re<PERSON>ie<PERSON><PERSON>, 
  Pie, 
  Cell, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from "recharts";
import { Cavalo, Manejo, Reproducao } from "../../../shared/schema";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

/**
 * EstatisticasPage
 * 
 * Página dedicada a exibir estatísticas e análises do sistema EquiGestor,
 * incluindo dados sobre cavalos, manejos, saúde e reprodução.
 */
const EstatisticasPage = () => {
  const [tabAtiva, setTabAtiva] = useState("geral");
  const [periodoFiltro, setPeriodoFiltro] = useState("6meses");
  const [tipoFiltro, setTipoFiltro] = useState("todos");
  
  // Buscar dados para estatísticas
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('/api/cavalos') || [];
      } catch (error) {
        console.error("Erro ao buscar cavalos:", error);
        return [];
      }
    }
  });
  
  const { data: manejos = [], isLoading: loadingManejos } = useQuery({
    queryKey: ['/api/manejos'],
    queryFn: async () => {
      try {
        return await apiRequest<Manejo[]>('/api/manejos') || [];
      } catch (error) {
        console.error("Erro ao buscar manejos:", error);
        return [];
      }
    }
  });
  
  const { data: reproducoes = [], isLoading: loadingReproducoes } = useQuery({
    queryKey: ['/api/reproducoes'],
    queryFn: async () => {
      try {
        return await apiRequest<Reproducao[]>('/api/reproducoes') || [];
      } catch (error) {
        console.error("Erro ao buscar dados de reprodução:", error);
        return [];
      }
    }
  });

  // Dados calculados
  const isLoading = loadingCavalos || loadingManejos || loadingReproducoes;
  
  // Estatísticas de cavalos por sexo
  const cavalosPorSexo = cavalos.reduce((acc, cavalo) => {
    const sexo = cavalo.sexo ? cavalo.sexo.toLowerCase() : 'não informado';
    
    // Normalizar o valor do sexo
    let sexoNormalizado = 'não informado';
    if (sexo.includes('macho') || sexo === 'm') {
      sexoNormalizado = 'macho';
    } else if (sexo.includes('fêmea') || sexo.includes('femea') || sexo === 'f') {
      sexoNormalizado = 'fêmea';
    }
    
    if (!acc[sexoNormalizado]) {
      acc[sexoNormalizado] = 0;
    }
    
    acc[sexoNormalizado]++;
    return acc;
  }, {} as Record<string, number>);
  
  // Formatar dados para gráfico de sexo
  const dadosSexo = Object.entries(cavalosPorSexo).map(([sexo, quantidade]) => ({
    name: sexo.charAt(0).toUpperCase() + sexo.slice(1),
    value: quantidade
  }));
  
  // Estatísticas de cavalos por raça
  const cavalosPorRaca = cavalos.reduce((acc, cavalo) => {
    const raca = cavalo.breed || 'não informado';
    
    if (!acc[raca]) {
      acc[raca] = 0;
    }
    
    acc[raca]++;
    return acc;
  }, {} as Record<string, number>);
  
  // Formatar dados para gráfico de raça
  const dadosRaca = Object.entries(cavalosPorRaca)
    .map(([raca, quantidade]) => ({
      name: raca.charAt(0).toUpperCase() + raca.slice(1),
      value: quantidade
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 5); // Mostrar só as 5 raças mais comuns
  
  // Estatísticas de manejos por tipo
  const manejosPorTipo = manejos.reduce((acc, manejo) => {
    const tipo = manejo.tipo || 'outro';
    
    if (!acc[tipo]) {
      acc[tipo] = 0;
    }
    
    acc[tipo]++;
    return acc;
  }, {} as Record<string, number>);
  
  // Formatar dados para gráfico de manejos
  const dadosManejos = Object.entries(manejosPorTipo).map(([tipo, quantidade]) => {
    const tipoFormatado = (() => {
      switch(tipo) {
        case 'veterinary': return 'Veterinário';
        case 'farrier': return 'Ferrageamento';
        case 'dental': return 'Odontológico';
        case 'vaccination': return 'Vacinação';
        case 'deworming': return 'Vermifugação';
        case 'training': return 'Treinamento';
        default: return tipo.charAt(0).toUpperCase() + tipo.slice(1);
      }
    })();
    
    return {
      name: tipoFormatado,
      value: quantidade
    };
  });
  
  // Calcular estatísticas de manejos por mês (últimos 6 meses)
  const manejosPorMes = manejos.reduce((acc, manejo) => {
    const dataManejo = new Date(manejo.data);
    const mesAno = `${dataManejo.getFullYear()}-${String(dataManejo.getMonth() + 1).padStart(2, '0')}`;
    
    if (!acc[mesAno]) {
      acc[mesAno] = 0;
    }
    
    acc[mesAno]++;
    return acc;
  }, {} as Record<string, number>);
  
  // Ordenar por data e pegar os últimos 6 meses
  const mesesOrdenados = Object.keys(manejosPorMes)
    .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    .slice(-6);
  
  // Formatar dados para gráfico de manejos por mês
  const dadosManejosPorMes = mesesOrdenados.map(mesAno => {
    const [ano, mes] = mesAno.split('-');
    const nomesMeses = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];
    const nomeMes = nomesMeses[parseInt(mes) - 1];
    
    return {
      mes: `${nomeMes}/${ano.slice(2)}`,
      quantidade: manejosPorMes[mesAno]
    };
  });
  
  // Cores para os gráficos
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-blue-700 tracking-tight">Estatísticas</h1>
        <p className="mt-1 text-gray-600">
          Análise de dados e métricas do seu plantel
        </p>
      </div>
      
      <div className="mb-6">
        <Tabs defaultValue="geral" className="w-full" onValueChange={setTabAtiva}>
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="geral" className="flex items-center">
                <BarChart3 className="mr-2 h-4 w-4" />
                Geral
              </TabsTrigger>
              <TabsTrigger value="saude" className="flex items-center">
                <Activity className="mr-2 h-4 w-4" />
                Saúde
              </TabsTrigger>
              <TabsTrigger value="reproducao" className="flex items-center">
                <Target className="mr-2 h-4 w-4" />
                Reprodução
              </TabsTrigger>
            </TabsList>
            
            <div className="flex gap-2">
              <Select value={periodoFiltro} onValueChange={setPeriodoFiltro}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Período" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3meses">Últimos 3 meses</SelectItem>
                  <SelectItem value="6meses">Últimos 6 meses</SelectItem>
                  <SelectItem value="1ano">Último ano</SelectItem>
                  <SelectItem value="todos">Todo período</SelectItem>
                </SelectContent>
              </Select>
              
              {tabAtiva === 'saude' && (
                <Select value={tipoFiltro} onValueChange={setTipoFiltro}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Tipo de Manejo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os tipos</SelectItem>
                    <SelectItem value="veterinary">Veterinário</SelectItem>
                    <SelectItem value="farrier">Ferrageamento</SelectItem>
                    <SelectItem value="dental">Odontológico</SelectItem>
                    <SelectItem value="vaccination">Vacinação</SelectItem>
                    <SelectItem value="training">Treinamento</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          
          <TabsContent value="geral">
            {isLoading ? (
              <div className="flex justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <>
                {/* Cards de estatísticas resumidas */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Total de Cavalos</p>
                          <h3 className="mt-1 text-3xl font-semibold">{cavalos.length}</h3>
                          <p className="mt-1 text-sm text-gray-500">
                            {cavalosPorSexo['macho'] || 0} machos, {cavalosPorSexo['fêmea'] || 0} fêmeas
                          </p>
                        </div>
                        <div className="p-2 bg-blue-100 rounded-full">
                          <PieChart className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-green-600">Total de Manejos</p>
                          <h3 className="mt-1 text-3xl font-semibold">{manejos.length}</h3>
                          <p className="mt-1 text-sm text-gray-500">
                            {manejos.filter(m => m.status === 'concluido').length} concluídos
                          </p>
                        </div>
                        <div className="p-2 bg-green-100 rounded-full">
                          <TrendingUp className="h-6 w-6 text-green-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-purple-600">Eventos Reprodutivos</p>
                          <h3 className="mt-1 text-3xl font-semibold">{reproducoes.length}</h3>
                          <p className="mt-1 text-sm text-gray-500">
                            {reproducoes.filter(r => r.estado === 'sucesso').length} sucessos
                          </p>
                        </div>
                        <div className="p-2 bg-purple-100 rounded-full">
                          <Target className="h-6 w-6 text-purple-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Gráficos e estatísticas detalhadas */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  <Card>
                    <CardHeader>
                      <CardTitle>Distribuição por Sexo</CardTitle>
                      <CardDescription>
                        Proporção de machos e fêmeas no plantel
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RePieChart>
                            <Pie
                              data={dadosSexo}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {dadosSexo.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip formatter={(value) => [`${value} cavalos`, 'Quantidade']} />
                            <Legend />
                          </RePieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Principais Raças</CardTitle>
                      <CardDescription>
                        As 5 raças mais comuns no seu plantel
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={dadosRaca}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis dataKey="name" type="category" />
                            <Tooltip formatter={(value) => [`${value} cavalos`, 'Quantidade']} />
                            <Bar dataKey="value" fill="#0088FE" barSize={30} />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                <div className="grid grid-cols-1 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Evolução de Manejos</CardTitle>
                      <CardDescription>
                        Quantidade de manejos registrados nos últimos 6 meses
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={dadosManejosPorMes}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="mes" />
                            <YAxis />
                            <Tooltip formatter={(value) => [`${value} manejos`, 'Quantidade']} />
                            <Legend />
                            <Line 
                              type="monotone" 
                              dataKey="quantidade" 
                              name="Manejos Registrados"
                              stroke="#82ca9d" 
                              activeDot={{ r: 8 }}
                              strokeWidth={2}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </TabsContent>
          
          <TabsContent value="saude">
            {isLoading ? (
              <div className="flex justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-red-600">Manejos Veterinários</p>
                          <h3 className="mt-1 text-3xl font-semibold">
                            {manejos.filter(m => m.tipo === 'veterinary').length}
                          </h3>
                          <div className="mt-1 flex items-center">
                            <Badge className="bg-green-100 text-green-700 border-green-200">
                              {Math.round((manejos.filter(m => m.tipo === 'veterinary' && m.status === 'concluido').length / 
                              Math.max(manejos.filter(m => m.tipo === 'veterinary').length, 1)) * 100)}% concluídos
                            </Badge>
                          </div>
                        </div>
                        <div className="p-2 bg-red-100 rounded-full">
                          <Activity className="h-6 w-6 text-red-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-amber-600">Manejos com Ferradores</p>
                          <h3 className="mt-1 text-3xl font-semibold">
                            {manejos.filter(m => m.tipo === 'farrier').length}
                          </h3>
                          <div className="mt-1 flex items-center">
                            <Badge className="bg-green-100 text-green-700 border-green-200">
                              {Math.round((manejos.filter(m => m.tipo === 'farrier' && m.status === 'concluido').length / 
                              Math.max(manejos.filter(m => m.tipo === 'farrier').length, 1)) * 100)}% concluídos
                            </Badge>
                          </div>
                        </div>
                        <div className="p-2 bg-amber-100 rounded-full">
                          <ArrowDown className="h-6 w-6 text-amber-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Nível de Cuidados</p>
                          <h3 className="mt-1 text-3xl font-semibold">
                            {Math.round((manejos.filter(m => m.status === 'concluido').length / 
                            Math.max(manejos.length, 1)) * 100)}%
                          </h3>
                          <div className="mt-1 flex items-center">
                            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-sm text-green-600">
                              Boa taxa de conclusão
                            </span>
                          </div>
                        </div>
                        <div className="p-2 bg-blue-100 rounded-full">
                          <Percent className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Tipos de Manejos</CardTitle>
                      <CardDescription>
                        Distribuição dos manejos por categoria
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RePieChart>
                            <Pie
                              data={dadosManejos}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {dadosManejos.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip formatter={(value) => [`${value} manejos`, 'Quantidade']} />
                            <Legend />
                          </RePieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Status de Manejos</CardTitle>
                      <CardDescription>
                        Proporção de manejos concluídos e pendentes
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={[
                              { status: 'Concluídos', quantidade: manejos.filter(m => m.status === 'concluido').length },
                              { status: 'Pendentes', quantidade: manejos.filter(m => m.status !== 'concluido').length }
                            ]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="status" />
                            <YAxis />
                            <Tooltip formatter={(value) => [`${value} manejos`, 'Quantidade']} />
                            <Legend />
                            <Bar dataKey="quantidade" name="Quantidade" fill="#8884d8" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </TabsContent>
          
          <TabsContent value="reproducao">
            {isLoading ? (
              <div className="flex justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : reproducoes.length === 0 ? (
              <Card className="bg-gray-50 border border-dashed">
                <CardContent className="py-16">
                  <div className="text-center">
                    <Target className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum dado reprodutivo registrado</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Registre eventos reprodutivos para visualizar estatísticas nesta seção.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-purple-600">Total de Acasalamentos</p>
                          <h3 className="mt-1 text-3xl font-semibold">{reproducoes.length}</h3>
                          <div className="mt-1 flex items-center">
                            <span className="text-sm text-gray-500">
                              {reproducoes.filter(r => r.tipoCobertura === 'artificial').length} inseminações
                            </span>
                          </div>
                        </div>
                        <div className="p-2 bg-purple-100 rounded-full">
                          <Target className="h-6 w-6 text-purple-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-green-600">Taxa de Sucesso</p>
                          <h3 className="mt-1 text-3xl font-semibold">
                            {Math.round((reproducoes.filter(r => r.estado === 'sucesso').length / 
                            Math.max(reproducoes.length, 1)) * 100)}%
                          </h3>
                          <div className="mt-1 flex items-center">
                            <span className="text-sm text-gray-500">
                              {reproducoes.filter(r => r.estado === 'sucesso').length} gestações confirmadas
                            </span>
                          </div>
                        </div>
                        <div className="p-2 bg-green-100 rounded-full">
                          <ArrowUp className="h-6 w-6 text-green-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-blue-600">Éguas em Reprodução</p>
                          <h3 className="mt-1 text-3xl font-semibold">
                            {new Set(reproducoes.map(r => r.horse_id)).size}
                          </h3>
                          <div className="mt-1 flex items-center">
                            <span className="text-sm text-gray-500">
                              {Math.round(reproducoes.length / Math.max(new Set(reproducoes.map(r => r.horse_id)).size, 1))} média/égua
                            </span>
                          </div>
                        </div>
                        <div className="p-2 bg-blue-100 rounded-full">
                          <PieChart className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Resultados por Tipo de Cobertura</CardTitle>
                      <CardDescription>
                        Comparação entre tipos de cobertura e suas taxas de sucesso
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={[
                              { 
                                tipo: 'Natural', 
                                sucesso: reproducoes.filter(r => r.tipoCobertura === 'natural' && r.estado === 'sucesso').length,
                                falha: reproducoes.filter(r => r.tipoCobertura === 'natural' && r.estado !== 'sucesso').length 
                              },
                              { 
                                tipo: 'Artificial', 
                                sucesso: reproducoes.filter(r => r.tipoCobertura === 'artificial' && r.estado === 'sucesso').length,
                                falha: reproducoes.filter(r => r.tipoCobertura === 'artificial' && r.estado !== 'sucesso').length 
                              }
                            ]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="tipo" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="sucesso" name="Sucesso" stackId="a" fill="#82ca9d" />
                            <Bar dataKey="falha" name="Não confirmado" stackId="a" fill="#FF8042" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Status Atual de Reproduções</CardTitle>
                      <CardDescription>
                        Distribuição dos estados reprodutivos registrados
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RePieChart>
                            <Pie
                              data={
                                (() => {
                                  const estados = reproducoes.reduce((acc, rep) => {
                                    const estado = rep.estado || 'não informado';
                                    if (!acc[estado]) acc[estado] = 0;
                                    acc[estado]++;
                                    return acc;
                                  }, {} as Record<string, number>);
                                  
                                  return Object.entries(estados).map(([estado, quantidade]) => ({
                                    name: estado.charAt(0).toUpperCase() + estado.slice(1),
                                    value: quantidade
                                  }));
                                })()
                              }
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {
                                (() => {
                                  const estados = reproducoes.reduce((acc, rep) => {
                                    const estado = rep.estado || 'não informado';
                                    if (!acc[estado]) acc[estado] = 0;
                                    acc[estado]++;
                                    return acc;
                                  }, {} as Record<string, number>);
                                  
                                  return Object.entries(estados).map(([estado, quantidade], index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                  ));
                                })()
                              }
                            </Pie>
                            <Tooltip formatter={(value) => [`${value} reproduções`, 'Quantidade']} />
                            <Legend />
                          </RePieChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EstatisticasPage;