import React, { useState } from 'react';
import { Loader2, Upload } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface FileUploaderProps {
  horse_id: number;
  onUploadSuccess?: (fileData: any) => void;
  className?: string;
}

export function FileUploader({ horse_id, onUploadSuccess, className }: FileUploaderProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [fileType, setFileType] = useState('');
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setSelectedFile(file);
      
      // Auto-detect file type
      if (file.type.startsWith('image/')) {
        setFileType('image');
      } else if (file.type === 'application/pdf') {
        setFileType('pdf');
      } else if (file.type.startsWith('video/')) {
        setFileType('video');
      } else {
        setFileType('other');
      }
    }
  };
  
  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "Erro",
        description: "Selecione um arquivo para enviar",
        variant: "destructive"
      });
      return;
    }
    
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('horse_id', horse_id.toString());
      formData.append('description', description);
      formData.append('fileType', fileType);
      
      // Obter o usuário do localStorage
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const user_id = user?.id || localStorage.getItem('user_id');
      
      if (!user_id) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }
      
      // Adicionar user_id aos dados
      formData.append('user_id', user_id.toString());
      
      console.log(`Enviando arquivo com user_id: ${user_id} e horse_id: ${horse_id}`);
      
      const response = await fetch('/api/arquivos', {
        method: 'POST',
        headers: {
          'user-id': user_id.toString(),
        },
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao enviar arquivo: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      toast({
        title: "Sucesso",
        description: "Arquivo enviado com sucesso",
      });
      
      setSelectedFile(null);
      setDescription('');
      
      // Callback quando upload for bem-sucedido
      if (onUploadSuccess) {
        onUploadSuccess(data);
      }
    } catch (error) {
      console.error('Erro no upload:', error);
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Erro desconhecido ao enviar arquivo",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  return (
    <div className={`p-4 border rounded-md ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Enviar Arquivo</h3>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="file-upload">Arquivo</Label>
          <Input 
            id="file-upload" 
            type="file" 
            onChange={handleFileChange}
            className="mt-1"
            accept="image/*,.pdf,video/*"
          />
          {selectedFile && (
            <p className="text-sm text-gray-500 mt-1">
              {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
            </p>
          )}
        </div>
        
        <div>
          <Label htmlFor="description">Descrição</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="mt-1"
            placeholder="Adicione uma descrição para o arquivo"
          />
        </div>
        
        <Button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Enviando...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Enviar Arquivo
            </>
          )}
        </Button>
      </div>
    </div>
  );
}