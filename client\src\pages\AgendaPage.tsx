import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { apiRequest } from "@/lib/queryClient";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ist, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { CalendarioAgenda } from "@/components/CalendarioAgenda";
import { EventoForm } from "@/components/EventoForm";
import { 
  Card,
  CardContent
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  CalendarDays, 
  ChevronDown, 
  ChevronUp, 
  Filter, 
  ListFilter, 
  PlusCircle, 
  X 
} from "lucide-react";
import { Evento, Cavalo } from "../../../shared/schema";

/**
 * AgendaPage
 * 
 * Página para visualização e gerenciamento de eventos e compromissos
 * agendados para os cavalos, com visualização em calendário.
 */
const AgendaPage = () => {
  const [user] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });

  // Estados
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [showEventoForm, setShowEventoForm] = useState(false);
  const [selectedEventoId, setSelectedEventoId] = useState<number | undefined>(undefined);
  
  // Estados para filtros
  const [filterType, setFilterType] = useState<string>("todos");
  const [filterPriority, setFilterPriority] = useState<string>("todos");
  const [filterStatus, setFilterStatus] = useState<string>("todos");
  const [filterHorseId, setFilterHorseId] = useState<number | string>("todos");
  const [showFilters, setShowFilters] = useState(false);
  
  // Consultar eventos
  const { data: eventos = [], isLoading: loadingEventos } = useQuery({
    queryKey: ['/api/eventos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        const response = await apiRequest<Evento[]>('/api/eventos', 'GET');
        return response || [];
      } catch (error) {
        console.error("Erro ao buscar eventos:", error);
        return [];
      }
    }
  });
  
  // Consultar cavalos para o mapeamento de nomes
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        return await apiRequest('/api/cavalos', 'GET') || [];
      } catch (error) {
        console.error("Erro ao buscar cavalos:", error);
        return [];
      }
    }
  });
  
  // Mapa de ID para nome do cavalo
  const cavaloNameMap = new Map<number, string>();
  cavalos.forEach((cavalo: Cavalo) => {
    cavaloNameMap.set(cavalo.id, cavalo.name);
  });
  
  // Função auxiliar para converter string/number para number
  const getHorseIdAsNumber = (id: string | number): number | null => {
    if (id === 'todos') return null;
    return typeof id === 'string' ? parseInt(id) : id;
  };
  
  // Filtrar eventos com base nos critérios selecionados
  const eventosFiltrados = eventos.filter(evento => {
    // Filtrar por tipo de evento
    if (filterType && filterType !== 'todos' && evento.tipo !== filterType) {
      return false;
    }
    
    // Filtrar por prioridade
    if (filterPriority && filterPriority !== 'todos' && evento.prioridade !== filterPriority) {
      return false;
    }
    
    // Filtrar por status
    if (filterStatus && filterStatus !== 'todos' && evento.status !== filterStatus) {
      return false;
    }
    
    // Filtrar por cavalo
    if (filterHorseId && filterHorseId.toString() !== 'todos') {
      const numericHorseId = getHorseIdAsNumber(filterHorseId);
      if (numericHorseId !== null && evento.horse_id !== numericHorseId) {
        return false;
      }
    }
    
    return true;
  });
  
  // Agrupar eventos por mês/semana para visualização em lista
  const eventosPorData = eventosFiltrados.reduce((acc, evento) => {
    const dataEvento = new Date(evento.data);
    const dataKey = dataEvento.toISOString().split('T')[0];
    
    if (!acc[dataKey]) {
      acc[dataKey] = [];
    }
    
    acc[dataKey].push(evento);
    return acc;
  }, {} as Record<string, Evento[]>);
  
  // Ordenar as datas para visualização em lista
  const datasOrdenadas = Object.keys(eventosPorData).sort((a, b) => {
    // Para priorizar datas futuras mais próximas primeiro, seguidas pelas datas passadas mais recentes
    const now = new Date().getTime();
    const dateA = new Date(a).getTime();
    const dateB = new Date(b).getTime();
    
    // Dividir em eventos futuros e passados
    const aFuturo = dateA >= now;
    const bFuturo = dateB >= now;
    
    // Se um é futuro e o outro é passado, o futuro vem primeiro
    if (aFuturo && !bFuturo) return -1;
    if (!aFuturo && bFuturo) return 1;
    
    // Se ambos são futuros, o mais próximo vem primeiro
    if (aFuturo && bFuturo) {
      return dateA - dateB; // Ordem crescente de data para eventos futuros
    }
    
    // Se ambos são passados, o mais recente (mais próximo de hoje) vem primeiro
    return dateB - dateA; // Ordem decrescente de data para eventos passados
  });
  
  // Limpar todos os filtros
  const clearFilters = () => {
    setFilterType("todos");
    setFilterPriority("todos");
    setFilterStatus("todos");
    setFilterHorseId("todos");
  };
  
  // Handlers
  const handleCreateEvent = () => {
    setSelectedEventoId(undefined);
    setShowEventoForm(true);
  };
  
  const handleEditEvent = (eventoId: number) => {
    setSelectedEventoId(eventoId);
    setShowEventoForm(true);
  };
  
  // Formatação de data
  const formatarData = (dataStr: string) => {
    const data = new Date(dataStr);
    return format(data, "EEEE, d 'de' MMMM 'de' yyyy", { locale: ptBR });
  };
  
  // Obter classe baseada no tipo de evento
  const getEventoTypeClass = (tipo: string) => {
    const classes = {
      'veterinario': 'bg-blue-50 text-blue-700 border-blue-200',
      'ferrador': 'bg-amber-50 text-amber-700 border-amber-200',
      'treinamento': 'bg-green-50 text-green-700 border-green-200',
      'competicao': 'bg-purple-50 text-purple-700 border-purple-200',
      'reprodutivo': 'bg-pink-50 text-pink-700 border-pink-200',
      'outro': 'bg-gray-50 text-gray-700 border-gray-200'
    } as Record<string, string>;
    
    return classes[tipo.toLowerCase()] || classes['outro'];
  };
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-blue-700 tracking-tight">Agenda</h1>
            <p className="mt-1 text-gray-600">
              Visualize e gerencie os eventos e compromissos programados
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-2">
            <Button 
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              <span>Filtros</span>
              {showFilters ? (
                <ChevronUp className="h-3 w-3 ml-1" />
              ) : (
                <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </Button>
            
            <Button 
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setViewMode(viewMode === 'calendar' ? 'list' : 'calendar')}
            >
              {viewMode === 'calendar' ? (
                <>
                  <ListFilter className="h-4 w-4" />
                  <span>Ver Lista</span>
                </>
              ) : (
                <>
                  <CalendarDays className="h-4 w-4" />
                  <span>Ver Calendário</span>
                </>
              )}
            </Button>
            
            <Button 
              size="sm"
              onClick={handleCreateEvent}
              className="flex items-center gap-1"
            >
              <PlusCircle className="h-4 w-4" />
              <span>Novo Evento</span>
            </Button>
          </div>
        </div>
        
        {/* Filtros de Eventos */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 border rounded-lg">
            <div className="flex flex-wrap justify-between items-center mb-3">
              <h3 className="text-sm font-medium text-gray-700">Filtrar Eventos</h3>
              
              {((filterType && filterType !== 'todos') || 
                (filterPriority && filterPriority !== 'todos') || 
                (filterStatus && filterStatus !== 'todos') || 
                (filterHorseId && filterHorseId !== 'todos')) && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={clearFilters}
                  className="text-xs flex items-center text-gray-500 hover:text-gray-700"
                >
                  <X className="h-3 w-3 mr-1" />
                  Limpar Filtros
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
              {/* Filtro por Tipo */}
              <div>
                <Select
                  value={filterType}
                  onValueChange={(value) => setFilterType(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Tipo de Evento" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Tipos</SelectItem>
                    <SelectItem value="veterinario">Consulta Veterinária</SelectItem>
                    <SelectItem value="ferrador">Ferrageamento</SelectItem>
                    <SelectItem value="treinamento">Treinamento</SelectItem>
                    <SelectItem value="competicao">Competição</SelectItem>
                    <SelectItem value="reprodutivo">Reprodutivo</SelectItem>
                    <SelectItem value="outro">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Filtro por Prioridade */}
              <div>
                <Select
                  value={filterPriority}
                  onValueChange={(value) => setFilterPriority(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todas as Prioridades</SelectItem>
                    <SelectItem value="baixa">Baixa</SelectItem>
                    <SelectItem value="media">Média</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Filtro por Status */}
              <div>
                <Select
                  value={filterStatus}
                  onValueChange={(value) => setFilterStatus(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Status</SelectItem>
                    <SelectItem value="pendente">Pendente</SelectItem>
                    <SelectItem value="concluido">Concluído</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Filtro por Cavalo */}
              <div>
                <Select
                  value={filterHorseId?.toString() || "todos"}
                  onValueChange={(value) => {
                    if (value === "todos") {
                      setFilterHorseId("todos");
                    } else if (value) {
                      setFilterHorseId(Number(value));
                    }
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Cavalo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os Cavalos</SelectItem>
                    {cavalos.map((cavalo: Cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Resumo de filtros aplicados */}
            {((filterType && filterType !== 'todos') || 
              (filterPriority && filterPriority !== 'todos') || 
              (filterStatus && filterStatus !== 'todos') || 
              (filterHorseId && filterHorseId !== 'todos')) && (
              <div className="mt-3 flex flex-wrap gap-2">
                {filterType && filterType !== 'todos' && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100 border-blue-200">
                    Tipo: {filterType}
                    <button 
                      className="ml-1 hover:text-red-500" 
                      onClick={() => setFilterType("todos")}
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                )}
                
                {filterPriority && filterPriority !== 'todos' && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-100 border-amber-200">
                    Prioridade: {filterPriority}
                    <button 
                      className="ml-1 hover:text-red-500" 
                      onClick={() => setFilterPriority("todos")}
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                )}
                
                {filterStatus && filterStatus !== 'todos' && (
                  <Badge variant="outline" className={filterStatus === 'concluido' ? 
                    'bg-green-50 text-green-700 hover:bg-green-100 border-green-200' : 
                    'bg-yellow-50 text-yellow-700 hover:bg-yellow-100 border-yellow-200'
                  }>
                    Status: {filterStatus === 'concluido' ? 'Concluído' : 'Pendente'}
                    <button 
                      className="ml-1 hover:text-red-500" 
                      onClick={() => setFilterStatus("todos")}
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                )}
                
                {filterHorseId && filterHorseId !== 'todos' && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-100 border-purple-200">
                    Cavalo: {cavaloNameMap.get(getHorseIdAsNumber(filterHorseId) || 0) || "Desconhecido"}
                    <button 
                      className="ml-1 hover:text-red-500" 
                      onClick={() => setFilterHorseId("todos")}
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                )}
              </div>
            )}
          </div>
        )}
      </div>
      
      {loadingEventos ? (
        <div className="flex justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <>
          {viewMode === 'calendar' ? (
            <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
              <div className="lg:col-span-7">
                <CalendarioAgenda 
                  eventos={eventosFiltrados} 
                  selectedDate={selectedDate}
                  onSelectDate={setSelectedDate}
                  onCreateEvent={() => handleCreateEvent()}
                />
              </div>
            </div>
          ) : (
            // Visualização em lista
            <div className="space-y-6">
              {datasOrdenadas.length === 0 ? (
                <div className="text-center p-8 bg-gray-50 rounded-lg border border-dashed">
                  <div className="flex flex-col items-center justify-center">
                    <CalendarDays className="h-12 w-12 text-gray-400" />
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhum evento agendado</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Adicione eventos para visualizá-los aqui.
                    </p>
                    <Button 
                      onClick={handleCreateEvent}
                      className="mt-4"
                    >
                      Criar Primeiro Evento
                    </Button>
                  </div>
                </div>
              ) : (
                datasOrdenadas.map(data => {
                  // Formatar data para cabeçalho
                  const formattedDate = formatarData(data);
                  const isToday = new Date(data).toDateString() === new Date().toDateString();
                  
                  return (
                    <div key={data} className="bg-white rounded-lg shadow overflow-hidden border">
                      <div className={`px-4 py-3 border-b ${isToday ? 'bg-blue-50' : 'bg-gray-50'}`}>
                        <h3 className={`font-medium capitalize ${isToday ? 'text-blue-700' : 'text-gray-700'}`}>
                          {isToday ? '💫 Hoje - ' : ''}{formattedDate}
                        </h3>
                      </div>
                      
                      <div className="divide-y divide-gray-100">
                        {eventosPorData[data].map(evento => (
                          <div 
                            key={evento.id} 
                            className="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                            onClick={() => handleEditEvent(evento.id)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <div className={`px-2 py-0.5 text-xs rounded-full ${getEventoTypeClass(evento.tipo)}`}>
                                    {evento.tipo.charAt(0).toUpperCase() + evento.tipo.slice(1)}
                                  </div>
                                  <h4 className="font-semibold text-gray-900">
                                    {evento.titulo}
                                  </h4>
                                </div>
                                
                                {evento.horse_id && (
                                  <p className="text-sm text-gray-600">
                                    Cavalo: {cavaloNameMap.get(evento.horse_id) || "Não especificado"}
                                  </p>
                                )}
                                
                                <div className="flex items-center text-xs text-gray-500 gap-2">
                                  {evento.horaInicio && (
                                    <span className="inline-flex items-center gap-1">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                      </svg>
                                      {evento.horaInicio}{evento.horaFim ? ` - ${evento.horaFim}` : ''}
                                    </span>
                                  )}
                                  
                                  {evento.prioridade && (
                                    <span className={`
                                      inline-flex items-center gap-1
                                      ${evento.prioridade === 'alta' ? 'text-red-600' : 
                                        evento.prioridade === 'media' ? 'text-amber-600' : 
                                        'text-green-600'}
                                    `}>
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5L21 11.5V19a2 2 0 01-2 2h-8a2 2 0 01-2-2z" />
                                      </svg>
                                      Prioridade {evento.prioridade}
                                    </span>
                                  )}
                                  
                                  {evento.status && (
                                    <span className={`
                                      inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs
                                      ${evento.status === 'concluido' ? 'bg-green-50 text-green-700' : 'bg-amber-50 text-amber-700'}
                                    `}>
                                      {evento.status === 'concluido' ? 'Concluído' : 'Pendente'}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            {evento.descricao && (
                              <div className="mt-2 text-sm text-gray-600 border-t border-gray-100 pt-2">
                                {evento.descricao.length > 120 
                                  ? `${evento.descricao.substring(0, 120)}...` 
                                  : evento.descricao
                                }
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          )}
        </>
      )}
      
      {/* Modal de Formulário de Evento */}
      <EventoForm 
        isOpen={showEventoForm}
        onClose={() => setShowEventoForm(false)}
        selectedDate={selectedDate}
        eventoId={selectedEventoId}
      />
    </div>
  );
};

export default AgendaPage;