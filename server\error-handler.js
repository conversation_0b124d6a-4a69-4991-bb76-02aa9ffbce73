
/**
 * Middleware de tratamento de erro robusto para evitar erros 500
 */

function createRobustErrorHandler() {
  return (error, req, res, next) => {
    console.error('🚨 Erro capturado:', {
      message: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
    
    // Erros específicos do PostgreSQL
    if (error.code === '42P01') {
      // Tabela não existe
      return res.status(500).json({
        error: 'Recurso temporariamente indisponível',
        message: 'Funcionalidade em manutenção',
        timestamp: new Date().toISOString()
      });
    }
    
    if (error.code === '42703') {
      // Coluna não existe
      return res.status(500).json({
        error: 'Estrutura de dados incompatível',
        message: 'Sistema sendo atualizado',
        timestamp: new Date().toISOString()
      });
    }
    
    // Erro genérico
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Tente novamente em alguns instantes',
      timestamp: new Date().toISOString()
    });
  };
}

function wrapAsyncRoute(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = { createRobustErrorHandler, wrapAsyncRoute };
  