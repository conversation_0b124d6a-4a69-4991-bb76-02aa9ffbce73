// Improved horse routes with better architecture
import { Router } from 'express';
import { HorseService } from '../services/horse.service';
import { getDatabaseService } from '../core/database';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth.middleware';
import { asyncHandler } from '../core/errors';
import { validatePagination } from '../core/validation';
import { validate, cavaloValidation, authValidation } from '../validation';

const router = Router();
const db = getDatabaseService();
const horseService = new HorseService(db);

// GET /api/cavalos - Get all horses for user
router.get('/', authenticateUser, validate(cavaloValidation.search, 'query'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const pagination = validatePagination(req.query);
  const filters = {
    search: req.query.search as string,
    status: req.query.status as string
  };

  const result = await horseService.findByUserId(userId, filters, pagination);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json(result.data);
}));

// GET /api/cavalos/statistics - Get horse statistics
router.get('/statistics', authenticateUser, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  const result = await horseService.getHorseStatistics(userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// GET /api/cavalos/search - Search horses
router.get('/search', authenticateUser, validate(cavaloValidation.search, 'query'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const searchTerm = req.query.q as string;
  const pagination = validatePagination(req.query);

  if (!searchTerm) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Search term is required'
      }
    });
  }

  const result = await horseService.searchHorses(userId, searchTerm, pagination);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data,
    pagination
  });
}));

// GET /api/cavalos/:id - Get specific horse
router.get('/:id', authenticateUser, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const horseId = req.params.id as unknown as number;
  const userId = req.user!.id;

  const result = await horseService.findById(horseId, userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// GET /api/cavalos/:id/genealogy - Get horse genealogy
router.get('/:id/genealogy', authenticateUser, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const horseId = req.params.id as unknown as number;
  const userId = req.user!.id;

  const result = await horseService.getHorseGenealogy(horseId, userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// POST /api/cavalos - Create new horse
router.post('/', authenticateUser, validate(cavaloValidation.create), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const horseData = {
    ...req.body,
    user_id: userId
  };

  const result = await horseService.create(horseData);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.status(201).json({
    success: true,
    data: result.data
  });
}));

// PUT /api/cavalos/:id - Update horse
router.put('/:id', authenticateUser, validate(authValidation.idParam, 'params'), validate(cavaloValidation.update), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const horseId = req.params.id as unknown as number;
  const userId = req.user!.id;

  const result = await horseService.update(horseId, req.body, userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// PUT /api/cavalos/:id/status - Update horse status
router.put('/:id/status', authenticateUser, validate(authValidation.idParam, 'params'), validate(cavaloValidation.updateStatus), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const horseId = req.params.id as unknown as number;
  const userId = req.user!.id;
  const { status, reason } = req.body;

  const result = await horseService.updateStatus(horseId, userId, status, reason);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// DELETE /api/cavalos/:id - Delete horse
router.delete('/:id', authenticateUser, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const horseId = req.params.id as unknown as number;
  const userId = req.user!.id;

  const result = await horseService.delete(horseId, userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

export default router;