import React, { useState } from 'react';
import { useC<PERSON>lo } from '@/hooks/use-cavalo';
import { useAuth } from '@/hooks/use-auth';
import { CavaloCard } from '@/components/cavalo/CavaloCard';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Filter, X, FileText, ChevronDown, Archive } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogBody,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FilterControls } from '@/components/cavalo/FilterControls';
import { CavaloForm, CavaloFormValues } from '@/components/cavalo/CavaloForm';
import { C<PERSON>loFormNew } from '@/components/cavalo/CavaloFormNew';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useLocation } from 'wouter';

/**
 * Página de Cavalos Refatorada
 * 
 * Versão melhorada da página de cavalos usando os novos componentes
 * modulares e o hook personalizado useCavalo para uma melhor estrutura
 * de código, reutilização e manutenibilidade.
 */
export default function CavalosPageRefactored() {
  const cavalo = useCavalo();
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);
  const [selectedCavaloId, setSelectedCavaloId] = useState<number | null>(null);
  
  // Encontrar o cavalo selecionado para edição ou exclusão
  const selectedCavalo = selectedCavaloId 
    ? cavalo.cavalosQuery.data?.find(c => c.id === selectedCavaloId) 
    : null;
  
  // Handler para visualizar detalhes do cavalo
  const handleView = (id: number) => {
    setLocation(`/cavalo/${id}`);
  };
  
  // Handler para editar cavalo (redirecionar para página de detalhes)
  const handleEdit = (id: number) => {
    // Em vez de abrir o modal, redirecionamos para a página de detalhes
    setLocation(`/cavalo/${id}`);
  };
  
  // Handler para abrir o diálogo de confirmação de exclusão
  const handleDeleteConfirm = (id: number) => {
    setSelectedCavaloId(id);
    setIsDeleteDialogOpen(true);
  };
  
  // Handler para excluir cavalo após confirmação
  const handleDelete = () => {
    if (selectedCavaloId) {
      cavalo.deleteCavaloMutation.mutate(selectedCavaloId);
      setIsDeleteDialogOpen(false);
      setSelectedCavaloId(null);
    }
  };
  
  // Handler para upload de arquivos
  const handleUpload = (id: number) => {
    setLocation(`/cavalo/${id}/upload`);
  };
  
  // Handler para biometria
  const handleBiometria = (id: number) => {
    setLocation(`/cavalos/biometria?id=${id}`);
  };
  
  // Handler para histórico
  const handleHistorico = (id: number) => {
    setLocation(`/cavalo/${id}?tab=historico`);
  };
  
  // Handler para o envio do formulário de adição
  const handleAddSubmit = (values: CavaloFormValues) => {
    cavalo.addCavaloMutation.mutate({
      ...values,
      user_id: user?.id
    }, {
      onSuccess: () => {
        setIsAddDialogOpen(false);
        // Mostrar mensagem de sucesso - opcional, poderia ser implementado com um toast
      },
      onError: (error) => {
        console.error('Erro ao adicionar cavalo:', error);
        // Mostrar mensagem de erro - opcional, poderia ser implementado com um toast
      }
    });
  };
  

  
  // Limpar todos os filtros
  const clearFilters = () => {
    cavalo.setSearchTerm('');
    cavalo.setFilterRaca('');
    cavalo.setFilterSexo('');
    cavalo.setFilterStatus('');
  };
  
  // Como não vamos mais editar via modal, essa função não é mais necessária
  // mas vamos mantê-la como comentário para futura referência
  /*
  const getEditDefaultValues = () => {
    // O código foi removido porque agora a edição acontece na página de detalhes do cavalo
    // Em vez de abrir um modal, redirecionamos para a página /cavalo/ID 
  };
  */
  
  // Mostrar estado de loading enquanto as consultas carregam
  if (cavalo.cavalosQuery.isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-3 sm:py-4 md:py-6 px-3 sm:px-4 overflow-x-hidden">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6 gap-4">
        <div className="min-w-0">
          <h1 className="text-2xl sm:text-3xl font-bold">Cavalos</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Gerencie seu plantel de animais
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <Button 
            variant="outline" 
            onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
            className="w-full sm:w-auto min-h-[48px] text-sm"
          >
            {isFiltersExpanded ? <X className="mr-2 h-4 w-4" /> : <Filter className="mr-2 h-4 w-4" />}
            <span className="hidden xs:inline">{isFiltersExpanded ? 'Ocultar Filtros' : 'Filtros'}</span>
            <span className="xs:hidden">{isFiltersExpanded ? 'Ocultar' : 'Filtros'}</span>
          </Button>
          
          {/* Menu dropdown para adicionar novo cavalo ou importar da ABCCC */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="w-full sm:w-auto min-h-[48px] text-sm">
                <Plus className="mr-2 h-4 w-4" />
                <span className="hidden xs:inline">Adicionar Cavalo</span>
                <span className="xs:hidden">Adicionar</span>
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => setLocation('/cavalo/cadastro')} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                <span>Cadastro Manual</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setLocation('/genetica/importar-abccc')} className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                <span>Importar da ABCCC</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Adicionar Novo Cavalo</DialogTitle>
                <DialogDescription>
                  Preencha os dados abaixo para cadastrar um novo animal no sistema.
                </DialogDescription>
              </DialogHeader>
              
              <DialogBody>
                <CavaloForm
                  onSubmit={handleAddSubmit}
                  isSubmitting={cavalo.addCavaloMutation.isPending}
                  user_id={user?.id || 0}
                  cavalos={cavalo.cavalosQuery.data || []}
                />
              </DialogBody>
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  className="w-full sm:w-auto"
                >
                  Cancelar
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {isFiltersExpanded && (
        <div className="mb-6">
          <FilterControls
            title="Filtros de Cavalos"
            description="Filtre a lista por nome, raça, sexo ou status"
            searchTerm={cavalo.searchTerm}
            setSearchTerm={cavalo.setSearchTerm}
            filterRaca={cavalo.filterRaca}
            setFilterRaca={cavalo.setFilterRaca}
            racasOptions={cavalo.racasUnicas}
            filterSexo={cavalo.filterSexo}
            setFilterSexo={cavalo.setFilterSexo}
            sexosOptions={cavalo.sexosDisponiveis}
            filterStatus={cavalo.filterStatus}
            setFilterStatus={cavalo.setFilterStatus}
            statusOptions={cavalo.statusDisponiveis}
          />
          
          {(cavalo.searchTerm || cavalo.filterRaca || cavalo.filterSexo || cavalo.filterStatus) && (
            <div className="flex items-center justify-between mt-2">
              <div className="text-sm text-muted-foreground">
                {cavalo.cavalosFiltrados.length} {cavalo.cavalosFiltrados.length === 1 ? 'resultado' : 'resultados'} encontrados
              </div>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                <X className="mr-2 h-4 w-4" /> Limpar Filtros
              </Button>
            </div>
          )}
        </div>
      )}
      
      <Tabs defaultValue="ativos" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="ativos">Plantel Ativo</TabsTrigger>
          <TabsTrigger value="inativos" className="flex items-center gap-2">
            <Archive className="h-4 w-4" />
            Cavalos Inativos
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="ativos" className="mt-6">
          {cavalo.cavalosFiltrados.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cavalo.cavalosFiltrados.map((cavaloItem) => (
                <CavaloCard
                  key={cavaloItem.id}
                  cavalo={cavaloItem}
                  formatarData={cavalo.formatarData}
                  calcularIdade={cavalo.calcularIdade}
                  onView={handleView}
                  onEdit={handleEdit}
                  onDelete={handleDeleteConfirm}
                  onUpload={handleUpload}
                  onBiometria={handleBiometria}
                  onHistorico={handleHistorico}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16 border rounded-lg">
              <div className="flex justify-center">
                <Search className="h-16 w-16 mb-4 text-muted-foreground" />
              </div>
              
              <h3 className="text-xl font-semibold">Nenhum cavalo ativo encontrado</h3>
              
              {(cavalo.searchTerm || cavalo.filterRaca || cavalo.filterSexo || cavalo.filterStatus) ? (
                <p className="text-muted-foreground mt-2 mb-6">
                  Tente ajustar os filtros para encontrar o que procura
                </p>
              ) : (
                <p className="text-muted-foreground mt-2 mb-6">
                  Cadastre seu primeiro animal no sistema
                </p>
              )}
              
              <div className="flex gap-2 justify-center">
                <Button onClick={() => setLocation('/cavalo/cadastro')}>
                  <Plus className="mr-2 h-4 w-4" /> Cadastro Manual
                </Button>
                <Button variant="outline" onClick={() => setLocation('/genetica/importar-abccc')}>
                  <FileText className="mr-2 h-4 w-4" /> Importar da ABCCC
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="inativos" className="mt-6">
          {cavalo.cavalosInativosQuery.isLoading ? (
            <div className="text-center py-16">
              <div className="text-muted-foreground">Carregando cavalos inativos...</div>
            </div>
          ) : cavalo.cavalosInativosQuery.data && cavalo.cavalosInativosQuery.data.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cavalo.cavalosInativosQuery.data.map((cavaloItem) => (
                <CavaloCard
                  key={cavaloItem.id}
                  cavalo={cavaloItem}
                  formatarData={cavalo.formatarData}
                  calcularIdade={cavalo.calcularIdade}
                  onView={handleView}
                  onEdit={handleEdit}
                  onDelete={() => {}} // Disabled for inactive horses
                  onUpload={handleUpload}
                  onBiometria={handleBiometria}
                  onHistorico={handleHistorico}
                  isInactive={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16 border rounded-lg">
              <div className="flex justify-center">
                <Archive className="h-16 w-16 mb-4 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold">Nenhum cavalo inativo</h3>
              <p className="text-muted-foreground mt-2">
                Cavalos removidos do plantel aparecerão aqui.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      {/* Dialog de confirmação de remoção do plantel */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remover do plantel</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja remover o cavalo <span className="font-semibold">{selectedCavalo?.name}</span> do seu plantel?
              O cavalo será marcado como inativo, mas todos os dados históricos serão preservados.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedCavaloId(null)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-orange-600 text-white hover:bg-orange-700"
            >
              Remover do plantel
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}