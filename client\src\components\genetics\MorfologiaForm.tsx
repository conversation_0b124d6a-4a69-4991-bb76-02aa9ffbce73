import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarIcon, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  Slider
} from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { morfologiaFormSchema, converterMorfologiaFormParaBanco } from '@shared/schema';
import { z } from 'zod';

type AvaliacaoFormValues = z.infer<typeof morfologiaFormSchema>;

interface MorfologiaFormProps {
  horse_id: number;
  horseName: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function MorfologiaForm({ horse_id, horseName, onSuccess, onCancel }: MorfologiaFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Valores padrão seguindo padrão padronizado
  const defaultValues: Partial<AvaliacaoFormValues> = {
    horse_id,
    responsavelMedicao: '',
    dataMedicao: new Date(),
    pontuacaoCabeca: 5,
    pontuacaoPescoco: 5,
    pontuacaoEspalda: 5,
    pontuacaoDorso: 5,
    pontuacaoGarupa: 5,
    pontuacaoMembros: 5,
    pontuacaoAprumos: 5,
    pontuacaoAndamento: 5,
    pontuacaoHarmonia: 5,
    observacoes: '',
  };

  const form = useForm<AvaliacaoFormValues>({
    resolver: zodResolver(morfologiaFormSchema),
    defaultValues,
  });

  // Calcular pontuação total usando o padrão padronizado
  const calcularPontuacaoTotal = (): number => {
    const values = form.getValues();
    return (
      values.pontuacaoCabeca +
      values.pontuacaoPescoco +
      values.pontuacaoEspalda +
      values.pontuacaoDorso +
      values.pontuacaoGarupa +
      values.pontuacaoMembros +
      values.pontuacaoAprumos +
      values.pontuacaoAndamento +
      values.pontuacaoHarmonia
    );
  };

  // Submit do formulário usando conversão padronizada
  const onSubmit = async (data: AvaliacaoFormValues) => {
    setIsSubmitting(true);

    try {
      // Converter dados do formulário para formato do banco usando função padronizada
      const dadosConvertidos = converterMorfologiaFormParaBanco(data, 1);
      
      // Identificar pontos fortes (pontuação >= 8)
      const pontosFortes = [];
      if (data.pontuacaoCabeca >= 8) pontosFortes.push('Cabeça');
      if (data.pontuacaoPescoco >= 8) pontosFortes.push('Pescoço');
      if (data.pontuacaoEspalda >= 8) pontosFortes.push('Espádua');
      if (data.pontuacaoDorso >= 8) pontosFortes.push('Dorso');
      if (data.pontuacaoGarupa >= 8) pontosFortes.push('Garupa');
      if (data.pontuacaoMembros >= 8) pontosFortes.push('Membros');
      if (data.pontuacaoAprumos >= 8) pontosFortes.push('Aprumos');
      if (data.pontuacaoAndamento >= 8) pontosFortes.push('Andamento');
      if (data.pontuacaoHarmonia >= 8) pontosFortes.push('Harmonia');
      
      // Identificar pontos fracos (pontuação <= 3)
      const pontosFracos = [];
      if (data.pontuacaoCabeca <= 3) pontosFracos.push('Cabeça');
      if (data.pontuacaoPescoco <= 3) pontosFracos.push('Pescoço');
      if (data.pontuacaoEspalda <= 3) pontosFracos.push('Espádua');
      if (data.pontuacaoDorso <= 3) pontosFracos.push('Dorso');
      if (data.pontuacaoGarupa <= 3) pontosFracos.push('Garupa');
      if (data.pontuacaoMembros <= 3) pontosFracos.push('Membros');
      if (data.pontuacaoAprumos <= 3) pontosFracos.push('Aprumos');
      if (data.pontuacaoAndamento <= 3) pontosFracos.push('Andamento');

      // Preparar dados completos para API
      const avaliacaoData = {
        ...data,
        pontuacaoTotal,
        pontosFortes,
        pontosFracos,
        user_id: 1, // Temporário, deve usar o ID do usuário logado
      };

      // Enviar para API
      const response = await fetch('/api/cavalos/morfologia', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-ID': '1' // Deve ser substituído pelo ID do usuário autenticado em produção
        },
        body: JSON.stringify(avaliacaoData),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar avaliação morfológica');
      }

      toast({
        title: 'Avaliação salva com sucesso!',
        description: `Pontuação total: ${pontuacaoTotal}/80`,
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao salvar avaliação:', error);
      toast({
        title: 'Erro ao salvar avaliação',
        description: 'Não foi possível salvar a avaliação morfológica.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Nova Avaliação Morfológica</h2>
            <div className="text-sm text-muted-foreground">
              Cavalo: <span className="font-medium text-primary">{horseName}</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="avaliador"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Avaliador</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Dr. João Silva" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dataMedicao"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Data da Avaliação</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div>
            <h3 className="text-md font-medium mb-4">Pontuações (1-10)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="pontuacaoCabeca"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Cabeça</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Formato, proporções e características raciais
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoPescoco"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Pescoço</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Forma, comprimento, inserção e musculatura
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoEspadua"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Espádua</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Inclinação, comprimento e musculatura
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoDorso"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Dorso</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Comprimento, largura e conformação
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoGarupa"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Garupa</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Comprimento, largura e inclinação
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoMembrosDianteiros"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Membros Dianteiros</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Conformação, aprumos e articulações
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoMembrosPosteriores"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Membros Posteriores</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Conformação, aprumos e potência
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pontuacaoAndamento"
                render={({ field: { value, onChange } }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel>Andamento</FormLabel>
                      <span className="text-sm font-medium">{value}</span>
                    </div>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[value]}
                        onValueChange={(vals) => onChange(vals[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Qualidade, regularidade e amplitude
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="bg-muted/30 p-4 rounded-md">
            <div className="font-medium">Pontuação Total</div>
            <div className="text-2xl font-bold text-primary">{calcularPontuacaoTotal()}/80</div>
          </div>

          <FormField
            control={form.control}
            name="observacoes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Observações adicionais sobre a avaliação..."
                    className="min-h-[80px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar Avaliação
          </Button>
        </div>
      </form>
    </Form>
  );
}