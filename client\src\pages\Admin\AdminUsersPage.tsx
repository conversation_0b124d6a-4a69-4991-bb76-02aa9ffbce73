import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRequireAdmin } from '@/hooks/use-require-admin';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Users, Crown, User, Mail, Calendar, Settings, RefreshCw } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  role: 'ADMIN' | 'USER';
  created_at: string;
  updated_at: string;
}

export default function AdminUsersPage() {
  const { isAdmin } = useRequireAdmin();
  const { toast } = useToast();
  
  // Query para buscar todos os usuários
  const {
    data: users = [],
    isLoading,
    error,
    refetch
  } = useQuery<User[]>({
    queryKey: ['/api/users'],
    enabled: isAdmin, // Só executa a query se for admin
  });

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Lista atualizada",
      description: "Os dados dos usuários foram atualizados com sucesso.",
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg">Carregando usuários...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center gap-2">
              <Users className="h-5 w-5" />
              Erro ao carregar usuários
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">
              Ocorreu um erro ao carregar a lista de usuários. Verifique sua conexão e tente novamente.
            </p>
            <Button onClick={handleRefresh} variant="outline" className="border-red-300 text-red-700">
              <RefreshCw className="h-4 w-4 mr-2" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Se não é admin, não renderiza (o hook já fez o redirect)
  if (!isAdmin) {
    return null;
  }

  const adminUsers = users.filter(user => user.role === 'ADMIN');
  const regularUsers = users.filter(user => user.role === 'USER');

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-600" />
              Painel de Controle de Usuários
            </h1>
            <p className="text-gray-600 mt-2">
              Gerencie todos os usuários cadastrados no sistema
            </p>
          </div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Total de Usuários
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">{users.length}</div>
            <p className="text-sm text-gray-600">usuários cadastrados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Crown className="h-5 w-5 text-purple-600" />
              Administradores
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">{adminUsers.length}</div>
            <p className="text-sm text-gray-600">usuários admin</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5 text-green-600" />
              Usuários Padrão
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">{regularUsers.length}</div>
            <p className="text-sm text-gray-600">usuários comuns</p>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Lista de Usuários
          </CardTitle>
          <CardDescription>
            Todos os usuários cadastrados no sistema com suas informações principais
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">ID</TableHead>
                  <TableHead>Nome</TableHead>
                  <TableHead>Username</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Data de Cadastro</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-3 text-gray-500">
                        <Users className="h-12 w-12" />
                        <p>Nenhum usuário encontrado</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-mono text-sm">{user.id}</TableCell>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell className="text-gray-600">{user.username}</TableCell>
                      <TableCell className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        {user.email}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={user.role === 'ADMIN' ? 'default' : 'secondary'}
                          className={
                            user.role === 'ADMIN' 
                              ? 'bg-purple-100 text-purple-800 border-purple-200' 
                              : 'bg-green-100 text-green-800 border-green-200'
                          }
                        >
                          {user.role === 'ADMIN' ? (
                            <>
                              <Crown className="h-3 w-3 mr-1" />
                              Admin
                            </>
                          ) : (
                            <>
                              <User className="h-3 w-3 mr-1" />
                              Usuário
                            </>
                          )}
                        </Badge>
                      </TableCell>
                      <TableCell className="flex items-center gap-2 text-gray-600">
                        <Calendar className="h-4 w-4" />
                        {new Date(user.created_at).toLocaleDateString('pt-BR')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8"
                            onClick={() => {
                              toast({
                                title: "Funcionalidade em desenvolvimento",
                                description: "A edição de usuários será implementada em breve.",
                                variant: "default",
                              });
                            }}
                          >
                            Editar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 text-red-600 border-red-200 hover:bg-red-50"
                            onClick={() => {
                              toast({
                                title: "Funcionalidade em desenvolvimento",
                                description: "A remoção de usuários será implementada em breve.",
                                variant: "destructive",
                              });
                            }}
                          >
                            Remover
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}