/**
 * Regra ESLint customizada para impedir set direto de 'cor' em insert/update de cavalo
 * Conforme especificação da refatoração de pelagens
 */

module.exports = {
  rules: {
    'no-literal-cor': {
      meta: {
        type: 'problem',
        docs: {
          description: 'impedir set de cor direto em insert/update de cavalo',
          category: 'Best Practices',
          recommended: true
        },
        fixable: null,
        schema: []
      },
      create: function(context) {
        return {
          Property(node) {
            // Verificar se é uma propriedade 'cor' em objeto de insert/update de cavalo
            if (node.key && 
                (node.key.name === 'cor' || 
                 (node.key.type === 'Literal' && node.key.value === 'cor'))) {
              
              // Verificar se está em contexto de insert/update de cavalo
              const sourceCode = context.getSourceCode();
              const text = sourceCode.getText();
              
              // Buscar por padrões que indicam operação de cavalo
              const isCavaloOperation = text.includes('cavalos') || 
                                       text.includes('insert') || 
                                       text.includes('update') ||
                                       text.includes('Cavalo');
              
              if (isCavaloOperation) {
                context.report({
                  node,
                  message: 'Não defina "cor" diretamente. Use pelagem_id via resolvePelagemId() e mantenha cor apenas como fallback legível.'
                });
              }
            }
          }
        };
      }
    }
  }
};