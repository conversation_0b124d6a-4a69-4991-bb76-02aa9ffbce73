/**
 * Testes para o middleware de tracing
 */
import { Request, Response } from 'express';
import { describe, test, expect, vi } from 'vitest';
import { traceMiddleware, getTraceId, TRACE_ID_KEY } from '../../middleware/tracing';
import { logger } from '../../logger';

// Mock para o objeto de requisição
const mockRequest = () => {
  const req = {} as Request;
  req.headers = {};
  req.body = {};
  req.method = 'GET';
  req.path = '/test';
  return req;
};

// Mock para o objeto de resposta
const mockResponse = () => {
  const res = {} as Response;
  res.setHeader = vi.fn();
  res.on = vi.fn().mockImplementation((event, callback) => {
    if (event === 'finish') {
      callback();
    }
    return res;
  });
  res.statusCode = 200;
  return res;
};

describe('Middleware de Tracing', () => {
  test('deve gerar um traceId único para cada requisição', () => {
    const req = mockRequest();
    const res = mockResponse();
    const next = vi.fn();

    traceMiddleware(req, res, next);

    // Verifica se o traceId foi gerado e adicionado aos headers
    expect(req.headers[TRACE_ID_KEY]).toBeDefined();
    expect(typeof req.headers[TRACE_ID_KEY]).toBe('string');
    expect(res.setHeader).toHaveBeenCalledWith(TRACE_ID_KEY, req.headers[TRACE_ID_KEY]);
    expect(next).toHaveBeenCalled();
  });

  test('deve usar o traceId existente se fornecido', () => {
    const req = mockRequest();
    const existingTraceId = 'existing-trace-id';
    req.headers[TRACE_ID_KEY] = existingTraceId;
    
    const res = mockResponse();
    const next = vi.fn();

    traceMiddleware(req, res, next);

    // Verifica se o traceId existente foi mantido
    expect(req.headers[TRACE_ID_KEY]).toBe(existingTraceId);
    expect(res.setHeader).toHaveBeenCalledWith(TRACE_ID_KEY, existingTraceId);
    expect(next).toHaveBeenCalled();
  });

  test('deve adicionar um logger à requisição', () => {
    const req = mockRequest();
    const res = mockResponse();
    const next = vi.fn();

    traceMiddleware(req, res, next);

    // Verifica se o logger foi adicionado à requisição
    expect((req as any).logger).toBeDefined();
    expect(next).toHaveBeenCalled();
  });

  test('deve registrar o início e o fim da requisição', () => {
    const req = mockRequest();
    const res = mockResponse();
    const next = vi.fn();

    // Espiona o método debug do logger
    const debugSpy = vi.spyOn(logger, 'debug');

    traceMiddleware(req, res, next);

    // Verifica se o início da requisição foi registrado
    expect(debugSpy).toHaveBeenCalledWith(expect.objectContaining({
      msg: 'Requisição iniciada',
    }));

    // Verifica se o fim da requisição foi registrado
    expect(debugSpy).toHaveBeenCalledWith(expect.objectContaining({
      msg: 'Requisição finalizada',
      statusCode: 200,
    }));
  });

  test('deve continuar mesmo se ocorrer um erro', () => {
    const req = mockRequest();
    const res = mockResponse();
    const next = vi.fn();

    // Força um erro no middleware
    res.setHeader = vi.fn().mockImplementation(() => {
      throw new Error('Erro de teste');
    });

    // Espiona o método error do logger
    const errorSpy = vi.spyOn(logger, 'error');

    traceMiddleware(req, res, next);

    // Verifica se o erro foi registrado
    expect(errorSpy).toHaveBeenCalledWith(expect.objectContaining({
      msg: 'Erro no middleware de tracing',
    }));

    // Verifica se o next foi chamado mesmo com o erro
    expect(next).toHaveBeenCalled();
  });

  test('getTraceId deve retornar o traceId da requisição', () => {
    const req = mockRequest();
    const traceId = 'test-trace-id';
    req.headers[TRACE_ID_KEY] = traceId;

    const result = getTraceId(req);

    expect(result).toBe(traceId);
  });

  test('getTraceId deve retornar "unknown" se não houver traceId', () => {
    const req = mockRequest();
    req.headers = {};

    const result = getTraceId(req);

    expect(result).toBe('unknown');
  });
});
