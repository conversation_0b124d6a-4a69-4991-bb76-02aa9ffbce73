import { pgTable, text, serial, integer, timestamp, real, date, time, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { horses } from './cavalos';
import { 
  observacoesSchema,
  optionalDateSchema,
  UserContext 
} from './core';

// ============================================================================
// ENUMS
// ============================================================================

export const tipoManejoEnum = pgEnum('tipo_manejo', [
  'alimentacao', 'exercicio', 'higiene', 'medicacao', 
  'vacinacao', 'vermifugacao', 'casqueamento', 'tosquia',
  'transporte', 'manejo_reprodutivo', 'outros'
]);

export const statusManejoEnum = pgEnum('status_manejo', [
  'agendado', 'em_andamento', 'concluido', 'cancelado', 'adiado'
]);

export const prioridadeEnum = pgEnum('prioridade', ['baixa', 'media', 'alta', 'urgente']);

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const manejos = pgTable("manejos", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  user_id: integer("user_id").notNull(),
  
  // Tipo e descrição do manejo
  tipo: tipoManejoEnum("tipo").notNull(),
  titulo: text("titulo").notNull(),
  descricao: text("descricao"),
  
  // Agendamento
  dataAgendada: date("data_agendada").notNull(),
  horaAgendada: time("hora_agendada"),
  dataRealizacao: timestamp("data_realizacao"),
  
  // Status e prioridade
  status: statusManejoEnum("status").default('agendado'),
  prioridade: prioridadeEnum("prioridade").default('media'),
  
  // Responsáveis
  responsavel: text("responsavel"),
  veterinario: text("veterinario"),
  
  // Resultados e observações
  resultados: text("resultados"),
  observacoes: text("observacoes"),
  
  // Custos
  custoEstimado: real("custo_estimado"),
  custoReal: real("custo_real"),
  
  // Próximo agendamento (para manejos recorrentes)
  proximoManejo: date("proximo_manejo"),
  recorrencia: text("recorrencia"), // "semanal", "mensal", "trimestral", etc.
  
  // Arquivos relacionados
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const manejosRelations = relations(manejos, ({ one }) => ({
  horse: one(horses, {
    fields: [manejos.horse_id],
    references: [horses.id],
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const manejoSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  user_id: z.number().int().positive(),
  
  tipo: z.enum([
    'alimentacao', 'exercicio', 'higiene', 'medicacao',
    'vacinacao', 'vermifugacao', 'casqueamento', 'tosquia',
    'transporte', 'manejo_reprodutivo', 'outros'
  ]),
  titulo: z.string().min(1, 'Título é obrigatório').max(100),
  descricao: z.string().max(500).optional().nullable(),
  
  dataAgendada: z.string().datetime().or(z.date()),
  horaAgendada: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional().nullable(),
  dataRealizacao: optionalDateSchema,
  
  status: z.enum(['agendado', 'em_andamento', 'concluido', 'cancelado', 'adiado']).default('agendado'),
  prioridade: z.enum(['baixa', 'media', 'alta', 'urgente']).default('media'),
  
  responsavel: z.string().max(100).optional().nullable(),
  veterinario: z.string().max(100).optional().nullable(),
  
  resultados: z.string().max(1000).optional().nullable(),
  observacoes: observacoesSchema,
  
  custoEstimado: z.number().nonnegative().optional().nullable(),
  custoReal: z.number().nonnegative().optional().nullable(),
  
  proximoManejo: optionalDateSchema,
  recorrencia: z.enum(['diario', 'semanal', 'quinzenal', 'mensal', 'bimestral', 'trimestral', 'semestral', 'anual']).optional().nullable(),
  
  arquivoIds: z.array(z.string()).optional().nullable(),
}).refine((data) => {
  // Business rule: data de realização deve ser posterior ao agendamento
  if (data.dataRealizacao && data.dataAgendada) {
    const realizacao = new Date(data.dataRealizacao);
    const agendamento = new Date(data.dataAgendada);
    return realizacao >= agendamento;
  }
  return true;
}, {
  message: "Data de realização deve ser posterior ao agendamento",
  path: ["dataRealizacao"]
}).refine((data) => {
  // Business rule: custo real não pode ser muito diferente do estimado
  if (data.custoReal && data.custoEstimado) {
    const diferenca = Math.abs(data.custoReal - data.custoEstimado);
    const percentual = (diferenca / data.custoEstimado) * 100;
    return percentual <= 200; // Máximo 200% de diferença
  }
  return true;
}, {
  message: "Custo real muito divergente do estimado (máximo 200% de diferença)",
  path: ["custoReal"]
});

export const insertManejoSchema = createInsertSchema(manejos).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type Manejo = typeof manejos.$inferSelect;
export type NewManejo = typeof manejos.$inferInsert;
export type ManejoData = z.infer<typeof manejoSchema>;

// ============================================================================
// BUSINESS LOGIC
// ============================================================================

/**
 * Calculates next management date based on recurrence
 */
export function calcularProximoManejo(dataBase: string, recorrencia: string): string {
  const data = new Date(dataBase);
  
  switch (recorrencia) {
    case 'diario':
      data.setDate(data.getDate() + 1);
      break;
    case 'semanal':
      data.setDate(data.getDate() + 7);
      break;
    case 'quinzenal':
      data.setDate(data.getDate() + 15);
      break;
    case 'mensal':
      data.setMonth(data.getMonth() + 1);
      break;
    case 'bimestral':
      data.setMonth(data.getMonth() + 2);
      break;
    case 'trimestral':
      data.setMonth(data.getMonth() + 3);
      break;
    case 'semestral':
      data.setMonth(data.getMonth() + 6);
      break;
    case 'anual':
      data.setFullYear(data.getFullYear() + 1);
      break;
    default:
      throw new Error(`Recorrência inválida: ${recorrencia}`);
  }
  
  return data.toISOString().split('T')[0];
}

/**
 * Validates management timing based on type
 */
export function validarTimingManejo(tipo: string, dataAgendada: string): string[] {
  const errors: string[] = [];
  const agendamento = new Date(dataAgendada);
  const hoje = new Date();
  
  // Remove time component for date comparison
  hoje.setHours(0, 0, 0, 0);
  agendamento.setHours(0, 0, 0, 0);
  
  switch (tipo) {
    case 'vacinacao':
      // Vacinações devem ser agendadas com antecedência
      const diasAntecedencia = (agendamento.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24);
      if (diasAntecedencia < 1) {
        errors.push("Vacinações devem ser agendadas com pelo menos 1 dia de antecedência");
      }
      break;
      
    case 'vermifugacao':
      // Vermifugação não deve ser muito frequente
      if (agendamento < hoje) {
        errors.push("Data de vermifugação não pode ser no passado");
      }
      break;
      
    case 'casqueamento':
      // Casqueamento tem periodicidade específica
      if (agendamento < hoje) {
        errors.push("Data de casqueamento não pode ser no passado");
      }
      break;
  }
  
  return errors;
}