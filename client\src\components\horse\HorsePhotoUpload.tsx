/**
 * Componente para upload de fotos de cavalos
 */
import React, { useState, useRef } from 'react';
import { Upload, Camera, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { queryClient } from '@/lib/queryClient';

interface HorsePhotoUploadProps {
  horse_id: number;
  onUploadSuccess?: () => void;
  onClose?: () => void;
  className?: string;
}

export function HorsePhotoUpload({ 
  horse_id, 
  onUploadSuccess, 
  onClose, 
  className = '' 
}: HorsePhotoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [description, setDescription] = useState('');
  const [isProfilePhoto, setIsProfilePhoto] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const acceptedTypes = 'image/jpeg,image/jpg,image/png,image/gif,image/webp';
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Formato inválido",
        description: "Por favor, selecione apenas arquivos de imagem (JPEG, PNG, GIF, WebP).",
        variant: "destructive",
      });
      return;
    }

    if (file.size > maxFileSize) {
      toast({
        title: "Arquivo muito grande",
        description: `O arquivo deve ter no máximo ${Math.round(maxFileSize / (1024 * 1024))}MB.`,
        variant: "destructive",
      });
      return;
    }

    setSelectedFile(file);

    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "Nenhum arquivo selecionado",
        description: "Por favor, selecione uma foto para fazer upload.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('photo', selectedFile);
      formData.append('description', description);
      formData.append('isProfilePhoto', isProfilePhoto.toString());

      const response = await fetch(`/api/cavalos/${horse_id}/photos`, {
        method: 'POST',
        headers: {
          'user-id': localStorage.getItem('user_id') || '',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao fazer upload');
      }

      toast({
        title: "Upload concluído!",
        description: "Foto enviada com sucesso.",
        variant: "default",
      });

      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${horse_id}/photos`] });
      
      setSelectedFile(null);
      setPreview(null);
      setDescription('');
      setIsProfilePhoto(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      onUploadSuccess?.();

    } catch (error) {
      console.error('Erro no upload:', error);
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Erro desconhecido ao enviar foto.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Adicionar Foto</Label>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {!preview ? (
          <div 
            className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Camera className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-1">
              Clique para selecionar uma foto
            </p>
            <p className="text-xs text-gray-500">
              JPEG, PNG, GIF ou WebP - Máximo {Math.round(maxFileSize / (1024 * 1024))}MB
            </p>
          </div>
        ) : (
          <div className="relative">
            <img 
              src={preview} 
              alt="Preview" 
              className="w-full h-48 object-cover rounded-lg"
            />
            <Button
              variant="secondary"
              size="sm"
              className="absolute top-2 right-2"
              onClick={clearSelection}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        <Input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes}
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {selectedFile && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="description" className="text-sm font-medium">
              Descrição (opcional)
            </Label>
            <Textarea
              id="description"
              placeholder="Ex: Foto do cavalo na competição..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="mt-1"
              rows={2}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isProfilePhoto"
              checked={isProfilePhoto}
              onCheckedChange={(checked) => setIsProfilePhoto(checked as boolean)}
            />
            <Label htmlFor="isProfilePhoto" className="text-sm">
              Definir como foto principal
            </Label>
          </div>

          <div className="text-xs text-gray-500 space-y-1">
            <p>Arquivo: {selectedFile.name}</p>
            <p>Tamanho: {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB</p>
          </div>
        </div>
      )}

      {selectedFile && (
        <div className="flex gap-2">
          <Button 
            onClick={handleUpload} 
            disabled={isUploading}
            className="flex-1"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Enviando...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Enviar Foto
              </>
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={clearSelection}
            disabled={isUploading}
          >
            Cancelar
          </Button>
        </div>
      )}
    </div>
  );
}

export default HorsePhotoUpload;