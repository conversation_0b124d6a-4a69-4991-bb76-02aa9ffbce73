import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  PlusCircle, Search, Edit, Trash2, Filter, Microscope, Pill, AlertTriangle,
  Calendar, ShoppingCart, FileText
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LayoutWrapper } from '@/components/Layout';

/**
 * Interface para definir a estrutura de um medicamento
 */
interface Medicamento {
  id: number;
  nome: string;
  principioAtivo: string;
  tipo: string;
  fabricante: string;
  lote: string;
  dataValidade: string;
  quantidadeEstoque: number;
  unidade: string;
  estoqueMinimo: number;
  dosagem: string;
  indicacoes: string;
  statusEstoque: 'normal' | 'baixo' | 'critico';
  statusValidade: 'valido' | 'proximo' | 'vencido';
  valorUnitario: number;
  ultimaCompra: string | null;
  controlado: boolean;
}

/**
 * Componente da página de medicamentos
 */
export function MedicamentosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [tipoFilter, setTipoFilter] = useState('todos');
  const [statusFilter, setStatusFilter] = useState('todos');
  const [validadeFilter, setValidadeFilter] = useState('todos');

  // Filtrar medicamentos com base nos filtros aplicados
  const filteredMedicamentos = medicamentosData.filter(medicamento => {
    const matchesSearch = 
      medicamento.nome.toLowerCase().includes(searchTerm.toLowerCase()) || 
      medicamento.principioAtivo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medicamento.fabricante.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTipo = tipoFilter === 'todos' || medicamento.tipo === tipoFilter;
    
    const matchesStatus = statusFilter === 'todos' || 
                         (statusFilter === 'baixo' && medicamento.statusEstoque === 'baixo') || 
                         (statusFilter === 'critico' && medicamento.statusEstoque === 'critico') ||
                         (statusFilter === 'normal' && medicamento.statusEstoque === 'normal');
    
    const matchesValidade = validadeFilter === 'todos' || 
                           (validadeFilter === 'proximo' && medicamento.statusValidade === 'proximo') || 
                           (validadeFilter === 'vencido' && medicamento.statusValidade === 'vencido') ||
                           (validadeFilter === 'valido' && medicamento.statusValidade === 'valido');
    
    return matchesSearch && matchesTipo && matchesStatus && matchesValidade;
  });

  // Função para renderizar o status do estoque
  const renderEstoqueStatus = (medicamento: Medicamento) => {
    if (medicamento.statusEstoque === 'critico') {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Crítico
      </Badge>;
    } else if (medicamento.statusEstoque === 'baixo') {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600 flex items-center gap-1">
        <AlertTriangle className="h-3 w-3" />
        Baixo
      </Badge>;
    } else {
      return <Badge variant="outline" className="text-green-600 border-green-600">
        Normal
      </Badge>;
    }
  };

  // Função para renderizar o status de validade
  const renderValidadeStatus = (medicamento: Medicamento) => {
    const dataValidade = new Date(medicamento.dataValidade);
    
    if (medicamento.statusValidade === 'vencido') {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <Calendar className="h-3 w-3" />
        Vencido
      </Badge>;
    } else if (medicamento.statusValidade === 'proximo') {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600 flex items-center gap-1">
        <Calendar className="h-3 w-3" />
        Próximo
      </Badge>;
    } else {
      return <Badge variant="outline" className="text-green-600 border-green-600">
        Válido
      </Badge>;
    }
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <LayoutWrapper pageTitle="Medicamentos Veterinários">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Medicamentos Veterinários</h1>
            <p className="text-muted-foreground">
              Controle e rastreamento de todos os medicamentos em estoque
            </p>
          </div>
          
          <div className="flex gap-2">
            <Link href="/medicamentos/novo">
              <Button className="flex items-center gap-2">
                <PlusCircle className="h-4 w-4" />
                <span>Novo Medicamento</span>
              </Button>
            </Link>
            <Link href="/medicamentos/compra">
              <Button variant="outline" className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                <span>Registrar Compra</span>
              </Button>
            </Link>
          </div>
        </div>
        
        {/* Informações em destaque */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="border-red-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                Estoque Crítico
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {medicamentosData.filter(m => m.statusEstoque === 'critico').length}
              </div>
              <p className="text-sm text-muted-foreground">
                Medicamentos que precisam de reposição
              </p>
            </CardContent>
          </Card>
          
          <Card className="border-yellow-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2 text-yellow-600">
                <Calendar className="h-5 w-5" />
                Próximo de Vencer
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {medicamentosData.filter(m => m.statusValidade === 'proximo').length}
              </div>
              <p className="text-sm text-muted-foreground">
                Medicamentos com validade próxima ao fim
              </p>
            </CardContent>
          </Card>
          
          <Card className="border-blue-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2 text-blue-600">
                <Pill className="h-5 w-5" />
                Medicamentos Controlados
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {medicamentosData.filter(m => m.controlado).length}
              </div>
              <p className="text-sm text-muted-foreground">
                Medicamentos que necessitam de registro especial
              </p>
            </CardContent>
          </Card>
        </div>
        
        {/* Card de filtros e busca */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Filtros e Busca</CardTitle>
            <CardDescription>
              Pesquise e filtre medicamentos de acordo com suas necessidades
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar por nome, princípio ativo ou fabricante..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Select 
                  value={tipoFilter} 
                  onValueChange={setTipoFilter}
                >
                  <SelectTrigger className="w-full sm:w-40">
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="Antibiótico">Antibiótico</SelectItem>
                    <SelectItem value="Anti-inflamatório">Anti-inflamatório</SelectItem>
                    <SelectItem value="Analgésico">Analgésico</SelectItem>
                    <SelectItem value="Antiparasitário">Antiparasitário</SelectItem>
                    <SelectItem value="Antifúngico">Antifúngico</SelectItem>
                    <SelectItem value="Vitamina">Vitamina</SelectItem>
                    <SelectItem value="Vacina">Vacina</SelectItem>
                    <SelectItem value="Suplemento">Suplemento</SelectItem>
                  </SelectContent>
                </Select>
                
                <Tabs 
                  defaultValue="todos" 
                  onValueChange={setStatusFilter}
                  className="w-full sm:w-auto"
                >
                  <TabsList className="grid grid-cols-3 w-full sm:w-auto">
                    <TabsTrigger value="todos">Todos</TabsTrigger>
                    <TabsTrigger value="baixo">Baixo</TabsTrigger>
                    <TabsTrigger value="critico">Crítico</TabsTrigger>
                  </TabsList>
                </Tabs>
                
                <Tabs 
                  defaultValue="todos" 
                  onValueChange={setValidadeFilter}
                  className="w-full sm:w-auto"
                >
                  <TabsList className="grid grid-cols-3 w-full sm:w-auto">
                    <TabsTrigger value="todos">Validade</TabsTrigger>
                    <TabsTrigger value="proximo">Próximo</TabsTrigger>
                    <TabsTrigger value="vencido">Vencido</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de medicamentos */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Lista de Medicamentos</CardTitle>
            <CardDescription>
              {filteredMedicamentos.length} medicamentos encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredMedicamentos.length === 0 ? (
              <div className="text-center py-8">
                <Pill className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Nenhum medicamento encontrado</h3>
                <p className="text-muted-foreground">
                  {searchTerm || tipoFilter !== 'todos' || statusFilter !== 'todos' || validadeFilter !== 'todos' ? 
                    'Tente ajustar os critérios de busca' : 
                    'Adicione medicamentos ao sistema para começar'}
                </p>
              </div>
            ) : (
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome / Princípio Ativo</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead className="text-center">Quantidade</TableHead>
                      <TableHead className="text-center">Estoque</TableHead>
                      <TableHead className="text-center">Validade</TableHead>
                      <TableHead className="text-right">Valor</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMedicamentos.map(medicamento => (
                      <TableRow key={medicamento.id}>
                        <TableCell>
                          <div className="font-medium flex items-center gap-2">
                            {medicamento.nome}
                            {medicamento.controlado && (
                              <Badge variant="secondary" className="text-xs">Controlado</Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {medicamento.principioAtivo}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Fabricante: {medicamento.fabricante} | Lote: {medicamento.lote}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-normal">
                            {medicamento.tipo}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center font-medium">
                          {medicamento.quantidadeEstoque} {medicamento.unidade}
                          <div className="text-xs text-muted-foreground">
                            Min: {medicamento.estoqueMinimo} {medicamento.unidade}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          {renderEstoqueStatus(medicamento)}
                        </TableCell>
                        <TableCell className="text-center">
                          {renderValidadeStatus(medicamento)}
                          <div className="text-xs mt-1">
                            {new Date(medicamento.dataValidade).toLocaleDateString('pt-BR')}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(medicamento.valorUnitario)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <span className="sr-only">Abrir menu</span>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                  className="h-5 w-5"
                                >
                                  <path d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z"></path>
                                </svg>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <Link href={`/medicamentos/${medicamento.id}`}>
                                <DropdownMenuItem>
                                  <Pill className="mr-2 h-4 w-4" />
                                  Ver Detalhes
                                </DropdownMenuItem>
                              </Link>
                              <Link href={`/medicamentos/${medicamento.id}/editar`}>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Editar
                                </DropdownMenuItem>
                              </Link>
                              <Link href={`/medicamentos/aplicacao/${medicamento.id}`}>
                                <DropdownMenuItem>
                                  <Microscope className="mr-2 h-4 w-4" />
                                  Registrar Aplicação
                                </DropdownMenuItem>
                              </Link>
                              <Link href={`/medicamentos/bula/${medicamento.id}`}>
                                <DropdownMenuItem>
                                  <FileText className="mr-2 h-4 w-4" />
                                  Ver Bula
                                </DropdownMenuItem>
                              </Link>
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Medicamentos vencidos */}
        {medicamentosData.filter(m => m.statusValidade === 'vencido').length > 0 && (
          <Card className="border-red-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-red-600 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Medicamentos Vencidos
              </CardTitle>
              <CardDescription>
                Medicamentos que precisam ser descartados adequadamente
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Lote</TableHead>
                      <TableHead className="text-center">Validade</TableHead>
                      <TableHead className="text-center">Quantidade</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {medicamentosData
                      .filter(m => m.statusValidade === 'vencido')
                      .map(medicamento => (
                        <TableRow key={`vencido-${medicamento.id}`}>
                          <TableCell className="font-medium">{medicamento.nome}</TableCell>
                          <TableCell>{medicamento.lote}</TableCell>
                          <TableCell className="text-center text-red-600">
                            {new Date(medicamento.dataValidade).toLocaleDateString('pt-BR')}
                          </TableCell>
                          <TableCell className="text-center">
                            {medicamento.quantidadeEstoque} {medicamento.unidade}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="outline" size="sm" className="text-red-600 border-red-600">
                              <Trash2 className="mr-1 h-3 w-3" />
                              <span>Registrar Descarte</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </LayoutWrapper>
  );
}

// Função para calcular o status de validade
const calcularStatusValidade = (dataValidade: string): 'valido' | 'proximo' | 'vencido' => {
  const hoje = new Date();
  const dataVenc = new Date(dataValidade);
  const diffDias = Math.floor((dataVenc.getTime() - hoje.getTime()) / (1000 * 3600 * 24));
  
  if (diffDias < 0) return 'vencido';
  if (diffDias < 30) return 'proximo';
  return 'valido';
};

// Dados de exemplo para medicamentos (apenas para demonstração)
const medicamentosData: Medicamento[] = [
  {
    id: 1,
    nome: 'Ivermectina Equina 1%',
    principioAtivo: 'Ivermectina',
    tipo: 'Antiparasitário',
    fabricante: 'Zoetis',
    lote: 'ZOE2023A456',
    dataValidade: '2025-06-30',
    quantidadeEstoque: 12,
    unidade: 'fr',
    estoqueMinimo: 5,
    dosagem: '1ml para cada 50kg',
    indicacoes: 'Controle de parasitas internos e externos',
    statusEstoque: 'normal',
    statusValidade: 'valido',
    valorUnitario: 78.90,
    ultimaCompra: new Date(Date.now() - 86400000 * 45).toISOString(),
    controlado: false
  },
  {
    id: 2,
    nome: 'Flunixin Meglumine',
    principioAtivo: 'Flunixina',
    tipo: 'Anti-inflamatório',
    fabricante: 'MSD Saúde Animal',
    lote: 'MSD2023B789',
    dataValidade: '2025-02-15',
    quantidadeEstoque: 3,
    unidade: 'fr',
    estoqueMinimo: 3,
    dosagem: '1.1mg/kg',
    indicacoes: 'Dor e inflamação, cólicas, febre',
    statusEstoque: 'baixo',
    statusValidade: 'proximo',
    valorUnitario: 125.50,
    ultimaCompra: new Date(Date.now() - 86400000 * 30).toISOString(),
    controlado: false
  },
  {
    id: 3,
    nome: 'Xilazina 10%',
    principioAtivo: 'Cloridrato de Xilazina',
    tipo: 'Sedativo',
    fabricante: 'Vetnil',
    lote: 'VET2022C321',
    dataValidade: '2025-04-25',
    quantidadeEstoque: 1,
    unidade: 'fr',
    estoqueMinimo: 2,
    dosagem: '0.5-1.0mg/kg',
    indicacoes: 'Sedação e analgesia para procedimentos',
    statusEstoque: 'critico',
    statusValidade: 'valido',
    valorUnitario: 189.00,
    ultimaCompra: new Date(Date.now() - 86400000 * 120).toISOString(),
    controlado: true
  },
  {
    id: 4,
    nome: 'Oxitetraciclina LA',
    principioAtivo: 'Oxitetraciclina',
    tipo: 'Antibiótico',
    fabricante: 'Bayer',
    lote: 'BAY2022D654',
    dataValidade: '2024-03-10',
    quantidadeEstoque: 8,
    unidade: 'fr',
    estoqueMinimo: 4,
    dosagem: '20mg/kg a cada 48h',
    indicacoes: 'Infecções bacterianas',
    statusEstoque: 'normal',
    statusValidade: 'proximo',
    valorUnitario: 95.75,
    ultimaCompra: new Date(Date.now() - 86400000 * 60).toISOString(),
    controlado: false
  },
  {
    id: 5,
    nome: 'Fenilbutazona',
    principioAtivo: 'Fenilbutazona',
    tipo: 'Anti-inflamatório',
    fabricante: 'Vetbrands',
    lote: 'VBR2022E987',
    dataValidade: '2023-06-20',
    quantidadeEstoque: 5,
    unidade: 'fr',
    estoqueMinimo: 3,
    dosagem: '2.2-4.4mg/kg',
    indicacoes: 'Dor e inflamação musculoesquelética',
    statusEstoque: 'normal',
    statusValidade: 'vencido',
    valorUnitario: 68.90,
    ultimaCompra: new Date(Date.now() - 86400000 * 240).toISOString(),
    controlado: false
  },
  {
    id: 6,
    nome: 'Ketamina 10%',
    principioAtivo: 'Cloridrato de Ketamina',
    tipo: 'Anestésico',
    fabricante: 'Agener União',
    lote: 'AGU2023F123',
    dataValidade: '2025-05-30',
    quantidadeEstoque: 3,
    unidade: 'fr',
    estoqueMinimo: 2,
    dosagem: '2.2mg/kg',
    indicacoes: 'Anestesia e sedação',
    statusEstoque: 'normal',
    statusValidade: 'valido',
    valorUnitario: 210.00,
    ultimaCompra: new Date(Date.now() - 86400000 * 75).toISOString(),
    controlado: true
  },
  {
    id: 7,
    nome: 'Vacina Encefalomielite',
    principioAtivo: 'Vírus inativado',
    tipo: 'Vacina',
    fabricante: 'Laboratório Biovet',
    lote: 'BIO2023G456',
    dataValidade: '2024-04-15',
    quantidadeEstoque: 2,
    unidade: 'fr',
    estoqueMinimo: 5,
    dosagem: '2ml por animal',
    indicacoes: 'Prevenção de encefalomielite equina',
    statusEstoque: 'critico',
    statusValidade: 'proximo',
    valorUnitario: 135.80,
    ultimaCompra: new Date(Date.now() - 86400000 * 180).toISOString(),
    controlado: false
  },
  {
    id: 8,
    nome: 'Dexametasona',
    principioAtivo: 'Dexametasona',
    tipo: 'Corticosteroide',
    fabricante: 'Ouro Fino',
    lote: 'OUF2023H789',
    dataValidade: '2023-12-05',
    quantidadeEstoque: 6,
    unidade: 'fr',
    estoqueMinimo: 3,
    dosagem: '0.05-0.2mg/kg',
    indicacoes: 'Anti-inflamatório, alergias, choque',
    statusEstoque: 'normal',
    statusValidade: 'vencido',
    valorUnitario: 45.60,
    ultimaCompra: new Date(Date.now() - 86400000 * 120).toISOString(),
    controlado: false
  },
];

export default MedicamentosPage;