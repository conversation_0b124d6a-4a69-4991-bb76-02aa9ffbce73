import { useState, useEffect, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { openDB, DBSchema } from "idb";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

// Definindo tipos
export type MessageType = "user" | "assistant" | "system";

// Possíveis estados do assistente de coleta contextual
export type AssistantMode = "idle" | "collecting_horse" | "collecting_reason" | "analyzing";

// Interface para tipos de assuntos pré-definidos
export interface AssistantTopic {
  id: string;
  title: string;
  description: string;
  promptTemplate: string;
}

export interface Message {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
}

interface StoredMessage {
  id: string;
  type: MessageType;
  content: string;
  timestamp: string; // Armazenado como string no IndexedDB
}

interface ChatMessage {
  content: string;
  role: string;
}

// Dados contextuais para coleta incremental
interface ContextData {
  selectedHorseId: string | null;
  selectedTopic: string | null;
  collectionMode: AssistantMode;
  userQuery: string | null;
  additionalInfo: Record<string, any>;
}

// Schema para o IndexedDB
interface ChatDBSchema extends DBSchema {
  messages: {
    key: string;
    value: StoredMessage; // Usando o tipo com timestamp como string
    indexes: {
      "by-timestamp": string;
    };
  };
  pendingMessages: {
    key: string;
    value: {
      message: string;
      horse_id: string | null;
      timestamp: string; // Também armazenado como string
    };
  };
  contextData: {
    key: string;
    value: {
      data: string; // JSON de ContextData
      timestamp: string;
    };
  };
}

// Nome do banco de dados e versão
const DB_NAME = "equigestor-chat";
const DB_VERSION = 2; // Versão 2 para adicionar store de contextData

// Lista de tópicos pré-definidos para o assistente
export const ASSISTANT_TOPICS: AssistantTopic[] = [
  {
    id: "vacinacao",
    title: "Vacinação",
    description: "Informações sobre vacinas e calendário de vacinação",
    promptTemplate: "Preciso de informações sobre vacinação para o meu cavalo. {horseContext} Por favor, forneça detalhes sobre as vacinas recomendadas, o calendário de vacinação e cuidados importantes."
  },
  {
    id: "nutricao",
    title: "Nutrição",
    description: "Dicas e informações sobre alimentação equina",
    promptTemplate: "Quero saber mais sobre a alimentação adequada para cavalos. {horseContext} Quais alimentos são recomendados, quantidade ideal e frequência de alimentação?"
  },
  {
    id: "saude",
    title: "Saúde",
    description: "Informações sobre cuidados de saúde e prevenção de doenças",
    promptTemplate: "Preciso de orientações sobre a saúde geral de cavalos. {horseContext} Quais são os principais cuidados preventivos, sinais de alerta para problemas de saúde e quando devo chamar um veterinário?"
  },
  {
    id: "reproducao",
    title: "Reprodução",
    description: "Informações sobre manejo reprodutivo",
    promptTemplate: "Necessito de informações sobre reprodução equina. {horseContext} Quais são as melhores práticas para manejo reprodutivo, ciclos reprodutivos e cuidados com éguas prenhes e potros?"
  },
  {
    id: "treinamento",
    title: "Treinamento",
    description: "Dicas e técnicas de treinamento",
    promptTemplate: "Busco orientações sobre treinamento de cavalos. {horseContext} Quais são as melhores técnicas, frequência ideal de treinamento e como lidar com problemas comportamentais?"
  }
];

/**
 * Hook para gerenciar a comunicação com o assistente virtual usando coleta contextual
 * @param horse_id ID opcional do cavalo para contextualização
 * @returns Funções e estados para interagir com o assistente de forma incremental
 */
export function useAssistenteContextual(horse_id: string | null = null) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [contextData, setContextData] = useState<ContextData>({
    selectedHorseId: horse_id,
    selectedTopic: null,
    collectionMode: "idle",
    userQuery: null,
    additionalInfo: {}
  });

  // Inicializar o banco de dados IndexedDB
  const initDB = useCallback(async () => {
    return openDB<ChatDBSchema>(DB_NAME, DB_VERSION, {
      upgrade(db, oldVersion, newVersion) {
        // Check current version and create/update stores as needed
        if (oldVersion < 1) {
          // Criar store para mensagens
          const messageStore = db.createObjectStore("messages", {
            keyPath: "id"
          });
          messageStore.createIndex("by-timestamp", "timestamp");
          
          // Criar store para mensagens pendentes
          db.createObjectStore("pendingMessages", {
            keyPath: "timestamp"
          });
        }
        
        if (oldVersion < 2) {
          // Add new store for context data in version 2
          db.createObjectStore("contextData", {
            keyPath: "timestamp"
          });
        }
      }
    });
  }, []);

  // Carregar mensagens do IndexedDB quando o componente montar
  useEffect(() => {
    const loadMessages = async () => {
      try {
        const db = await initDB();
        // Carregar apenas as últimas 50 mensagens (evitar sobrecarga)
        const allMessages = await db.getAllFromIndex(
          "messages",
          "by-timestamp",
          null
        );
        
        // Mapear as mensagens de volta, convertendo o timestamp de string para Date
        const mappedMessages = allMessages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        
        // Ordenar por timestamp e limitar a 50
        const sortedMessages = mappedMessages
          .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
          .slice(-50);
        
        console.log("[Assistente] Mensagens carregadas:", sortedMessages);
        setMessages(sortedMessages);
        
        // Carregar dados contextuais se existirem
        try {
          const contextEntries = await db.getAll("contextData");
          if (contextEntries.length > 0) {
            // Get the most recent context
            const latestContext = contextEntries.sort((a, b) => 
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            )[0];
            
            const parsedContext = JSON.parse(latestContext.data);
            setContextData(prev => ({
              ...prev,
              ...parsedContext,
              selectedHorseId: horse_id || parsedContext.selectedHorseId // Priorizar o horse_id do parâmetro
            }));
          }
        } catch (err) {
          console.error("Erro ao carregar contexto:", err);
          // Continuar normalmente mesmo se falhar
        }
        
        // Verificar se há mensagens pendentes para enviar
        const pendingMessages = await db.getAll("pendingMessages");
        if (pendingMessages.length > 0) {
          console.log(`${pendingMessages.length} mensagens pendentes encontradas`);
          // Tentar enviar as mensagens pendentes se estiver online
          if (navigator.onLine) {
            processPendingMessages();
          }
        }
      } catch (error) {
        console.error("Erro ao carregar mensagens:", error);
        toast({
          title: "Erro ao carregar histórico",
          description: "Não foi possível carregar o histórico de mensagens.",
          variant: "destructive",
        });
      }
    };

    loadMessages();
    
    // Adicionar listeners para eventos de conectividade
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Atualizar o horse_id quando ele mudar
  useEffect(() => {
    setContextData(prev => ({
      ...prev,
      selectedHorseId: horse_id
    }));
  }, [horse_id]);
  
  // Salvar o contexto sempre que ele mudar
  useEffect(() => {
    const saveContext = async () => {
      try {
        const db = await initDB();
        
        await db.add("contextData", {
          data: JSON.stringify(contextData),
          timestamp: new Date().toISOString()
        });
        
        // Limitar a 5 entradas mais recentes para economizar espaço
        const allContexts = await db.getAll("contextData");
        if (allContexts.length > 5) {
          const sortedContexts = allContexts.sort((a, b) => 
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          );
          
          // Remover os contextos mais antigos
          for (let i = 5; i < sortedContexts.length; i++) {
            await db.delete("contextData", sortedContexts[i].timestamp);
          }
        }
      } catch (error) {
        console.error("Erro ao salvar contexto:", error);
      }
    };
    
    saveContext();
  }, [contextData]);

  // Handler para quando a conexão for restabelecida
  const handleOnline = useCallback(() => {
    console.log("Conexão online detectada");
    toast({
      title: "Você está online",
      description: "Sincronizando mensagens pendentes...",
    });
    processPendingMessages();
  }, []);

  // Handler para quando a conexão cair
  const handleOffline = useCallback(() => {
    console.log("Conexão offline detectada");
    toast({
      title: "Você está offline",
      description: "As mensagens serão enviadas quando a conexão for restabelecida.",
      variant: "destructive",
    });
  }, []);

  // Processar mensagens pendentes (quando voltar online)
  const processPendingMessages = useCallback(async () => {
    try {
      const db = await initDB();
      const pendingMessages = await db.getAll("pendingMessages");
      
      if (pendingMessages.length === 0) return;
      
      console.log(`[Assistente] Tentando enviar ${pendingMessages.length} mensagens pendentes:`, pendingMessages);
      
      for (const pendingMsg of pendingMessages) {
        try {
          // Tentar enviar a mensagem
          console.log("[Assistente] Enviando mensagem pendente:", pendingMsg.message);
          const response = await sendMessageToAPI(pendingMsg.message, pendingMsg.horse_id);
          console.log("[Assistente] Resposta recebida para mensagem pendente:", response);
          
          // Se enviar com sucesso, remover da fila
          console.log("[Assistente] Removendo mensagem pendente com timestamp:", pendingMsg.timestamp);
          await db.delete("pendingMessages", pendingMsg.timestamp);
        } catch (error) {
          console.error("[Assistente] Erro ao enviar mensagem pendente:", error);
          // Se falhar, mantém na fila para tentar novamente depois
          break; // Para de tentar se uma falhar
        }
      }
      
      // Verificar se há mais mensagens pendentes
      const remainingMessages = await db.getAll("pendingMessages");
      if (remainingMessages.length === 0) {
        toast({
          title: "Sincronização completa",
          description: "Todas as mensagens pendentes foram enviadas.",
        });
      } else {
        toast({
          title: "Sincronização parcial",
          description: `${pendingMessages.length - remainingMessages.length} de ${pendingMessages.length} mensagens enviadas.`,
        });
      }
    } catch (error) {
      console.error("[Assistente] Erro ao processar mensagens pendentes:", error);
    }
  }, []);

  // Adicionar uma mensagem ao IndexedDB
  const addMessageToIndexedDB = async (message: Message) => {
    try {
      const db = await initDB();
      // Converter a data para string para evitar problemas de serialização
      const messageToStore = {
        ...message,
        timestamp: message.timestamp.toISOString()
      };
      await db.add("messages", messageToStore as any);
    } catch (error) {
      console.error("Erro ao salvar mensagem no IndexedDB:", error);
    }
  };

  // Adicionar uma mensagem pendente quando estiver offline
  const addPendingMessage = async (message: string, horse_id: string | null) => {
    try {
      const db = await initDB();
      await db.add("pendingMessages", {
        message,
        horse_id,
        timestamp: new Date().toISOString(),
      });
      return true;
    } catch (error) {
      console.error("Erro ao salvar mensagem pendente:", error);
      return false;
    }
  };

  // Limpar todas as mensagens
  const clearMessages = useCallback(async () => {
    try {
      const db = await initDB();
      await db.clear("messages");
      setMessages([]);
      
      // Resetar também o contexto para idle
      setContextData({
        selectedHorseId: horse_id,
        selectedTopic: null,
        collectionMode: "idle",
        userQuery: null,
        additionalInfo: {}
      });
    } catch (error) {
      console.error("Erro ao limpar mensagens:", error);
      toast({
        title: "Erro",
        description: "Não foi possível limpar o histórico de mensagens.",
        variant: "destructive",
      });
    }
  }, [horse_id]);

  // Enviar mensagem para a API
  const sendMessageToAPI = async (message: string, currentHorseId: string | null) => {
    // Preparar as mensagens para envio à API
    const apiMessages: ChatMessage[] = [];
    
    // Adicionar as últimas 5 mensagens do histórico para contexto
    // (limitado para reduzir carga no modelo GPT-3.5-turbo)
    const contextMessages = messages.slice(-5);
    
    contextMessages.forEach((msg) => {
      apiMessages.push({
        role: msg.type === "user" ? "user" : (msg.type === "system" ? "system" : "assistant"),
        content: msg.content,
      });
    });
    
    // Adicionar a mensagem atual
    apiMessages.push({
      role: "user",
      content: message,
    });
    
    console.log("[Assistente] Enviando mensagens para API:", JSON.stringify(apiMessages));
    
    try {
      // Fazer a requisição para a API com timeout aumentado
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 segundos de timeout
      
      const response = await apiRequest("/api/chat", "POST", {
        messages: apiMessages,
        horse_id: currentHorseId,
      }, { signal: controller.signal });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Erro desconhecido" }));
        throw new Error(errorData.message || "Erro ao comunicar com o assistente");
      }
      
      const data = await response.json();
      console.log("[Assistente] Resposta da API recebida:", data);
      return data.content;
    } catch (error) {
      console.error("[Assistente] Erro na comunicação com API:", error);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error("A requisição demorou muito tempo. Tente novamente.");
        }
        
        // Preservar a mensagem de erro original se for um objeto Error
        throw error;
      } else {
        // Se não for um Error, criar um novo objeto de erro com melhor mensagem
        throw new Error("Falha na comunicação com o servidor. Tente novamente.");
      }
    }
  };
  
  // Função para gerar uma mensagem formatada com base em um template
  const formatPromptTemplate = (template: string, data: { [key: string]: any } = {}): string => {
    let result = template;
    
    // Substituir placeholders com dados do contexto
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{${key}}`;
      result = result.replace(placeholder, value || '');
    }
    
    // Remover placeholders não substituídos
    result = result.replace(/\{[^{}]*\}/g, '');
    
    return result;
  };
  
  // Função para processar a entrada do usuário baseada no modo do assistente
  const processUserInput = async (userInput: string): Promise<string> => {
    console.log("[Assistente] Processando entrada do usuário no modo:", contextData.collectionMode);
    
    // Com base no modo atual, processar a entrada e atualizar o contexto
    switch (contextData.collectionMode) {
      case "idle":
        // Verificar se a entrada corresponde a algum tópico predefinido
        const matchedTopic = ASSISTANT_TOPICS.find(topic => 
          userInput.toLowerCase().includes(topic.title.toLowerCase())
        );
        
        if (matchedTopic) {
          // Se encontrou um tópico, perguntar sobre o cavalo
          setContextData(prev => ({
            ...prev,
            selectedTopic: matchedTopic.id,
            collectionMode: "collecting_horse"
          }));
          
          // Se já temos um cavalo selecionado, pular para a coleta do motivo
          if (contextData.selectedHorseId) {
            setContextData(prev => ({
              ...prev,
              selectedTopic: matchedTopic.id,
              collectionMode: "collecting_reason"
            }));
            
            return `Entendi que você quer informações sobre ${matchedTopic.title}. Pode me dar mais detalhes sobre o que exatamente você gostaria de saber sobre este assunto?`;
          }
          
          return `Entendi que você quer informações sobre ${matchedTopic.title}. Para qual cavalo você precisa dessas informações? (Você pode responder "nenhum" se não for para um cavalo específico)`;
        }
        
        // Se não é um tópico predefinido, perguntar se o usuário deseja escolher um tópico
        setContextData(prev => ({
          ...prev,
          userQuery: userInput,
          collectionMode: "collecting_reason"
        }));
        
        return "Entendi. Poderia me dar mais detalhes sobre o que você gostaria de saber? Isso me ajudará a fornecer informações mais precisas.";
        
      case "collecting_horse":
        // Processar a entrada do usuário para identificar o cavalo
        if (userInput.toLowerCase().includes("nenhum") || 
            userInput.toLowerCase().includes("geral") ||
            userInput.toLowerCase().includes("não") ||
            userInput.toLowerCase().includes("nao")) {
          // Usuário não quer um cavalo específico
          setContextData(prev => ({
            ...prev,
            selectedHorseId: null,
            collectionMode: "collecting_reason"
          }));
          
          const topic = ASSISTANT_TOPICS.find(t => t.id === contextData.selectedTopic);
          return `Entendi que você busca informações gerais sobre ${topic?.title || "este assunto"}. Pode me dar mais detalhes sobre o que exatamente você gostaria de saber?`;
        }
        
        // Armazenar a resposta como o ID do cavalo (ou nome, dependendo da implementação)
        setContextData(prev => ({
          ...prev,
          additionalInfo: {
            ...prev.additionalInfo,
            horseInfo: userInput
          },
          collectionMode: "collecting_reason"
        }));
        
        const topic = ASSISTANT_TOPICS.find(t => t.id === contextData.selectedTopic);
        return `Obrigado pela informação sobre o cavalo. Pode me dar mais detalhes sobre o que exatamente você gostaria de saber sobre ${topic?.title || "este assunto"}?`;
        
      case "collecting_reason":
        // Armazenar a razão/detalhes adicionais
        setContextData(prev => ({
          ...prev,
          additionalInfo: {
            ...prev.additionalInfo,
            reason: userInput
          },
          collectionMode: "analyzing"
        }));
        
        // Agora temos informações suficientes para fazer uma consulta completa
        // Vamos preparar um prompt completo com base no template e dados coletados
        let fullPrompt = "";
        
        // Se temos um tópico selecionado, usar seu template
        if (contextData.selectedTopic) {
          const selectedTopic = ASSISTANT_TOPICS.find(t => t.id === contextData.selectedTopic);
          if (selectedTopic) {
            // Preparar dados para o template
            const templateData: Record<string, string> = {
              horseContext: contextData.selectedHorseId 
                ? `Estou perguntando especificamente sobre o cavalo com ID ${contextData.selectedHorseId}.` 
                : contextData.additionalInfo.horseInfo 
                  ? `Estou perguntando sobre um cavalo descrito como: ${contextData.additionalInfo.horseInfo}.`
                  : ""
            };
            
            // Formatar o prompt usando o template
            fullPrompt = formatPromptTemplate(selectedTopic.promptTemplate, templateData);
            
            // Adicionar detalhes específicos se disponíveis
            if (contextData.additionalInfo.reason) {
              fullPrompt += ` Especificamente: ${contextData.additionalInfo.reason}`;
            }
          }
        } else if (contextData.userQuery) {
          // Se não temos um tópico mas temos uma consulta inicial
          fullPrompt = contextData.userQuery;
          
          // Adicionar contexto do cavalo se disponível
          if (contextData.selectedHorseId) {
            fullPrompt += ` Estou perguntando especificamente sobre o cavalo com ID ${contextData.selectedHorseId}.`;
          } else if (contextData.additionalInfo.horseInfo) {
            fullPrompt += ` Estou perguntando sobre um cavalo descrito como: ${contextData.additionalInfo.horseInfo}.`;
          }
          
          // Adicionar detalhes específicos
          if (contextData.additionalInfo.reason) {
            fullPrompt += ` Especificamente: ${contextData.additionalInfo.reason}`;
          }
        } else {
          // Usamos diretamente o input do usuário como query completa
          fullPrompt = userInput;
        }
        
        console.log("[Assistente] Prompt completo formatado:", fullPrompt);
        
        // Enviar o prompt completo para a API
        try {
          const response = await sendMessageToAPI(fullPrompt, contextData.selectedHorseId);
          
          // Resetar o modo de coleta
          setContextData(prev => ({
            ...prev,
            collectionMode: "idle",
            userQuery: null,
            additionalInfo: {}
          }));
          
          return response;
        } catch (error) {
          console.error("[Assistente] Erro ao enviar prompt completo:", error);
          
          // Resetar o modo de coleta em caso de erro
          setContextData(prev => ({
            ...prev,
            collectionMode: "idle",
            userQuery: null,
            additionalInfo: {}
          }));
          
          return "Desculpe, não consegui processar sua solicitação. Poderia tentar novamente com uma pergunta diferente?";
        }
        
      case "analyzing":
        // Se estamos analisando, resetar para idle e processar como nova consulta
        setContextData(prev => ({
          ...prev,
          collectionMode: "idle",
          userQuery: null,
          additionalInfo: {}
        }));
        
        // Tratar como uma nova consulta
        return await processUserInput(userInput);
        
      default:
        // Modo desconhecido, resetar e tratar como nova consulta
        setContextData(prev => ({
          ...prev,
          collectionMode: "idle",
          userQuery: null,
          additionalInfo: {}
        }));
        
        return await processUserInput(userInput);
    }
  };

  // Função para selecionar um tópico específico manualmente
  const selectTopic = (topicId: string) => {
    const topic = ASSISTANT_TOPICS.find(t => t.id === topicId);
    if (!topic) return;
    
    // Adicionar uma mensagem do sistema indicando a seleção do tópico
    const systemMessage: Message = {
      id: uuidv4(),
      type: "system",
      content: `Tópico selecionado: ${topic.title}`,
      timestamp: new Date(),
    };
    
    // Adicionar a mensagem ao histórico
    setMessages(prev => [...prev, systemMessage]);
    addMessageToIndexedDB(systemMessage);
    
    // Atualizar o contexto
    setContextData(prev => ({
      ...prev,
      selectedTopic: topicId,
      collectionMode: contextData.selectedHorseId ? "collecting_reason" : "collecting_horse"
    }));
    
    // Adicionar uma mensagem do assistente solicitando informações
    const responseMessage = contextData.selectedHorseId
      ? `Você selecionou o tópico ${topic.title}. Pode me dar mais detalhes sobre o que exatamente você gostaria de saber sobre este assunto?`
      : `Você selecionou o tópico ${topic.title}. Para qual cavalo você precisa dessas informações? (Você pode responder "nenhum" se não for para um cavalo específico)`;
    
    const assistantMessage: Message = {
      id: uuidv4(),
      type: "assistant",
      content: responseMessage,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, assistantMessage]);
    addMessageToIndexedDB(assistantMessage);
  };

  // Função principal para enviar mensagem
  const sendMessage = async (message: string) => {
    if (message.trim() === "") return;
    
    setIsLoading(true);
    
    try {
      // Criar e adicionar a mensagem do usuário imediatamente
      const userMessage: Message = {
        id: uuidv4(),
        type: "user",
        content: message,
        timestamp: new Date(),
      };
      
      console.log("[Assistente] Adicionando mensagem do usuário:", userMessage);
      
      // Adicionar à lista de mensagens e ao IndexedDB
      setMessages((prev) => [...prev, userMessage]);
      await addMessageToIndexedDB(userMessage);
      
      let assistantResponse;
      
      // Verificar se está online
      if (navigator.onLine) {
        try {
          // Processar a mensagem com base no modo do assistente
          console.log("[Assistente] Processando mensagem no modo:", contextData.collectionMode);
          
          assistantResponse = await processUserInput(message);
          
        } catch (error) {
          console.error("[Assistente] Erro ao processar mensagem:", error);
          
          // Se falhar, adicionar à lista de mensagens pendentes
          const added = await addPendingMessage(message, contextData.selectedHorseId);
          
          if (added) {
            // Informar ao usuário que a mensagem será enviada posteriormente
            assistantResponse = "Parece que estou com dificuldades para me conectar ao servidor. Sua mensagem foi salva e será processada assim que a conexão for restabelecida.";
          } else {
            // Falha total - não conseguiu nem salvar localmente
            assistantResponse = "Não foi possível processar sua mensagem no momento. Por favor, tente novamente mais tarde.";
          }
        }
      } else {
        // Estamos offline, salvar para envio posterior
        await addPendingMessage(message, contextData.selectedHorseId);
        assistantResponse = "Você parece estar offline. Sua mensagem foi salva e será enviada quando a conexão for restabelecida.";
      }
      
      // Criar e adicionar a resposta do assistente
      const assistantMessage: Message = {
        id: uuidv4(),
        type: "assistant",
        content: assistantResponse || "Desculpe, não consegui processar sua mensagem. Tente novamente mais tarde.",
        timestamp: new Date(),
      };
      
      console.log("[Assistente] Adicionando resposta:", assistantMessage);
      
      // Adicionar à lista de mensagens e ao IndexedDB
      setMessages((prev) => [...prev, assistantMessage]);
      await addMessageToIndexedDB(assistantMessage);
      
    } catch (error) {
      console.error("[Assistente] Erro geral ao enviar mensagem:", error);
      toast({
        title: "Erro ao enviar mensagem",
        description: error instanceof Error ? error.message : "Houve um problema ao processar sua mensagem. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { 
    messages, 
    sendMessage, 
    isLoading,
    clearMessages,
    selectTopic,
    topics: ASSISTANT_TOPICS,
    contextData
  };
}