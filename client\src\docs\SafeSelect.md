# SafeSelect - Guia de Uso

Este documento explica como usar os componentes `SafeSelect` e `SafeSelectItem` para evitar erros com valores vazios em componentes Select.

## Problema

O componente `SelectItem` do Radix UI (usado em nossa biblioteca de componentes) não permite valores vazios (`value=""`). Isso causa o seguinte erro:

```
[plugin:runtime-error-plugin] A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
```

## Solução

Para resolver esse problema, criamos os seguintes componentes e utilitários:

1. `SafeSelectItem` - Um componente que garante que o valor nunca será vazio
2. `useSafeSelect` - Um hook para trabalhar com valores de select de forma segura
3. `processSelectValues` - Uma função para processar valores de select em formulários

## Como usar

### 1. SafeSelectItem

Use `SafeSelectItem` em vez de `SelectItem` quando precisar de um item com valor vazio:

```tsx
// Antes (causa erro):
<SelectItem value="">Nenhum item</SelectItem>

// Depois (seguro):
<SafeSelectItem originalValue="">Nenhum item</SafeSelectItem>
```

O `SafeSelectItem` automaticamente converte o valor vazio para um valor seguro (por padrão, "nao_informado").

### 2. useSafeSelect

Use o hook `useSafeSelect` para trabalhar com valores de select de forma segura:

```tsx
// Antes:
const [value, setValue] = useState<string | null>(null);

// ...

<Select
  value={value || ""}
  onValueChange={(newValue) => setValue(newValue === "nao_informado" ? null : newValue)}
>
  {/* ... */}
</Select>

// Depois:
const [value, setValue] = useState<string | null>(null);
const safeSelect = useSafeSelect(value, setValue);

// ...

<Select
  value={safeSelect.value}
  onValueChange={safeSelect.onChange}
>
  {/* ... */}
</Select>
```

O hook `useSafeSelect` cuida automaticamente da conversão entre valores seguros e valores originais.

### 3. processSelectValues

Use a função `processSelectValues` para processar valores de select em formulários:

```tsx
// Antes:
const handleSubmit = (values: FormValues) => {
  // Processar valores "nao_informado" para valores vazios
  const processedValues = {
    ...values,
    // Converter "nao_informado" para string vazia
    sexo: values.sexo === "nao_informado" ? "" : values.sexo,
    cor: values.cor === "nao_informado" ? "" : values.cor,
    // ...
  };
  
  onSubmit(processedValues);
};

// Depois:
const handleSubmit = (values: FormValues) => {
  // Processar valores de select para valores originais
  const processedValues = processSelectValues(values);
  
  onSubmit(processedValues);
};
```

A função `processSelectValues` processa recursivamente um objeto e converte todos os valores de select para seus valores originais.

## Opções Avançadas

### SafeSelectItem

O componente `SafeSelectItem` aceita as seguintes props:

- `originalValue` - O valor original (pode ser vazio)
- `fallbackValue` - O valor de fallback a ser usado (padrão: "nao_informado")
- `prefix` - Um prefixo para o valor (útil para identificar o contexto)

```tsx
<SafeSelectItem 
  originalValue="" 
  fallbackValue="nenhum" 
  prefix="animal"
>
  Nenhum animal
</SafeSelectItem>
```

### useSafeSelect

O hook `useSafeSelect` aceita as seguintes opções:

- `emptyValue` - O valor a retornar quando o SelectItem representa "vazio" (padrão: null)
- `fallbackValue` - O valor de fallback a ser usado (padrão: "nao_informado")
- `prefix` - Um prefixo para o valor (útil para identificar o contexto)
- `convertToNumber` - Se deve converter o valor para número (padrão: false)

```tsx
const safeSelect = useSafeSelect(value, setValue, {
  emptyValue: "",
  fallbackValue: "nenhum",
  prefix: "animal",
  convertToNumber: true
});
```

### processSelectValues

A função `processSelectValues` aceita as seguintes opções:

- `emptyValue` - O valor a retornar quando o SelectItem representa "vazio" (padrão: "")

```tsx
const processedValues = processSelectValues(values, null);
```

## Exemplo Completo

```tsx
import React, { useState } from 'react';
import { 
  SafeSelect, 
  SafeSelectItem, 
  useSafeSelect 
} from '@/components/ui/safe-select';
import { processSelectValues } from '@/lib/select-utils';
import { 
  Select, 
  SelectContent, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

function MyForm() {
  const [value, setValue] = useState<string | null>(null);
  
  // Usando o hook useSafeSelect
  const safeSelect = useSafeSelect(value, setValue, {
    convertToNumber: true
  });
  
  const handleSubmit = (formValues) => {
    // Processar valores de select para valores originais
    const processedValues = processSelectValues(formValues);
    
    // Fazer algo com os valores processados
    console.log(processedValues);
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Usando SafeSelect */}
      <SafeSelect 
        value={value} 
        onValueChange={setValue}
      >
        <SelectTrigger>
          <SelectValue placeholder="Selecione uma opção" />
        </SelectTrigger>
        <SelectContent>
          <SafeSelectItem originalValue="">Nenhuma opção</SafeSelectItem>
          <SelectItem value="opcao1">Opção 1</SelectItem>
          <SelectItem value="opcao2">Opção 2</SelectItem>
        </SelectContent>
      </SafeSelect>
      
      {/* Usando Select com useSafeSelect */}
      <Select 
        value={safeSelect.value} 
        onValueChange={safeSelect.onChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Selecione uma opção" />
        </SelectTrigger>
        <SelectContent>
          <SafeSelectItem originalValue="">Nenhuma opção</SafeSelectItem>
          <SelectItem value="opcao1">Opção 1</SelectItem>
          <SelectItem value="opcao2">Opção 2</SelectItem>
        </SelectContent>
      </Select>
      
      <button type="submit">Enviar</button>
    </form>
  );
}
```

## Dicas

1. Sempre use `SafeSelectItem` em vez de `SelectItem` quando precisar de um item com valor vazio.
2. Use o hook `useSafeSelect` para simplificar o trabalho com valores de select.
3. Use a função `processSelectValues` para processar valores de select em formulários.
4. Se estiver usando TypeScript, você pode precisar usar `as string` ao acessar propriedades de objetos processados.

## Solução de Problemas

Se você ainda estiver vendo o erro, verifique:

1. Se todos os `SelectItem` com valor vazio foram substituídos por `SafeSelectItem`.
2. Se você está usando a versão mais recente dos componentes.
3. Se você está processando corretamente os valores no envio do formulário.

Para ajudar a detectar problemas, você pode usar o componente `SelectValidator`:

```tsx
import { SelectValidator } from '@/components/ui/select-validator';

function MyForm() {
  return (
    <SelectValidator location="MyForm">
      <Select>
        {/* ... */}
      </Select>
    </SelectValidator>
  );
}
```

O `SelectValidator` emitirá avisos no console se encontrar `SelectItem` com valores inválidos.
