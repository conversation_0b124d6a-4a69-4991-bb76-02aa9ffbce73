// Script para migrar os dados existentes e marcar cavalos da genealogia como externos
import { db, pool } from './db';
import { cavalos } from '@shared/schema';
import { eq, and, isNull, or } from 'drizzle-orm';
import { getModuleLogger } from './logger';

const logger = getModuleLogger('migration-external');

/**
 * Função para identificar e marcar todos os cavalos de genealogia como externos
 */
export async function migrateExternalHorses() {
  logger.info('Iniciando migração para marcar cavalos da genealogia como externos');
  
  try {
    // Vamos usar a abordagem via Drizzle ORM em vez de SQL direto
    // para evitar problemas de compatibilidade
    
    // 1. Buscar todos os cavalos
    const allHorses = await db.select().from(cavalos);
    logger.info(`Total de cavalos no sistema: ${allHorses.length}`);
    
    // 2. Identificar cavalos usados como pais ou mães
    const familyIds = new Set<number>();
    
    // Coletar IDs de cavalos usados como pais
    for (const horse of allHorses) {
      if (horse.pai_id) {
        familyIds.add(horse.pai_id);
      }
      if (horse.mae_id) {
        familyIds.add(horse.mae_id);
      }
    }
    
    logger.info(`Total de cavalos referenciados na genealogia: ${familyIds.size}`);
    
    // 3. Atualizar os cavalos identificados como externos
    let updatedCount = 0;
    
    // Converter Set para Array para usar no in()
    const familyIdsArray = Array.from(familyIds);
    
    if (familyIdsArray.length > 0) {
      // Fazer atualização em grupos de 100 para evitar limite de parâmetros
      const batchSize = 100;
      for (let i = 0; i < familyIdsArray.length; i += batchSize) {
        const batch = familyIdsArray.slice(i, i + batchSize);
        
        try {
          const result = await db.update(cavalos)
            .set({ is_external: true })
            .where(eq(cavalos.id, batch[0])); // Adicionando primeiro ID para evitar erro de sintaxe
          
          // O restante do batch será processado individualmente
          for (let j = 1; j < batch.length; j++) {
            await db.update(cavalos)
              .set({ is_external: true })
              .where(eq(cavalos.id, batch[j]));
          }
          
          updatedCount += batch.length;
        } catch (error) {
          logger.warn(`Erro ao atualizar lote ${i}-${i+batchSize}: ${error.message}`);
        }
      }
      
      logger.info(`Total de cavalos atualizados para externos: ${updatedCount}`);
    }
    
    logger.info('Migração concluída com sucesso!');
    return true;
  } catch (error: any) {
    logger.error(`Erro na migração: ${error.message}`);
    return false;
  }
}

// Exportar a função para ser chamada na inicialização do servidor
export default migrateExternalHorses;