import React, { useState, useEffect } from 'react';
import { 
  PlayCircle, 
  PauseCircle, 
  RefreshCw,
  Info
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Características genéticas para demonstração
const demoTraits = [
  {
    id: 'coat-color',
    name: 'Cor da Pelagem',
    description: 'Cor base da pelagem do cavalo',
    dominantAllele: 'B',
    recessiveAllele: 'b',
    phenotypeDescription: {
      dominant: 'Preto (BB)',
      heterozygous: 'Preto (Bb)',
      recessive: 'Castan<PERSON> (bb)',
    },
    inheritance: 'dominant',
    categories: ['appearance', 'color']
  },
  {
    id: 'cream-dilution',
    name: '<PERSON><PERSON><PERSON><PERSON> Creme',
    description: 'Diluição que afeta os pigmentos',
    dominantAllele: 'C',
    recessiveAllele: 'c',
    phenotypeDescription: {
      dominant: 'Sem diluição (CC)',
      heterozygous: 'Palomino/Buckskin (Cc)',
      recessive: 'Cremelo/Perlino (cc)',
    },
    inheritance: 'incomplete',
    categories: ['appearance', 'color']
  },
  {
    id: 'gait-type',
    name: 'Tipo de Andamento',
    description: 'Capacidade para andamentos extras',
    dominantAllele: 'G',
    recessiveAllele: 'g',
    phenotypeDescription: {
      dominant: 'Andamentos extras (GG)',
      heterozygous: 'Predisposição a andamentos extras (Gg)',
      recessive: 'Andamentos normais (gg)',
    },
    inheritance: 'incomplete',
    categories: ['performance', 'movement']
  }
];

// Função para gerar alelos aleatórios
const generateRandomAlleles = (trait) => {
  const options = [
    { symbol: trait.dominantAllele, isDominant: true },
    { symbol: trait.recessiveAllele, isDominant: false }
  ];
  
  return [
    options[Math.floor(Math.random() * 2)],
    options[Math.floor(Math.random() * 2)]
  ];
};

// Função para determinar o fenótipo com base no genótipo
const determinePhenotype = (genotype) => {
  const isRecessivePair = !genotype.alleles[0].isDominant && !genotype.alleles[1].isDominant;
  const isHeterozygous = genotype.alleles[0].isDominant !== genotype.alleles[1].isDominant;
  
  if (isRecessivePair) {
    return genotype.trait.phenotypeDescription.recessive;
  } else if (isHeterozygous && genotype.trait.inheritance === 'incomplete') {
    return genotype.trait.phenotypeDescription.heterozygous || genotype.trait.phenotypeDescription.dominant;
  } else {
    return genotype.trait.phenotypeDescription.dominant;
  }
};

// Função para determinar o resultado da herança genética
const inheritAlleles = (sireGenotype, damGenotype) => {
  const sireAllele = sireGenotype.alleles[Math.floor(Math.random() * 2)];
  const damAllele = damGenotype.alleles[Math.floor(Math.random() * 2)];
  
  return {
    trait: sireGenotype.trait,
    alleles: [sireAllele, damAllele]
  };
};

const TraitInheritanceAnimation = ({ sire, dam, onSelectTrait }) => {
  // Estado para a característica genética selecionada
  const [selectedTrait, setSelectedTrait] = useState(demoTraits[0]);
  
  // Estados para os genótipos
  const [sireGenotype, setSireGenotype] = useState(null);
  const [damGenotype, setDamGenotype] = useState(null);
  const [offspringGenotype, setOffspringGenotype] = useState(null);
  
  // Estado para animação
  const [isSimulated, setIsSimulated] = useState(false);
  
  // Inicialização dos genótipos quando muda o traço selecionado
  useEffect(() => {
    if (!selectedTrait) return;
    
    // Gerar genótipos aleatórios para demonstração
    setSireGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
    
    setDamGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
    
    // Reset de offspring
    setOffspringGenotype(null);
    setIsSimulated(false);
    
    if (onSelectTrait) {
      onSelectTrait(selectedTrait);
    }
  }, [selectedTrait, onSelectTrait]);
  
  // Simular a herança genética
  const simulate = () => {
    if (sireGenotype && damGenotype) {
      const offspring = inheritAlleles(sireGenotype, damGenotype);
      setOffspringGenotype(offspring);
      setIsSimulated(true);
    }
  };
  
  // Resetar a simulação
  const reset = () => {
    setSireGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
    
    setDamGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
    
    setOffspringGenotype(null);
    setIsSimulated(false);
  };
  
  // Renderizar alelo como elemento visual
  const renderAllele = (allele, key) => (
    <div
      key={key}
      className={`w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold border-2 
        ${allele.isDominant 
          ? 'bg-purple-100 border-purple-500 text-purple-700' 
          : 'bg-blue-100 border-blue-500 text-blue-700'}`}
    >
      {allele.symbol}
    </div>
  );
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Simulador de Herança Genética</h2>
        <p className="text-gray-600 mb-4">
          Visualize como as características genéticas são transmitidas dos pais para os filhos.
        </p>
        
        {/* Seleção de característica */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Selecione uma característica genética:</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
            {demoTraits.map(trait => (
              <button
                key={trait.id}
                className={`border rounded p-2 cursor-pointer hover:bg-gray-50 transition-colors text-left
                  ${selectedTrait.id === trait.id ? 'border-primary bg-primary/5' : 'border-gray-200'}`}
                onClick={() => setSelectedTrait(trait)}
              >
                <div className="font-medium">{trait.name || 'Característica'}</div>
                <div className="text-xs text-gray-500">
                  {trait.inheritance === 'dominant' 
                    ? 'Herança dominante' 
                    : trait.inheritance === 'recessive' 
                      ? 'Herança recessiva'
                      : trait.inheritance === 'codominant'
                        ? 'Herança codominante'
                        : 'Dominância incompleta'
                  }
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Informações sobre a característica selecionada */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            {selectedTrait.name}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 ml-2 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{selectedTrait.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardTitle>
          <CardDescription>
            {selectedTrait.inheritance === 'dominant' 
              ? 'Herança dominante' 
              : selectedTrait.inheritance === 'recessive' 
                ? 'Herança recessiva'
                : selectedTrait.inheritance === 'codominant'
                  ? 'Herança codominante'
                  : 'Dominância incompleta'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border rounded-md p-3 bg-purple-50">
              <div className="font-medium mb-1 text-sm">Alelo Dominante ({selectedTrait.dominantAllele})</div>
              <div className="text-xs">{selectedTrait.phenotypeDescription.dominant}</div>
            </div>
            {selectedTrait.inheritance === 'incomplete' && (
              <div className="border rounded-md p-3 bg-gradient-to-r from-purple-50 to-blue-50">
                <div className="font-medium mb-1 text-sm">Heterozigoto ({selectedTrait.dominantAllele}{selectedTrait.recessiveAllele})</div>
                <div className="text-xs">{selectedTrait.phenotypeDescription.heterozygous}</div>
              </div>
            )}
            <div className="border rounded-md p-3 bg-blue-50">
              <div className="font-medium mb-1 text-sm">Alelo Recessivo ({selectedTrait.recessiveAllele})</div>
              <div className="text-xs">{selectedTrait.phenotypeDescription.recessive}</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Controles de simulação */}
      <div className="flex items-center justify-center mb-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex space-x-4">
          <Button variant="default" size="sm" onClick={simulate} disabled={isSimulated}>
            <PlayCircle className="mr-1 h-4 w-4" />
            Simular Cruzamento
          </Button>
          <Button variant="outline" size="sm" onClick={reset}>
            <RefreshCw className="mr-1 h-4 w-4" />
            Gerar Novos Genótipos
          </Button>
        </div>
      </div>
      
      {/* Visualização dos genótipos e resultado */}
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-100 p-3 border-b">
          <h3 className="font-medium">Simulação de Cruzamento - {selectedTrait.name}</h3>
        </div>
        
        <div className="p-6">
          {/* Pais */}
          <div className="grid grid-cols-2 gap-8 w-full mb-10">
            {/* Pai (Sire) */}
            <div className="flex flex-col items-center">
              <div className="mb-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Pai
              </div>
              <div className="text-center mb-4">
                <div className="font-bold">{sire?.name || "Garanhão"}</div>
                {sireGenotype && (
                  <div className="text-sm text-gray-600 mt-1">
                    Genótipo: {sireGenotype.alleles[0].symbol}{sireGenotype.alleles[1].symbol}
                  </div>
                )}
              </div>
              
              {sireGenotype && (
                <div className="flex space-x-2">
                  {renderAllele(sireGenotype.alleles[0], 'sire-1')}
                  {renderAllele(sireGenotype.alleles[1], 'sire-2')}
                </div>
              )}
            </div>
            
            {/* Mãe (Dam) */}
            <div className="flex flex-col items-center">
              <div className="mb-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm font-medium">
                Mãe
              </div>
              <div className="text-center mb-4">
                <div className="font-bold">{dam?.name || "Égua"}</div>
                {damGenotype && (
                  <div className="text-sm text-gray-600 mt-1">
                    Genótipo: {damGenotype.alleles[0].symbol}{damGenotype.alleles[1].symbol}
                  </div>
                )}
              </div>
              
              {damGenotype && (
                <div className="flex space-x-2">
                  {renderAllele(damGenotype.alleles[0], 'dam-1')}
                  {renderAllele(damGenotype.alleles[1], 'dam-2')}
                </div>
              )}
            </div>
          </div>
          
          {/* Separador visual */}
          <div className="relative w-full flex justify-center my-6">
            <div className="w-32 h-0.5 bg-gray-300" />
          </div>
          
          {/* Resultado - Potro */}
          <div className="flex flex-col items-center">
            <div className="mb-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
              Potro (Resultado do Cruzamento)
            </div>
            
            {!isSimulated ? (
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Clique em "Simular Cruzamento" para visualizar o resultado</p>
              </div>
            ) : offspringGenotype && (
              <div className="text-center">
                <div className="font-bold mb-2">Genótipo Resultante</div>
                <div className="flex space-x-2 justify-center mb-4">
                  {renderAllele(offspringGenotype.alleles[0], 'offspring-1')}
                  {renderAllele(offspringGenotype.alleles[1], 'offspring-2')}
                </div>
                <div className="text-sm">
                  <span className="font-medium">Genótipo:</span> {offspringGenotype.alleles[0].symbol}{offspringGenotype.alleles[1].symbol}
                </div>
                <div className="mt-2 text-sm">
                  <span className="font-medium">Fenótipo:</span> {determinePhenotype(offspringGenotype)}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Informações educativas */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-lg font-medium mb-2">Sobre a Herança Genética</h2>
        <p className="text-sm text-gray-700 mb-4">
          Os cavalos, como todos os organismos, herdam características genéticas de seus pais. Cada característica 
          é controlada por um ou mais genes, e cada gene pode ter diferentes variantes chamadas alelos.
        </p>
        
        <h3 className="text-md font-medium mb-1">Tipos de Herança</h3>
        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
          <li><span className="font-medium">Dominante:</span> O alelo dominante sempre se expressa, mesmo na presença de um alelo recessivo.</li>
          <li><span className="font-medium">Recessiva:</span> O alelo recessivo só se expressa quando dois alelos recessivos estão presentes.</li>
          <li><span className="font-medium">Codominante:</span> Ambos os alelos se expressam simultaneamente no fenótipo.</li>
          <li><span className="font-medium">Dominância incompleta:</span> Os heterozigotos têm um fenótipo intermediário entre os dois homozigotos.</li>
        </ul>
      </div>
    </div>
  );
};

export default TraitInheritanceAnimation;