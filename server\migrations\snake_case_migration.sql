-- Zero-downtime migration for snake_case column naming consistency
-- Onda 1: Create compatibility views (24h duration)

BEGIN;

-- Check if manejos table has correct snake_case columns
DO $$
BEGIN
    -- Verify current schema state
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'manejos' AND column_name = 'cavalo_id'
    ) THEN
        RAISE EXCEPTION 'Expected cavalo_id column not found in manejos table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'manejos' AND column_name = 'user_id'
    ) THEN
        RAISE EXCEPTION 'Expected user_id column not found in manejos table';
    END IF;
    
    RAISE NOTICE 'Manejos table already has correct snake_case columns';
END $$;

-- Create compatibility view for any legacy code that might reference old names
CREATE OR REPLACE VIEW manejos_legacy AS
SELECT 
    id,
    cavalo_id AS "horseId",
    tipo,
    data_execucao,
    descricao,
    observacoes,
    responsavel,
    custo,
    user_id AS "userId",
    created_at,
    status,
    -- Also include original column names for safety
    cavalo_id,
    user_id
FROM manejos;

-- Add comment to track migration
COMMENT ON VIEW manejos_legacy IS 'Compatibility view for manejos table - created for zero-downtime migration on 2025-06-13';

-- Grant appropriate permissions
GRANT SELECT ON manejos_legacy TO PUBLIC;

COMMIT;

-- Instructions for rollback if needed:
-- DROP VIEW IF EXISTS manejos_legacy;