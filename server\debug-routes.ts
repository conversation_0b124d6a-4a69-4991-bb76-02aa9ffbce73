/**
 * Rotas de debugging e diagnóstico para identificar Error 500s nas abas
 * Sistema completo de monitoramento e análise de erros
 */

import express from 'express';
import { pool } from './db';
import { logger, getError500Report } from './logger';
import { safeSqlQuery } from './error-tracker';

const router = express.Router();

// Diagnóstico completo do sistema para identificar Error 500s
router.get('/system-health', async (req, res) => {
  try {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      system: 'EquiGestor AI - System Health Check',
      errors: getError500Report(),
      database: await checkDatabase(),
      endpoints: await checkCriticalEndpoints(),
      tables: await checkTableStructure(),
      permissions: await checkPermissions()
    };

    res.json(diagnostics);
  } catch (error: any) {
    logger.error('Error in system health check', { error: error.message, stack: error.stack });
    res.status(500).json({
      error: 'Failed to generate system health report',
      message: error.message
    });
  }
});

// Verificar conexão e estrutura do banco de dados
async function checkDatabase() {
  try {
    const checks = [];
    
    // Test basic connection
    const connectionTest = await safeSqlQuery(pool, 'SELECT NOW() as current_time', [], 'health-check');
    checks.push({
      test: 'Database Connection',
      status: 'OK',
      details: `Connected at ${connectionTest.rows[0].current_time}`
    });

    // Check critical tables
    const tables = ['cavalos', 'users', 'manejos', 'nutricao', 'morfologia', 'genealogia', 'pelagens'];
    for (const table of tables) {
      try {
        const result = await safeSqlQuery(pool, `SELECT COUNT(*) as count FROM ${table}`, [], `table-check-${table}`);
        checks.push({
          test: `Table ${table}`,
          status: 'OK',
          details: `${result.rows[0].count} records`
        });
      } catch (error: any) {
        checks.push({
          test: `Table ${table}`,
          status: 'ERROR',
          details: error.message
        });
      }
    }

    return { status: 'OK', checks };
  } catch (error: any) {
    return { 
      status: 'ERROR', 
      error: error.message,
      checks: []
    };
  }
}

// Verificar endpoints críticos que podem estar causando Error 500
async function checkCriticalEndpoints() {
  const endpoints = [
    { name: 'Cavalos List', path: '/api/cavalos', method: 'GET' },
    { name: 'Nutrição', path: '/api/nutricao', method: 'GET' },
    { name: 'Morfologia', path: '/api/morfologia', method: 'GET' },
    { name: 'Manejos', path: '/api/manejos', method: 'GET' },
    { name: 'Genealogia', path: '/api/genealogia', method: 'GET' },
    { name: 'Pelagens', path: '/api/pelagens', method: 'GET' }
  ];

  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      // Simulate basic endpoint check by validating SQL queries
      if (endpoint.name === 'Cavalos List') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM cavalos WHERE "user_id" = $1', [1], 'endpoint-check-cavalos');
      } else if (endpoint.name === 'Nutrição') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM nutricao WHERE "user_id" = $1', [1], 'endpoint-check-nutricao');
      } else if (endpoint.name === 'Morfologia') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM morfologia WHERE "user_id" = $1', [1], 'endpoint-check-morfologia');
      } else if (endpoint.name === 'Manejos') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM manejos WHERE "user_id" = $1', [1], 'endpoint-check-manejos');
      } else if (endpoint.name === 'Genealogia') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM genealogia WHERE "user_id" = $1', [1], 'endpoint-check-genealogia');
      } else if (endpoint.name === 'Pelagens') {
        await safeSqlQuery(pool, 'SELECT COUNT(*) FROM pelagens', [], 'endpoint-check-pelagens');
      }
      
      results.push({
        endpoint: endpoint.name,
        status: 'OK',
        path: endpoint.path,
        method: endpoint.method
      });
    } catch (error: any) {
      results.push({
        endpoint: endpoint.name,
        status: 'ERROR',
        path: endpoint.path,
        method: endpoint.method,
        error: error.message,
        code: error.code
      });
    }
  }

  return results;
}

// Verificar estrutura das tabelas para identificar problemas de schema
async function checkTableStructure() {
  try {
    const tableChecks = [];
    
    // Check cavalos table structure
    const cavalosColumns = await safeSqlQuery(pool, `
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'cavalos' 
      ORDER BY ordinal_position
    `, [], 'schema-check-cavalos');
    
    tableChecks.push({
      table: 'cavalos',
      columnCount: cavalosColumns.rows.length,
      hasUserId: cavalosColumns.rows.some(col => col.column_name === 'user_id'),
      hasPelagemId: cavalosColumns.rows.some(col => col.column_name === 'pelagemId'),
      criticalColumns: cavalosColumns.rows
        .filter(col => ['id', 'name', 'user_id', 'pelagemId'].includes(col.column_name))
        .map(col => `${col.column_name}:${col.data_type}`)
    });

    // Check pelagens table
    const pelagensColumns = await safeSqlQuery(pool, `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'pelagens'
    `, [], 'schema-check-pelagens');
    
    tableChecks.push({
      table: 'pelagens',
      exists: pelagensColumns.rows.length > 0,
      columns: pelagensColumns.rows.map(col => `${col.column_name}:${col.data_type}`)
    });

    return tableChecks;
  } catch (error: any) {
    return { error: error.message };
  }
}

// Verificar permissões e configurações
async function checkPermissions() {
  try {
    const checks = [];
    
    // Test user access
    const userTest = await safeSqlQuery(pool, 'SELECT id, username FROM users LIMIT 1', [], 'permission-check-users');
    checks.push({
      test: 'User Access',
      status: userTest.rows.length > 0 ? 'OK' : 'WARNING',
      details: `Found ${userTest.rows.length} users`
    });

    // Test data access with user_id filter
    const dataAccess = await safeSqlQuery(pool, 'SELECT COUNT(*) as count FROM cavalos WHERE "user_id" = $1', [1], 'permission-check-data');
    checks.push({
      test: 'Data Access (user_id=1)',
      status: 'OK',
      details: `Can access ${dataAccess.rows[0].count} cavalos records`
    });

    return checks;
  } catch (error: any) {
    return { error: error.message };
  }
}

// Endpoint para gerar relatório de Error 500 específico
router.get('/error-500-report', (req, res) => {
  try {
    const report = getError500Report();
    res.json({
      timestamp: new Date().toISOString(),
      system: 'EquiGestor AI - Error 500 Tracker',
      ...report,
      recommendations: generateRecommendations(report)
    });
  } catch (error: any) {
    res.status(500).json({
      error: 'Failed to generate Error 500 report',
      message: error.message
    });
  }
});

// Gerar recomendações baseadas nos erros encontrados
function generateRecommendations(report: any) {
  const recommendations = [];
  
  if (report.totalOccurrences > 0) {
    recommendations.push('Sistema detectou Error 500s. Verifique os endpoints mais afetados.');
    
    if (report.errors?.length > 0) {
      const topError = report.errors[0];
      recommendations.push(`Endpoint mais problemático: ${topError.endpoint} (${topError.occurrences} ocorrências)`);
      
      if (topError.endpoint.includes('cavalos')) {
        recommendations.push('Problema na aba de Cavalos - verifique queries SQL e schema da tabela cavalos');
      }
      if (topError.endpoint.includes('nutricao')) {
        recommendations.push('Problema na aba de Nutrição - verifique validação de dados e foreign keys');
      }
      if (topError.endpoint.includes('morfologia')) {
        recommendations.push('Problema na aba de Morfologia - verifique schema e relacionamentos');
      }
      if (topError.endpoint.includes('manejos')) {
        recommendations.push('Problema na aba de Manejos - verifique autenticação e permissões');
      }
    }
  } else {
    recommendations.push('Nenhum Error 500 detectado recentemente. Sistema operando normalmente.');
  }
  
  return recommendations;
}

// Teste específico de endpoints da aplicação
router.post('/test-endpoint', async (req, res) => {
  const { endpoint, method = 'GET', user_id = 1 } = req.body;
  
  try {
    logger.info(`Testing endpoint: ${method} ${endpoint}`, { user_id });
    
    // Simulate endpoint test based on the path
    let testResult;
    
    if (endpoint.includes('/api/cavalos')) {
      testResult = await safeSqlQuery(pool, 'SELECT COUNT(*) as count FROM cavalos WHERE "user_id" = $1', [user_id], 'endpoint-test');
    } else if (endpoint.includes('/api/nutricao')) {
      testResult = await safeSqlQuery(pool, 'SELECT COUNT(*) as count FROM nutricao WHERE "user_id" = $1', [user_id], 'endpoint-test');
    } else if (endpoint.includes('/api/morfologia')) {
      testResult = await safeSqlQuery(pool, 'SELECT COUNT(*) as count FROM morfologia WHERE "user_id" = $1', [user_id], 'endpoint-test');
    } else if (endpoint.includes('/api/manejos')) {
      testResult = await safeSqlQuery(pool, 'SELECT COUNT(*) as count FROM manejos WHERE "user_id" = $1', [user_id], 'endpoint-test');
    } else {
      testResult = { rows: [{ count: 0 }] };
    }
    
    res.json({
      endpoint,
      method,
      status: 'SUCCESS',
      testResult: testResult.rows[0],
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    logger.error(`Endpoint test failed: ${endpoint}`, { error: error.message, user_id });
    
    res.json({
      endpoint,
      method,
      status: 'ERROR',
      error: {
        message: error.message,
        code: error.code,
        detail: error.detail
      },
      timestamp: new Date().toISOString()
    });
  }
});

export default router;