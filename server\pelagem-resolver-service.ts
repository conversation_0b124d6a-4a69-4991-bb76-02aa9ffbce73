/**
 * Serviço de resolução de pelagens com normalização e criação automática
 * Implementa o sistema de slug normalizado conforme especificação
 */

import { pool } from './db';

/**
 * Normaliza string para slug (lowercase + sem acentuação)
 */
function normalizeToSlug(input: string): string {
  if (!input) return '';
  
  return input
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
    .trim()
    .replace(/\s+/g, '_'); // Substitui espaços por underscore
}

/**
 * Resolve pelagem_id a partir do nome
 * Se não encontrar, cria nova pelagem automaticamente
 */
export async function resolvePelagemId(nome: string, fonte: string = 'ABCCC Import'): Promise<number> {
  if (!nome || nome.trim() === '') {
    throw new Error('Nome da pelagem não pode ser vazio');
  }

  const slug = normalizeToSlug(nome);
  
  try {
    // Primeiro, tentar encontrar pelagem existente pelo nome
    const existingResult = await pool.query(
      'SELECT id FROM pelagens WHERE nome = $1 LIMIT 1',
      [nome.trim()]
    );

    if (existingResult.rows.length > 0) {
      return existingResult.rows[0].id;
    }

    // Se não encontrou, criar nova pelagem
    const insertResult = await pool.query(
      `INSERT INTO pelagens (nome, descricao, fonte, created_at) 
       VALUES ($1, $2, $3, CURRENT_TIMESTAMP) 
       RETURNING id`,
      [
        nome.trim(),
        `Pelagem criada automaticamente via ${fonte}`,
        fonte
      ]
    );

    console.log(`[PELAGEM-RESOLVER] Nova pelagem criada: ${nome} (id: ${insertResult.rows[0].id})`);
    
    return insertResult.rows[0].id;
    
  } catch (error) {
    console.error('[PELAGEM-RESOLVER] Erro ao resolver pelagem:', error);
    
    // Se houve erro de constraint (nome duplicado), tentar buscar novamente
    if (error.code === '23505') { // unique_violation
      const retryResult = await pool.query(
        'SELECT id FROM pelagens WHERE nome = $1 LIMIT 1',
        [nome]
      );
      
      if (retryResult.rows.length > 0) {
        return retryResult.rows[0].id;
      }
    }
    
    throw new Error(`Erro ao resolver pelagem "${nome}": ${error.message}`);
  }
}

/**
 * Busca pelagens para autocomplete
 */
export async function searchPelagens(search: string = '', limit: number = 20): Promise<Array<{id: number, nome: string}>> {
  try {
    let query = 'SELECT id, nome FROM pelagens';
    let params: any[] = [];
    
    if (search.trim()) {
      query += ' WHERE nome ILIKE $1';
      params = [`%${search}%`];
    }
    
    query += ' ORDER BY nome LIMIT $' + (params.length + 1);
    params.push(limit);
    
    const result = await pool.query(query, params);
    return result.rows;
    
  } catch (error) {
    console.error('[PELAGEM-RESOLVER] Erro ao buscar pelagens:', error);
    return [];
  }
}

/**
 * Estatísticas das pelagens por fonte
 */
export async function getPelagemStats(): Promise<Record<string, number>> {
  try {
    const result = await pool.query(
      'SELECT fonte, COUNT(*) as count FROM pelagens GROUP BY fonte ORDER BY fonte'
    );
    
    const stats: Record<string, number> = {};
    result.rows.forEach(row => {
      stats[row.fonte] = parseInt(row.count);
    });
    
    return stats;
    
  } catch (error) {
    console.error('[PELAGEM-RESOLVER] Erro ao buscar estatísticas:', error);
    return {};
  }
}