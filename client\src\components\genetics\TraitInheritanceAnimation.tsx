import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PlayCircle, 
  PauseCircle, 
  RefreshCw,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';

// Tipos para as características genéticas
export interface GeneticTrait {
  id: string;
  name: string;
  description: string;
  dominantAllele: string;
  recessiveAllele: string;
  phenotypeDescription: {
    dominant: string;
    recessive: string;
    heterozygous?: string;
  };
  inheritance: 'dominant' | 'recessive' | 'codominant' | 'incomplete';
  categories: string[];
}

// Tipos para os alelos que compõem um genótipo
export interface Allele {
  symbol: string;
  isDominant: boolean;
}

// Tipo para o genótipo de um cavalo para uma característica específica
export interface Genotype {
  trait: GeneticTrait;
  alleles: [Allele, Allele]; // Um par de alelos
}

interface TraitInheritanceAnimationProps {
  sire?: { id: number; name: string; genotypes?: Genotype[] };
  dam?: { id: number; name: string; genotypes?: Genotype[] };
  offspring?: { id: number; name: string; genotypes?: Genotype[] };
  traits?: GeneticTrait[];
  onSelectTrait?: (trait: GeneticTrait) => void;
}

// Características genéticas fictícias para demonstração se não forem fornecidas
const demoTraits: GeneticTrait[] = [
  {
    id: 'coat-color',
    name: 'Cor da Pelagem',
    description: 'Cor base da pelagem do cavalo, determinada por múltiplos genes',
    dominantAllele: 'B',
    recessiveAllele: 'b',
    phenotypeDescription: {
      dominant: 'Preto (BB)',
      heterozygous: 'Preto (Bb)',
      recessive: 'Castanho (bb)',
    },
    inheritance: 'dominant',
    categories: ['appearance', 'color']
  },
  {
    id: 'dun-dilution',
    name: 'Diluição Dun',
    description: 'Gene que causa padrão primitivo com listras e traços escuros',
    dominantAllele: 'D',
    recessiveAllele: 'd',
    phenotypeDescription: {
      dominant: 'Com diluição Dun (DD)',
      heterozygous: 'Com diluição Dun (Dd)',
      recessive: 'Sem diluição Dun (dd)',
    },
    inheritance: 'dominant',
    categories: ['appearance', 'color']
  },
  {
    id: 'cream-dilution',
    name: 'Diluição Creme',
    description: 'Diluição que afeta os pigmentos vermelhos e pretos',
    dominantAllele: 'C',
    recessiveAllele: 'c',
    phenotypeDescription: {
      dominant: 'Sem diluição (CC)',
      heterozygous: 'Palomino/Buckskin (Cc)',
      recessive: 'Cremelo/Perlino (cc)',
    },
    inheritance: 'incomplete',
    categories: ['appearance', 'color']
  },
  {
    id: 'gait-type',
    name: 'Tipo de Andamento',
    description: 'Capacidade para andamentos laterais adicionais',
    dominantAllele: 'G',
    recessiveAllele: 'g',
    phenotypeDescription: {
      dominant: 'Andamentos extras (GG)',
      heterozygous: 'Predisposição a andamentos extras (Gg)',
      recessive: 'Andamentos normais (gg)',
    },
    inheritance: 'incomplete',
    categories: ['performance', 'movement']
  },
  {
    id: 'genetic-disease',
    name: 'HYPP',
    description: 'Paralisia Periódica Hipercalêmica - doença muscular hereditária',
    dominantAllele: 'H',
    recessiveAllele: 'h',
    phenotypeDescription: {
      dominant: 'HYPP positivo - severo (HH)',
      heterozygous: 'HYPP positivo - leve (Hh)',
      recessive: 'HYPP negativo (hh)',
    },
    inheritance: 'dominant',
    categories: ['health', 'disease']
  }
];

// Função para gerar alelos aleatórios para uma característica (para demonstração)
const generateRandomAlleles = (trait: GeneticTrait): [Allele, Allele] => {
  const options = [
    { symbol: trait.dominantAllele, isDominant: true },
    { symbol: trait.recessiveAllele, isDominant: false }
  ];
  
  return [
    options[Math.floor(Math.random() * 2)],
    options[Math.floor(Math.random() * 2)]
  ];
};

// Função para determinar o fenótipo com base no genótipo
const determinePhenotype = (genotype: Genotype): string => {
  const isDominant = genotype.alleles[0].isDominant;
  const isRecessivePair = genotype.alleles[0].isDominant === false && genotype.alleles[1].isDominant === false;
  const isHeterozygous = genotype.alleles[0].isDominant !== genotype.alleles[1].isDominant;
  
  if (isRecessivePair) {
    return genotype.trait.phenotypeDescription.recessive;
  } else if (isHeterozygous && genotype.trait.inheritance === 'incomplete') {
    return genotype.trait.phenotypeDescription.heterozygous || genotype.trait.phenotypeDescription.dominant;
  } else {
    return genotype.trait.phenotypeDescription.dominant;
  }
};

// Função para gerar genótipos aleatórios para um cavalo (para demonstração)
const generateRandomGenotypes = (traits: GeneticTrait[]): Genotype[] => {
  return traits.map(trait => ({
    trait,
    alleles: generateRandomAlleles(trait)
  }));
};

// Função para determinar o resultado da herança genética
const inheritAlleles = (sireGenotype: Genotype, damGenotype: Genotype): Genotype => {
  // Escolha aleatória de um alelo de cada genitor
  const sireAllele = sireGenotype.alleles[Math.floor(Math.random() * 2)];
  const damAllele = damGenotype.alleles[Math.floor(Math.random() * 2)];
  
  return {
    trait: sireGenotype.trait,
    alleles: [sireAllele, damAllele]
  };
};

const TraitInheritanceAnimation: React.FC<TraitInheritanceAnimationProps> = ({ 
  sire,
  dam,
  offspring,
  traits = demoTraits,
  onSelectTrait
}) => {
  // Estado para a característica genética selecionada
  const [selectedTrait, setSelectedTrait] = useState<GeneticTrait>(traits[0]);
  
  // Estados para os genótipos
  const [sireGenotype, setSireGenotype] = useState<Genotype | null>(null);
  const [damGenotype, setDamGenotype] = useState<Genotype | null>(null);
  const [offspringGenotype, setOffspringGenotype] = useState<Genotype | null>(null);
  
  // Estado para a categoria de características filtrada
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  
  // Estados para controlar a animação
  const [isAnimating, setIsAnimating] = useState(false);
  const [step, setStep] = useState(0);
  const [speed, setSpeed] = useState(1.0);
  const animationTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Extrair todas as categorias únicas dos traços
  const allCategories = ["all", ...Array.from(new Set(traits.flatMap(trait => trait.categories)))];
  
  // Inicialização e reset dos genótipos quando muda o traço selecionado
  useEffect(() => {
    if (!selectedTrait) return;
    
    // Se os cavalos têm genótipos já definidos, use-os
    if (sire?.genotypes) {
      const existingGenotype = sire.genotypes.find(g => g.trait.id === selectedTrait.id);
      setSireGenotype(existingGenotype || {
        trait: selectedTrait,
        alleles: generateRandomAlleles(selectedTrait)
      });
    } else {
      setSireGenotype({
        trait: selectedTrait,
        alleles: generateRandomAlleles(selectedTrait)
      });
    }
    
    if (dam?.genotypes) {
      const existingGenotype = dam.genotypes.find(g => g.trait.id === selectedTrait.id);
      setDamGenotype(existingGenotype || {
        trait: selectedTrait,
        alleles: generateRandomAlleles(selectedTrait)
      });
    } else {
      setDamGenotype({
        trait: selectedTrait,
        alleles: generateRandomAlleles(selectedTrait)
      });
    }
    
    // Reset de offspring
    setOffspringGenotype(null);
    
    // Reset da animação
    setStep(0);
    setIsAnimating(false);
    
    if (onSelectTrait) {
      onSelectTrait(selectedTrait);
    }
  }, [selectedTrait, sire, dam, onSelectTrait]);
  
  // Controle da animação
  useEffect(() => {
    if (isAnimating && step < 3) {
      animationTimer.current = setTimeout(() => {
        setStep(prevStep => {
          const nextStep = prevStep + 1;
          // Finalização da animação
          if (nextStep === 3) {
            if (sireGenotype && damGenotype) {
              setOffspringGenotype(inheritAlleles(sireGenotype, damGenotype));
            }
            setIsAnimating(false);
          }
          return nextStep;
        });
      }, 1500 / speed); // Velocidade da animação
    }
    
    return () => {
      if (animationTimer.current) {
        clearTimeout(animationTimer.current);
      }
    };
  }, [isAnimating, step, sireGenotype, damGenotype, speed]);
  
  // Iniciar a animação
  const startAnimation = () => {
    setStep(0);
    setOffspringGenotype(null);
    setIsAnimating(true);
  };
  
  // Pausar a animação
  const pauseAnimation = () => {
    setIsAnimating(false);
  };
  
  // Resetar a animação
  const resetAnimation = () => {
    setStep(0);
    setOffspringGenotype(null);
    setIsAnimating(false);
    
    // Gerar novos genótipos aleatórios para os pais
    setSireGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
    
    setDamGenotype({
      trait: selectedTrait,
      alleles: generateRandomAlleles(selectedTrait)
    });
  };
  
  // Renderizar alelos como elementos visuais
  const renderAllele = (allele: Allele, key: string, animate: boolean = false) => (
    <motion.div
      key={key}
      className={`w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold border-2 
        ${allele.isDominant 
          ? 'bg-purple-100 border-purple-500 text-purple-700' 
          : 'bg-blue-100 border-blue-500 text-blue-700'}`}
      initial={animate ? { scale: 0, opacity: 0 } : { scale: 1, opacity: 1 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ type: 'spring', duration: 0.8 }}
    >
      {allele.symbol}
    </motion.div>
  );
  
  // Função para filtrar as características pela categoria
  const filteredTraits = categoryFilter === 'all' 
    ? traits 
    : traits.filter(trait => trait.categories.includes(categoryFilter));
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Simulador de Herança Genética</h2>
        <p className="text-gray-600 mb-4">
          Visualize como as características genéticas são transmitidas dos pais para os filhos.
        </p>
        
        {/* Filtro por categoria */}
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Filtrar por categoria:</h3>
          <div className="flex flex-wrap gap-2">
            {allCategories.map(category => (
              <Badge 
                key={category}
                variant={categoryFilter === category ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setCategoryFilter(category)}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* Seleção de característica */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Selecione uma característica genética:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
            {filteredTraits.map(trait => (
              <div
                key={trait.id}
                className={`border rounded p-2 cursor-pointer hover:bg-gray-50 transition-colors
                  ${selectedTrait.id === trait.id ? 'border-primary bg-primary/5' : 'border-gray-200'}`}
                onClick={() => setSelectedTrait(trait)}
              >
                <div className="font-medium">{trait.name}</div>
                <div className="text-xs text-gray-500">{trait.inheritance === 'dominant' 
                  ? 'Herança dominante' 
                  : trait.inheritance === 'recessive' 
                    ? 'Herança recessiva'
                    : trait.inheritance === 'codominant'
                      ? 'Herança codominante'
                      : 'Dominância incompleta'
                }</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Informações sobre a característica selecionada */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            {selectedTrait.name}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 ml-2 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{selectedTrait.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardTitle>
          <CardDescription>
            {selectedTrait.inheritance === 'dominant' 
              ? 'Herança dominante' 
              : selectedTrait.inheritance === 'recessive' 
                ? 'Herança recessiva'
                : selectedTrait.inheritance === 'codominant'
                  ? 'Herança codominante'
                  : 'Dominância incompleta'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border rounded-md p-3 bg-purple-50">
              <div className="font-medium mb-1 text-sm">Alelo Dominante ({selectedTrait.dominantAllele})</div>
              <div className="text-xs">{selectedTrait.phenotypeDescription.dominant}</div>
            </div>
            {selectedTrait.inheritance === 'incomplete' && (
              <div className="border rounded-md p-3 bg-gradient-to-r from-purple-50 to-blue-50">
                <div className="font-medium mb-1 text-sm">Heterozigoto ({selectedTrait.dominantAllele}{selectedTrait.recessiveAllele})</div>
                <div className="text-xs">{selectedTrait.phenotypeDescription.heterozygous}</div>
              </div>
            )}
            <div className="border rounded-md p-3 bg-blue-50">
              <div className="font-medium mb-1 text-sm">Alelo Recessivo ({selectedTrait.recessiveAllele})</div>
              <div className="text-xs">{selectedTrait.phenotypeDescription.recessive}</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Controles de animação */}
      <div className="flex items-center justify-between mb-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex space-x-2">
          {isAnimating ? (
            <Button variant="outline" size="sm" onClick={pauseAnimation}>
              <PauseCircle className="mr-1 h-4 w-4" />
              Pausar
            </Button>
          ) : (
            <Button variant="default" size="sm" onClick={startAnimation}>
              <PlayCircle className="mr-1 h-4 w-4" />
              Iniciar
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={resetAnimation}>
            <RefreshCw className="mr-1 h-4 w-4" />
            Redefinir
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Velocidade:</span>
          <div className="w-32">
            <Slider
              value={[speed]}
              min={0.5}
              max={2}
              step={0.5}
              onValueChange={(values) => setSpeed(values[0])}
            />
          </div>
          <span className="text-sm font-medium">{speed}x</span>
        </div>
      </div>
      
      {/* Visualização da animação */}
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-100 p-3 border-b">
          <h3 className="font-medium">Herança de {selectedTrait.name}</h3>
        </div>
        
        <div className="p-6 flex flex-col items-center">
          {/* Pais */}
          <div className="grid grid-cols-2 gap-8 w-full mb-10">
            {/* Pai (Sire) */}
            <div className="flex flex-col items-center">
              <div className="mb-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Pai
              </div>
              <div className="text-center mb-4">
                <div className="font-bold">{sire?.name || "garanhao"}</div>
                {sireGenotype && (
                  <div className="text-sm text-gray-600">
                    Genótipo: {sireGenotype.alleles[0].symbol}{sireGenotype.alleles[1].symbol}
                  </div>
                )}
              </div>
              
              {sireGenotype && (
                <div className="flex space-x-2">
                  {renderAllele(sireGenotype.alleles[0], 'sire-1')}
                  {renderAllele(sireGenotype.alleles[1], 'sire-2')}
                </div>
              )}
            </div>
            
            {/* Mãe (Dam) */}
            <div className="flex flex-col items-center">
              <div className="mb-2 bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm font-medium">
                Mãe
              </div>
              <div className="text-center mb-4">
                <div className="font-bold">{dam?.name || "femea"}</div>
                {damGenotype && (
                  <div className="text-sm text-gray-600">
                    Genótipo: {damGenotype.alleles[0].symbol}{damGenotype.alleles[1].symbol}
                  </div>
                )}
              </div>
              
              {damGenotype && (
                <div className="flex space-x-2">
                  {renderAllele(damGenotype.alleles[0], 'dam-1')}
                  {renderAllele(damGenotype.alleles[1], 'dam-2')}
                </div>
              )}
            </div>
          </div>
          
          {/* Linhas conectoras e setas de transmissão */}
          <div className="relative w-full h-24 mb-6">
            {/* Linhas verticais dos pais */}
            <div className="absolute left-1/4 top-0 w-0.5 h-6 bg-gray-300" />
            <div className="absolute right-1/4 top-0 w-0.5 h-6 bg-gray-300" />
            
            {/* Linha horizontal conectando */}
            <div className="absolute left-1/4 top-6 right-1/4 h-0.5 bg-gray-300" />
            
            {/* Linha vertical para o filho */}
            <div className="absolute left-1/2 top-6 w-0.5 h-16 bg-gray-300" />
            
            {/* Animação de alelos sendo passados */}
            <AnimatePresence>
              {step >= 1 && sireGenotype && (
                <motion.div
                  className="absolute left-1/4 top-3"
                  initial={{ y: 0, x: 0 }}
                  animate={{ y: [0, 20, 60], x: [0, 100, 0] }}
                  transition={{ duration: 1, times: [0, 0.5, 1], ease: "easeInOut" }}
                >
                  {renderAllele(
                    sireGenotype.alleles[Math.floor(Math.random() * 2)], 
                    'sire-moving', 
                    true
                  )}
                </motion.div>
              )}
              
              {step >= 2 && damGenotype && (
                <motion.div
                  className="absolute right-1/4 top-3"
                  initial={{ y: 0, x: 0 }}
                  animate={{ y: [0, 20, 60], x: [0, -100, 0] }}
                  transition={{ duration: 1, times: [0, 0.5, 1], ease: "easeInOut" }}
                >
                  {renderAllele(
                    damGenotype.alleles[Math.floor(Math.random() * 2)], 
                    'dam-moving', 
                    true
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Filho (Offspring) */}
          <div className="flex flex-col items-center">
            <div className="mb-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
              Filho
            </div>
            <div className="text-center mb-4">
              <div className="font-bold">{offspring?.name || "Potro"}</div>
              {offspringGenotype && (
                <div className="text-sm text-gray-600">
                  Genótipo: {offspringGenotype.alleles[0].symbol}{offspringGenotype.alleles[1].symbol}
                </div>
              )}
            </div>
            
            {offspringGenotype ? (
              <div className="flex space-x-2">
                {renderAllele(offspringGenotype.alleles[0], 'offspring-1', true)}
                {renderAllele(offspringGenotype.alleles[1], 'offspring-2', true)}
              </div>
            ) : (
              <div className="w-24 h-10 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <span className="text-sm text-gray-400">Pendente</span>
              </div>
            )}
            
            {offspringGenotype && (
              <div className="mt-4 p-3 bg-gray-50 rounded-md">
                <h3 className="font-medium text-center">Fenótipo resultante:</h3>
                <div className="text-center mt-1">
                  {determinePhenotype(offspringGenotype)}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Explicação sobre probabilidades */}
      {sireGenotype && damGenotype && (
        <div className="mt-6 p-4 bg-yellow-50 rounded-md">
          <h3 className="font-medium mb-2">Probabilidades de Herança</h3>
          <p className="text-sm mb-3">
            Com base nos genótipos dos pais ({sireGenotype.alleles[0].symbol}{sireGenotype.alleles[1].symbol} x {damGenotype.alleles[0].symbol}{damGenotype.alleles[1].symbol}),
            as possíveis combinações e suas probabilidades são:
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {[
              { sire: sireGenotype.alleles[0], dam: damGenotype.alleles[0] },
              { sire: sireGenotype.alleles[0], dam: damGenotype.alleles[1] },
              { sire: sireGenotype.alleles[1], dam: damGenotype.alleles[0] },
              { sire: sireGenotype.alleles[1], dam: damGenotype.alleles[1] }
            ].map((combo, idx) => {
              const genotypeText = `${combo.sire.symbol}${combo.dam.symbol}`;
              const isDominantPresent = combo.sire.isDominant || combo.dam.isDominant;
              const isHomozygousRecessive = !combo.sire.isDominant && !combo.dam.isDominant;
              
              let phenotype;
              if (isHomozygousRecessive) {
                phenotype = selectedTrait.phenotypeDescription.recessive;
              } else if (combo.sire.isDominant !== combo.dam.isDominant && selectedTrait.inheritance === 'incomplete') {
                phenotype = selectedTrait.phenotypeDescription.heterozygous || selectedTrait.phenotypeDescription.dominant;
              } else {
                phenotype = selectedTrait.phenotypeDescription.dominant;
              }
              
              return (
                <div key={idx} className="border rounded p-2 text-sm">
                  <div className="font-bold">{genotypeText}</div>
                  <div className="text-xs text-gray-600">25% de chance</div>
                  <div className="text-xs mt-1">{phenotype}</div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default TraitInheritanceAnimation;