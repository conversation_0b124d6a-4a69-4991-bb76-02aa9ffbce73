@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Mobile-first responsive improvements */
  html {
    /* Better touch target sizing */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-text-size-adjust: 100%;
  }

  /* Ensure minimum touch target size on mobile */
  button, 
  [role="button"], 
  input[type="button"], 
  input[type="submit"], 
  input[type="reset"], 
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile input handling */
  input, 
  textarea, 
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  @media (max-width: 640px) {
    input, 
    textarea, 
    select {
      font-size: 16px !important;
    }
  }
}

/* Animações para o gráfico de radar morfológico */
/* Mobile-first utility classes */
@layer utilities {
  /* Touch-friendly spacing */
  .touch-spacing {
    @apply p-4 gap-4;
  }

  /* Mobile-optimized text sizes */
  .mobile-text-xs { font-size: 12px; }
  .mobile-text-sm { font-size: 14px; }
  .mobile-text-base { font-size: 16px; }
  .mobile-text-lg { font-size: 18px; }

  /* Container responsive padding */
  .container-mobile {
    @apply px-3 sm:px-4 md:px-6 lg:px-8;
  }

  /* Safe scrolling areas */
  .safe-scroll {
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-optimized grid gaps */
  .grid-gap-mobile {
    @apply gap-3 sm:gap-4 md:gap-6;
  }

  /* Responsive card padding */
  .card-mobile {
    @apply p-3 sm:p-4 md:p-6;
  }

  /* Touch-friendly buttons */
  .btn-mobile {
    @apply min-h-[48px] px-4 py-3 text-sm;
  }

  /* Hide on mobile utilities */
  .hidden-mobile {
    @apply hidden sm:block;
  }

  .show-mobile {
    @apply block sm:hidden;
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0); opacity: 0; }
  80% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes drawPolygon {
  0% { 
    stroke-dashoffset: 1000; 
    opacity: 0;
  }
  70% { opacity: 0.7; }
  100% { 
    stroke-dashoffset: 0; 
    opacity: 1;
  }
}

@keyframes expandOutward {
  0% { 
    transform: scale(0.5);
    opacity: 0; 
  }
  100% { 
    transform: scale(1);
    opacity: 1; 
  }
}

@keyframes pulsePoint {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.animate-radar-circles {
  animation: expandOutward 0.8s ease-out forwards;
}

.animate-radar-lines {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-radar-polygon {
  animation: drawPolygon 1.2s ease-out forwards;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
}

.animate-radar-points {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-radar-labels {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-pulse {
  animation: pulsePoint 1.5s infinite;
}

/* Estilos aprimorados para dispositivos móveis */
@media (max-width: 768px) {
  /* Prevenir scroll quando o menu estiver aberto */
  .sidebar-mobile-open body {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }
  
  /* Melhorar visibilidade e interatividade do botão de menu */
  button[aria-label="Menu principal"] {
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    transition: background-color 0.2s;
  }
  
  button[aria-label="Menu principal"]:active {
    background-color: #134282;
  }
  
  /* Melhorar visibilidade do menu mobile */
  .mobile-sidebar {
    box-shadow: 0 0 20px rgba(0,0,0,0.5);
  }
  
  /* Melhoria na animação e interatividade do overlay */
  .mobile-overlay {
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
  }
}