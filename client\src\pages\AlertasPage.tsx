import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Link } from "wouter";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  AlertTriangle, 
  Bell, 
  Clock, 
  CheckCircle, 
  Calendar, 
  CalendarDays, 
  AlertCircle, 
  ChevronRight, 
  Filter 
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Manejo, Cavalo } from "../../../shared/schema";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

/**
 * AlertasPage
 * 
 * Página dedicada a exibir alertas do sistema, incluindo manejos vencidos ou próximos do vencimento,
 * eventos importantes e notificações relacionadas à saúde e bem-estar dos animais.
 */
const AlertasPage = () => {
  const [filtroStatus, setFiltroStatus] = useState<string>("todos");
  const [filtroPrioridade, setFiltroPrioridade] = useState<string>("todos");
  const [filtroTipo, setFiltroTipo] = useState<string>("todos");
  const [mostrarConcluidos, setMostrarConcluidos] = useState<boolean>(false);
  
  // Consultar dados de manejos
  const { data: manejos = [], isLoading: loadingManejos } = useQuery({
    queryKey: ['/api/manejos'],
    queryFn: async () => {
      try {
        const response = await apiRequest<Manejo[]>('/api/manejos');
        return response || [];
      } catch (error) {
        console.error("Erro ao buscar manejos:", error);
        return [];
      }
    }
  });
  
  // Consultar cavalos para mapeamento de nomes
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('/api/cavalos') || [];
      } catch (error) {
        console.error("Erro ao buscar cavalos:", error);
        return [];
      }
    }
  });
  
  // Mapa de ID para nome do cavalo
  const cavaloNameMap = new Map();
  cavalos.forEach(cavalo => {
    cavaloNameMap.set(cavalo.id, cavalo.name);
  });
  
  // Processar manejos para calcular status de vencimento
  const hoje = new Date();
  const manejosProcessados = manejos.map(manejo => {
    const dataVencimento = manejo.dataVencimento ? new Date(manejo.dataVencimento) : null;
    const vencido = dataVencimento ? dataVencimento < hoje : false;
    
    // Calcular dias para vencimento
    let diasParaVencimento = null;
    if (dataVencimento) {
      const diffTime = Math.abs(dataVencimento.getTime() - hoje.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      diasParaVencimento = vencido ? -diffDays : diffDays;
    }
    
    return {
      ...manejo,
      vencido,
      diasParaVencimento,
      proximoDoVencimento: !vencido && diasParaVencimento !== null && diasParaVencimento <= 7
    };
  });
  
  // Filtrar manejos com base nas seleções
  const manejosFiltrados = manejosProcessados.filter(manejo => {
    // Filtro por status (vencido, próximo, todos)
    if (filtroStatus === "vencidos" && !manejo.vencido) return false;
    if (filtroStatus === "proximos" && !manejo.proximoDoVencimento) return false;
    if (filtroStatus === "semData" && manejo.dataVencimento) return false;
    
    // Filtro para exibir ou não concluídos
    if (!mostrarConcluidos && manejo.status === "concluido") return false;
    
    // Filtro por prioridade
    if (filtroPrioridade !== "todos" && manejo.prioridade !== filtroPrioridade) return false;
    
    // Filtro por tipo
    if (filtroTipo !== "todos" && manejo.tipo !== filtroTipo) return false;
    
    return true;
  });
  
  // Ordenar por criticidade: vencidos primeiro (mais antigos), depois próximos do vencimento (mais próximos)
  const manejosOrdenados = [...manejosFiltrados].sort((a, b) => {
    // Se ambos estão vencidos, mostrar o mais atrasado primeiro
    if (a.vencido && b.vencido) {
      return (a.diasParaVencimento || 0) - (b.diasParaVencimento || 0);
    }
    
    // Vencidos sempre vêm primeiro
    if (a.vencido && !b.vencido) return -1;
    if (!a.vencido && b.vencido) return 1;
    
    // Se ambos estão próximos, mostrar o mais próximo primeiro
    if (a.proximoDoVencimento && b.proximoDoVencimento) {
      return (a.diasParaVencimento || 999) - (b.diasParaVencimento || 999);
    }
    
    // Próximos vêm depois de vencidos
    if (a.proximoDoVencimento && !b.proximoDoVencimento) return -1;
    if (!a.proximoDoVencimento && b.proximoDoVencimento) return 1;
    
    // Por último, ordenar por data de manejo (mais recentes primeiro)
    return new Date(b.data).getTime() - new Date(a.data).getTime();
  });
  
  // Formatação de data
  const formatarData = (dataStr: string) => {
    const data = new Date(dataStr);
    return new Intl.DateTimeFormat('pt-BR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    }).format(data);
  };
  
  // Função para formatar a exibição de tempo restante
  const formatTempoRestante = (dias: number | null) => {
    if (dias === null) return 'Sem prazo';
    
    if (dias < 0) {
      const diasAtraso = Math.abs(dias);
      return `Atrasado em ${diasAtraso} ${diasAtraso === 1 ? 'dia' : 'dias'}`;
    } else if (dias === 0) {
      return 'Vence hoje';
    } else {
      return `Em ${dias} ${dias === 1 ? 'dia' : 'dias'}`;
    }
  };
  
  // Obter classes de badge com base no status
  const getStatusBadgeClass = (manejo: any) => {
    if (manejo.status === 'concluido') {
      return 'bg-green-100 text-green-800 border-green-200';
    }
    
    if (manejo.vencido) {
      return 'bg-red-100 text-red-800 border-red-200';
    }
    
    if (manejo.proximoDoVencimento) {
      return 'bg-amber-100 text-amber-800 border-amber-200';
    }
    
    return 'bg-blue-100 text-blue-800 border-blue-200';
  };
  
  // Obter nome formatado para o tipo de manejo
  const getTipoManejoNome = (tipo: string) => {
    const tiposManejo: Record<string, string> = {
      'veterinary': 'Veterinário',
      'farrier': 'Ferrageamento',
      'vaccination': 'Vacinação',
      'deworming': 'Vermifugação',
      'dental': 'Odontológico',
      'training': 'Treinamento',
      'other': 'Outro'
    };
    
    return tiposManejo[tipo] || tipo.charAt(0).toUpperCase() + tipo.slice(1);
  };
  
  // Agrupamento de alertas por categoria
  const alertasVencidos = manejosOrdenados.filter(m => m.vencido);
  const alertasProximos = manejosOrdenados.filter(m => !m.vencido && m.proximoDoVencimento);
  const alertasSemData = manejosOrdenados.filter(m => !m.dataVencimento);
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-blue-700 tracking-tight">Alertas</h1>
            <p className="mt-1 text-gray-600">
              Monitore prazos, manejos vencidos e tarefas pendentes
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Link href="/manejos">
              <Button>
                <Calendar className="mr-2 h-4 w-4" />
                Gerenciar Manejos
              </Button>
            </Link>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <Tabs defaultValue="todos" onValueChange={setFiltroStatus}>
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="todos" className="flex items-center">
                <Bell className="mr-2 h-4 w-4" />
                Todos
                {manejosProcessados.length > 0 && (
                  <Badge className="ml-2 bg-blue-100 text-blue-800 border-blue-200">
                    {manejosProcessados.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="vencidos" className="flex items-center">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Vencidos
                {manejosProcessados.filter(m => m.vencido).length > 0 && (
                  <Badge className="ml-2 bg-red-100 text-red-800 border-red-200">
                    {manejosProcessados.filter(m => m.vencido).length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="proximos" className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                Próximos
                {manejosProcessados.filter(m => !m.vencido && m.proximoDoVencimento).length > 0 && (
                  <Badge className="ml-2 bg-amber-100 text-amber-800 border-amber-200">
                    {manejosProcessados.filter(m => !m.vencido && m.proximoDoVencimento).length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="semData" className="flex items-center">
                <CalendarDays className="mr-2 h-4 w-4" />
                Sem Data
                {manejosProcessados.filter(m => !m.dataVencimento).length > 0 && (
                  <Badge className="ml-2 bg-gray-100 text-gray-800 border-gray-200">
                    {manejosProcessados.filter(m => !m.dataVencimento).length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg border mb-6">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Filtros:</span>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 w-full">
                <Select value={filtroTipo} onValueChange={setFiltroTipo}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Tipo de Manejo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos os tipos</SelectItem>
                    <SelectItem value="veterinary">Veterinário</SelectItem>
                    <SelectItem value="farrier">Ferrageamento</SelectItem>
                    <SelectItem value="dental">Odontológico</SelectItem>
                    <SelectItem value="vaccination">Vacinação</SelectItem>
                    <SelectItem value="deworming">Vermifugação</SelectItem>
                    <SelectItem value="training">Treinamento</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={filtroPrioridade} onValueChange={setFiltroPrioridade}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Prioridade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todas as prioridades</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                    <SelectItem value="media">Média</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="baixa">Baixa</SelectItem>
                  </SelectContent>
                </Select>
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="mostrar-concluidos"
                    checked={mostrarConcluidos}
                    onCheckedChange={setMostrarConcluidos}
                  />
                  <Label htmlFor="mostrar-concluidos">Mostrar concluídos</Label>
                </div>
              </div>
            </div>
          </div>
          
          {loadingManejos ? (
            <div className="flex justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : manejosOrdenados.length === 0 ? (
            <Card className="bg-gray-50 border border-dashed">
              <CardContent className="py-16">
                <div className="text-center">
                  <CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900">
                    {(() => {
                      if (filtroStatus === 'vencidos') return "Nenhum manejo vencido";
                      if (filtroStatus === 'proximos') return "Nenhum manejo próximo do vencimento";
                      if (filtroStatus === 'semData') return "Nenhum manejo sem data de vencimento";
                      return "Nenhum manejo encontrado com os filtros atuais";
                    })()}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {(() => {
                      if (filtroStatus === 'vencidos') return "Todos os manejos estão dentro do prazo ou foram concluídos.";
                      if (filtroStatus === 'proximos') return "Nenhum manejo está próximo do vencimento.";
                      if (filtroStatus === 'semData') return "Todos os manejos possuem uma data de vencimento definida.";
                      return "Tente mudar os filtros para visualizar outros manejos.";
                    })()}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {manejosOrdenados.map(manejo => (
                <Card 
                  key={manejo.id} 
                  className={`border-l-4 ${
                    manejo.status === 'concluido' 
                      ? 'border-l-green-500' 
                      : manejo.vencido 
                        ? 'border-l-red-500' 
                        : manejo.proximoDoVencimento 
                          ? 'border-l-amber-500' 
                          : 'border-l-blue-500'
                  }`}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center">
                          <Badge variant="outline" className={getStatusBadgeClass(manejo)}>
                            {manejo.status === 'concluido' 
                              ? 'Concluído' 
                              : manejo.vencido 
                                ? 'Vencido' 
                                : manejo.proximoDoVencimento 
                                  ? 'Próximo' 
                                  : 'Pendente'}
                          </Badge>
                          <h3 className="ml-2 text-lg font-medium text-gray-900">{getTipoManejoNome(manejo.tipo)}</h3>
                          {manejo.prioridade && manejo.prioridade !== 'normal' && (
                            <Badge variant="outline" className={`ml-2 ${
                              manejo.prioridade === 'alta' 
                                ? 'bg-red-50 text-red-800 border-red-200' 
                                : manejo.prioridade === 'media' 
                                  ? 'bg-amber-50 text-amber-800 border-amber-200'
                                  : 'bg-blue-50 text-blue-800 border-blue-200'
                            }`}>
                              Prioridade {manejo.prioridade}
                            </Badge>
                          )}
                        </div>
                        
                        {/* Cavalo associado */}
                        {manejo.horse_id ? (
                          <p className="mt-2 text-sm text-gray-600">
                            Cavalo: {cavaloNameMap.get(manejo.horse_id) || "Não especificado"}
                          </p>
                        ) : (
                          <p className="mt-2 text-sm text-gray-600">
                            Manejo geral (sem cavalo específico)
                          </p>
                        )}
                        
                        {/* Datas */}
                        <div className="mt-2 flex flex-wrap gap-x-4 gap-y-2 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="mr-1 h-4 w-4" />
                            <span>Data: {formatarData(manejo.data)}</span>
                          </div>
                          
                          {manejo.dataVencimento && (
                            <div className="flex items-center">
                              <Clock className={`mr-1 h-4 w-4 ${
                                manejo.vencido 
                                  ? 'text-red-500' 
                                  : manejo.proximoDoVencimento 
                                    ? 'text-amber-500' 
                                    : 'text-gray-500'
                              }`} />
                              <span className={
                                manejo.vencido 
                                  ? 'text-red-600 font-medium' 
                                  : manejo.proximoDoVencimento 
                                    ? 'text-amber-600 font-medium' 
                                    : ''
                              }>
                                {formatTempoRestante(manejo.diasParaVencimento)}
                              </span>
                            </div>
                          )}
                        </div>
                        
                        {/* Observações */}
                        {manejo.observacoes && (
                          <p className="mt-3 text-sm text-gray-700 bg-gray-50 p-2 rounded border border-gray-100">
                            {manejo.observacoes}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex flex-col items-end">
                        <Link href={`/manejo/${manejo.id}`}>
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                            <span className="sr-only">Ver detalhes</span>
                            <ChevronRight className="h-5 w-5" />
                          </Button>
                        </Link>
                        
                        {manejo.status !== 'concluido' && (
                          <Button variant="outline" size="sm" className="mt-2">
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Concluir
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default AlertasPage;