/**
 * Rotas de Estoque - EquiGestor AI
 * API endpoints para gerenciamento de estoque e alertas
 */

import { Router, Request, Response } from "express";
import { stockService } from "../services/stock.service";
import { authenticateUser } from "../auth";
import { z } from "zod";

const router = Router();

// Aplicar autenticação em todas as rotas
router.use(authenticateUser);

/**
 * GET /api/stock/alerts - Lista alertas de estoque ativos
 */
router.get("/alerts", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    const alerts = await stockService.getActiveAlerts(user_id);
    
    // Agrupar alertas por prioridade para melhor visualização
    const groupedAlerts = {
      high: alerts.filter(a => a.priority === "high"),
      medium: alerts.filter(a => a.priority === "medium"),
      low: alerts.filter(a => a.priority === "low")
    };
    
    res.json({
      alerts,
      grouped: groupedAlerts,
      summary: {
        total: alerts.length,
        high: groupedAlerts.high.length,
        medium: groupedAlerts.medium.length,
        low: groupedAlerts.low.length
      }
    });
  } catch (error) {
    console.error("Erro ao buscar alertas de estoque:", error);
    res.status(500).json({ error: "Erro ao buscar alertas de estoque" });
  }
});

/**
 * GET /api/stock/batches - Lista todos os lotes de estoque
 */
router.get("/batches", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    const batches = await stockService.getUserStock(user_id);
    
    // Calcular valor total do estoque
    const totalValue = batches.reduce((sum, batch) => 
      sum + (batch.quantityKg * batch.unitCost), 0
    );
    
    res.json({
      batches,
      summary: {
        totalBatches: batches.length,
        totalValue: +totalValue.toFixed(2),
        items: [...new Set(batches.map(b => b.item))].length
      }
    });
  } catch (error) {
    console.error("Erro ao buscar lotes de estoque:", error);
    res.status(500).json({ error: "Erro ao buscar lotes de estoque" });
  }
});

/**
 * POST /api/stock/batches - Adiciona novo lote ao estoque
 */
router.post("/batches", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    
    const batchSchema = z.object({
      item: z.string().min(1, "Nome do item é obrigatório"),
      quantityKg: z.number().positive("Quantidade deve ser positiva"),
      unitCost: z.number().min(0, "Custo deve ser zero ou positivo"),
      expiry: z.string().optional().nullable(),
      supplier: z.string().optional().nullable(),
      batchNumber: z.string().optional().nullable()
    });
    
    const validatedData = batchSchema.parse(req.body);
    const batch = await stockService.addStockBatch({
      ...validatedData,
      user_id
    });
    
    res.status(201).json(batch);
  } catch (error: any) {
    console.error("Erro ao adicionar lote:", error);
    
    if (error?.name === 'ZodError') {
      return res.status(400).json({ 
        error: "Dados inválidos", 
        details: error.errors 
      });
    }
    
    res.status(500).json({ error: "Erro ao adicionar lote ao estoque" });
  }
});

/**
 * GET /api/stock/days-remaining/:item - Calcula dias restantes para um item
 */
router.get("/days-remaining/:item", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    const item = decodeURIComponent(req.params.item);
    
    const daysRemaining = await stockService.calculateDaysRemaining(user_id, item);
    const dailyConsumption = await stockService.calculateDailyConsumption(user_id, item);
    
    res.json({
      item,
      daysRemaining,
      dailyConsumption: +dailyConsumption.toFixed(2),
      status: daysRemaining === 0 ? "esgotado" : 
              daysRemaining < 7 ? "crítico" : 
              daysRemaining < 15 ? "baixo" : "normal"
    });
  } catch (error) {
    console.error("Erro ao calcular dias restantes:", error);
    res.status(500).json({ error: "Erro ao calcular dias restantes" });
  }
});

/**
 * POST /api/stock/check-alerts - Força verificação de alertas
 */
router.post("/check-alerts", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    const newAlerts = await stockService.checkAndCreateAlerts(user_id);
    
    res.json({
      message: "Verificação de alertas concluída",
      newAlerts: newAlerts.length,
      alerts: newAlerts
    });
  } catch (error) {
    console.error("Erro ao verificar alertas:", error);
    res.status(500).json({ error: "Erro ao verificar alertas" });
  }
});

/**
 * GET /api/stock/overview - Visão geral do estoque
 */
router.get("/overview", async (req: Request, res: Response) => {
  try {
    const user_id = Number((req as any).user?.id);
    
    // Buscar dados em paralelo para melhor performance
    const [batches, alerts] = await Promise.all([
      stockService.getUserStock(user_id),
      stockService.getActiveAlerts(user_id)
    ]);
    
    // Calcular estatísticas
    const totalValue = batches.reduce((sum, batch) => 
      sum + (batch.quantityKg * batch.unitCost), 0
    );
    
    const itemsOverview = batches.reduce((acc, batch) => {
      if (!acc[batch.item]) {
        acc[batch.item] = {
          item: batch.item,
          totalQuantity: 0,
          totalValue: 0,
          batches: 0
        };
      }
      
      acc[batch.item].totalQuantity += batch.quantityKg;
      acc[batch.item].totalValue += batch.quantityKg * batch.unitCost;
      acc[batch.item].batches += 1;
      
      return acc;
    }, {} as Record<string, any>);
    
    res.json({
      summary: {
        totalItems: Object.keys(itemsOverview).length,
        totalBatches: batches.length,
        totalValue: +totalValue.toFixed(2),
        activeAlerts: alerts.length,
        criticalAlerts: alerts.filter(a => a.priority === "high").length
      },
      items: Object.values(itemsOverview),
      recentAlerts: alerts.slice(0, 5) // 5 alertas mais recentes
    });
  } catch (error) {
    console.error("Erro ao buscar visão geral do estoque:", error);
    res.status(500).json({ error: "Erro ao buscar visão geral do estoque" });
  }
});

export default router;