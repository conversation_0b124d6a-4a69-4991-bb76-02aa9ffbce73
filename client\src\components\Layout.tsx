import { ReactNode, useState, useEffect } from 'react';
import { Link, useLocation } from 'wouter';
import { Sidebar } from '@/components/Sidebar';
import { MobileNav } from '@/components/MobileNav';
import { CavalosLateral } from '@/components/CavalosLateral';

import { AssistantButton } from '@/components/assistente/AssistantButton';

import { logger, logError, logInfo } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import { 
  BellIcon, Search, Settings, ChevronDown, User, 
  LogOut, ChevronsLeft, Maximize2, Power
} from 'lucide-react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { Manejo, Cavalo } from '@shared/schema';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from '@/hooks/use-mobile';
import { ExtendedDocument, ExtendedHTMLElement } from '@/types/dom';

// Interface para as props do layout
interface LayoutProps {
  children: ReactNode;
  pageTitle?: string;
  showBackButton?: boolean;
  backUrl?: string;
}

export function Layout({ 
  children, 
  pageTitle = "RS Horse", 
  showBackButton = false,
  backUrl = "/"
}: LayoutProps) {
  const [user, setUser] = useState<any>(null);
  const [, setLocation] = useLocation();
  const isMobile = useIsMobile();
  
  // Carregar usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);
  
  // Consulta de cavalos
  const { 
    data: cavalos = [], 
    isLoading: isLoadingCavalos 
  } = useQuery({
    queryKey: ['/api/cavalos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        return await apiRequest<Cavalo[]>('/api/cavalos', 'GET');
      } catch (error) {
        console.error('Erro ao buscar cavalos:', error);
        return [];
      }
    }
  });

  // Consulta de manejos
  const { 
    data: manejos = [], 
    isLoading: isLoadingManejos 
  } = useQuery({
    queryKey: ['/api/manejos'],
    enabled: !!user,
    queryFn: async () => {
      try {
        return await apiRequest<Manejo[]>('/api/manejos', 'GET');
      } catch (error) {
        console.error('Erro ao buscar manejos:', error);
        return [];
      }
    }
  });
  
  // Mutação para atualizar status de manejo
  const updateManejoStatusMutation = useMutation({
    mutationFn: async ({ manejoId, newStatus }: { manejoId: number, newStatus: string }) => {
      return await apiRequest(`/api/manejos/${manejoId}`, 'PUT', { status: newStatus });
    },
    onSuccess: () => {
      // Recarregar manejos após atualização
      queryClient.invalidateQueries({ queryKey: ['/api/manejos'] });
    }
  });
  
  // Margens para acomodar a sidebar em desktop
  const contentMargin = isMobile ? "ml-0" : "ml-14 md:ml-64";
  
  // Handler para logout
  const handleLogout = () => {
    try {
      localStorage.removeItem('user');
      setUser(null);
      setLocation('/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  // Função para entrar em modo tela cheia com suporte multiplataforma
  const toggleFullscreen = () => {
    try {
      // Cast do documento para o tipo estendido
      const extDoc = document as ExtendedDocument;
      
      // Verificar se a API Fullscreen está disponível
      if (!extDoc.fullscreenEnabled && 
          !extDoc.webkitFullscreenEnabled && 
          !extDoc.mozFullScreenEnabled && 
          !extDoc.msFullscreenEnabled) {
        console.log("Fullscreen API não suportada neste dispositivo/navegador");
        return;
      }
      
      // Cast do elemento para o tipo estendido
      const docEl = document.documentElement as ExtendedHTMLElement;
      
      if (!extDoc.fullscreenElement && 
          !extDoc.webkitFullscreenElement && 
          !extDoc.mozFullScreenElement && 
          !extDoc.msFullscreenElement) {
        // Entrar em modo tela cheia com suporte a diferentes navegadores
        if (docEl.requestFullscreen) {
          docEl.requestFullscreen();
        } else if (docEl.webkitRequestFullscreen) {
          docEl.webkitRequestFullscreen();
        } else if (docEl.mozRequestFullScreen) {
          docEl.mozRequestFullScreen();
        } else if (docEl.msRequestFullscreen) {
          docEl.msRequestFullscreen();
        }
      } else {
        // Sair do modo tela cheia
        if (extDoc.exitFullscreen) {
          extDoc.exitFullscreen();
        } else if (extDoc.webkitExitFullscreen) {
          extDoc.webkitExitFullscreen();
        } else if (extDoc.mozCancelFullScreen) {
          extDoc.mozCancelFullScreen();
        } else if (extDoc.msExitFullscreen) {
          extDoc.msExitFullscreen();
        }
      }
    } catch (error) {
      console.error("Erro ao alternar modo tela cheia:", error);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-100 ${isMobile ? 'has-mobile-menu' : ''}`}>
      {/* Sidebar (apenas para desktop) */}
      {!isMobile && <Sidebar />}
      
      {/* Lista de cavalos lateral (apenas para desktop) */}
      {user && !isMobile && <CavalosLateral />}
      
      {/* Menu de navegação móvel */}
      {isMobile && <MobileNav />}
      
      {/* Main content area */}
      <div className={`transition-all duration-300 ${isMobile ? '' : contentMargin}`}>
        {/* Top navigation bar */}
        <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-[#0A3364] text-white px-4 sm:px-6 shadow-md">
          {/* Left side: Title / back button */}
          <div className="flex items-center gap-2">
            {showBackButton && (
              <Link href={backUrl}>
                <Button variant="ghost" size="icon" className="mr-1 text-white hover:bg-[#134282]">
                  <ChevronsLeft className="h-5 w-5" />
                </Button>
              </Link>
            )}
            <h1 className="text-lg font-semibold tracking-wide">{pageTitle}</h1>
          </div>
          
          {/* Right side: search & user menu */}
          <div className="ml-auto flex items-center gap-2">
            {/* User dropdown - Sempre visível */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="flex items-center gap-2 p-1 px-2 text-white hover:bg-[#134282] rounded-full"
                >
                  <User className="h-5 w-5" />
                  <span className="hidden text-sm font-medium md:block">
                    {user?.username || "Usuário"}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Minha Conta</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Perfil</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Configurações</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sair</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Configuração button - Oculto em dispositivos móveis pequenos */}
            {!isMobile && (
              <Button variant="ghost" size="icon" className="text-white hover:bg-[#134282] rounded-full">
                <Settings className="h-5 w-5" />
              </Button>
            )}
            
            {/* Fullscreen button - Oculto em dispositivos móveis pequenos */}
            {!isMobile && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-white hover:bg-[#134282] rounded-full"
                onClick={toggleFullscreen}
              >
                <Maximize2 className="h-5 w-5" />
              </Button>
            )}
            
            {/* Power/logout button - Sempre visível */}
            <Button 
              variant="ghost" 
              size="icon" 
              className="text-white hover:bg-[#134282] rounded-full"
              onClick={handleLogout}
            >
              <Power className="h-5 w-5" />
            </Button>
          </div>
        </header>
        
        {/* Page content */}
        <main className="flex-1 px-2 sm:px-4 py-2 sm:py-4">
          <div className="w-full">
            {children}
          </div>
        </main>
        
        {/* Footer */}
        <footer className="py-6 border-t mt-10 bg-white">
          <div className="container mx-auto px-4 text-center text-gray-600 text-sm">
            RS Horse © {new Date().getFullYear()} - Sistema Inteligente de Gestão Equina
          </div>
        </footer>
      </div>
      
      {/* Assistente inteligente */}
      {user && <AssistantButton initialOpen={false} />}
      

    </div>
  );
  
  // Log de inicialização do componente
  logInfo('layout', 'Componente Layout inicializado');
}

// Interface e componente LayoutWrapper para uso nas páginas
export interface LayoutWrapperProps {
  children: ReactNode;
  pageTitle: string;
  showBackButton?: boolean;
  backUrl?: string;
}

export const LayoutWrapper = ({ 
  children, 
  pageTitle, 
  showBackButton = false, 
  backUrl = "/" 
}: LayoutWrapperProps) => (
  <Layout 
    pageTitle={pageTitle} 
    showBackButton={showBackButton} 
    backUrl={backUrl}
  >
    {children}
  </Layout>
);

export default Layout;