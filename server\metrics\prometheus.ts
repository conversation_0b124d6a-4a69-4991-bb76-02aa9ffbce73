/**
 * Métricas Prometheus para o EquiGestor AI
 */
import { Request, Response } from 'express';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';

// Coletar métricas padrão do Node.js
collectDefaultMetrics();

// Métricas customizadas
export const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10]
});

export const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

export const databaseConnections = new Gauge({
  name: 'database_connections_active',
  help: 'Number of active database connections'
});

export const authenticationAttempts = new Counter({
  name: 'authentication_attempts_total',
  help: 'Total number of authentication attempts',
  labelNames: ['result'] // 'success' or 'failure'
});

export const horsesTotal = new Gauge({
  name: 'horses_total',
  help: 'Total number of horses in the system'
});

export const apiErrors = new Counter({
  name: 'api_errors_total',
  help: 'Total number of API errors',
  labelNames: ['endpoint', 'error_type']
});

/**
 * Middleware para coletar métricas HTTP
 */
export function metricsMiddleware(req: Request, res: Response, next: Function) {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = (req as any).route?.path || req.path;
    
    httpRequestsTotal.inc({
      method: req.method,
      route,
      status_code: res.statusCode.toString()
    });
    
    httpRequestDuration.observe({
      method: req.method,
      route,
      status_code: res.statusCode.toString()
    }, duration);
  });
  
  next();
}

/**
 * Handler do endpoint /metrics
 */
export async function metricsHandler(req: Request, res: Response): Promise<void> {
  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    const logger = (req as any).logger;
    if (logger) {
      logger.error({
        event: 'metrics_error',
        error: error instanceof Error ? error.message : 'Unknown error'
      }, 'Failed to generate metrics');
    }
    
    res.status(500).end('Error generating metrics');
  }
}

/**
 * Função para incrementar contador de autenticação
 */
export function recordAuthAttempt(success: boolean) {
  authenticationAttempts.inc({ result: success ? 'success' : 'failure' });
}

/**
 * Função para atualizar contagem de cavalos
 */
export function updateHorsesCount(count: number) {
  horsesTotal.set(count);
}

/**
 * Função para registrar erro de API
 */
export function recordApiError(endpoint: string, errorType: string) {
  apiErrors.inc({ endpoint, error_type: errorType });
}