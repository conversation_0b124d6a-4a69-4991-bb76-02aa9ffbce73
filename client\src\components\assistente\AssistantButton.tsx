import { useState } from 'react';
import { BotMessageSquare, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SmartAssistantAI } from './SmartAssistantAI';

interface AssistantButtonProps {
  initialOpen?: boolean;
}

export function AssistantButton({ initialOpen = false }: AssistantButtonProps) {
  const [isOpen, setIsOpen] = useState(initialOpen);

  return (
    <>
      {/* Botão flutuante para abrir/fechar o assistente */}
      <div className="fixed bottom-16 sm:bottom-20 right-4 sm:right-6 z-50">
        <Button
          onClick={() => setIsOpen(!isOpen)}
          size="lg"
          className={`rounded-full h-12 w-12 sm:h-14 sm:w-14 shadow-lg flex items-center justify-center btn-mobile ${
            isOpen ? 'bg-red-600 hover:bg-red-700' : 'bg-primary hover:bg-primary/90'
          }`}
          aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
        >
          {isOpen ? (
            <X className="h-5 w-5 sm:h-6 sm:w-6" />
          ) : (
            <BotMessageSquare className="h-5 w-5 sm:h-6 sm:w-6" />
          )}
        </Button>
      </div>

      {/* Modal do assistente responsivo */}
      {isOpen && (
        <div className="fixed inset-x-2 sm:inset-x-auto sm:bottom-32 sm:right-6 bottom-28 z-40 max-w-md w-auto sm:w-full md:w-1/3">
          <SmartAssistantAI isExpanded={true} onToggleExpand={() => setIsOpen(false)} />
        </div>
      )}
    </>
  );
}