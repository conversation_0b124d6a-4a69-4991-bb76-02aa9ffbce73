import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Eye, FileText, Calendar, AlertCircle, CheckCircle 
} from 'lucide-react';
import { Reproducao, Cavalo } from '@shared/schema';

/**
 * Componente de Detalhes de Cobrições
 * Exibe informações detalhadas sobre cobrições e montas
 */
export function DetalhesReproducaoCobricoes() {
  // Consultar dados de reprodução
  const { data: reproducoes = [], isLoading: loadingReproducoes } = useQuery<Reproducao[]>({
    queryKey: ['/api/reproducao'],
  });
  
  // Consultar dados de cavalos 
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });
  
  // Filtrar apenas reproduções que são cobrições
  const cobricoes = reproducoes.filter(rep => 
    rep.resultado !== 'parto_realizado' && 
    !rep.observacoes?.toLowerCase().includes('parto') &&
    !rep.observacoes?.toLowerCase().includes('nasceu')
  );

  // Função para obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    const cavalo = cavalos.find(c => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };
  
  // Função para formatar data
  const formatarData = (dataString: string | null) => {
    if (!dataString) return 'Não informada';
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  };

  // Função para obter o método de cobertura
  const getMetodoCobertura = (cobertura: Reproducao): string => {
    if (cobertura.tipo_cobertura) return cobertura.tipo_cobertura;
    
    if (cobertura.observacoes) {
      const obs = cobertura.observacoes.toLowerCase();
      if (obs.includes('inseminação')) return 'Inseminação Artificial';
      if (obs.includes('natural')) return 'Monta Natural';
      if (obs.includes('transferência')) return 'Transferência de Embrião';
    }
    
    return 'Não especificado';
  };

  // Função para obter a cor do badge conforme o status
  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'gestante':
      case 'prenhez_confirmada':
        return 'default';
      case 'em_observacao':
      case 'vazia':
        return 'secondary';
      case 'aborto':
      case 'falha_embriao':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Calcular estatísticas
  const cobricoesRealizadas = cobricoes.length;
  const cobricoesComPrenhez = cobricoes.filter(c => 
    c.resultado === 'gestante' || c.resultado === 'prenhez_confirmada'
  ).length;
  const cobricoesSemSucesso = cobricoes.filter(c => 
    c.resultado === 'aborto' || c.resultado === 'falha_embriao' || c.resultado === 'vazia'
  ).length;
  const cobricoesEmObservacao = cobricoes.filter(c => 
    c.resultado === 'em_observacao' || !c.resultado
  ).length;

  const taxaExito = cobricoesRealizadas ? 
    ((cobricoesComPrenhez / cobricoesRealizadas) * 100).toFixed(1) : '0';

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Controle de Cobrições</h1>
        <p className="text-muted-foreground">
          Histórico e monitoramento de todas as cobrições e montas
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader className="bg-blue-50">
          <CardTitle>Resumo</CardTitle>
          <CardDescription>Estatísticas de cobrições</CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-5 gap-4">
            <div className="flex flex-col p-4 bg-blue-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Total de Cobrições</span>
              <span className="text-2xl font-bold">{cobricoesRealizadas}</span>
            </div>
            <div className="flex flex-col p-4 bg-green-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Prenhez Confirmada</span>
              <span className="text-2xl font-bold">{cobricoesComPrenhez}</span>
            </div>
            <div className="flex flex-col p-4 bg-amber-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Em Observação</span>
              <span className="text-2xl font-bold">{cobricoesEmObservacao}</span>
            </div>
            <div className="flex flex-col p-4 bg-red-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Sem Sucesso</span>
              <span className="text-2xl font-bold">{cobricoesSemSucesso}</span>
            </div>
            <div className="flex flex-col p-4 bg-purple-50 rounded-lg">
              <span className="text-sm text-muted-foreground">Taxa de Êxito</span>
              <span className="text-2xl font-bold">{taxaExito}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Registro de Cobrições</CardTitle>
          <CardDescription>Detalhes de todas as cobrições</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingReproducoes || loadingCavalos ? (
            <div className="text-center py-8">Carregando informações...</div>
          ) : cobricoes.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Égua</TableHead>
                  <TableHead>Padreador</TableHead>
                  <TableHead>Método</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Previsão</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cobricoes.map((cobricao) => (
                  <TableRow key={cobricao.id}>
                    <TableCell>{formatarData(cobricao.data_cobertura)}</TableCell>
                    <TableCell>{getCavaloName(cobricao.egua_id)}</TableCell>
                    <TableCell>
                      {cobricao.garanhao_id ? getCavaloName(cobricao.garanhao_id) : 'Não informado'}
                    </TableCell>
                    <TableCell>{getMetodoCobertura(cobricao)}</TableCell>
                    <TableCell>
                      <StatusBadge estado={cobricao.resultado} />
                    </TableCell>
                    <TableCell>
                      {cobricao.data_parto_previsto 
                        ? formatarData(cobricao.data_parto_previsto)
                        : 'Não agendado'}
                    </TableCell>
                    <TableCell className="text-right space-x-1">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Eye className="h-4 w-4" />
                      </Button>
                      {cobricao.resultado === 'em_observacao' && (
                        <>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-green-500">
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500">
                            <AlertCircle className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Calendar className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Nenhuma cobrição registrada</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Componente StatusBadge - traduz o estado do BD para badges visuais
function StatusBadge({ estado }: { estado: string | null }) {
  if (!estado) return <Badge variant="outline">Não definido</Badge>;

  // Padronizar os estados (mesmos nomes usados no sistema)
  const estadoNormalizado = 
    estado === 'prenhez_confirmada' ? 'gestante' :
    estado === 'abortado' ? 'aborto' :
    estado;
  
  const getVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    switch (estadoNormalizado) {
      case 'gestante':
        return 'default';
      case 'em_observacao':
      case 'vazia':
        return 'secondary';
      case 'aborto':
      case 'falha_embriao':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getLabel = (): string => {
    switch (estadoNormalizado) {
      case 'gestante':
        return 'Gestante';
      case 'em_observacao':
        return 'Em Observação';
      case 'vazia':
        return 'Vazia';
      case 'aborto':
        return 'Aborto';
      case 'falha_embriao':
        return 'Falha Embrionária';
      case 'parto_realizado':
        return 'Parto Realizado';
      default:
        return estadoNormalizado.charAt(0).toUpperCase() + estadoNormalizado.slice(1);
    }
  };

  return <Badge variant={getVariant()}>{getLabel()}</Badge>;
}