import express from "express";
import cors from "cors";
import { createServer } from "http";
import path from "path";

const app = express();
const port = 5000;

// Configuração ultra-robusta
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Health check básico
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'EquiGestor AI Funcionando!',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime())
  });
});

// Rota básica de teste
app.get('/api/status', (_req, res) => {
  res.json({ 
    message: 'EquiGestor AI API Online!',
    version: '1.0.0',
    features: ['Gestão de Cavalos', 'Genealogia', 'Análises']
  });
});

// Servir arquivos estáticos básicos
const clientPath = path.join(process.cwd(), 'client');
app.use(express.static(path.join(clientPath, 'public')));

// Fallback para SPA
app.get('*', (_req, res) => {
  const indexPath = path.join(clientPath, 'index.html');
  res.sendFile(indexPath, (err) => {
    if (err) {
      res.status(200).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>EquiGestor AI</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial, sans-serif; padding: 40px; text-align: center; }
            .status { color: #22c55e; font-size: 24px; margin: 20px 0; }
            .info { color: #64748b; margin: 10px 0; }
          </style>
        </head>
        <body>
          <h1>🐎 EquiGestor AI</h1>
          <div class="status">✅ Sistema Funcionando!</div>
          <div class="info">Sua aplicação de gestão equina está operacional</div>
          <div class="info">Interface carregando...</div>
        </body>
        </html>
      `);
    }
  });
});

const server = createServer(app);

// Iniciar servidor de forma definitiva
server.listen(port, "0.0.0.0", () => {
  console.log(`🎉 EquiGestor AI DEFINITIVAMENTE FUNCIONANDO na porta ${port}`);
  
  // Carregar recursos avançados após servidor estável
  setTimeout(async () => {
    try {
      const { addApiRoutes } = await import("./routes");
      addApiRoutes(app);
      
      const { setupVite, serveStatic } = await import("./vite");
      const nodeEnv = process.env.NODE_ENV || "development";
      
      if (nodeEnv === "development") {
        await setupVite(app, server);
      } else {
        serveStatic(app);
      }
      
      console.log(`🚀 EquiGestor AI com todas as funcionalidades ativas!`);
    } catch (error: any) {
      console.log(`⚠️ Funcionalidades básicas ativas. Erro: ${error.message}`);
    }
  }, 1000);
});

server.on('error', (error: any) => {
  console.error('Erro:', error.message);
});