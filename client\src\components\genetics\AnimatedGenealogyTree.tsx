import React, { useRef, useEffect, useState, useMemo } from 'react';
import * as d3 from 'd3';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Squirrel,
  CircleDot,
  Circle,
  AlertTriangle,
  ZoomIn,
  ZoomOut,
  MoveHorizontal,
  RotateCcw,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import ConsanguinityIndicator from './ConsanguinityIndicator';
import { cn } from '@/lib/utils';

// Reutilizando a interface GenealogyNode do componente original
export interface GenealogyNode {
  id: number | null;
  name: string;
  gender: 'M' | 'F';
  breed?: string;
  color?: string;
  consanguinity?: number;
  isUnknown?: boolean;
  isHighlighted?: boolean;
  father?: GenealogyNode;
  mother?: GenealogyNode;
}

// Interface para os dados processados pelo D3
interface HierarchyNode extends d3.HierarchyNode<GenealogyNode> {
  x: number;
  y: number;
  data: GenealogyNode;
}

interface AnimatedGenealogyTreeProps {
  rootNode: GenealogyNode;
  onNodeClick?: (id: number | null) => void;
  className?: string;
  maxGenerations?: number;
}

/**
 * Componente de árvore genealógica interativa com animações, zoom e hover
 */
const AnimatedGenealogyTree: React.FC<AnimatedGenealogyTreeProps> = ({
  rootNode,
  onNodeClick,
  className = '',
  maxGenerations = 4,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const [hoveredNode, setHoveredNode] = useState<HierarchyNode | null>(null);
  const [selectedNode, setSelectedNode] = useState<HierarchyNode | null>(null);
  
  // Configurações da árvore
  const nodeWidth = 200;
  const nodeHeight = 100;
  const horizontalSpacing = 100;
  const verticalSpacing = 120;
  
  // Processamento dos dados para D3
  const processedData = useMemo(() => {
    // Transformar a estrutura em um formato adequado para D3
    const limitDepth = (node: GenealogyNode | undefined, currentDepth: number): GenealogyNode | undefined => {
      if (!node || currentDepth >= maxGenerations) return undefined;
      
      return {
        ...node,
        father: limitDepth(node.father, currentDepth + 1),
        mother: limitDepth(node.mother, currentDepth + 1)
      };
    };
    
    // Limitar a profundidade da árvore
    const limitedTree = limitDepth(rootNode, 0);
    
    if (!limitedTree) return null;
    
    // Criar hierarquia D3
    const hierarchy = d3.hierarchy(limitedTree);
    
    // Configurar layout de árvore
    const treeLayout = d3.tree<GenealogyNode>()
      .nodeSize([nodeWidth + horizontalSpacing, nodeHeight + verticalSpacing])
      .separation((a, b) => (a.parent === b.parent ? 1.2 : 1.4));
    
    // Aplicar layout
    return treeLayout(hierarchy);
  }, [rootNode, maxGenerations]);
  
  // Efeito para redimensionar a visualização
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.clientWidth,
          height: Math.max(containerRef.current.clientHeight, 600)
        });
      }
    };
    
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);
  
  // Renderizar tooltip para informações detalhadas ao passar o mouse
  const renderTooltip = (node: HierarchyNode) => {
    const data = node.data;
    
    return (
      <TooltipContent className="max-w-[300px] p-4">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {data.gender === 'M' ? (
              <Circle className="h-4 w-4 text-blue-500" />
            ) : (
              <CircleDot className="h-4 w-4 text-pink-500" />
            )}
            <h3 className="font-bold">{data.name}</h3>
          </div>
          
          {data.breed && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Raça:</span>
              <Badge variant="outline">{data.breed}</Badge>
            </div>
          )}
          
          {data.color && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Pelagem:</span>
              <span className="text-sm">{data.color}</span>
            </div>
          )}
          
          {data.consanguinity !== undefined && data.consanguinity > 0 && (
            <div className="space-y-1">
              <span className="text-sm font-medium">Consanguinidade:</span>
              <ConsanguinityIndicator 
                coefficient={data.consanguinity} 
                size="sm"
                showInfo={true}
              />
            </div>
          )}
          
          <div className="text-xs text-muted-foreground mt-1">
            <p>Clique para visualizar a ficha completa</p>
          </div>
        </div>
      </TooltipContent>
    );
  };
  
  // Renderizar nós com estilo mais avançado
  const renderNode = (node: HierarchyNode) => {
    const data = node.data;
    const isHovered = hoveredNode === node;
    const isSelected = selectedNode === node;
    
    // Classes para estilização
    const genderColor = data.gender === 'M' ? 'text-blue-500' : 'text-pink-500';
    const nodeClasses = cn(
      'genealogy-node relative transform transition-all duration-200 cursor-pointer',
      {
        'scale-105 shadow-lg z-10': isHovered || isSelected,
        'ring-2 ring-primary': isSelected,
        'opacity-80': !isHovered && !isSelected && selectedNode !== null,
      }
    );
    
    // Ícone do gênero
    const GenderIcon = data.gender === 'M' ? Circle : CircleDot;
    
    return (
      <g
        className={nodeClasses}
        transform={`translate(${node.x},${node.y})`}
        onMouseEnter={() => setHoveredNode(node)}
        onMouseLeave={() => setHoveredNode(null)}
        onClick={() => {
          setSelectedNode(node);
          if (onNodeClick && data.id !== null) {
            onNodeClick(data.id);
          }
        }}
      >
        <foreignObject
          x={-nodeWidth / 2}
          y={-nodeHeight / 2}
          width={nodeWidth}
          height={nodeHeight}
          className="overflow-visible"
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className={cn(
                    "bg-card border rounded-md shadow-sm p-3 h-full w-full transition-colors",
                    isHovered || isSelected ? "border-primary" : "border-border",
                  )}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Squirrel className={`h-4 w-4 ${genderColor}`} />
                      <GenderIcon className={`h-4 w-4 ${genderColor}`} />
                    </div>
                    {data.breed && (
                      <Badge variant="outline" className="text-xs">
                        {data.breed}
                      </Badge>
                    )}
                  </div>
                  
                  <h4 className="font-medium text-sm mt-2 truncate">{data.name}</h4>
                  
                  {data.color && (
                    <div className="text-xs text-muted-foreground mt-1 truncate">
                      Cor: {data.color}
                    </div>
                  )}
                  
                  {data.consanguinity !== undefined && data.consanguinity > 0 && (
                    <div className="mt-2">
                      <ConsanguinityIndicator 
                        coefficient={data.consanguinity} 
                        size="sm"
                        showInfo={false}
                      />
                    </div>
                  )}
                </div>
              </TooltipTrigger>
              {renderTooltip(node)}
            </Tooltip>
          </TooltipProvider>
        </foreignObject>
      </g>
    );
  };
  
  // Renderização condicional para erro
  if (!processedData) {
    return (
      <div className="flex items-center justify-center p-8 bg-muted/10 rounded-lg">
        <AlertTriangle className="h-6 w-6 text-amber-500 mr-2" />
        <span>Não foi possível processar dados da árvore genealógica</span>
      </div>
    );
  }
  
  // Calcular conexões entre os nós
  const connections = processedData.links().map(link => {
    // Obter as posições dos nós fonte e destino
    const source = { x: link.source.x, y: link.source.y };
    const target = { x: link.target.x, y: link.target.y };
    
    // Definir pontos para a curva
    const midY = (source.y + target.y) / 2;
    
    // Definir o tipo de linha baseado na relação (pai/mãe)
    // Assumir que nós à esquerda são pais e à direita são mães
    const sourceNode = link.source.data;
    const targetNode = link.target.data;
    const isFather = sourceNode.gender === 'M';
    
    // Cor baseada no gênero
    const strokeColor = isFather ? 'rgb(59, 130, 246)' : 'rgb(236, 72, 153)';
    const strokeStyle = isFather ? '' : '5,5';
    const opacity = 0.5;
    
    // Definir caminho da curva
    return {
      id: `${source.x}-${source.y}-${target.x}-${target.y}`,
      d: `M${source.x},${source.y} C${source.x},${midY} ${target.x},${midY} ${target.x},${target.y}`,
      stroke: strokeColor,
      strokeWidth: 2,
      opacity,
      strokeDasharray: strokeStyle,
      markerEnd: ""
    };
  });
  
  return (
    <div 
      ref={containerRef} 
      className={cn("w-full h-[600px] border rounded-lg bg-card/50 overflow-hidden relative", className)}
    >
      <TransformWrapper
        initialScale={0.8}
        minScale={0.3}
        maxScale={2}
        centerOnInit={true}
        wheel={{ step: 0.08 }}
      >
        {({ zoomIn, zoomOut, resetTransform }) => (
          <>
            {/* Controles de zoom e navegação */}
            <div className="absolute top-2 right-2 z-10 flex flex-col space-y-1">
              <Button
                variant="outline"
                size="icon"
                onClick={() => zoomIn()}
                title="Ampliar"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => zoomOut()}
                title="Reduzir"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => resetTransform()}
                title="Resetar visualização"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          
            {/* Instrução para usuário */}
            <div className="absolute left-2 top-2 z-10 text-xs bg-background/80 p-2 rounded-md">
              <div className="flex items-center">
                <MoveHorizontal className="h-3 w-3 mr-1" />
                <span>Arraste para mover, use a roda do mouse para zoom</span>
              </div>
            </div>
          
            <TransformComponent
              wrapperStyle={{ width: "100%", height: "100%" }}
              contentStyle={{ width: "100%", height: "100%" }}
            >
              <svg 
                ref={svgRef}
                width="100%" 
                height="100%"
                viewBox={`${-dimensions.width/2} ${-dimensions.height/2} ${dimensions.width} ${dimensions.height}`}
                className="genealogy-tree"
              >
                <g className="connections">
                  {connections.map(conn => (
                    <path
                      key={conn.id}
                      d={conn.d}
                      stroke={conn.stroke}
                      strokeWidth={conn.strokeWidth}
                      opacity={conn.opacity}
                      strokeDasharray={conn.strokeDasharray}
                      fill="none"
                      markerEnd={conn.markerEnd}
                      className="transition-all duration-300"
                    />
                  ))}
                </g>
                <g className="nodes">
                  {processedData.descendants().map((node, i) => (
                    <React.Fragment key={`node-${i}-${node.data.id || i}`}>
                      {renderNode(node as HierarchyNode)}
                    </React.Fragment>
                  ))}
                </g>
              </svg>
            </TransformComponent>
          </>
        )}
      </TransformWrapper>
    </div>
  );
};

export default AnimatedGenealogyTree;