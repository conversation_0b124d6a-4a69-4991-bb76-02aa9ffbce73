import { z } from 'zod';
import { pgTable, serial, integer, date, text, timestamp, real } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { cavalos } from "./schema";
import { users } from "./schema";

// Tabela de medidas morfológicas dos cavalos
export const medidasMorfologicas = pgTable("medidas_morfologicas", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull().references(() => cavalos.id, { onDelete: "cascade" }),
  user_id: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  dataMedicao: timestamp("data_medicao").notNull(),

  // Medidas básicas
  alturaCernelha: real("altura_cernelha"), // em cm
  comprimentoCorpo: real("comprimento_corpo"), // em cm
  perimetroToracico: real("perimetro_toracico"), // em cm
  comprimentoGarupa: real("comprimento_garupa"), // em cm
  perimetroCanela: real("perimetro_canela"), // em cm
  comprimentoCabeca: real("comprimento_cabeca"), // em cm
  larguraCabeca: real("largura_cabeca"), // em cm

  // Dados adicionais
  temperatura: real("temperatura"), // em ºC
  racaReferencia: text("raca_referencia"), // Raça usada para comparação
  observacoes: text("observacoes"),

  // Índices calculados
  indiceRelativo: real("indice_relativo"), // Índice corporal relativo
  indiceRobustez: real("indice_robustez"), // Índice de robustez
  indiceTorax: real("indice_torax"), // Índice torácico
  indiceCefalico: real("indice_cefalico"), // Índice cefálico

  created_at: timestamp("created_at").defaultNow(),
});

export const insertMedidasMorfologicasSchema = z.object({
  horse_id: z.number().int().positive(),
  user_id: z.number().int().positive(),
  dataMedicao: z.string().or(z.date()),
  alturaCernelha: z.number().min(0).nullable(),
  comprimentoCorpo: z.number().min(0).nullable(),
  perimetroToracico: z.number().min(0).nullable(),
  comprimentoGarupa: z.number().min(0).nullable(),
  perimetroCanela: z.number().min(0).nullable(),
  comprimentoCabeca: z.number().min(0).nullable(),
  larguraCabeca: z.number().min(0).nullable(),
  temperatura: z.number().min(30).max(45).nullable(),
  racaReferencia: z.string().nullable(),
  observacoes: z.string().nullable(),
  indiceRelativo: z.number().nullable(),
  indiceRobustez: z.number().nullable(),
  indiceTorax: z.number().nullable(),
  indiceCefalico: z.number().nullable()
}).transform((data) => ({
  ...data,
  dataMedicao: new Date(data.dataMedicao),
  alturaCernelha: data.alturaCernelha ? Number(data.alturaCernelha) : null,
  comprimentoCorpo: data.comprimentoCorpo ? Number(data.comprimentoCorpo) : null,
  perimetroToracico: data.perimetroToracico ? Number(data.perimetroToracico) : null,
  comprimentoGarupa: data.comprimentoGarupa ? Number(data.comprimentoGarupa) : null,
  perimetroCanela: data.perimetroCanela ? Number(data.perimetroCanela) : null,
  comprimentoCabeca: data.comprimentoCabeca ? Number(data.comprimentoCabeca) : null,
  larguraCabeca: data.larguraCabeca ? Number(data.larguraCabeca) : null,
  temperatura: data.temperatura ? Number(data.temperatura) : null,
  indiceRelativo: data.indiceRelativo ? Number(data.indiceRelativo) : null,
  indiceRobustez: data.indiceRobustez ? Number(data.indiceRobustez) : null,
  indiceTorax: data.indiceTorax ? Number(data.indiceTorax) : null,
  indiceCefalico: data.indiceCefalico ? Number(data.indiceCefalico) : null
}));

// Tipos para uso no projeto
export type InsertMedidasMorfologicas = z.infer<typeof insertMedidasMorfologicasSchema>;
export type MedidasMorfologicas = typeof medidasMorfologicas.$inferSelect;