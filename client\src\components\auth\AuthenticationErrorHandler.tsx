import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';

interface AuthenticationErrorHandlerProps {
  error: unknown;
  title?: string;
  message?: string;
  buttonText?: string;
}

/**
 * Componente para tratamento de erros de autenticação
 * 
 * Exibe uma mensagem de erro amigável e oferece uma opção para
 * o usuário fazer login novamente. Pode ser reutilizado em
 * qualquer parte da aplicação onde erros de autenticação possam ocorrer.
 */
export const AuthenticationErrorHandler: React.FC<AuthenticationErrorHandlerProps> = ({
  error,
  title = "Erro de Autenticação",
  message = "Sua sessão expirou ou você não está autenticado. Por favor, faça login novamente para acessar esta página.",
  buttonText = "Ir para a página de login"
}) => {
  // Verificar se o erro é realmente um erro de autenticação
  const isAuthError = error instanceof Error && (
    error.message.includes('Usuário não autenticado') ||
    error.message.includes('401') ||
    error.message.includes('Não autorizado')
  );

  // Se não for um erro de autenticação, não exibir nada
  if (!isAuthError) return null;

  // Função para redirecionar para a página de login
  const handleLoginRedirect = () => {
    try {
      // Limpar dados de autenticação
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('token_expiration');
      
      // Redirecionar para a página de login
      window.location.href = '/login';
    } catch (e) {
      console.error('Erro ao redirecionar para login:', e);
    }
  };

  return (
    <Card className="mx-auto max-w-md">
      <CardHeader>
        <div className="flex items-center justify-center mb-2">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <CardTitle className="text-center text-red-700">
          {title}
        </CardTitle>
        <CardDescription className="text-center">
          {message}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={handleLoginRedirect} className="w-full">
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
};

/**
 * Verifica se um erro é um erro de autenticação
 * @param error Erro a ser verificado
 * @returns Verdadeiro se for um erro de autenticação, falso caso contrário
 */
export function isAuthenticationError(error: unknown): boolean {
  return error instanceof Error && (
    error.message.includes('Usuário não autenticado') ||
    error.message.includes('401') ||
    error.message.includes('Não autorizado')
  );
}

export default AuthenticationErrorHandler;