import { useState, useEffect } from 'react';

/**
 * Hook para detectar se a interface está em modo mobile
 * 
 * @param breakpoint Breakpoint em pixels para considerar como dispositivo móvel
 * @returns Boolean indicando se o dispositivo é móvel
 */
export function useIsMobile(breakpoint: number = 768): boolean {
  const [isMobile, setIsMobile] = useState<boolean>(
    typeof window !== 'undefined' ? window.innerWidth < breakpoint : false
  );

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkMobile = (): void => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    // Verificar inicialmente
    checkMobile();

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [breakpoint]);

  return isMobile;
}

/**
 * Hook para verificar o tamanho da tela e retornar o tipo de dispositivo
 * Útil para lógica mais complexa de responsividade
 * 
 * @returns Objeto com flags para diferentes tamanhos de tela
 */
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState({
    isMobile: false,      // < 640px
    isTablet: false,      // 640px - 1023px
    isDesktop: false,     // 1024px - 1279px
    isLargeDesktop: false // >= 1280px
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkScreenSize = (): void => {
      const width = window.innerWidth;
      setScreenSize({
        isMobile: width < 640,
        isTablet: width >= 640 && width < 1024,
        isDesktop: width >= 1024 && width < 1280,
        isLargeDesktop: width >= 1280
      });
    };

    // Verificar inicialmente
    checkScreenSize();

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  return screenSize;
}

export default useIsMobile;