/**
 * Sistema de proteção contra tentativas de login suspeitas
 * 
 * Este módulo implementa um mecanismo mais avançado para detectar e bloquear
 * tentativas de login potencialmente maliciosas, complementando a proteção
 * básica de rate limiting.
 */

import { Request } from 'express';
import NodeCache from 'node-cache';

// Cache para armazenar tentativas de login
// Em produção, usar Redis ou um banco de dados seria mais apropriado
const loginAttemptCache = new NodeCache({
  stdTTL: 3600, // 1 hora de expiração
  checkperiod: 600, // Verificar expiração a cada 10 minutos
});

// Estrutura para armazenar detalhes de tentativa de login
interface LoginAttempt {
  username: string;        // Nome de usuário tentado
  timestamp: number;       // Timestamp da tentativa
  successful: boolean;     // Se a tentativa foi bem-sucedida
  ip: string;              // Endereço IP
  userAgent?: string;      // User-Agent do cliente
  country?: string;        // País (se disponível via geolocalização)
  suspiciousFactors: string[]; // Fatores que tornam a tentativa suspeita
}

// Configurações para detecção de tentativas suspeitas
const config = {
  // Número máximo de tentativas falhas por usuário em um período
  maxFailedAttemptsPerUser: 5,
  
  // Número máximo de tentativas por IP em um período
  maxAttemptsPerIP: 10,
  
  // Período para considerar (em milissegundos, 30 minutos)
  periodMS: 30 * 60 * 1000,
  
  // Tempo de bloqueio após atingir o limite (em milissegundos, 1 hora)
  blockTimeMS: 60 * 60 * 1000,
  
  // Lista de países considerados de alto risco (exemplo)
  highRiskCountries: ['unknown'],
  
  // Lista de padrões suspeitos de User-Agent
  suspiciousUserAgents: [
    'curl', 
    'python-requests', 
    'wget', 
    'bot',
    'selenium',
    'automation',
    'headless'
  ]
};

/**
 * Extrai informações da requisição para análise de segurança
 * @param req Objeto de requisição Express
 * @returns Objeto com informações extraídas
 */
function extractRequestInfo(req: Request): {
  ip: string;
  userAgent?: string;
  country?: string;
} {
  return {
    // Obter IP real, considerando possíveis proxies
    ip: req.headers['x-forwarded-for'] as string || 
        req.socket.remoteAddress || 
        'unknown',
    
    // User-Agent do cliente
    userAgent: req.headers['user-agent'],
    
    // Em uma implementação real, poderíamos usar um serviço de geolocalização
    // por IP para determinar o país de origem
    country: 'unknown'
  };
}

/**
 * Verifica se uma tentativa de login parece suspeita
 * @param attempt Detalhes da tentativa de login
 * @returns Array de fatores suspeitos encontrados ou array vazio se não houver suspeitas
 */
function checkSuspiciousFactors(attempt: Omit<LoginAttempt, 'suspiciousFactors'>): string[] {
  const suspiciousFactors: string[] = [];
  
  // Verificar se o país de origem é considerado de alto risco
  if (attempt.country && config.highRiskCountries.includes(attempt.country)) {
    suspiciousFactors.push('high_risk_location');
  }
  
  // Verificar se o User-Agent parece suspeito
  if (attempt.userAgent) {
    const userAgentLower = attempt.userAgent.toLowerCase();
    for (const pattern of config.suspiciousUserAgents) {
      if (userAgentLower.includes(pattern)) {
        suspiciousFactors.push('suspicious_user_agent');
        break;
      }
    }
  }
  
  // Verificar a frequência de tentativas do mesmo IP
  const recentIPAttempts = getAttemptsByIP(attempt.ip, config.periodMS);
  if (recentIPAttempts.length >= config.maxAttemptsPerIP) {
    suspiciousFactors.push('high_frequency_ip');
  }
  
  // Verificar padrões de tentativas falhas do usuário
  const recentUserAttempts = getAttemptsByUsername(attempt.username, config.periodMS);
  const failedUserAttempts = recentUserAttempts.filter(a => !a.successful);
  
  if (failedUserAttempts.length >= config.maxFailedAttemptsPerUser) {
    suspiciousFactors.push('multiple_failed_attempts');
  }
  
  // Verificar tentativas de login distribuídas (múltiplos IPs para o mesmo usuário)
  const uniqueIPs = new Set(recentUserAttempts.map(a => a.ip));
  if (uniqueIPs.size > 3) { // Se mais de 3 IPs diferentes tentarem o mesmo usuário
    suspiciousFactors.push('distributed_attack');
  }
  
  return suspiciousFactors;
}

/**
 * Registra uma tentativa de login para análise
 * @param req Objeto de requisição Express
 * @param username Nome de usuário tentado
 * @param successful Se a tentativa foi bem-sucedida
 * @returns Objeto com informações da tentativa e fatores suspeitos, se houver
 */
export function recordLoginAttempt(req: Request, username: string, successful: boolean): {
  isSuspicious: boolean;
  isBlocked: boolean;
  suspiciousFactors: string[];
  attemptDetails: LoginAttempt;
} {
  // Extrair informações da requisição
  const { ip, userAgent, country } = extractRequestInfo(req);
  
  // Preparar dados da tentativa
  const attemptData: Omit<LoginAttempt, 'suspiciousFactors'> = {
    username,
    timestamp: Date.now(),
    successful,
    ip,
    userAgent,
    country
  };
  
  // Verificar fatores suspeitos
  const suspiciousFactors = checkSuspiciousFactors(attemptData);
  
  // Criar o registro completo da tentativa
  const attempt: LoginAttempt = {
    ...attemptData,
    suspiciousFactors
  };
  
  // Gerar um ID único para esta tentativa
  const attemptId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  
  // Armazenar a tentativa em cache
  loginAttemptCache.set(attemptId, attempt);
  
  // Verificar se o usuário ou IP está bloqueado
  const isBlocked = isUserBlocked(username) || isIPBlocked(ip);
  
  // Se a tentativa for suspeita e falhar, considerar bloquear
  const isSuspicious = suspiciousFactors.length > 0;
  if (isSuspicious && !successful) {
    // Bloquear temporariamente após múltiplas tentativas suspeitas
    if (getRecentSuspiciousAttempts(username, ip).length >= 3) {
      blockUser(username, config.blockTimeMS);
      blockIP(ip, config.blockTimeMS);
    }
  }
  
  return {
    isSuspicious,
    isBlocked,
    suspiciousFactors,
    attemptDetails: attempt
  };
}

/**
 * Obtém tentativas recentes por nome de usuário
 * @param username Nome de usuário para filtrar
 * @param timeWindowMS Janela de tempo em milissegundos
 * @returns Array de tentativas
 */
function getAttemptsByUsername(username: string, timeWindowMS: number): LoginAttempt[] {
  const cutoffTime = Date.now() - timeWindowMS;
  const attempts: LoginAttempt[] = [];
  
  // Buscar no cache todas as tentativas para este usuário
  loginAttemptCache.keys().forEach(key => {
    const attempt = loginAttemptCache.get<LoginAttempt>(key);
    if (attempt && attempt.username === username && attempt.timestamp >= cutoffTime) {
      attempts.push(attempt);
    }
  });
  
  return attempts;
}

/**
 * Obtém tentativas recentes por endereço IP
 * @param ip Endereço IP para filtrar
 * @param timeWindowMS Janela de tempo em milissegundos
 * @returns Array de tentativas
 */
function getAttemptsByIP(ip: string, timeWindowMS: number): LoginAttempt[] {
  const cutoffTime = Date.now() - timeWindowMS;
  const attempts: LoginAttempt[] = [];
  
  // Buscar no cache todas as tentativas para este IP
  loginAttemptCache.keys().forEach(key => {
    const attempt = loginAttemptCache.get<LoginAttempt>(key);
    if (attempt && attempt.ip === ip && attempt.timestamp >= cutoffTime) {
      attempts.push(attempt);
    }
  });
  
  return attempts;
}

/**
 * Obtém tentativas recentes suspeitas para um usuário ou IP
 * @param username Nome de usuário
 * @param ip Endereço IP
 * @param timeWindowMS Janela de tempo em milissegundos (30 minutos por padrão)
 * @returns Array de tentativas suspeitas
 */
function getRecentSuspiciousAttempts(
  username: string, 
  ip: string, 
  timeWindowMS: number = 30 * 60 * 1000
): LoginAttempt[] {
  const cutoffTime = Date.now() - timeWindowMS;
  const attempts: LoginAttempt[] = [];
  
  // Buscar no cache todas as tentativas suspeitas
  loginAttemptCache.keys().forEach(key => {
    const attempt = loginAttemptCache.get<LoginAttempt>(key);
    if (
      attempt && 
      (attempt.username === username || attempt.ip === ip) && 
      attempt.timestamp >= cutoffTime &&
      attempt.suspiciousFactors.length > 0
    ) {
      attempts.push(attempt);
    }
  });
  
  return attempts;
}

// Caches para bloqueios temporários
const blockedUsers = new NodeCache({ stdTTL: 3600 }); // 1 hora padrão
const blockedIPs = new NodeCache({ stdTTL: 3600 });   // 1 hora padrão

/**
 * Bloqueia um usuário temporariamente
 * @param username Nome de usuário a bloquear
 * @param durationMS Duração do bloqueio em milissegundos
 */
function blockUser(username: string, durationMS: number): void {
  const expiresAt = Date.now() + durationMS;
  blockedUsers.set(username, expiresAt, Math.ceil(durationMS / 1000));
  console.log(`Usuário "${username}" bloqueado temporariamente até ${new Date(expiresAt).toLocaleString()}`);
}

/**
 * Bloqueia um endereço IP temporariamente
 * @param ip Endereço IP a bloquear
 * @param durationMS Duração do bloqueio em milissegundos
 */
function blockIP(ip: string, durationMS: number): void {
  const expiresAt = Date.now() + durationMS;
  blockedIPs.set(ip, expiresAt, Math.ceil(durationMS / 1000));
  console.log(`IP "${ip}" bloqueado temporariamente até ${new Date(expiresAt).toLocaleString()}`);
}

/**
 * Verifica se um usuário está temporariamente bloqueado
 * @param username Nome de usuário a verificar
 * @returns Verdadeiro se o usuário estiver bloqueado
 */
export function isUserBlocked(username: string): boolean {
  return blockedUsers.has(username);
}

/**
 * Verifica se um IP está temporariamente bloqueado
 * @param ip Endereço IP a verificar
 * @returns Verdadeiro se o IP estiver bloqueado
 */
export function isIPBlocked(ip: string): boolean {
  return blockedIPs.has(ip);
}

/**
 * Desbloqueia um usuário
 * @param username Nome de usuário a desbloquear
 */
export function unblockUser(username: string): void {
  blockedUsers.del(username);
  console.log(`Usuário "${username}" desbloqueado`);
}

/**
 * Desbloqueia um endereço IP
 * @param ip Endereço IP a desbloquear
 */
export function unblockIP(ip: string): void {
  blockedIPs.del(ip);
  console.log(`IP "${ip}" desbloqueado`);
}

/**
 * Limpa tentativas antigas de login do cache
 * @param olderThanMS Tempo em milissegundos (padrão: 24 horas)
 */
export function cleanupOldAttempts(olderThanMS: number = 24 * 60 * 60 * 1000): void {
  const cutoffTime = Date.now() - olderThanMS;
  let removedCount = 0;
  
  loginAttemptCache.keys().forEach(key => {
    const attempt = loginAttemptCache.get<LoginAttempt>(key);
    if (attempt && attempt.timestamp < cutoffTime) {
      loginAttemptCache.del(key);
      removedCount++;
    }
  });
  
  console.log(`Limpeza concluída: ${removedCount} tentativas antigas de login removidas`);
}

// Agendar limpeza diária de tentativas antigas
setInterval(() => {
  cleanupOldAttempts();
}, 24 * 60 * 60 * 1000); // Executar a cada 24 horas