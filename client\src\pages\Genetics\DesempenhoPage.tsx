import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useGeneticsContext } from "@/contexts/GeneticsContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, PlusCircle, Medal, BarChart2, LineChart, Table as TableIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

// Componentes de genética
import PerformanceChart from "@/components/genetics/PerformanceChart";

// Interfaces genéticas
import { RegistroDesempenho } from "@/types/genetica";

export default function DesempenhoPage() {
  const { selectedHorseId } = useGeneticsContext();
  const { toast } = useToast();

  // Interface para os cavalos
  interface Cavalo {
    id: number;
    name: string;
    breed: string;
  }

  // Buscar cavalos
  const {
    data: cavalos,
    isLoading: isLoadingCavalos,
    error: cavalosError,
  } = useQuery<Cavalo[]>({
    queryKey: ["/api/cavalos"],
  });

  // Buscar registros de desempenho do cavalo selecionado
  const {
    data: desempenhos,
    isLoading: isLoadingDesempenhos,
    error: desempenhosError,
  } = useQuery<RegistroDesempenho[]>({
    queryKey: ["/api/cavalos", selectedHorseId, "desempenho"],
    enabled: selectedHorseId !== null,
  });

  // Atualizar visualização quando mudar o cavalo
  useEffect(() => {
    if (cavalos && cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Mostrar erros
  useEffect(() => {
    if (cavalosError) {
      toast({
        title: "Erro ao carregar cavalos",
        description: "Não foi possível carregar a lista de cavalos.",
        variant: "destructive",
      });
    }
    if (desempenhosError) {
      toast({
        title: "Erro ao carregar registros de desempenho",
        description: "Não foi possível carregar o histórico de desempenho.",
        variant: "destructive",
      });
    }
  }, [cavalosError, desempenhosError, toast]);

  // Manipulador para adicionar novo registro
  const handleAdicionarRegistro = () => {
    toast({
      title: "Função em implementação",
      description: "Adicionar registro de desempenho será disponibilizado em breve.",
    });
  };

  // Classificar posição para cor de badge
  const getBadgeVariant = (posicao?: number) => {
    if (!posicao) return "outline";
    if (posicao === 1) return "destructive";
    if (posicao === 2) return "default";
    if (posicao === 3) return "secondary";
    return "outline";
  };

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-primary">Histórico de Desempenho</h2>
        <Button onClick={handleAdicionarRegistro}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Novo Registro
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Selecione um Cavalo</CardTitle>
          <CardDescription>
            Visualize o histórico completo de desempenho em competições e eventos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedHorseId?.toString() || ""}
            onValueChange={(value) => setSelectedHorseId(Number(value))}
          >
            <SelectTrigger className="w-full md:w-[300px]">
              <SelectValue placeholder="Selecione um cavalo" />
            </SelectTrigger>
            <SelectContent>
              {cavalos?.map((cavalo) => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name} ({cavalo.breed})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedHorseId && (
        <Card>
          <CardHeader>
            <CardTitle>
              Desempenho
              {cavalos && selectedHorseId && (
                <span className="text-primary">
                  {" "}
                  - {cavalos.find((c) => c.id === selectedHorseId)?.name}
                </span>
              )}
            </CardTitle>
            <CardDescription>
              Análise detalhada da performance em eventos, competições e treinamentos
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingDesempenhos ? (
              <div className="flex items-center justify-center py-10">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Carregando registros...</span>
              </div>
            ) : desempenhos && desempenhos.length > 0 ? (
              <Tabs defaultValue="tabela" className="w-full">
                <TabsList className="mb-6 grid grid-cols-2">
                  <TabsTrigger value="tabela" className="flex items-center justify-center">
                    <TableIcon className="w-4 h-4 mr-2" />
                    Tabela de Registros
                  </TabsTrigger>
                  <TabsTrigger value="graficos" className="flex items-center justify-center">
                    <BarChart2 className="w-4 h-4 mr-2" />
                    Visualização Gráfica
                  </TabsTrigger>
                </TabsList>
                
                {/* Tab com Tabela de Registros */}
                <TabsContent value="tabela">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Data</TableHead>
                        <TableHead>Evento</TableHead>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Categoria</TableHead>
                        <TableHead>Posição/Resultado</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {desempenhos.map((registro: RegistroDesempenho) => (
                        <TableRow key={registro.id}>
                          <TableCell>
                            {registro.dataEvento ? 
                              format(new Date(registro.dataEvento), "dd/MM/yyyy", {
                                locale: ptBR,
                              }) : 
                              (registro.data ? format(new Date(registro.data), "dd/MM/yyyy", {
                                locale: ptBR,
                              }) : "Data não informada")
                            }
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{registro.nomeEvento}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{registro.tipoEvento}</Badge>
                          </TableCell>
                          <TableCell>
                            {registro.categoria || "Não especificada"}
                          </TableCell>
                          <TableCell className="flex items-center">
                            {registro.posicao ? (
                              <Badge
                                variant={getBadgeVariant(registro.posicao)}
                                className="mr-2"
                              >
                                {registro.posicao}º lugar
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="mr-2">
                                Participação
                              </Badge>
                            )}
                            {registro.posicao && registro.posicao <= 3 && (
                              <Medal className="h-4 w-4 text-yellow-500" />
                            )}
                            {registro.tempoOuPontuacao && (
                              <span className="ml-2 text-sm text-muted-foreground">
                                ({registro.tempoOuPontuacao})
                              </span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TabsContent>
                
                {/* Tab com Visualização Gráfica */}
                <TabsContent value="graficos">
                  {/* Aqui vamos adaptar os dados do desempenho para o formato esperado pelo PerformanceChart */}
                  {(() => {
                    const selectedHorse = cavalos && cavalos.length > 0 
                      ? cavalos.find((c: Cavalo) => c.id === selectedHorseId) || null
                      : null;
                    
                    // Verificação de segurança para garantir que desempenhos existe
                    if (!desempenhos || !desempenhos.length) return null;
                    
                    // Converter os registros de desempenho para o formato esperado pelo PerformanceChart
                    const metricas = desempenhos.map((registro) => {
                      // Aqui estamos usando valores determinísticos baseados no ID
                      // para simular métricas consistentes entre renderizações
                      // Em produção, esses valores viriam diretamente do backend
                      const seed = registro.id % 100 / 100; // Valor entre 0 e 1 baseado no ID
                      
                      return {
                        id: registro.id,
                        horse_id: registro.horse_id,
                        date: registro.dataEvento || registro.data || "",
                        // Valores entre 5 e 10 baseados no ID para consistência
                        velocidade: 5 + (seed * 5),
                        resistencia: 5 + ((seed + 0.1) % 1 * 5),
                        agilidade: 5 + ((seed + 0.2) % 1 * 5),
                        forca: 5 + ((seed + 0.3) % 1 * 5),
                        temperamento: 5 + ((seed + 0.4) % 1 * 5),
                        observacoes: registro.observacoes || registro.desempenhoDetalhes
                      };
                    });
                    
                    return (
                      <PerformanceChart 
                        metrics={metricas} 
                        horseName={selectedHorse?.name || "Cavalo Selecionado"}
                      />
                    );
                  })()}
                </TabsContent>
              </Tabs>
            ) : (
              <div className="text-center py-10 text-muted-foreground">
                <p>Nenhum registro de desempenho cadastrado para este cavalo.</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={handleAdicionarRegistro}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Adicionar Primeiro Registro
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}