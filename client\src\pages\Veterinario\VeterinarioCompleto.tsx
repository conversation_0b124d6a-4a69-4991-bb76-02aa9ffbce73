import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Stethoscope, 
  Calendar,
  FileText,
  Pill,
  Activity,
  AlertTriangle,
  TrendingUp,
  Clock,
  Heart,
  PlusCircle,
  Search,
  Filter,
  Download
} from "lucide-react";
import { format, parseISO, isAfter, isBefore, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

// Tipos para as funcionalidades veterinárias
interface ConsultaMedica {
  id: number;
  horse_id: number;
  veterinarioNome: string;
  dataConsulta: string;
  motivoConsulta: string;
  diagnostico?: string;
  status: 'Agendada' | 'Em andamento' | 'Concluída' | 'Cancelada';
  urgencia: 'Baixa' | 'Normal' | 'Alta' | 'Crítica';
  custo?: number;
}

interface AlertaVeterinario {
  id: number;
  tipo: 'vacina_vencida' | 'consulta_pendente' | 'medicamento_acabando' | 'exame_atrasado';
  cavaloId: number;
  cavaloNome: string;
  mensagem: string;
  prioridade: 'Alta' | 'Média' | 'Baixa';
  dataLimite?: string;
}

/**
 * Dashboard Veterinário Completo
 * 
 * Página principal do módulo veterinário que oferece:
 * - Visão geral da saúde do plantel
 * - Alertas e lembretes importantes
 * - Acesso rápido às funcionalidades
 * - Estatísticas e relatórios
 */
export default function VeterinarioCompleto() {
  const [selectedTab, setSelectedTab] = useState("dashboard");

  // Buscar dados básicos para o dashboard
  const { data: cavalos = [] } = useQuery({
    queryKey: ['/api/cavalos'],
  });

  const { data: procedimentosVet = [] } = useQuery({
    queryKey: ['/api/procedimentos-vet'],
  });

  // Simular dados de consultas médicas (será implementado com dados reais)
  const consultasRecentes: ConsultaMedica[] = [
    {
      id: 1,
      horse_id: 129,
      veterinarioNome: "Dr. Carlos Veterinário",
      dataConsulta: "2025-05-20",
      motivoConsulta: "Consulta de rotina",
      diagnostico: "Animal saudável",
      status: "Concluída",
      urgencia: "Normal",
      custo: 150.00
    },
    {
      id: 2,
      horse_id: 130,
      veterinarioNome: "Dra. Maria Silva",
      dataConsulta: "2025-05-25",
      motivoConsulta: "Claudicação",
      status: "Agendada",
      urgencia: "Alta"
    }
  ];

  // Gerar alertas inteligentes baseados nos dados
  const gerarAlertas = (): AlertaVeterinario[] => {
    const alertas: AlertaVeterinario[] = [];
    const hoje = new Date();

    // Verificar vacinas vencidas com dados reais
    if (Array.isArray(procedimentosVet) && Array.isArray(cavalos)) {
      procedimentosVet
        .filter((p: any) => p.tipo?.toLowerCase().includes('vacina'))
        .forEach((proc: any) => {
          try {
            const dataProc = parseISO(proc.data);
            if (isBefore(addDays(dataProc, 365), hoje)) {
              const cavalo = cavalos.find((c: any) => c.id === proc.horse_id);
              if (cavalo) {
                alertas.push({
                  id: proc.id,
                  tipo: 'vacina_vencida',
                  cavaloId: cavalo.id,
                  cavaloNome: cavalo.name,
                  mensagem: `Vacina ${proc.tipo} vencida há mais de 1 ano`,
                  prioridade: 'Alta',
                  dataLimite: proc.data
                });
              }
            }
          } catch (error) {
            console.warn('Erro ao processar data do procedimento:', error);
          }
        });
    }

    return alertas;
  };

  const alertas = gerarAlertas();

  // Estatísticas do dashboard baseadas em dados reais
  const estatisticas = {
    totalCavalos: Array.isArray(cavalos) ? cavalos.length : 0,
    consultasEsteAno: consultasRecentes.length,
    alertasAtivos: alertas.length,
    consultasPendentes: consultasRecentes.filter(c => c.status === 'Agendada').length,
    custoMedioConsulta: consultasRecentes
      .filter(c => c.custo)
      .reduce((acc, c) => acc + (c.custo || 0), 0) / (consultasRecentes.filter(c => c.custo).length || 1)
  };

  // Dados para gráficos
  const dadosConsultasPorMes = [
    { mes: 'Jan', consultas: 8 },
    { mes: 'Fev', consultas: 12 },
    { mes: 'Mar', consultas: 15 },
    { mes: 'Abr', consultas: 10 },
    { mes: 'Mai', consultas: 18 }
  ];

  const dadosUrgenciaPieChart = [
    { name: 'Normal', value: 60, color: '#10b981' },
    { name: 'Alta', value: 25, color: '#f59e0b' },
    { name: 'Crítica', value: 15, color: '#ef4444' }
  ];

  const cores = ['#10b981', '#f59e0b', '#ef4444'];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Cabeçalho */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Stethoscope className="h-8 w-8 text-blue-600" />
            Centro Veterinário
          </h1>
          <p className="text-gray-600 mt-1">
            Gestão completa da saúde do seu plantel
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Relatório
          </Button>
          <Button className="gap-2">
            <PlusCircle className="h-4 w-4" />
            Nova Consulta
          </Button>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="consultas">Consultas</TabsTrigger>
          <TabsTrigger value="historico">Histórico</TabsTrigger>
          <TabsTrigger value="relatorios">Relatórios</TabsTrigger>
        </TabsList>

        {/* Dashboard Principal */}
        <TabsContent value="dashboard" className="space-y-6">
          {/* Cards de Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total de Cavalos</p>
                    <p className="text-2xl font-bold text-gray-900">{estatisticas.totalCavalos}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Heart className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Consultas Este Ano</p>
                    <p className="text-2xl font-bold text-gray-900">{estatisticas.consultasEsteAno}</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                    <Activity className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Alertas Ativos</p>
                    <p className="text-2xl font-bold text-red-600">{estatisticas.alertasAtivos}</p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Custo Médio/Consulta</p>
                    <p className="text-2xl font-bold text-gray-900">
                      R$ {estatisticas.custoMedioConsulta.toFixed(0)}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Alertas Importantes */}
          {alertas.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <AlertTriangle className="h-5 w-5" />
                  Alertas Importantes
                </CardTitle>
                <CardDescription>
                  Itens que requerem sua atenção imediata
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {alertas.map((alerta) => (
                    <div key={alerta.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-gray-900">{alerta.cavaloNome}</p>
                          <p className="text-sm text-gray-600">{alerta.mensagem}</p>
                        </div>
                      </div>
                      <Badge variant={alerta.prioridade === 'Alta' ? 'destructive' : 'secondary'}>
                        {alerta.prioridade}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Gráficos e Estatísticas */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Consultas por Mês</CardTitle>
                <CardDescription>
                  Evolução do número de consultas veterinárias
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={dadosConsultasPorMes}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="mes" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="consultas" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Distribuição por Urgência</CardTitle>
                <CardDescription>
                  Classificação das consultas por nível de urgência
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={dadosUrgenciaPieChart}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dadosUrgenciaPieChart.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={cores[index % cores.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Próximas Consultas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Próximas Consultas
              </CardTitle>
              <CardDescription>
                Consultas agendadas para os próximos dias
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {consultasRecentes
                  .filter(c => c.status === 'Agendada')
                  .map((consulta) => {
                    const cavalo = Array.isArray(cavalos) ? cavalos.find((c: any) => c.id === consulta.horse_id) : null;
                    return (
                      <div key={consulta.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Stethoscope className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{cavalo?.name}</p>
                            <p className="text-sm text-gray-600">{consulta.motivoConsulta}</p>
                            <p className="text-xs text-gray-500">
                              Dr. {consulta.veterinarioNome} • {format(parseISO(consulta.dataConsulta), 'dd/MM/yyyy', { locale: ptBR })}
                            </p>
                          </div>
                        </div>
                        <Badge 
                          variant={consulta.urgencia === 'Alta' ? 'destructive' : 
                                  consulta.urgencia === 'Crítica' ? 'destructive' : 'secondary'}
                        >
                          {consulta.urgencia}
                        </Badge>
                      </div>
                    );
                  })}
                {consultasRecentes.filter(c => c.status === 'Agendada').length === 0 && (
                  <p className="text-center text-gray-500 py-8">
                    Nenhuma consulta agendada para os próximos dias
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Consultas */}
        <TabsContent value="consultas">
          <Card>
            <CardHeader>
              <CardTitle>Gestão de Consultas</CardTitle>
              <CardDescription>
                Agende e gerencie consultas veterinárias
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Funcionalidade em Desenvolvimento
                </h3>
                <p className="text-gray-600 mb-6">
                  O sistema completo de consultas médicas será implementado em breve
                </p>
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Agendar Primeira Consulta
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Histórico */}
        <TabsContent value="historico">
          <Card>
            <CardHeader>
              <CardTitle>Histórico Médico</CardTitle>
              <CardDescription>
                Consulte o histórico médico completo dos animais
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Histórico Médico Centralizado
                </h3>
                <p className="text-gray-600 mb-6">
                  Acesse prontuários, exames e tratamentos de forma organizada
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Relatórios */}
        <TabsContent value="relatorios">
          <Card>
            <CardHeader>
              <CardTitle>Relatórios Veterinários</CardTitle>
              <CardDescription>
                Gere relatórios detalhados sobre a saúde do plantel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Relatórios Inteligentes
                </h3>
                <p className="text-gray-600 mb-6">
                  Análises estatísticas e relatórios personalizados
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}