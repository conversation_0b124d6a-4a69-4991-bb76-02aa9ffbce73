import { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardH<PERSON>er, 
  CardT<PERSON>le, 
  CardContent, 
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { 
  Ta<PERSON>, 
  <PERSON><PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings, 
  Users,
  Save,
  Upload
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/lib/queryClient';

interface UserProfile {
  id: number;
  name: string;
  username: string;
  email: string;
  avatar_url?: string;
}

export function ConfiguracoesPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: 0,
    name: '',
    username: '',
    email: '',
    avatar_url: ''
  });

  const [passwords, setPasswords] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [farmInfo, setFarmInfo] = useState({
    nomeFazenda: 'Fazenda Santa Maria',
    cnpj: '12.345.678/0001-90',
    cidade: 'Bagé - RS',
    telefone: '(53) 3242-1234'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [profileLoaded, setProfileLoaded] = useState(false);

  useEffect(() => {
    if (user && !profileLoaded) {
      setUserProfile({
        id: user.id,
        name: user.name || '',
        username: user.username || '',
        email: user.email || '',
        avatar_url: user.avatar_url || ''
      });
      setProfileLoaded(true);
    }
  }, [user, profileLoaded]);

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Verificar tamanho do arquivo (máximo 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: "Erro",
          description: "A imagem deve ter no máximo 2MB",
          variant: "destructive"
        });
        return;
      }

      // Verificar tipo do arquivo
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Erro", 
          description: "Apenas arquivos de imagem são permitidos",
          variant: "destructive"
        });
        return;
      }

      setAvatarFile(file);
      
      // Preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setUserProfile(prev => ({
          ...prev,
          avatar_url: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadAvatar = async (): Promise<string | null> => {
    if (!avatarFile) return null;

    const formData = new FormData();
    formData.append('avatar', avatarFile);

    try {
      const response = await apiRequest('/api/upload/avatar', 'POST', formData);
      return response.url || null;
    } catch (error) {
      console.error('Erro no upload do avatar:', error);
      throw new Error('Falha no upload da imagem');
    }
  };

  const saveProfile = async () => {
    setIsLoading(true);
    try {
      let avatarUrl: string | null = userProfile.avatar_url || null;

      // Upload do avatar se houver arquivo novo
      if (avatarFile) {
        const uploadResult = await uploadAvatar();
        avatarUrl = uploadResult || null;
      }

      // Salvar dados do perfil
      const profileData = {
        name: userProfile.name,
        email: userProfile.email,
        avatar_url: avatarUrl
      };

      console.log('Salvando perfil:', profileData);
      await apiRequest('/api/users/profile', 'PUT', profileData);

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso",
      });

      setAvatarFile(null);
    } catch (error) {
      console.error('Erro ao salvar perfil:', error);
      toast({
        title: "Erro",
        description: "Falha ao atualizar perfil",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async () => {
    if (passwords.newPassword !== passwords.confirmPassword) {
      toast({
        title: "Erro",
        description: "As senhas não coincidem",
        variant: "destructive"
      });
      return;
    }

    if (passwords.newPassword.length < 6) {
      toast({
        title: "Erro", 
        description: "A nova senha deve ter pelo menos 6 caracteres",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      await apiRequest('/api/users/change-password', 'PUT', {
        currentPassword: passwords.currentPassword,
        newPassword: passwords.newPassword
      });

      toast({
        title: "Sucesso",
        description: "Senha alterada com sucesso",
      });

      setPasswords({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      toast({
        title: "Erro",
        description: "Falha ao alterar senha. Verifique a senha atual.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveFarmInfo = async () => {
    setIsLoading(true);
    try {
      // Simular salvamento das informações da fazenda
      // Em uma implementação real, isso seria enviado para o backend
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular delay da API
      
      toast({
        title: "Sucesso",
        description: "Informações da fazenda salvas com sucesso",
      });
    } catch (error) {
      console.error('Erro ao salvar informações da fazenda:', error);
      toast({
        title: "Erro",
        description: "Falha ao salvar informações da fazenda",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
        <p className="text-muted-foreground">
          Gerencie suas informações pessoais e configurações de conta
        </p>
      </div>

      <Tabs defaultValue="geral" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="geral" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Geral</span>
          </TabsTrigger>
          <TabsTrigger value="perfil" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Perfil</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Geral - Informações da Fazenda */}
        <TabsContent value="geral">
          <Card>
            <CardHeader>
              <CardTitle>Informações da Fazenda</CardTitle>
              <CardDescription>
                Configure as informações básicas da fazenda/haras
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="nome-fazenda">Nome da Fazenda</Label>
                    <Input
                      id="nome-fazenda"
                      defaultValue="Fazenda Santa Maria"
                      placeholder="Nome da fazenda/haras"
                    />
                  </div>
                  <div>
                    <Label htmlFor="cnpj">CNPJ</Label>
                    <Input
                      id="cnpj"
                      defaultValue="12.345.678/0001-90"
                      placeholder="00.000.000/0000-00"
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="cidade">Cidade</Label>
                    <Input
                      id="cidade"
                      defaultValue="Bagé - RS"
                      placeholder="Cidade - Estado"
                    />
                  </div>
                  <div>
                    <Label htmlFor="telefone">Telefone</Label>
                    <Input
                      id="telefone"
                      defaultValue="(53) 3242-1234"
                      placeholder="(00) 0000-0000"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                Salvar Informações
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Tab Perfil - Dados do Usuário */}
        <TabsContent value="perfil">
          <div className="space-y-6">
            {/* Dados Pessoais */}
            <Card>
              <CardHeader>
                <CardTitle>Dados Pessoais</CardTitle>
                <CardDescription>
                  Atualize suas informações pessoais e foto de perfil
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Upload de Avatar */}
                <div className="flex items-center space-x-6">
                  <div className="relative">
                    <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                      {userProfile.avatar_url ? (
                        <img 
                          src={userProfile.avatar_url} 
                          alt="Avatar"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Users className="h-12 w-12 text-muted-foreground" />
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="avatar-upload" className="cursor-pointer">
                      <Button variant="outline" size="sm" asChild>
                        <span>
                          <Upload className="mr-2 h-4 w-4" />
                          Alterar Foto
                        </span>
                      </Button>
                    </Label>
                    <input
                      id="avatar-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                    />
                    <p className="text-xs text-muted-foreground">
                      JPG, PNG ou GIF. Máximo 2MB.
                    </p>
                  </div>
                </div>

                {/* Campos do Perfil */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input 
                      id="name"
                      value={userProfile.name}
                      onChange={(e) => setUserProfile(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Seu nome completo"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">E-mail</Label>
                    <Input 
                      id="email"
                      type="email"
                      value={userProfile.email}
                      onChange={(e) => setUserProfile(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="username">Nome de Usuário</Label>
                    <Input 
                      id="username"
                      value={userProfile.username}
                      disabled
                      className="bg-muted"
                      placeholder="Nome de usuário (não pode ser alterado)"
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveProfile} disabled={isLoading}>
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Salvando...' : 'Salvar Perfil'}
                </Button>
              </CardFooter>
            </Card>

            {/* Alterar Senha */}
            <Card>
              <CardHeader>
                <CardTitle>Alterar Senha</CardTitle>
                <CardDescription>
                  Mantenha sua conta segura com uma senha forte
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="current-password">Senha Atual</Label>
                  <Input 
                    id="current-password"
                    type="password"
                    value={passwords.currentPassword}
                    onChange={(e) => setPasswords(prev => ({ ...prev, currentPassword: e.target.value }))}
                    placeholder="Digite sua senha atual"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="new-password">Nova Senha</Label>
                    <Input 
                      id="new-password"
                      type="password"
                      value={passwords.newPassword}
                      onChange={(e) => setPasswords(prev => ({ ...prev, newPassword: e.target.value }))}
                      placeholder="Digite a nova senha"
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirm-password">Confirmar Nova Senha</Label>
                    <Input 
                      id="confirm-password"
                      type="password"
                      value={passwords.confirmPassword}
                      onChange={(e) => setPasswords(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="Confirme a nova senha"
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={changePassword} 
                  disabled={isLoading || !passwords.currentPassword || !passwords.newPassword}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Alterando...' : 'Alterar Senha'}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}