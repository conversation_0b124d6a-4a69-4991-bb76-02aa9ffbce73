import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

/**
 * A11yDialogContext
 * 
 * Contexto para gerenciar o estado de acessibilidade dos diálogos
 */
interface A11yDialogContextType {
  activeDialogId: string | null;
  registerDialog: (id: string) => void;
  unregisterDialog: (id: string) => void;
  setActiveDialog: (id: string | null) => void;
}

const A11yDialogContext = React.createContext<A11yDialogContextType>({
  activeDialogId: null,
  registerDialog: () => {},
  unregisterDialog: () => {},
  setActiveDialog: () => {},
});

/**
 * A11yDialogProvider
 * 
 * Provedor de contexto para gerenciar diálogos acessíveis
 */
export function A11yDialogProvider({ children }: { children: React.ReactNode }) {
  const [activeDialogId, setActiveDialogId] = React.useState<string | null>(null);
  const [registeredDialogs, setRegisteredDialogs] = React.useState<Set<string>>(new Set());
  
  // Gerenciar foco quando um diálogo é aberto/fechado
  React.useEffect(() => {
    if (activeDialogId) {
      // Salvar o elemento que tinha foco antes de abrir o diálogo
      const previouslyFocused = document.activeElement as HTMLElement;
      
      // Quando o diálogo for fechado, restaurar o foco
      return () => {
        if (previouslyFocused && previouslyFocused.focus) {
          previouslyFocused.focus();
        }
      };
    }
  }, [activeDialogId]);
  
  // Gerenciar eventos de teclado para acessibilidade
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Se não houver diálogo ativo, não fazer nada
      if (!activeDialogId) return;
      
      // Fechar o diálogo com Escape
      if (event.key === 'Escape') {
        setActiveDialogId(null);
        event.preventDefault();
      }
      
      // Manter o foco dentro do diálogo com Tab
      if (event.key === 'Tab') {
        const dialog = document.getElementById(activeDialogId);
        if (!dialog) return;
        
        const focusableElements = dialog.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        // Se Shift+Tab e o primeiro elemento está focado, mover para o último
        if (event.shiftKey && document.activeElement === firstElement) {
          lastElement.focus();
          event.preventDefault();
        }
        // Se Tab e o último elemento está focado, mover para o primeiro
        else if (!event.shiftKey && document.activeElement === lastElement) {
          firstElement.focus();
          event.preventDefault();
        }
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [activeDialogId]);
  
  const registerDialog = React.useCallback((id: string) => {
    setRegisteredDialogs(prev => {
      const newSet = new Set(prev);
      newSet.add(id);
      return newSet;
    });
  }, []);
  
  const unregisterDialog = React.useCallback((id: string) => {
    setRegisteredDialogs(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
    
    // Se o diálogo sendo removido for o ativo, limpar o ativo
    if (activeDialogId === id) {
      setActiveDialogId(null);
    }
  }, [activeDialogId]);
  
  const setActiveDialog = React.useCallback((id: string | null) => {
    setActiveDialogId(id);
  }, []);
  
  const value = React.useMemo(() => ({
    activeDialogId,
    registerDialog,
    unregisterDialog,
    setActiveDialog,
  }), [activeDialogId, registerDialog, unregisterDialog, setActiveDialog]);
  
  return (
    <A11yDialogContext.Provider value={value}>
      {children}
    </A11yDialogContext.Provider>
  );
}

/**
 * useA11yDialog
 * 
 * Hook para usar o contexto de diálogos acessíveis
 */
export function useA11yDialog() {
  return React.useContext(A11yDialogContext);
}

/**
 * A11yDialog
 * 
 * Componente de diálogo acessível
 */
interface A11yDialogProps {
  id: string;
  title: string;
  description?: string;
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

export function A11yDialog({
  id,
  title,
  description,
  isOpen,
  onClose,
  children,
  className,
}: A11yDialogProps) {
  const { registerDialog, unregisterDialog, setActiveDialog } = useA11yDialog();
  const isMobile = useIsMobile();
  
  // Registrar o diálogo quando montado
  React.useEffect(() => {
    registerDialog(id);
    return () => unregisterDialog(id);
  }, [id, registerDialog, unregisterDialog]);
  
  // Atualizar o diálogo ativo quando aberto/fechado
  React.useEffect(() => {
    if (isOpen) {
      setActiveDialog(id);
    } else if (!isOpen) {
      setActiveDialog(null);
    }
  }, [id, isOpen, setActiveDialog]);
  
  // Se não estiver aberto, não renderizar
  if (!isOpen) return null;
  
  return (
    <div
      id={id}
      role="dialog"
      aria-modal="true"
      aria-labelledby={`${id}-title`}
      aria-describedby={description ? `${id}-description` : undefined}
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        isMobile ? "p-0" : "p-4",
        className
      )}
    >
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Conteúdo do diálogo */}
      <div
        className={cn(
          "relative bg-background rounded-lg shadow-lg w-full overflow-y-auto",
          isMobile
            ? "h-[calc(100dvh-1rem)] m-2 rounded-none"
            : "max-w-md m-4"
        )}
      >
        {/* Cabeçalho */}
        <div className="p-4 border-b">
          <h2 id={`${id}-title`} className="text-lg font-semibold">
            {title}
          </h2>
          {description && (
            <p id={`${id}-description`} className="text-sm text-muted-foreground mt-1">
              {description}
            </p>
          )}
        </div>
        
        {/* Corpo */}
        <div className="p-4">
          {children}
        </div>
        
        {/* Botão de fechar */}
        <button
          type="button"
          onClick={onClose}
          aria-label="Fechar"
          className={cn(
            "absolute top-3 right-3 p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
            isMobile && "top-4 right-4"
          )}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  );
}
