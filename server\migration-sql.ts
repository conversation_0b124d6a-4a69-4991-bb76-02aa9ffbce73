// Script para executar migração SQL manual para adicionar a coluna is_external
import { pool } from './db';
import { getModuleLogger } from './logger';

const logger = getModuleLogger('migration-sql');

/**
 * Executa migração SQL para adicionar a coluna is_external à tabela cavalos
 */
export async function executeMigrationSql() {
  logger.info('Iniciando migração SQL para adicionar coluna is_external');
  
  const client = await pool.connect();
  
  try {
    // Verificar se a coluna já existe
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'cavalos' AND column_name = 'is_external'
    `;
    
    const checkResult = await client.query(checkColumnQuery);
    
    if (checkResult.rows.length === 0) {
      logger.info('Coluna is_external não existe. Adicionando coluna...');
      
      // Adicionar a coluna is_external
      const addColumnQuery = `
        ALTER TABLE cavalos 
        ADD COLUMN is_external BOOLEAN DEFAULT FALSE
      `;
      
      await client.query(addColumnQuery);
      logger.info('Coluna is_external adicionada com sucesso!');
      
      // Atualizar todos os cavalos que são referenciados como pais ou mães
      const updateQuery = `
        UPDATE cavalos 
        SET is_external = TRUE 
        WHERE id IN (
          SELECT DISTINCT pai_id FROM cavalos WHERE pai_id IS NOT NULL
          UNION
          SELECT DISTINCT mae_id FROM cavalos WHERE mae_id IS NOT NULL
        )
      `;
      
      const updateResult = await client.query(updateQuery);
      logger.info(`Atualizados ${updateResult.rowCount} cavalos para externos`);
      
      return true;
    } else {
      logger.info('Coluna is_external já existe. Pulando migração.');
      return true;
    }
  } catch (error: any) {
    logger.error(`Erro na migração SQL: ${error.message}`);
    return false;
  } finally {
    client.release();
  }
}

export default executeMigrationSql;