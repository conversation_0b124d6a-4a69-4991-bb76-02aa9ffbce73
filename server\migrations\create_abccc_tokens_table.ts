/**
 * Script de migração para criar a tabela de tokens da ABCCC
 */
import { db } from "../db";
import { sql } from "drizzle-orm";
import { getModuleLogger } from "../logger";

const migrationsLogger = getModuleLogger('migration-abccc-tokens');

export async function createAbcccTokensTable(): Promise<boolean> {
  try {
    // Verificar se a tabela já existe
    const result = await db.execute(sql`
      SELECT COUNT(*) > 0 as exists
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      AND table_name = 'abccc_tokens';
    `);
    
    // O resultado será um array de objetos com a propriedade 'exists'
    // @ts-ignore - Ignorando erro de tipagem para verificar existência da tabela
    const tableExists = result.rows && result.rows[0] && result.rows[0].exists === true;
    
    if (tableExists) {
      migrationsLogger.info('Tabela "abccc_tokens" já existe. Pulando migração.');
      return false;
    }
    
    // Criar a tabela
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS abccc_tokens (
        id SERIAL PRIMARY KEY,
        token VARCHAR(255) NOT NULL UNIQUE,
        valido BOOLEAN NOT NULL DEFAULT TRUE,
        ultimo_uso TIMESTAMP DEFAULT NOW(),
        sucessos INTEGER NOT NULL DEFAULT 0,
        falhas INTEGER NOT NULL DEFAULT 0,
        origem VARCHAR(50) NOT NULL DEFAULT 'manual',
        observacoes TEXT,
        criado_em TIMESTAMP DEFAULT NOW(),
        atualizado_em TIMESTAMP DEFAULT NOW()
      );
    `);
    
    migrationsLogger.info('Tabela "abccc_tokens" criada com sucesso!');
    return true;
  } catch (error) {
    migrationsLogger.error(`Erro ao criar tabela "abccc_tokens": ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}