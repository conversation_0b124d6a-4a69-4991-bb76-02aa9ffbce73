import { useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DollarSign, Plus, ArrowUpDown, Filter, FileDown, Search, CreditCard, Wallet, Building, ArrowDownRight, ArrowUpRight } from 'lucide-react';
import { formatMoney, getContaIcon } from "@/utils/finance";
import { LayoutWrapper } from "@/components/Layout";
import { useToast } from "@/hooks/use-toast";
import { DatePicker } from "@/components/DatePicker";

/**
 * Página principal do módulo financeiro
 * 
 * Exibe uma visão geral das finanças com totais de receitas, despesas,
 * saldo e permite acessar os submodules (lançamentos, centros de custo, etc.)
 */
export default function FinanceiroPage() {
  const [location, navigate] = useLocation();
  const [periodoInicio, setPeriodoInicio] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1) // Primeiro dia do mês atual
  );
  const [periodoFim, setPeriodoFim] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0) // Último dia do mês atual
  );
  const { toast } = useToast();
  
  // Dados simulados para demonstração
  const resumoFinanceiro = {
    receitas: 15850.00,
    despesas: 8320.50,
    saldo: 7529.50,
    contas: [
      { id: 1, nome: "Banco do Brasil", saldo: 5340.20, tipo: "Conta Corrente" },
      { id: 2, nome: "Caixa", saldo: 2189.30, tipo: "Dinheiro" },
      { id: 3, nome: "Banco Itaú", saldo: 0, tipo: "Conta Corrente" }
    ],
    categorias: [
      { id: 1, nome: "Alimentação", valor: 3250.80, tipo: "despesa" },
      { id: 2, nome: "Medicamentos", valor: 1840.30, tipo: "despesa" },
      { id: 3, nome: "Serviços Veterinários", valor: 2100.00, tipo: "despesa" },
      { id: 4, nome: "Ferrageamento", valor: 1129.40, tipo: "despesa" },
      { id: 5, nome: "Pensão", valor: 8500.00, tipo: "receita" },
      { id: 6, nome: "Serviços", valor: 7350.00, tipo: "receita" }
    ]
  };
  
  
  // Função para mudar a cor baseada no tipo de transação
  const getColorClass = (tipo: string, forText = true) => {
    if (tipo === 'receita') {
      return forText ? 'text-green-600' : 'bg-green-100 text-green-700';
    } else if (tipo === 'despesa') {
      return forText ? 'text-red-600' : 'bg-red-100 text-red-700';
    }
    return forText ? 'text-blue-600' : 'bg-blue-100 text-blue-700';
  };
  
  
  return (
    <LayoutWrapper pageTitle="Financeiro" showBackButton>
      <div className="container mx-auto p-4">
        {/* Seção de ações principais */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div className="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
            <div className="flex gap-3">
              <Link href="/financeiro/lancamentos">
                <Button variant="default" className="bg-green-600 hover:bg-green-700">
                  <Plus className="mr-2 h-4 w-4" /> Novo Lançamento
                </Button>
              </Link>
              
              <Button variant="outline" className="border-blue-200">
                <FileDown className="mr-2 h-4 w-4" /> Exportar
              </Button>
            </div>
            
            <div className="flex gap-3">
              <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-md">
                <DatePicker
                  date={periodoInicio}
                  onDateChange={setPeriodoInicio}
                  buttonClassName="border-0 h-8 hover:bg-gray-100"
                  placeholder="Data inicial"
                />
                <span className="text-gray-400">a</span>
                <DatePicker
                  date={periodoFim}
                  onDateChange={setPeriodoFim}
                  buttonClassName="border-0 h-8 hover:bg-gray-100"
                  placeholder="Data final"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* Resumo financeiro em cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border border-green-100 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-green-700 flex items-center">
                <ArrowDownRight className="h-5 w-5 mr-1.5 text-green-600" />
                Receitas
              </CardTitle>
              <CardDescription>Total de receitas no período</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600">
                {formatMoney(resumoFinanceiro.receitas)}
              </div>
            </CardContent>
          </Card>
          
          <Card className="border border-red-100 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-red-700 flex items-center">
                <ArrowUpRight className="h-5 w-5 mr-1.5 text-red-600" />
                Despesas
              </CardTitle>
              <CardDescription>Total de despesas no período</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-600">
                {formatMoney(resumoFinanceiro.despesas)}
              </div>
            </CardContent>
          </Card>
          
          <Card className="border border-blue-100 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-blue-700 flex items-center">
                <DollarSign className="h-5 w-5 mr-1.5 text-blue-600" />
                Saldo
              </CardTitle>
              <CardDescription>Saldo resultante no período</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">
                {formatMoney(resumoFinanceiro.saldo)}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Tabs de visualização */}
        <Tabs defaultValue="contas" className="mb-8">
          <TabsList className="bg-gray-100 border">
            <TabsTrigger value="contas">Contas Financeiras</TabsTrigger>
            <TabsTrigger value="categorias">Categorias</TabsTrigger>
          </TabsList>
          
          <TabsContent value="contas" className="mt-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl">Contas Financeiras</CardTitle>
                  <Link href="/financeiro/contas">
                    <Button variant="outline" size="sm">
                      <Plus className="mr-1 h-4 w-4" /> Nova Conta
                    </Button>
                  </Link>
                </div>
                <CardDescription>Saldos atuais das suas contas financeiras</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {resumoFinanceiro.contas.map(conta => (
                    <div key={conta.id} className="flex justify-between items-center p-3 rounded-lg border bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div className="flex items-center">
                        <div className="p-2 rounded-full bg-blue-100 mr-3">
                          {getContaIcon(conta.tipo)}
                        </div>
                        <div>
                          <h3 className="font-medium">{conta.nome}</h3>
                          <p className="text-xs text-gray-500">{conta.tipo}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="font-semibold">
                          {formatMoney(conta.saldo)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-end">
                <Link href="/financeiro/contas">
                  <Button variant="ghost" size="sm">
                    Ver Todas as Contas
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="categorias" className="mt-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl">Categorias</CardTitle>
                  <Link href="/financeiro/centros-custos">
                    <Button variant="outline" size="sm">
                      <Plus className="mr-1 h-4 w-4" /> Nova Categoria
                    </Button>
                  </Link>
                </div>
                <CardDescription>Distribuição por categorias no período</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {resumoFinanceiro.categorias.map(categoria => (
                    <div key={categoria.id} className="flex justify-between items-center p-3 rounded-lg border bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div className="flex items-center">
                        <div className={`p-2 rounded-full ${getColorClass(categoria.tipo, false)} mr-3`}>
                          {categoria.tipo === 'receita' 
                            ? <ArrowDownRight className="h-4 w-4" /> 
                            : <ArrowUpRight className="h-4 w-4" />}
                        </div>
                        <div>
                          <h3 className="font-medium">{categoria.nome}</h3>
                          <p className="text-xs text-gray-500">
                            {categoria.tipo === 'receita' ? 'Receita' : 'Despesa'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`font-semibold ${getColorClass(categoria.tipo)}`}>
                          {formatMoney(categoria.valor)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-end">
                <Link href="/financeiro/centros-custos">
                  <Button variant="ghost" size="sm">
                    Ver Todas as Categorias
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
        
        {/* Links rápidos para os módulos financeiros */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/financeiro/lancamentos" className="block">
            <Card className="h-full border-blue-100 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-700">
                  <ArrowUpDown className="h-5 w-5 mr-2" />
                  Lançamentos
                </CardTitle>
                <CardDescription>
                  Gerencie receitas e despesas
                </CardDescription>
              </CardHeader>
            </Card>
          </Link>
          
          <Link href="/financeiro/centros-custos" className="block">
            <Card className="h-full border-blue-100 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-700">
                  <Filter className="h-5 w-5 mr-2" />
                  Centros de Custos
                </CardTitle>
                <CardDescription>
                  Categorias de receitas e despesas
                </CardDescription>
              </CardHeader>
            </Card>
          </Link>
          
          <Link href="/financeiro/contas" className="block">
            <Card className="h-full border-blue-100 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-700">
                  <Wallet className="h-5 w-5 mr-2" />
                  Contas
                </CardTitle>
                <CardDescription>
                  Gerencie suas contas financeiras
                </CardDescription>
              </CardHeader>
            </Card>
          </Link>
          
          <Link href="/financeiro/demonstrativos" className="block">
            <Card className="h-full border-blue-100 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-700">
                  <FileDown className="h-5 w-5 mr-2" />
                  Demonstrativos
                </CardTitle>
                <CardDescription>
                  Relatórios financeiros
                </CardDescription>
              </CardHeader>
            </Card>
          </Link>
        </div>
      </div>
    </LayoutWrapper>
  );
}