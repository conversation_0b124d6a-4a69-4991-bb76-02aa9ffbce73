import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Save, Printer, Ruler, ThermometerSnowflake, Clipboard, ArrowLeft, ArrowRight, Camera, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

// URL da imagem do esqueleto
const horseSkeletonImage = '/horse-skeleton-anatomy.png';

// SVG representando um cavalo com pontos de medição
const HorseMeasurementSVG = ({ medidas, highlighted = null }) => {
  // Função auxiliar para exibir o valor ou '?' se estiver vazio
  const displayValue = (value) => {
    return value !== '' && value !== undefined ? value : '?';
  };

  // Função para calcular os índices morfométricos
  const calcularIndices = () => {
    const indices = {};

    // Índice Corporal ou Relativo
    if (medidas.comprimentoCorpo && medidas.perimetroToracico) {
      indices.corporal = (medidas.comprimentoCorpo / medidas.perimetroToracico * 100).toFixed(2);
    }

    // Índice Torácico
    if (medidas.perimetroToracico && medidas.alturaCernelha) {
      indices.toracico = (medidas.perimetroToracico / medidas.alturaCernelha * 100).toFixed(2);
    }

    // Índice de Compacidade
    if (medidas.perimetroToracico && medidas.alturaCernelha) {
      indices.compacidade = (medidas.perimetroToracico * medidas.perimetroToracico / medidas.alturaCernelha).toFixed(2);
    }

    // Índice Dáctilo-Torácico
    if (medidas.perimetroCanela && medidas.perimetroToracico) {
      indices.dactiloToracico = (medidas.perimetroCanela / medidas.perimetroToracico * 100).toFixed(2);
    }

    // Índice Cefálico
    if (medidas.larguraCabeca && medidas.comprimentoCabeca) {
      indices.cefalico = (medidas.larguraCabeca / medidas.comprimentoCabeca * 100).toFixed(2);
    }

    return indices;
  };

  const indices = calcularIndices();

  return (
    <svg viewBox="0 0 800 600" className="w-full max-w-[700px]">
      {/* Fundo branco para melhorar a visibilidade */}
      <rect x="0" y="0" width="800" height="600" fill="white" />

      {/* Imagem de fundo do esqueleto */}
      <image href={horseSkeletonImage} width="700" height="400" x="50" y="100" opacity="0.9" preserveAspectRatio="xMidYMid meet" />

      {/* Pontos de medição com círculos, linhas e rótulos */}
      {/* Altura na cernelha */}
      <line x1="320" y1="200" x2="320" y2="480" stroke="#3b82f6" strokeWidth="3" strokeDasharray={highlighted === 'alturaCernelha' ? "none" : "5,5"} />
      <circle cx="320" cy="200" r={highlighted === 'alturaCernelha' ? "14" : "10"} fill={highlighted === 'alturaCernelha' ? "#3b82f6" : "#6b7280"} />
      <rect x="150" y="160" width="170" height="30" rx="5" fill={highlighted === 'alturaCernelha' ? "rgba(59, 130, 246, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'alturaCernelha' ? "#3b82f6" : "#6b7280"} strokeWidth="1" />
      <text x="160" y="180" fontFamily="sans-serif" fontSize="16" fontWeight="bold" fill={highlighted === 'alturaCernelha' ? "#3b82f6" : "#6b7280"}>Cernelha: {displayValue(medidas.alturaCernelha)} cm</text>

      {/* Comprimento do corpo */}
      <line x1="200" y1="270" x2="600" y2="270" stroke="#10b981" strokeWidth="3" strokeDasharray={highlighted === 'comprimentoCorpo' ? "none" : "5,5"} />
      <circle cx="200" cy="270" r={highlighted === 'comprimentoCorpo' ? "14" : "10"} fill={highlighted === 'comprimentoCorpo' ? "#10b981" : "#6b7280"} />
      <circle cx="600" cy="270" r={highlighted === 'comprimentoCorpo' ? "14" : "10"} fill={highlighted === 'comprimentoCorpo' ? "#10b981" : "#6b7280"} />
      <rect x="300" y="240" width="210" height="30" rx="5" fill={highlighted === 'comprimentoCorpo' ? "rgba(16, 185, 129, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'comprimentoCorpo' ? "#10b981" : "#6b7280"} strokeWidth="1" />
      <text x="310" y="260" fontFamily="sans-serif" fontSize="16" fontWeight="bold" fill={highlighted === 'comprimentoCorpo' ? "#10b981" : "#6b7280"}>Comprimento: {displayValue(medidas.comprimentoCorpo)} cm</text>

      {/* Perímetro torácico */}
      <ellipse cx="320" cy="280" rx="80" ry="50" stroke="#ef4444" strokeWidth="3" strokeDasharray={highlighted === 'perimetroToracico' ? "none" : "5,5"} fill="none" />
      <rect x="410" y="280" width="200" height="30" rx="5" fill={highlighted === 'perimetroToracico' ? "rgba(239, 68, 68, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'perimetroToracico' ? "#ef4444" : "#6b7280"} strokeWidth="1" />
      <text x="420" y="300" fontFamily="sans-serif" fontSize="18" fontWeight="bold" fill={highlighted === 'perimetroToracico' ? "#ef4444" : "#6b7280"}>Perímetro: {displayValue(medidas.perimetroToracico)} cm</text>

      {/* Comprimento da garupa */}
      <line x1="500" y1="240" x2="600" y2="240" stroke="#8b5cf6" strokeWidth="3" strokeDasharray={highlighted === 'comprimentoGarupa' ? "none" : "5,5"} />
      <circle cx="500" cy="240" r={highlighted === 'comprimentoGarupa' ? "14" : "10"} fill={highlighted === 'comprimentoGarupa' ? "#8b5cf6" : "#6b7280"} />
      <circle cx="600" cy="240" r={highlighted === 'comprimentoGarupa' ? "14" : "10"} fill={highlighted === 'comprimentoGarupa' ? "#8b5cf6" : "#6b7280"} />
      <rect x="450" y="200" width="180" height="30" rx="5" fill={highlighted === 'comprimentoGarupa' ? "rgba(139, 92, 246, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'comprimentoGarupa' ? "#8b5cf6" : "#6b7280"} strokeWidth="1" />
      <text x="460" y="220" fontFamily="sans-serif" fontSize="18" fontWeight="bold" fill={highlighted === 'comprimentoGarupa' ? "#8b5cf6" : "#6b7280"}>Garupa: {displayValue(medidas.comprimentoGarupa)} cm</text>

      {/* Perímetro da canela */}
      <circle cx="280" cy="420" r="18" stroke="#f59e0b" strokeWidth="3" strokeDasharray={highlighted === 'perimetroCanela' ? "none" : "5,5"} fill="none" />
      <rect x="100" y="420" width="180" height="30" rx="5" fill={highlighted === 'perimetroCanela' ? "rgba(245, 158, 11, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'perimetroCanela' ? "#f59e0b" : "#6b7280"} strokeWidth="1" />
      <text x="110" y="440" fontFamily="sans-serif" fontSize="18" fontWeight="bold" fill={highlighted === 'perimetroCanela' ? "#f59e0b" : "#6b7280"}>Canela: {displayValue(medidas.perimetroCanela)} cm</text>

      {/* Comprimento da cabeça */}
      <line x1="120" y1="180" x2="220" y2="180" stroke="#ec4899" strokeWidth="3" strokeDasharray={highlighted === 'comprimentoCabeca' ? "none" : "5,5"} />
      <circle cx="120" cy="180" r={highlighted === 'comprimentoCabeca' ? "14" : "10"} fill={highlighted === 'comprimentoCabeca' ? "#ec4899" : "#6b7280"} />
      <circle cx="220" cy="180" r={highlighted === 'comprimentoCabeca' ? "14" : "10"} fill={highlighted === 'comprimentoCabeca' ? "#ec4899" : "#6b7280"} />
      <rect x="60" y="140" width="180" height="30" rx="5" fill={highlighted === 'comprimentoCabeca' ? "rgba(236, 72, 153, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'comprimentoCabeca' ? "#ec4899" : "#6b7280"} strokeWidth="1" />
      <text x="70" y="160" fontFamily="sans-serif" fontSize="18" fontWeight="bold" fill={highlighted === 'comprimentoCabeca' ? "#ec4899" : "#6b7280"}>Cabeça: {displayValue(medidas.comprimentoCabeca)} cm</text>

      {/* Largura da cabeça */}
      <line x1="170" y1="150" x2="170" y2="210" stroke="#06b6d4" strokeWidth="3" strokeDasharray={highlighted === 'larguraCabeca' ? "none" : "5,5"} />
      <circle cx="170" cy="150" r={highlighted === 'larguraCabeca' ? "14" : "10"} fill={highlighted === 'larguraCabeca' ? "#06b6d4" : "#6b7280"} />
      <circle cx="170" cy="210" r={highlighted === 'larguraCabeca' ? "14" : "10"} fill={highlighted === 'larguraCabeca' ? "#06b6d4" : "#6b7280"} />
      <rect x="60" y="200" width="180" height="30" rx="5" fill={highlighted === 'larguraCabeca' ? "rgba(6, 182, 212, 0.1)" : "rgba(107, 114, 128, 0.1)"} stroke={highlighted === 'larguraCabeca' ? "#06b6d4" : "#6b7280"} strokeWidth="1" />
      <text x="70" y="220" fontFamily="sans-serif" fontSize="18" fontWeight="bold" fill={highlighted === 'larguraCabeca' ? "#06b6d4" : "#6b7280"}>Largura: {displayValue(medidas.larguraCabeca)} cm</text>

      {/* Índices Morfométricos */}
      <rect x="50" y="500" width="700" height="120" rx="10" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" />
      <text x="60" y="525" fontFamily="sans-serif" fontSize="20" fontWeight="bold" fill="#3b82f6">Índices Morfométricos:</text>

      <text x="60" y="555" fontFamily="sans-serif" fontSize="18" fill="#4b5563">Índice Corporal: <tspan fontWeight="bold">{indices.corporal || '?'}</tspan></text>
      <text x="240" y="555" fontFamily="sans-serif" fontSize="18" fill="#4b5563">Índice Torácico: <tspan fontWeight="bold">{indices.toracico || '?'}</tspan></text>
      <text x="420" y="555" fontFamily="sans-serif" fontSize="18" fill="#4b5563">Índice de Compacidade: <tspan fontWeight="bold">{indices.compacidade || '?'}</tspan></text>
      <text x="60" y="585" fontFamily="sans-serif" fontSize="18" fill="#4b5563">Índice Dáctilo-Torácico: <tspan fontWeight="bold">{indices.dactiloToracico || '?'}</tspan></text>
      <text x="420" y="585" fontFamily="sans-serif" fontSize="18" fill="#4b5563">Índice Cefálico: <tspan fontWeight="bold">{indices.cefalico || '?'}</tspan></text>

      <text x="60" y="615" fontFamily="sans-serif" fontSize="16" fill="#3b82f6" fontStyle="italic">* Os índices são calculados automaticamente com base nas medidas informadas</text>
    </svg>
  );
};

// Valores de referência para raças comuns
const valoresReferencia = {
  'Quarto de Milha': {
    alturaCernelha: { min: 142, max: 160, ideal: 152 },
    comprimentoCorpo: { min: 150, max: 170, ideal: 160 },
    perimetroToracico: { min: 170, max: 190, ideal: 180 },
    comprimentoGarupa: { min: 50, max: 60, ideal: 56 },
    perimetroCanela: { min: 18, max: 22, ideal: 20 },
    comprimentoCabeca: { min: 55, max: 65, ideal: 60 },
    larguraCabeca: { min: 20, max: 25, ideal: 22 }
  },
  'Mangalarga Marchador': {
    alturaCernelha: { min: 147, max: 157, ideal: 152 },
    comprimentoCorpo: { min: 148, max: 165, ideal: 158 },
    perimetroToracico: { min: 168, max: 188, ideal: 178 },
    comprimentoGarupa: { min: 48, max: 58, ideal: 52 },
    perimetroCanela: { min: 17, max: 20, ideal: 18.5 },
    comprimentoCabeca: { min: 54, max: 62, ideal: 58 },
    larguraCabeca: { min: 19, max: 23, ideal: 21 }
  },
  'Árabe': {
    alturaCernelha: { min: 145, max: 155, ideal: 150 },
    comprimentoCorpo: { min: 145, max: 165, ideal: 155 },
    perimetroToracico: { min: 165, max: 185, ideal: 175 },
    comprimentoGarupa: { min: 46, max: 54, ideal: 50 },
    perimetroCanela: { min: 17, max: 20, ideal: 18 },
    comprimentoCabeca: { min: 50, max: 60, ideal: 55 },
    larguraCabeca: { min: 17, max: 22, ideal: 19 }
  },
  'Puro Sangue Inglês': {
    alturaCernelha: { min: 155, max: 170, ideal: 163 },
    comprimentoCorpo: { min: 160, max: 175, ideal: 168 },
    perimetroToracico: { min: 175, max: 195, ideal: 185 },
    comprimentoGarupa: { min: 52, max: 62, ideal: 58 },
    perimetroCanela: { min: 19, max: 23, ideal: 21 },
    comprimentoCabeca: { min: 58, max: 68, ideal: 63 },
    larguraCabeca: { min: 20, max: 24, ideal: 22 }
  },
  'Crioulo': {
    alturaCernelha: { min: 138, max: 150, ideal: 144 },
    comprimentoCorpo: { min: 140, max: 155, ideal: 148 },
    perimetroToracico: { min: 165, max: 180, ideal: 173 },
    comprimentoGarupa: { min: 45, max: 55, ideal: 50 },
    perimetroCanela: { min: 17, max: 21, ideal: 19 },
    comprimentoCabeca: { min: 52, max: 62, ideal: 57 },
    larguraCabeca: { min: 19, max: 23, ideal: 21 }
  }
};

// Definições das medidas com instruções e descrições
const medidasDefinicoes = {
  alturaCernelha: {
    nome: 'Altura na Cernelha',
    descricao: 'Distância vertical do solo até o ponto mais alto da cernelha (região do dorso entre as escápulas)',
    instrucao: 'Posicione o cavalo em terreno plano, com os membros paralelos. Meça verticalmente do solo até o ponto mais alto da cernelha.',
    unidade: 'cm',
    cor: '#3b82f6',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Fundamental para classificação do animal em categorias de altura e conformidade com o padrão racial.'
  },
  comprimentoCorpo: {
    nome: 'Comprimento do Corpo',
    descricao: 'Distância entre a ponta da espádua e a ponta do ísquio',
    instrucao: 'Meça horizontalmente da ponta da espádua até a ponta do ísquio (osso da ponta da nádega).',
    unidade: 'cm',
    cor: '#10b981',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Define o equilíbrio corporal do animal e sua capacidade atlética.'
  },
  perimetroToracico: {
    nome: 'Perímetro Torácico',
    descricao: 'Circunferência do tórax, atrás da cernelha e passando pelo cilhadouro',
    instrucao: 'Passe a fita métrica ao redor do tórax, logo atrás da cernelha, passando pelo cilhadouro (região de inserção da cilha).',
    unidade: 'cm',
    cor: '#ef4444',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Indica a capacidade respiratória e desenvolvimento torácico.'
  },
  comprimentoGarupa: {
    nome: 'Comprimento da Garupa',
    descricao: 'Distância entre a ponta do ílio e a ponta do ísquio',
    instrucao: 'Meça horizontalmente da ponta do ílio (tuberosidade coxal) até a ponta do ísquio (tuberosidade isquiática).',
    unidade: 'cm',
    cor: '#8b5cf6',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Relacionado à propulsão e elasticidade dos movimentos.'
  },
  perimetroCanela: {
    nome: 'Perímetro da Canela',
    descricao: 'Circunferência da região mais estreita da canela (metacarpo) do membro anterior',
    instrucao: 'Meça a circunferência da canela no membro anterior, no ponto mais estreito, normalmente na metade do osso metacarpiano.',
    unidade: 'cm',
    cor: '#f59e0b',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Indicador de resistência óssea e capacidade de suporte de peso.'
  },
  comprimentoCabeca: {
    nome: 'Comprimento da Cabeça',
    descricao: 'Distância da nuca até a ponta do focinho',
    instrucao: 'Meça da base da nuca (crista occipital) até a ponta do focinho.',
    unidade: 'cm',
    cor: '#ec4899',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Importante para a caracterização racial e proporção corporal.'
  },
  larguraCabeca: {
    nome: 'Largura da Cabeça',
    descricao: 'Distância entre os arcos zigomáticos (maçãs do rosto)',
    instrucao: 'Meça a distância entre os pontos mais largos dos arcos zigomáticos.',
    unidade: 'cm',
    cor: '#06b6d4',
    icone: <Ruler className="h-4 w-4" />,
    importancia: 'Compõe a caracterização racial da cabeça.'
  }
};

// Índices de conformação corporal
const calcularIndices = (medidas) => {
  const indices = {};

  // Verificar se existem as medidas necessárias
  if (medidas.alturaCernelha && medidas.comprimentoCorpo) {
    // Índice corporal (comprimento/altura)
    indices.indiceRelativo = {
      valor: (medidas.comprimentoCorpo / medidas.alturaCernelha).toFixed(2),
      descricao: 'Índice Corporal Relativo',
      interpretacao: 'Relação entre comprimento e altura. Valores próximos a 1 indicam equilíbrio.',
      referencia: { baixo: 0.9, ideal: 1.0, alto: 1.1 }
    };
  }

  if (medidas.perimetroToracico && medidas.perimetroCanela) {
    // Índice de robustez (perímetro torácico/perímetro canela)
    indices.indiceRobustez = {
      valor: (medidas.perimetroToracico / medidas.perimetroCanela).toFixed(2),
      descricao: 'Índice de Robustez',
      interpretacao: 'Relação entre o perímetro torácico e o perímetro da canela. Indica a capacidade de suporte da estrutura óssea.',
      referencia: { baixo: 8.0, ideal: 9.0, alto: 10.0 }
    };
  }

  if (medidas.alturaCernelha && medidas.perimetroToracico) {
    // Índice torácico (perímetro torácico/altura)
    indices.indiceTorax = {
      valor: (medidas.perimetroToracico / medidas.alturaCernelha * 100).toFixed(2),
      descricao: 'Índice Torácico',
      interpretacao: 'Porcentagem do perímetro torácico em relação à altura. Valores maiores indicam melhor capacidade respiratória.',
      referencia: { baixo: 115, ideal: 118, alto: 125 }
    };
  }

  if (medidas.larguraCabeca && medidas.comprimentoCabeca) {
    // Índice cefálico (largura/comprimento da cabeça)
    indices.indiceCefalico = {
      valor: (medidas.larguraCabeca / medidas.comprimentoCabeca * 100).toFixed(2),
      descricao: 'Índice Cefálico',
      interpretacao: 'Relação entre a largura e o comprimento da cabeça. Caracteriza o tipo racial.',
      referencia: { baixo: 30, ideal: 35, alto: 40 }
    };
  }

  return indices;
};

// Comparação com valores de referência
const compararComReferencia = (valor, referencia) => {
  if (!valor || !referencia) return { status: 'neutro', mensagem: 'Sem dados para comparação' };

  if (valor < referencia.min) {
    return {
      status: 'abaixo',
      mensagem: `Abaixo do mínimo de referência (${referencia.min} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      diferenca: (valor - referencia.min).toFixed(1)
    };
  } else if (valor > referencia.max) {
    return {
      status: 'acima',
      mensagem: `Acima do máximo de referência (${referencia.max} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      diferenca: (valor - referencia.max).toFixed(1)
    };
  } else {
    const proximidadeIdeal = Math.abs(valor - referencia.ideal);
    const rangePermitido = (referencia.max - referencia.min) / 2;
    const percentualProximidade = (1 - (proximidadeIdeal / rangePermitido)) * 100;

    return {
      status: 'dentro',
      mensagem: `Dentro dos padrões de referência (ideal: ${referencia.ideal} ${medidasDefinicoes[Object.keys(referencia)[0]]?.unidade})`,
      proximidade: percentualProximidade.toFixed(0)
    };
  }
};

// Componente principal para medidas morfológicas
const MedidasMorfologicas = ({ cavalo, onSalvar }) => {
  const { toast } = useToast();
  const [medidas, setMedidas] = useState({
    alturaCernelha: '',
    comprimentoCorpo: '',
    perimetroToracico: '',
    comprimentoGarupa: '',
    perimetroCanela: '',
    comprimentoCabeca: '',
    larguraCabeca: ''
  });

  const [temperatura, setTemperatura] = useState('');
  const [observacoes, setObservacoes] = useState('');
  const [activeTab, setActiveTab] = useState('medicao');
  const [racaSelecionada, setRacaSelecionada] = useState('');
  const [highlightedMeasure, setHighlightedMeasure] = useState(null);
  const [errors, setErrors] = useState({});
  const [historicoMedidas, setHistoricoMedidas] = useState([]);
  const [carregandoHistorico, setCarregandoHistorico] = useState(false);
  const [medidaSelecionada, setMedidaSelecionada] = useState(0); // Índice da medida selecionada no histórico

  // Atualizar raça selecionada quando o cavalo mudar
  useEffect(() => {
    if (cavalo?.breed) {
      setRacaSelecionada(
        Object.keys(valoresReferencia).includes(cavalo.breed)
          ? cavalo.breed
          : ''
      );
    }

    // Carregar histórico de medidas quando o cavalo mudar
    if (cavalo?.id) {
      carregarHistoricoMedidas(cavalo.id);
    }
  }, [cavalo]);

  // Resetar o índice da medida selecionada quando o histórico mudar
  useEffect(() => {
    if (historicoMedidas.length > 0) {
      setMedidaSelecionada(0); // Seleciona a medida mais recente por padrão
    }
  }, [historicoMedidas]);

  // Função para carregar o histórico de medidas do cavalo
  const carregarHistoricoMedidas = async (horseId) => {
    if (!horseId) return;

    setCarregandoHistorico(true);

    try {
      const response = await fetch(`/api/medidas-morfologicas/${horseId}`, {
        headers: {
          'Content-Type': 'application/json',
          'user-id': cavalo.userId.toString()
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar histórico de medidas');
      }

      const result = await response.json();

      if (result.success) {
        setHistoricoMedidas(result.data);
      } else {
        throw new Error(result.message || 'Erro ao carregar histórico');
      }
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível carregar o histórico de medidas.',
        variant: 'destructive'
      });
      setHistoricoMedidas([]);
    } finally {
      setCarregandoHistorico(false);
    }
  };

  // Verificar se a raça selecionada tem valores de referência
  const temReferencia = racaSelecionada && valoresReferencia[racaSelecionada];

  // Atualizar medida específica
  const handleMedidaChange = (medida, valor) => {
    // Validar se é um número
    const numerico = valor.replace(/[^0-9.]/g, '');

    // Atualizar estado
    setMedidas(prev => ({
      ...prev,
      [medida]: numerico
    }));

    // Limpar erro se existir
    if (errors[medida]) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[medida];
        return newErrors;
      });
    }
  };

  // Calcular os índices com base nas medidas atuais
  const indices = calcularIndices(medidas);

  // Validar medidas antes de salvar
  const validarMedidas = () => {
    const novosErros = {};
    let valido = true;

    // Verificar campos obrigatórios
    Object.keys(medidasDefinicoes).forEach(medida => {
      if (!medidas[medida] || isNaN(parseFloat(medidas[medida]))) {
        novosErros[medida] = 'Este campo é obrigatório';
        valido = false;
      } else if (parseFloat(medidas[medida]) <= 0) {
        novosErros[medida] = 'Valor deve ser maior que zero';
        valido = false;
      }
    });

    setErrors(novosErros);
    return valido;
  };

  // Salvar medidas
  const salvarMedidas = async () => {
    if (!validarMedidas()) {
      toast({
        title: 'Formulário incompleto',
        description: 'Preencha todos os campos obrigatórios com valores válidos.',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Preparar dados para salvar
      const novaMedida = {
        horseId: cavalo.id,
        userId: cavalo.userId,
        dataMedicao: new Date().toISOString(),
        alturaCernelha: Number(medidas.alturaCernelha),
        comprimentoCorpo: Number(medidas.comprimentoCorpo),
        perimetroToracico: Number(medidas.perimetroToracico),
        comprimentoGarupa: Number(medidas.comprimentoGarupa),
        perimetroCanela: Number(medidas.perimetroCanela),
        comprimentoCabeca: Number(medidas.comprimentoCabeca),
        larguraCabeca: Number(medidas.larguraCabeca),
        temperatura: temperatura ? Number(temperatura) : null,
        racaReferencia: racaSelecionada,
        observacoes: observacoes,
        indiceRelativo: indices.indiceRelativo?.valor ? Number(indices.indiceRelativo.valor) : null,
        indiceRobustez: indices.indiceRobustez?.valor ? Number(indices.indiceRobustez.valor) : null,
        indiceTorax: indices.indiceTorax?.valor ? Number(indices.indiceTorax.valor) : null,
        indiceCefalico: indices.indiceCefalico?.valor ? Number(indices.indiceCefalico.valor) : null
      };

      // Fazer chamada à API usando a URL correta
      const response = await fetch('/api/medidas-morfologicas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': cavalo.userId.toString()
        },
        body: JSON.stringify(novaMedida)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Falha ao salvar as medidas');
      }

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Medidas salvas',
          description: 'As medidas morfológicas foram salvas com sucesso.'
        });

        if (onSalvar) {
          onSalvar(result.data);
        }

        // Atualizar o histórico de medidas
        carregarHistoricoMedidas(cavalo.id);

        // Limpar o formulário após salvar
        setMedidas({
          alturaCernelha: '',
          comprimentoCorpo: '',
          perimetroToracico: '',
          comprimentoGarupa: '',
          perimetroCanela: '',
          comprimentoCabeca: '',
          larguraCabeca: ''
        });
        setTemperatura('');
        setObservacoes('');

        // Mudar para a aba de histórico após salvar
        setActiveTab('historico');
      } else {
        throw new Error(result.message || 'Erro ao salvar as medidas');
      }
    } catch (error) {
      console.error('Erro ao salvar medidas:', error);
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível salvar as medidas morfológicas.',
        variant: 'destructive'
      });
    }
  };

  // Imprimir relatório
  const imprimirRelatorio = () => {
    window.print();
  };

  // Gerar um valor automático baseado na raça
  const gerarValorAutomatico = (medida) => {
    if (!racaSelecionada || !valoresReferencia[racaSelecionada]) {
      toast({
        title: 'Raça não selecionada',
        description: 'Selecione uma raça para gerar valores automáticos.',
        variant: 'destructive'
      });
      return;
    }

    const ref = valoresReferencia[racaSelecionada][medida];
    if (!ref) return;

    // Gerar um valor aleatório dentro do intervalo de referência
    const min = ref.min;
    const max = ref.max;
    const valor = (Math.random() * (max - min) + min).toFixed(1);

    handleMedidaChange(medida, valor);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-3">
          <TabsTrigger value="historico">Histórico</TabsTrigger>
          <TabsTrigger value="medicao">Nova Medição</TabsTrigger>
          <TabsTrigger value="analise">Análise</TabsTrigger>
        </TabsList>

        <TabsContent value="medicao" className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Coluna do desenho do cavalo */}
            <div className="md:w-1/2">
              <Card className="overflow-hidden h-full">
                <CardHeader className="pb-0">
                  <CardTitle>Cavalo {cavalo?.name}</CardTitle>
                  <CardDescription>
                    Pontos de referência para medições morfológicas
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center pt-4">
                  <HorseMeasurementSVG
                    medidas={medidas}
                    highlighted={highlightedMeasure}
                  />
                </CardContent>
                <CardFooter className="flex flex-col items-start space-y-2 text-sm text-muted-foreground">
                  <p>* Passe o mouse sobre os campos de medição para destacar os pontos de referência no desenho.</p>
                  {racaSelecionada ? (
                    <p>Visualizando referências para raça: <Badge variant="outline">{racaSelecionada}</Badge></p>
                  ) : (
                    <p>Selecione uma raça para ver valores de referência.</p>
                  )}
                </CardFooter>
              </Card>
            </div>

            {/* Coluna do formulário de medidas */}
            <div className="md:w-1/2">
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle>Registro de Medidas</CardTitle>
                    {cavalo?.breed && (
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="raca">Raça para referência:</Label>
                        <select
                          id="raca"
                          value={racaSelecionada}
                          onChange={(e) => setRacaSelecionada(e.target.value)}
                          className="px-3 py-1.5 text-sm border rounded-md"
                        >
                          <option value="">Selecione</option>
                          {Object.keys(valoresReferencia).map(raca => (
                            <option key={raca} value={raca}>{raca}</option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>
                  <CardDescription>
                    Insira as medidas morfológicas do animal
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Campos de medidas */}
                  {Object.entries(medidasDefinicoes).map(([key, definicao]) => (
                    <div
                      key={key}
                      className="grid grid-cols-1 gap-2"
                      onMouseEnter={() => setHighlightedMeasure(key)}
                      onMouseLeave={() => setHighlightedMeasure(null)}
                    >
                      <div className="flex justify-between items-center">
                        <Label htmlFor={key} className="flex items-center space-x-1">
                          <span className="flex items-center">
                            {definicao.icone}
                            <span className="ml-2">{definicao.nome}</span>
                          </span>
                        </Label>
                        {temReferencia && (
                          <span className="text-xs text-muted-foreground">
                            Ref: {valoresReferencia[racaSelecionada][key].min}-{valoresReferencia[racaSelecionada][key].max} {definicao.unidade}
                          </span>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <Input
                          id={key}
                          type="text"
                          placeholder={`${definicao.nome} (${definicao.unidade})`}
                          value={medidas[key]}
                          onChange={(e) => handleMedidaChange(key, e.target.value)}
                          onFocus={() => setHighlightedMeasure(key)}
                          onBlur={() => setHighlightedMeasure(null)}
                          className={errors[key] ? 'border-red-500' : ''}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => gerarValorAutomatico(key)}
                          disabled={!temReferencia}
                          title="Gerar valor automático baseado na referência da raça"
                        >
                          <Clipboard className="h-4 w-4" />
                        </Button>
                      </div>

                      {errors[key] && (
                        <p className="text-xs text-red-500">{errors[key]}</p>
                      )}

                      <p className="text-xs text-muted-foreground">{definicao.instrucao}</p>

                      {temReferencia && medidas[key] && (
                        <div className="mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${
                                parseFloat(medidas[key]) < valoresReferencia[racaSelecionada][key].min ? 'bg-red-500' :
                                parseFloat(medidas[key]) > valoresReferencia[racaSelecionada][key].max ? 'bg-orange-500' :
                                'bg-green-500'
                              }`}
                              style={{
                                width: `${Math.min(100, Math.max(0,
                                  ((parseFloat(medidas[key]) - valoresReferencia[racaSelecionada][key].min) /
                                  (valoresReferencia[racaSelecionada][key].max - valoresReferencia[racaSelecionada][key].min)) * 100
                                ))}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  <Separator className="my-4" />

                  {/* Temperatura e observações */}
                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="temperatura" className="flex items-center">
                      <ThermometerSnowflake className="h-4 w-4 mr-2" />
                      Temperatura corporal (opcional)
                    </Label>
                    <Input
                      id="temperatura"
                      type="text"
                      placeholder="Temperatura (°C)"
                      value={temperatura}
                      onChange={(e) => setTemperatura(e.target.value.replace(/[^0-9.]/g, ''))}
                    />
                    <p className="text-xs text-muted-foreground">
                      Temperatura normal para equinos: 37.5°C - 38.5°C
                    </p>
                  </div>

                  <div className="grid grid-cols-1 gap-2">
                    <Label htmlFor="observacoes">Observações</Label>
                    <textarea
                      id="observacoes"
                      placeholder="Observações sobre a medição ou condições do animal"
                      value={observacoes}
                      onChange={(e) => setObservacoes(e.target.value)}
                      rows={3}
                      className="w-full p-2 border rounded-md"
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setActiveTab('analise')}>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Ver Análise
                  </Button>
                  <Button onClick={salvarMedidas}>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar Medidas
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analise" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Medidas Morfológicas</CardTitle>
              <CardDescription>
                Comparação com valores de referência para {racaSelecionada || 'a raça do animal'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {!temReferencia && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Raça sem referências</AlertTitle>
                  <AlertDescription>
                    Não há valores de referência disponíveis para {cavalo?.breed || 'a raça selecionada'}.
                    Selecione uma raça com valores de referência para comparação.
                  </AlertDescription>
                </Alert>
              )}

              {temReferencia && (
                <>
                  <div>
                    <h3 className="text-lg font-medium">Medidas Básicas</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Medida</TableHead>
                          <TableHead>Valor</TableHead>
                          <TableHead>Referência</TableHead>
                          <TableHead>Análise</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Object.entries(medidasDefinicoes).map(([key, definicao]) => {
                          const valor = parseFloat(medidas[key]);
                          const referencia = valoresReferencia[racaSelecionada][key];
                          const analise = valor ? compararComReferencia(valor, referencia) : null;

                          return (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{definicao.nome}</TableCell>
                              <TableCell>{valor ? `${valor} ${definicao.unidade}` : '-'}</TableCell>
                              <TableCell>{referencia ? `${referencia.min}-${referencia.max} ${definicao.unidade}` : '-'}</TableCell>
                              <TableCell>
                                {analise ? (
                                  <div className="flex items-center">
                                    <span
                                      className={`h-3 w-3 rounded-full mr-2 ${
                                        analise.status === 'abaixo' ? 'bg-red-500' :
                                        analise.status === 'acima' ? 'bg-orange-500' :
                                        'bg-green-500'
                                      }`}
                                    ></span>
                                    <span className="text-sm">{analise.mensagem}</span>
                                  </div>
                                ) : '-'}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium">Índices Morfométricos</h3>
                    {Object.keys(indices).length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Índice</TableHead>
                            <TableHead>Valor</TableHead>
                            <TableHead>Interpretação</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Object.entries(indices).map(([key, indice]) => (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{indice.descricao}</TableCell>
                              <TableCell>{indice.valor}</TableCell>
                              <TableCell>{indice.interpretacao}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <p className="text-muted-foreground text-sm py-4">
                        Preencha todas as medidas necessárias para calcular os índices morfométricos.
                      </p>
                    )}
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <h3 className="text-lg font-medium">Recomendações</h3>

                    {Object.entries(medidas).some(([key, valor]) => {
                      const valorNum = parseFloat(valor);
                      const ref = valoresReferencia[racaSelecionada][key];
                      return valorNum && ref && (valorNum < ref.min || valorNum > ref.max);
                    }) ? (
                      <Alert className="bg-amber-50 border-amber-200">
                        <AlertCircle className="h-4 w-4 text-amber-600" />
                        <AlertTitle className="text-amber-800">Atenção às medidas fora do padrão</AlertTitle>
                        <AlertDescription className="text-amber-700">
                          Algumas medidas estão fora do intervalo de referência para a raça {racaSelecionada}.
                          Considere realizar uma avaliação mais detalhada com um veterinário especializado.
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <Alert className="bg-green-50 border-green-200">
                        <AlertCircle className="h-4 w-4 text-green-600" />
                        <AlertTitle className="text-green-800">Medidas dentro dos padrões</AlertTitle>
                        <AlertDescription className="text-green-700">
                          As medidas estão dentro dos intervalos de referência para a raça {racaSelecionada}.
                          Continue monitorando o desenvolvimento do animal.
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <Button variant="outline" onClick={imprimirRelatorio} className="w-full">
                        <Printer className="h-4 w-4 mr-2" />
                        Imprimir Relatório
                      </Button>
                      <Button onClick={salvarMedidas} className="w-full">
                        <Save className="h-4 w-4 mr-2" />
                        Salvar no Histórico
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setActiveTab('medicao')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar para Medição
              </Button>
              <Button variant="outline" onClick={() => setActiveTab('historico')}>
                <ArrowRight className="h-4 w-4 mr-2" />
                Ver Histórico
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="historico">
          {carregandoHistorico ? (
            <div className="flex justify-center items-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              <span className="ml-4 text-lg">Carregando histórico...</span>
            </div>
          ) : historicoMedidas.length === 0 ? (
            <div className="text-center py-16 space-y-4">
              <div className="text-4xl text-muted-foreground mb-4">📊</div>
              <h3 className="text-xl font-semibold">Nenhuma medição encontrada</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                Este animal ainda não possui medidas morfológicas registradas. Registre a primeira medição para começar a acompanhar seu desenvolvimento.
              </p>
              <Button className="mt-6" onClick={() => setActiveTab('medicao')}>
                <Camera className="h-4 w-4 mr-2" />
                Registrar Primeira Medição
              </Button>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Última medição em destaque */}
              <Card className="border-2 border-primary/20 shadow-lg">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-xl flex items-center">
                        Última Medição
                        <Badge variant="outline" className="ml-2">
                          {new Date(historicoMedidas[0].dataMedicao).toLocaleDateString()}
                        </Badge>
                      </CardTitle>
                      <CardDescription>
                        Visualização detalhada da medição mais recente
                      </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => carregarHistoricoMedidas(cavalo?.id)}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Atualizar
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col md:flex-row gap-6">
                    {/* Coluna do desenho do cavalo */}
                    <div className="md:w-1/2">
                      <div className="bg-muted/30 rounded-lg p-4">
                        <HorseMeasurementSVG
                          medidas={{
                            alturaCernelha: historicoMedidas[medidaSelecionada].alturaCernelha,
                            comprimentoCorpo: historicoMedidas[medidaSelecionada].comprimentoCorpo,
                            perimetroToracico: historicoMedidas[medidaSelecionada].perimetroToracico,
                            comprimentoGarupa: historicoMedidas[medidaSelecionada].comprimentoGarupa,
                            perimetroCanela: historicoMedidas[medidaSelecionada].perimetroCanela,
                            comprimentoCabeca: historicoMedidas[medidaSelecionada].comprimentoCabeca,
                            larguraCabeca: historicoMedidas[medidaSelecionada].larguraCabeca
                          }}
                          highlighted={highlightedMeasure}
                        />
                        <div className="mt-2 text-sm text-muted-foreground">
                          <p>* Passe o mouse sobre as medidas para destacar os pontos de referência</p>
                          {historicoMedidas[medidaSelecionada].racaReferencia && (
                            <p className="mt-1">Raça de referência: <Badge variant="outline">{historicoMedidas[medidaSelecionada].racaReferencia}</Badge></p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Coluna dos dados */}
                    <div className="md:w-1/2">
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-muted/30 p-4 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('alturaCernelha')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-sm font-medium text-muted-foreground mb-1">Altura na Cernelha</h4>
                            <p className="text-3xl font-bold">{historicoMedidas[medidaSelecionada].alturaCernelha} <span className="text-lg font-normal">cm</span></p>
                          </div>
                          <div className="bg-muted/30 p-4 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('comprimentoCorpo')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-sm font-medium text-muted-foreground mb-1">Comprimento do Corpo</h4>
                            <p className="text-3xl font-bold">{historicoMedidas[medidaSelecionada].comprimentoCorpo} <span className="text-lg font-normal">cm</span></p>
                          </div>
                          <div className="bg-muted/30 p-4 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('perimetroToracico')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-sm font-medium text-muted-foreground mb-1">Perímetro Torácico</h4>
                            <p className="text-3xl font-bold">{historicoMedidas[medidaSelecionada].perimetroToracico} <span className="text-lg font-normal">cm</span></p>
                          </div>
                          <div className="bg-muted/30 p-4 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('perimetroCanela')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-sm font-medium text-muted-foreground mb-1">Perímetro da Canela</h4>
                            <p className="text-3xl font-bold">{historicoMedidas[medidaSelecionada].perimetroCanela} <span className="text-lg font-normal">cm</span></p>
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-4">
                          <div className="bg-muted/30 p-3 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('comprimentoGarupa')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-xs font-medium text-muted-foreground mb-1">Comprimento da Garupa</h4>
                            <p className="text-2xl font-bold">{historicoMedidas[medidaSelecionada].comprimentoGarupa} <span className="text-sm font-normal">cm</span></p>
                          </div>
                          <div className="bg-muted/30 p-3 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('comprimentoCabeca')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-xs font-medium text-muted-foreground mb-1">Comprimento da Cabeça</h4>
                            <p className="text-2xl font-bold">{historicoMedidas[medidaSelecionada].comprimentoCabeca} <span className="text-sm font-normal">cm</span></p>
                          </div>
                          <div className="bg-muted/30 p-3 rounded-lg"
                               onMouseEnter={() => setHighlightedMeasure('larguraCabeca')}
                               onMouseLeave={() => setHighlightedMeasure(null)}>
                            <h4 className="text-xs font-medium text-muted-foreground mb-1">Largura da Cabeça</h4>
                            <p className="text-2xl font-bold">{historicoMedidas[medidaSelecionada].larguraCabeca} <span className="text-sm font-normal">cm</span></p>
                          </div>
                        </div>

                        <div className="bg-primary/10 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-primary mb-2 font-bold">Índices Morfométricos</h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="border-l-4 border-blue-400 pl-3 py-1">
                              <p className="text-xs text-muted-foreground">Índice Corporal</p>
                              <p className="text-xl font-semibold">
                                {(() => {
                                  const medida = historicoMedidas[medidaSelecionada];
                                  if (medida.comprimentoCorpo && medida.perimetroToracico) {
                                    return (medida.comprimentoCorpo / medida.perimetroToracico * 100).toFixed(2);
                                  }
                                  return '-';
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">Comprimento/Perímetro Torácico</p>
                            </div>

                            <div className="border-l-4 border-green-400 pl-3 py-1">
                              <p className="text-xs text-muted-foreground">Índice Torácico</p>
                              <p className="text-xl font-semibold">
                                {(() => {
                                  const medida = historicoMedidas[medidaSelecionada];
                                  if (medida.perimetroToracico && medida.alturaCernelha) {
                                    return (medida.perimetroToracico / medida.alturaCernelha * 100).toFixed(2);
                                  }
                                  return '-';
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">Perímetro Torácico/Altura</p>
                            </div>

                            <div className="border-l-4 border-purple-400 pl-3 py-1">
                              <p className="text-xs text-muted-foreground">Índice de Compacidade</p>
                              <p className="text-xl font-semibold">
                                {(() => {
                                  const medida = historicoMedidas[medidaSelecionada];
                                  if (medida.perimetroToracico && medida.alturaCernelha) {
                                    return (medida.perimetroToracico * medida.perimetroToracico / medida.alturaCernelha).toFixed(2);
                                  }
                                  return '-';
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">Perímetro Torácico²/Altura</p>
                            </div>

                            <div className="border-l-4 border-amber-400 pl-3 py-1">
                              <p className="text-xs text-muted-foreground">Índice Dáctilo-Torácico</p>
                              <p className="text-xl font-semibold">
                                {(() => {
                                  const medida = historicoMedidas[medidaSelecionada];
                                  if (medida.perimetroCanela && medida.perimetroToracico) {
                                    return (medida.perimetroCanela / medida.perimetroToracico * 100).toFixed(2);
                                  }
                                  return '-';
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">Perímetro Canela/Perímetro Torácico</p>
                            </div>

                            <div className="border-l-4 border-pink-400 pl-3 py-1 col-span-2 mt-2">
                              <p className="text-xs text-muted-foreground">Índice Cefálico</p>
                              <p className="text-xl font-semibold">
                                {(() => {
                                  const medida = historicoMedidas[medidaSelecionada];
                                  if (medida.larguraCabeca && medida.comprimentoCabeca) {
                                    return (medida.larguraCabeca / medida.comprimentoCabeca * 100).toFixed(2);
                                  }
                                  return '-';
                                })()}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">Largura/Comprimento da Cabeça</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-between">
                          <Button variant="outline" onClick={() => setActiveTab('medicao')}>
                            <Camera className="h-4 w-4 mr-2" />
                            Nova Medição
                          </Button>
                          <Button variant="outline" onClick={() => window.print()}>
                            <Printer className="h-4 w-4 mr-2" />
                            Imprimir Relatório
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Seletor de medição */}
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Histórico de Medições</h3>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="medida-selecionada">Visualizando:</Label>
                  <select
                    id="medida-selecionada"
                    value={medidaSelecionada}
                    onChange={(e) => setMedidaSelecionada(Number(e.target.value))}
                    className="px-3 py-1.5 text-sm border rounded-md"
                  >
                    {historicoMedidas.map((medida, index) => (
                      <option key={medida.id} value={index}>
                        {new Date(medida.dataMedicao).toLocaleDateString()} - {medida.alturaCernelha}cm
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Tabela de histórico */}
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Data</TableHead>
                        <TableHead>Altura</TableHead>
                        <TableHead>Comp. Corpo</TableHead>
                        <TableHead>Perím. Torácico</TableHead>
                        <TableHead>Perím. Canela</TableHead>
                        <TableHead>Índice Relativo</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {historicoMedidas.map((medida, index) => (
                        <TableRow
                          key={medida.id}
                          className={index === medidaSelecionada ? 'bg-primary/10 hover:bg-primary/20' : ''}
                          onClick={() => setMedidaSelecionada(index)}
                          style={{ cursor: 'pointer' }}
                        >
                          <TableCell>{new Date(medida.dataMedicao).toLocaleDateString()}</TableCell>
                          <TableCell>{medida.alturaCernelha} cm</TableCell>
                          <TableCell>{medida.comprimentoCorpo} cm</TableCell>
                          <TableCell>{medida.perimetroToracico} cm</TableCell>
                          <TableCell>{medida.perimetroCanela} cm</TableCell>
                          <TableCell>{medida.indiceRelativo || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MedidasMorfologicas;