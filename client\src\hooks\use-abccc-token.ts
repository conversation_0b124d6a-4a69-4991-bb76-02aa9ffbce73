import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AbcccToken, InsertAbcccToken, TokenStats } from '@shared/schema-tokens';
import axios from 'axios';
import { useToast } from '@/hooks/use-toast';

/**
 * Hook para obter a lista de tokens ABCCC
 */
export function useAbcccTokens() {
  return useQuery<AbcccToken[]>({
    queryKey: ['/api/abccc/tokens'],
    staleTime: 1000 * 60, // 1 minuto
  });
}

/**
 * Hook para obter estatísticas sobre os tokens ABCCC
 */
export function useAbcccTokenStats() {
  return useQuery<TokenStats>({
    queryKey: ['/api/abccc/tokens/stats'],
    staleTime: 1000 * 60, // 1 minuto
  });
}

/**
 * Hook para adicionar um novo token ABCCC
 */
export function useAddAbcccToken() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (tokenData: { token: string; origem?: string; observacoes?: string }) => {
      const { data } = await axios.post('/api/abccc/tokens', {
        method: 'add',
        data: tokenData
      });
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens'] });
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens/stats'] });
      toast({
        title: 'Token adicionado',
        description: 'O token foi adicionado com sucesso.',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao adicionar token',
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao adicionar o token.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para invalidar um token ABCCC
 */
export function useInvalidateAbcccToken() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (tokenId: string) => {
      const { data } = await axios.post(`/api/abccc/tokens/${tokenId}/invalidate`, {
        method: 'invalidate'
      });
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens'] });
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens/stats'] });
      toast({
        title: 'Token invalidado',
        description: 'O token foi marcado como inválido.',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao invalidar token',
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao invalidar o token.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para gerar um novo token ABCCC automaticamente
 */
export function useGenerateAbcccToken() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (origem: string) => {
      const { data } = await axios.post('/api/abccc/tokens/generate', {
        method: 'generate',
        data: { origem }
      });
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens'] });
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens/stats'] });
      toast({
        title: 'Token gerado',
        description: 'Um novo token foi gerado automaticamente.',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao gerar token',
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao gerar o token.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para gerar múltiplos tokens ABCCC automaticamente
 */
export function useGenerateAutomaticTokens() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (quantidade: number) => {
      const { data } = await axios.post('/api/abccc/tokens/generate-batch', {
        method: 'generate-batch',
        data: { quantidade }
      });
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens'] });
      queryClient.invalidateQueries({ queryKey: ['/api/abccc/tokens/stats'] });
      toast({
        title: 'Tokens gerados',
        description: `${data.count} novos tokens foram gerados automaticamente.`,
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao gerar tokens',
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao gerar os tokens.',
        variant: 'destructive',
      });
    },
  });
}