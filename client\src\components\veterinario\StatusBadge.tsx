import React from 'react';
import { Badge } from '@/components/ui/badge';

/**
 * Componente para exibir badges de status com cores apropriadas
 * 
 * Este componente determina automaticamente a cor e estilo do badge
 * com base no status fornecido, seguindo um padrão consistente em
 * todo o módulo veterinário.
 */
interface StatusBadgeProps {
  status: string;
  type?: 'vacinacao' | 'vermifugacao' | 'exame' | 'procedimento';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  type = 'procedimento'
}) => {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let label = status.charAt(0).toUpperCase() + status.slice(1);
  
  if (type === 'vacinacao') {
    switch (status) {
      case 'vencida':
        variant = "destructive";
        break;
      case 'próxima':
        variant = "secondary";
        break;
      case 'válida':
        variant = "default";
        break;
      default:
        variant = "outline";
        break;
    }
  } else if (type === 'vermifugacao') {
    switch (status) {
      case 'atrasado':
        variant = "destructive";
        break;
      case 'próximo':
        variant = "default";
        break;
      case 'agendado':
        variant = "secondary";
        break;
      case 'completo':
        variant = "outline";
        break;
      default:
        variant = "outline";
    }
  } else {
    // Procedimentos em geral
    switch (status) {
      case 'pendente':
      case 'atrasado':
        variant = "destructive";
        break;
      case 'agendado':
      case 'próximo':
        variant = "secondary";
        break;
      case 'em andamento':
        variant = "default";
        break;
      case 'concluído':
      case 'completo':
      case 'realizado':
        variant = "outline";
        break;
      default:
        variant = "outline";
    }
  }
  
  return <Badge variant={variant}>{label}</Badge>;
};