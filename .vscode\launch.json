{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Iniciar <PERSON>or (Desenvolvimento)",
            "skipFiles": ["<node_internals>/**"],
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "dev"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "sourceMaps": true,
            "restart": true,
            "env": {
                "NODE_ENV": "development"
            }
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Iniciar Servidor (Produção)",
            "skipFiles": ["<node_internals>/**"],
            "program": "${workspaceFolder}/dist/index.js",
            "sourceMaps": true,
            "outFiles": ["${workspaceFolder}/dist/**/*.js"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "env": {
                "NODE_ENV": "production"
            }
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Executar Testes",
            "skipFiles": ["<node_internals>/**"],
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "test"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
        },
        {
            "type": "node",
            "request": "attach",
            "name": "Anexar ao Processo",
            "port": 9229,
            "restart": true,
            "sourceMaps": true
        }
    ]
}