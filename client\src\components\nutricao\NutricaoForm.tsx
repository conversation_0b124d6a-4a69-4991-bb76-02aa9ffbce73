import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { nutricaoFormSchema, converterNutricaoBancoParaForm } from '@shared/schema';
import { z } from 'zod';

export type NutricaoFormValues = z.infer<typeof nutricaoFormSchema>;

interface NutricaoFormProps {
  onSubmit: (values: NutricaoFormValues) => void;
  defaultValues?: Partial<NutricaoFormValues>;
  isSubmitting: boolean;
  horse_id: number;
}

/**
 * Componente de formulário reutilizável para nutrição
 * 
 * Este componente pode ser usado tanto para criar quanto para editar
 * registros de nutrição, dependendo dos valores padrão fornecidos.
 */
export const NutricaoForm: React.FC<NutricaoFormProps> = ({
  onSubmit,
  defaultValues,
  isSubmitting,
  horse_id,
}) => {
  // Formulário com validação Zod
  const form = useForm<NutricaoFormValues>({
    resolver: zodResolver(nutricaoFormSchema),
    defaultValues: {
      horse_id: horse_id,
      data: format(new Date(), 'yyyy-MM-dd'),
      tipoAlimentacao: "",
      nomeAlimento: "",
      quantidade: 0,
      unidadeMedida: "kg",
      frequenciaDiaria: 2,
      horarios: "",
      custoUnitario: 0,
      observacoes: "",
      recomendacao: "",
      status: "ativo",
      fornecedor: "",
      ...defaultValues
    },
  });

  // Handler para o envio do formulário
  const handleSubmit = (values: NutricaoFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <input 
          type="hidden" 
          {...form.register("horse_id", { valueAsNumber: true })}
          value={horse_id}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="data"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="tipoAlimentacao"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de Alimentação</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Volumoso">Volumoso</SelectItem>
                    <SelectItem value="Concentrado">Concentrado</SelectItem>
                    <SelectItem value="Suplemento">Suplemento</SelectItem>
                    <SelectItem value="Mineral">Mineral</SelectItem>
                    <SelectItem value="Forragem">Forragem</SelectItem>
                    <SelectItem value="Ração">Ração</SelectItem>
                    <SelectItem value="Outro">Outro</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="nomeAlimento"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome do Alimento</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="quantidade"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantidade</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="unidadeMedida"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unidade</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="kg">kg</SelectItem>
                    <SelectItem value="g">g</SelectItem>
                    <SelectItem value="L">L</SelectItem>
                    <SelectItem value="ml">ml</SelectItem>
                    <SelectItem value="unidade">unidade</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="frequenciaDiaria"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Frequência Diária</FormLabel>
                <FormControl>
                  <Input type="number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="horarios"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Horários (opcional)</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Ex: 7h, 12h, 18h" />
              </FormControl>
              <FormDescription>
                Indique os horários de fornecimento separados por vírgula
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="custoUnitario"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Custo por {form.watch("unidadeMedida")} (R$)</FormLabel>
              <FormControl>
                <Input type="number" step="0.01" {...field} />
              </FormControl>
              <FormDescription>
                O custo mensal será calculado automaticamente
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="fornecedor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Fornecedor (opcional)</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="observacoes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações (opcional)</FormLabel>
                <FormControl>
                  <Textarea {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="recomendacao"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Recomendações (opcional)</FormLabel>
                <FormControl>
                  <Textarea {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end space-x-2">
          <Button 
            type="submit" 
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </form>
    </Form>
  );
};