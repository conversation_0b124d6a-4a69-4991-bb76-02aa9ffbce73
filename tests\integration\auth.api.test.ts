import request from 'supertest';
import express from 'express';
import { z } from 'zod';

// Mock da função setupAuth
const setupAuth = (app: express.Express) => {
  // Schema de validação para login
  const loginSchema = z.object({
    username: z.string().min(3, { message: "Nome de usuário deve ter pelo menos 3 caracteres" }),
    password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" })
  });

  // Rota para login mockada para teste
  app.post("/api/auth/login", async (req, res) => {
    try {
      // Validar dados de entrada usando o schema Zod
      const validatedData = loginSchema.safeParse(req.body);
      
      // Se os dados não forem válidos, retornar erros de validação
      if (!validatedData.success) {
        return res.status(400).json({ 
          message: "Dados de login inválidos",
          errors: validatedData.error.format() 
        });
      }
      
      const { username, password } = validatedData.data;
      
      // Mock de autenticação bem-sucedida para 'testuser/password123'
      if (username === 'testuser' && password === 'password123') {
        return res.json({ 
          user: { id: 1, username: 'testuser', email: '<EMAIL>' },
          token: 'mock-token-123',
          expiration: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        });
      }
      
      // Mock de falha de autenticação
      return res.status(401).json({ message: "Credenciais inválidas" });
    } catch (error) {
      console.error("Erro no login:", error);
      res.status(500).json({ message: "Erro ao fazer login" });
    }
  });
};

describe('Auth API Routes', () => {
  let app: express.Express;
  
  beforeEach(() => {
    // Configurar app express para testes
    app = express();
    app.use(express.json());
    setupAuth(app);
  });
  
  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });
      
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('user');
      expect(res.body).toHaveProperty('token');
      expect(res.body.user).toHaveProperty('username', 'testuser');
    });
    
    it('should reject login with invalid credentials', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        });
      
      expect(res.status).toBe(401);
      expect(res.body).toHaveProperty('message');
    });
    
    it('should validate request body', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'te', // Too short
          password: '12345' // Too short
        });
      
      expect(res.status).toBe(400);
      expect(res.body).toHaveProperty('errors');
      expect(res.body.errors).toHaveProperty('username');
      expect(res.body.errors).toHaveProperty('password');
    });
    
    it('should handle missing fields', async () => {
      const res = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser'
          // Missing password
        });
      
      expect(res.status).toBe(400);
      expect(res.body).toHaveProperty('errors');
      expect(res.body.errors).toHaveProperty('password');
    });
  });
});