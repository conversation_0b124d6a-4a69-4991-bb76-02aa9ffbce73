import { useMemo } from 'react';
import { Link } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Plus, 
  AlertTriangle, 
  Clock, 
  CheckSquare, 
  Activity,
  Calendar,
  Users,
  TrendingUp,
  Heart,
  Target,
  BarChart3,
  PieChart,
  ArrowUpRight,
  ArrowDownRight,
  Zap
} from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { format, isAfter, isBefore, subDays, startOfDay, endOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Types based on actual schema
interface Cavalo {
  id: number;
  name: string;
  breed: string | null;
  birth_date: string | null;
  sexo: 'Mac<PERSON>' | 'Fêmea' | '<PERSON><PERSON> (Castrado)' | 'Garanhão' | 'Égua' | null;
  status: 'ativo' | 'inativo' | 'vendido' | 'falecido' | 'nao_informado' | 'competicao' | 'reproducao';
  user_id: number;
  created_at: string;
  peso: number | null;
  altura: number | null;
  numero_registro: string | null;
  pelagem_id: number | null;
}

interface Manejo {
  id: number;
  cavalo_id: number;
  tipo: string;
  data_execucao: string;
  descricao: string | null;
  observacoes: string | null;
  responsavel: string | null;
  custo: string | null;
  status: string;
  user_id: number;
  created_at: string;
}

interface Evento {
  id: number;
  titulo: string;
  descricao: string | null;
  data: string;
  tipo: string | null;
  status: string | null;
  horse_id: number | null;
  user_id: number;
}

interface ProcedimentoVet {
  id: number;
  tipo: string;
  data: string;
  horse_id: number;
  veterinario: string | null;
  resultado: string | null;
}

const DashboardModern = () => {
  const { user } = useAuth();

  // Data fetching
  const { data: cavalos = [], isLoading: cavalosLoading } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });

  const { data: manejos = [], isLoading: manejosLoading } = useQuery<Manejo[]>({
    queryKey: ['/api/manejos'],
  });

  const { data: eventos = [], isLoading: eventosLoading } = useQuery<Evento[]>({
    queryKey: ['/api/eventos'],
  });

  const { data: procedimentosVet = [], isLoading: vetLoading } = useQuery<ProcedimentoVet[]>({
    queryKey: ['/api/procedimentos-vet'],
  });

  // Calculations
  const stats = useMemo(() => {
    const today = new Date();
    const thisWeek = subDays(today, 7);
    const thisMonth = subDays(today, 30);

    // Cavalos stats
    const totalCavalos = cavalos.length;
    const cavalosAtivos = cavalos.filter(c => c.status === 'ativo').length;
    const cavalosCompetindo = cavalos.filter(c => c.status === 'competicao').length;
    const cavalosReproducao = cavalos.filter(c => c.status === 'reproducao').length;

    // Manejos stats
    const totalManejos = manejos.length;
    const manejosRealizados = manejos.filter(m => m.status === 'realizado' || m.status === 'concluido').length;
    const manejosPendentes = manejos.filter(m => m.status === 'pendente' || m.status === 'agendado').length;
    const manejosEstaSemana = manejos.filter(m => 
      new Date(m.data_execucao) >= thisWeek && new Date(m.data_execucao) <= today
    ).length;

    // Eventos próximos
    const eventosProximos = eventos.filter(e => 
      new Date(e.data) >= today && new Date(e.data) <= subDays(today, -7)
    ).length;

    // Procedimentos veterinários recentes
    const procedimentosRecentes = procedimentosVet.filter(p => 
      new Date(p.data) >= thisMonth
    ).length;

    // Taxa de conclusão de manejos
    const taxaConclusao = totalManejos > 0 ? Math.round((manejosRealizados / totalManejos) * 100) : 0;

    // Distribuição por sexo
    const distribuicaoSexo = {
      machos: cavalos.filter(c => c.sexo === 'Macho' || c.sexo === 'Garanhão').length,
      femeas: cavalos.filter(c => c.sexo === 'Fêmea' || c.sexo === 'Égua').length,
      castrados: cavalos.filter(c => c.sexo === 'Macho (Castrado)').length,
    };

    // Tipos de manejo mais comuns
    const tiposManejoCount = manejos.reduce((acc, m) => {
      acc[m.tipo] = (acc[m.tipo] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const tipoManejoMaisComum = Object.entries(tiposManejoCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

    return {
      totalCavalos,
      cavalosAtivos,
      cavalosCompetindo,
      cavalosReproducao,
      totalManejos,
      manejosRealizados,
      manejosPendentes,
      manejosEstaSemana,
      eventosProximos,
      procedimentosRecentes,
      taxaConclusao,
      distribuicaoSexo,
      tipoManejoMaisComum,
      tiposManejoCount
    };
  }, [cavalos, manejos, eventos, procedimentosVet]);

  // Recent activities
  const atividadesRecentes = useMemo(() => {
    const atividades = [];
    
    // Manejos recentes
    manejos
      .filter(m => new Date(m.created_at) >= subDays(new Date(), 7))
      .slice(0, 3)
      .forEach(m => {
        const cavalo = cavalos.find(c => c.id === m.cavalo_id);
        atividades.push({
          id: `manejo-${m.id}`,
          tipo: 'manejo',
          titulo: `${m.tipo} - ${cavalo?.name || 'Cavalo'}`,
          data: m.data_execucao,
          status: m.status,
          icon: Activity
        });
      });

    // Eventos próximos
    eventos
      .filter(e => new Date(e.data) >= new Date())
      .slice(0, 2)
      .forEach(e => {
        atividades.push({
          id: `evento-${e.id}`,
          tipo: 'evento',
          titulo: e.titulo,
          data: e.data,
          status: e.status,
          icon: Calendar
        });
      });

    return atividades.sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime()).slice(0, 5);
  }, [manejos, eventos, cavalos]);

  const isLoading = cavalosLoading || manejosLoading || eventosLoading || vetLoading;

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-[100px] mb-2" />
                <Skeleton className="h-8 w-[60px] mb-2" />
                <Skeleton className="h-3 w-[80px]" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Visão geral do seu plantel e atividades
          </p>
        </div>
        <div className="flex gap-3 mt-4 lg:mt-0">
          <Button asChild>
            <Link href="/cavalos/novo">
              <Plus className="mr-2 h-4 w-4" />
              Novo Cavalo
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/manejos">
              <Calendar className="mr-2 h-4 w-4" />
              Agendar Manejo
            </Link>
          </Button>
        </div>
      </div>

      {/* Estatísticas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Cavalos</p>
                <p className="text-2xl font-bold">{stats.totalCavalos}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  {stats.cavalosAtivos} ativos
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Manejos Esta Semana</p>
                <p className="text-2xl font-bold">{stats.manejosEstaSemana}</p>
                <p className="text-xs text-blue-600 flex items-center mt-1">
                  <Activity className="h-3 w-3 mr-1" />
                  {stats.manejosPendentes} pendentes
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckSquare className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa de Conclusão</p>
                <p className="text-2xl font-bold">{stats.taxaConclusao}%</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-500" 
                    style={{ width: `${stats.taxaConclusao}%` }}
                  />
                </div>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Eventos Próximos</p>
                <p className="text-2xl font-bold">{stats.eventosProximos}</p>
                <p className="text-xs text-purple-600 flex items-center mt-1">
                  <Calendar className="h-3 w-3 mr-1" />
                  próximos 7 dias
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos e métricas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Distribuição do plantel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Distribuição do Plantel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Cavalos Ativos</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500" />
                  <span className="text-sm font-medium">{stats.cavalosAtivos}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Em Competição</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-blue-500" />
                  <span className="text-sm font-medium">{stats.cavalosCompetindo}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Reprodução</span>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-purple-500" />
                  <span className="text-sm font-medium">{stats.cavalosReproducao}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Distribuição por sexo */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Distribuição por Sexo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Machos/Garanhões</span>
                  <span>{stats.distribuicaoSexo.machos}</span>
                </div>
                <Progress value={(stats.distribuicaoSexo.machos / stats.totalCavalos) * 100} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Fêmeas/Éguas</span>
                  <span>{stats.distribuicaoSexo.femeas}</span>
                </div>
                <Progress value={(stats.distribuicaoSexo.femeas / stats.totalCavalos) * 100} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Castrados</span>
                  <span>{stats.distribuicaoSexo.castrados}</span>
                </div>
                <Progress value={(stats.distribuicaoSexo.castrados / stats.totalCavalos) * 100} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Atividades recentes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Atividades Recentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {atividadesRecentes.length > 0 ? (
                atividadesRecentes.map((atividade) => {
                  const Icon = atividade.icon;
                  return (
                    <div key={atividade.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{atividade.titulo}</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(atividade.data), 'dd/MM/yyyy', { locale: ptBR })}
                        </p>
                      </div>
                      <Badge variant={atividade.status === 'realizado' ? 'default' : 'secondary'} className="text-xs">
                        {atividade.status}
                      </Badge>
                    </div>
                  );
                })
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  Nenhuma atividade recente
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertas e notificações */}
      {stats.manejosPendentes > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Você tem {stats.manejosPendentes} manejo(s) pendente(s)
                </h3>
                <p className="text-sm text-orange-700">
                  Revise sua agenda para garantir que todos os cuidados sejam realizados.
                </p>
              </div>
              <Button asChild variant="outline" size="sm">
                <Link href="/manejos">
                  Ver Manejos
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as principais funcionalidades do sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/cavalos">
                <Users className="h-6 w-6" />
                <span className="text-sm">Plantel</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/manejos">
                <Activity className="h-6 w-6" />
                <span className="text-sm">Manejos</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/veterinario">
                <Heart className="h-6 w-6" />
                <span className="text-sm">Veterinário</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col gap-2">
              <Link href="/genetics">
                <TrendingUp className="h-6 w-6" />
                <span className="text-sm">Genética</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardModern;