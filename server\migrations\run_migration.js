import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Configuração da conexão com o banco de dados
// Estas informações devem ser substituídas pelas suas credenciais reais
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

// Obter o diretório atual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  const client = await pool.connect();
  try {
    // Ler o arquivo SQL
    const sqlFile = path.join(__dirname, 'add_indice_cefalico.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    console.log('Executando migração...');
    await client.query(sql);
    console.log('Migra<PERSON> concluída com sucesso!');
  } catch (err) {
    console.error('Erro ao executar migração:', err);
  } finally {
    client.release();
    await pool.end();
  }
}

runMigration();
