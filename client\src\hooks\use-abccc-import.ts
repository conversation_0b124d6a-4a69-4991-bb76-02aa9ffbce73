import { useMutation } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';

export interface ABCCCCavaloData {
  nome: string;
  registro: string;
  sexo: string;
  nascimento: string;
  pelagem: string;
  pai: string;
  mae: string;
  criador: string;
  proprietario: string;
  foto?: string;
  genealogia: any;
  avoPaternoNome?: string;
  avoPaternoPai?: string;
  avoPaternoMae?: string;
  avoMaternaNome?: string;
  avoMaternaPai?: string;
  avoMaternaMae?: string;
  titulos?: string[];
  exposicoes?: string[];
}

/**
 * Hook para buscar dados de cavalos no site da ABCCC pelo número de registro
 */
export function useABCCCImport() {
  const importMutation = useMutation({
    mutationFn: async (registro: string) => {
      if (!registro || registro.trim() === '') {
        throw new Error('Número de registro é obrigatório');
      }
      
      // Formatar o registro (remover espaços e padronizar)
      const registroFormatado = registro.trim().toUpperCase();
      console.log(`[useABCCCImport] Buscando dados para registro: ${registroFormatado}`);

      // Criar um AbortController para cancelar a requisição após 30 segundos
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos

      try {
        const response = await fetch(`/api/abccc/cavalo/${registroFormatado}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'User-Id': '1'
          },
          signal: controller.signal
        });

        // Limpar o timeout após a resposta ser recebida
        clearTimeout(timeoutId);

        if (!response.ok) {
          console.error(`[useABCCCImport] Erro HTTP ${response.status} ao buscar dados`);
          
          // Tratar códigos de erro específicos
          if (response.status === 404) {
            throw new Error(`Registro ${registroFormatado} não encontrado na base da ABCCC`);
          }
          
          if (response.status === 504) {
            throw new Error('O tempo de busca esgotou. Tente novamente mais tarde.');
          }
          
          // Tratamento especial para o erro 406 (registro B405132 ou outros com problemas conhecidos)
          if (response.status === 406) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Este registro não é suportado pela importação automática. Por favor, insira os dados manualmente.');
          }
          
          // Tentar obter detalhes do erro da resposta
          let errorMessage = `Erro ao buscar dados do cavalo (código ${response.status})`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorData.error || errorMessage;
          } catch (parseError) {
            console.error('[useABCCCImport] Não foi possível interpretar resposta de erro:', parseError);
          }
          
          throw new Error(errorMessage);
        }

        const data = await response.json();
        
        // Validar se os dados mínimos foram retornados
        if (!data.nome || !data.registro) {
          throw new Error('Os dados retornados pela ABCCC estão incompletos');
        }
        
        console.log(`[useABCCCImport] Dados obtidos com sucesso para registro ${registroFormatado}:`, data);
        
        // Verificar estrutura de genealogia
        if (data.genealogia) {
          console.log(`[useABCCCImport] Dados de genealogia encontrados:`, {
            pai: data.genealogia.pai?.nome,
            mae: data.genealogia.mae?.nome,
            avoPat: data.avoPaternoNome,
            avoMat: data.avoMaternaNome
          });
        }
        
        return data as ABCCCCavaloData;
      } catch (error) {
        console.error('[useABCCCImport] Exceção ao buscar dados:', error);
        
        // Tratar erros de timeout/abort
        if (error.name === 'AbortError') {
          throw new Error('A busca demorou muito tempo e foi cancelada. Tente novamente mais tarde.');
        }
        
        throw error;
      } finally {
        // Garantir que o timeout seja limpo em todos os casos
        clearTimeout(timeoutId);
      }
    },
    onError: (error) => {
      toast({
        title: 'Erro ao buscar dados da ABCCC',
        description: error instanceof Error ? error.message : 'Erro ao buscar dados do cavalo na ABCCC',
        variant: 'destructive',
        duration: 5000 // Duração maior para o usuário conseguir ler a mensagem
      });
    },
    onSuccess: (data) => {
      toast({
        title: 'Dados importados com sucesso',
        description: `Dados do cavalo ${data.nome} foram preenchidos automaticamente`,
        duration: 3000
      });
    }
  });

  return {
    importData: importMutation.mutateAsync,
    isLoading: importMutation.isPending,
    isError: importMutation.isError,
    error: importMutation.error,
    data: importMutation.data
  };
}