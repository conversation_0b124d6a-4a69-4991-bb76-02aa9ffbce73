import { z } from "zod";

// Auto-generated comprehensive Zod schemas
// This file ensures 100% compatibility between database structure and validation schemas

export const insertUserSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  username: z.string(),
  email: z.string().optional(),
  password: z.string(),
});

export const updateUserSchema = insertUserSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertCavaloSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  breed: z.string().optional(),
  birth_date: z.preprocess(
    (v) => typeof v === "string" ? new Date(v) : v,
    z.date().optional().nullable()
  ),
  sexo: z.enum(['Macho', 'Fêmea', '<PERSON><PERSON> (Castrado)', 'Garanh<PERSON>', 'Égua']),
  cor: z.string().nullable().optional(),
  pelagem_id: z.number().nullable().optional(),
  status: z.enum(['ativo', 'inativo', 'vendido', 'falecido', 'nao_informado', 'competicao', 'reproducao']).default('ativo'),
  user_id: z.number(),
  notes: z.string().nullable().optional(),
  // Campo observacoes removido - não existe na tabela cavalos real
  peso: z.number().nullable().optional(),
  altura: z.number().nullable().optional(),
  data_entrada: z.union([z.string(), z.date()]).optional().nullable(),
  data_saida: z.union([z.string(), z.date()]).optional().nullable(),
  motivo_saida: z.string().nullable().optional(),
  numero_registro: z.string().nullable().optional(),
  origem: z.string().nullable().optional(),
  criador: z.string().nullable().optional(),
  proprietario: z.string().nullable().optional(),
  valor_compra: z.number().nullable().optional(),
  data_compra: z.union([z.string(), z.date()]).optional().nullable(),
  inspetor: z.string().nullable().optional(),
  is_external: z.boolean().default(false),
  pai_id: z.number().nullable().optional(),
  mae_id: z.number().nullable().optional(),
  // Campos de genealogia removidos - não existem na tabela cavalos real
  // Frontend deve processar genealogia separadamente via tabela genealogia
});

export const updateCavaloSchema = insertCavaloSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertGenealogiaSchema = z.object({
  horse_id: z.number(),
  pai: z.string().nullable().optional(),
  mae: z.string().nullable().optional(),
  avo_paterno_id: z.number().nullable().optional(),
  avo_paterno: z.string().nullable().optional(),
  avo_paterna_id: z.number().nullable().optional(),
  avo_paterna: z.string().nullable().optional(),
  avo_materno_id: z.number().nullable().optional(),
  avo_materno: z.string().nullable().optional(),
  avo_materna_id: z.number().nullable().optional(),
  avo_materna: z.string().nullable().optional(),
  // Bisavós paternos (exatamente como no banco)
  bisavo_paterno_paterno: z.string().nullable().optional(),
  bisavo_paterno_paterno_id: z.number().nullable().optional(),
  bisavo_paterna_paterno: z.string().nullable().optional(),
  bisavo_paterna_paterno_id: z.number().nullable().optional(),
  bisavo_materno_paterno: z.string().nullable().optional(),
  bisavo_materno_paterno_id: z.number().nullable().optional(),
  bisavo_materna_paterno: z.string().nullable().optional(),
  bisavo_materna_paterno_id: z.number().nullable().optional(),
  // user_id removido - não existe na tabela genealogia real
});

export const updateGenealogiaSchema = insertGenealogiaSchema.partial().extend({
  id: z.number().int().positive(),
});

const baseManejoSchema = z.object({
  horse_id: z.number().optional(),
  cavalo_id: z.number().optional(),
  tipo: z.string().optional(),
  tipo_manejo: z.string().optional(),
  data: z.string().optional(),
  data_execucao: z.string().optional(),
  descricao: z.string().optional(),
  observacoes: z.string().optional(),
  responsavel: z.string().optional(),
  custo: z.number().optional(),
  user_id: z.number(),
  status: z.string().optional(),
  data_vencimento: z.string().optional(),
  prioridade: z.string().optional(),
});

export const insertManejoSchema = baseManejoSchema.transform((input) => {
  // Transform horse_id to cavalo_id if provided
  if (input.horse_id && !input.cavalo_id) {
    input.cavalo_id = input.horse_id;
  }
  // Transform data to data_execucao if provided
  if (input.data && !input.data_execucao) {
    input.data_execucao = input.data;
  }
  // Transform tipo_manejo to tipo if provided
  if (input.tipo_manejo && !input.tipo) {
    input.tipo = input.tipo_manejo;
  }
  // Ensure at least one tipo field is present
  if (!input.tipo && !input.tipo_manejo) {
    throw new Error('Campo tipo ou tipo_manejo é obrigatório');
  }
  // Remove the frontend-only fields
  const { horse_id, data, dataVencimento, prioridade, tipo_manejo, ...backendData } = input;
  return backendData;
});

export const updateManejoSchema = baseManejoSchema.partial().extend({
  id: z.number().int().positive(),
}).transform((input) => {
  // Transform horse_id to cavalo_id if provided
  if (input.horse_id && !input.cavalo_id) {
    input.cavalo_id = input.horse_id;
  }
  // Transform data to data_execucao if provided
  if (input.data && !input.data_execucao) {
    input.data_execucao = input.data;
  }
  // Transform tipo_manejo to tipo if provided
  if (input.tipo_manejo && !input.tipo) {
    input.tipo = input.tipo_manejo;
  }
  // Remove the frontend-only fields
  const { horse_id, data, dataVencimento, prioridade, tipo_manejo, ...backendData } = input;
  return backendData;
});

export const insertTaskSchema = z.object({
  title: z.string(),
  due_date: z.string().optional(),
  completed: z.boolean().default(false),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const updateTaskSchema = insertTaskSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertArquivoSchema = z.object({
  nome_arquivo: z.string(),
  caminho_arquivo: z.string(),
  tipo_arquivo: z.string().optional(),
  descricao: z.string().optional(),
  cavalo_id: z.number().optional(),
  tamanho_arquivo: z.number().optional(),
  is_photo: z.boolean().default(false),
  is_primary_photo: z.boolean().default(false),
  user_id: z.number(),
});

export const updateArquivoSchema = insertArquivoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertEventoSchema = z.object({
  titulo: z.string(),
  descricao: z.string().optional(),
  data: z.string(),
  hora_inicio: z.string().optional(),
  hora_fim: z.string().optional(),
  tipo: z.string().optional(),
  status: z.string().optional(),
  prioridade: z.string().optional(),
  horse_id: z.number().optional(),
  user_id: z.number(),
});

export const updateEventoSchema = insertEventoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertProcedimentoVetSchema = z.object({
  tipo: z.string(),
  descricao: z.string().optional(),
  data: z.string(),
  veterinario: z.string().optional(),
  crmv: z.string().optional(),
  medicamentos: z.string().optional(),
  dosagem: z.string().optional(),
  resultado: z.string().optional(),
  recomendacoes: z.string().optional(),
  horse_id: z.number(),
  user_id: z.number(),
});

export const updateProcedimentoVetSchema = insertProcedimentoVetSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertReproducaoSchema = z.object({
  egua_id: z.number(),
  garanhao_id: z.number().nullable().optional(),
  garanhao_nome: z.string().nullable().optional(),
  data_cobertura: z.preprocess(
    (v) => typeof v === "string" ? new Date(v) : v,
    z.date().nullable().optional()
  ),
  data_parto_previsto: z.preprocess(
    (v) => typeof v === "string" ? new Date(v) : v,
    z.date().nullable().optional()
  ),
  data_parto_real: z.preprocess(
    (v) => typeof v === "string" ? new Date(v) : v,
    z.date().nullable().optional()
  ),
  tipo_cobertura: z.string().nullable().optional(),
  resultado: z.string().nullable().optional(),
  observacoes: z.string().nullable().optional(),
  user_id: z.number(),
  status: z.string().default("planejado").optional(),
});

export const updateReproducaoSchema = insertReproducaoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertNutricaoSchema = z.object({
  cavalo_id: z.number(),
  tipo_alimento: z.string().min(1),
  quantidade: z.number().positive(),
  unidade: z.string(),
  frequencia: z.string(),
  horario_alimentacao: z.string().optional(),
  observacoes: z.string().optional(),
  custo_mensal: z.number().min(0).optional(),
  data_inicio: z.string(),
  data_fim: z.string().optional(),
  status: z.string().optional(),
  user_id: z.number(),
});

export const updateNutricaoSchema = insertNutricaoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertPelagemSchema = z.object({
  nome: z.string(),
  descricao: z.string().optional(),
  slug: z.string().optional(),
  fonte: z.string().optional(),
  user_id: z.number().optional(),
});

export const updatePelagemSchema = insertPelagemSchema.partial().extend({
  id: z.number().int().positive(),
});

// Morfologia/Medidas Morfológicas schemas - aligned with actual database structure
const baseMorfologiaSchema = z.object({
  cavalo_id: z.number().optional(),
  horse_id: z.number().optional(),
  data_medicao: z.string().optional(),
  dataMedicao: z.string().optional(),
  altura_cernelha: z.number().optional(),
  comprimento_corpo: z.number().optional(),
  circunferencia_toracica: z.number().optional(),
  perimetro_toracico: z.number().optional(), // Maps to circunferencia_toracica
  comprimento_cabeca: z.number().optional(),
  largura_cabeca: z.number().optional(),
  circunferencia_canela: z.number().optional(),
  perimetro_canela: z.number().optional(), // Maps to circunferencia_canela
  angulo_casco: z.number().optional(),
  indice_cefalico: z.number().optional(),
  pontuacao_cabeca: z.number().optional(),
  pontuacao_pescoco: z.number().optional(),
  pontuacao_espalda: z.number().optional(),
  pontuacao_dorso: z.number().optional(),
  pontuacao_garupa: z.number().optional(),
  pontuacao_membros: z.number().optional(),
  pontuacao_aprumos: z.number().optional(),
  pontuacao_andamento: z.number().optional(),
  pontuacao_harmonia: z.number().optional(),
  pontuacao_total: z.number().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const insertMorfologiaSchema = baseMorfologiaSchema.transform((input) => {
  const result: any = {};
  
  // Handle cavalo_id mapping (required field)
  if (input.horse_id) {
    result.cavalo_id = input.horse_id;
  } else if (input.cavalo_id) {
    result.cavalo_id = input.cavalo_id;
  } else {
    throw new Error('Campo cavalo_id ou horse_id é obrigatório');
  }
  
  // Handle data_medicao mapping (required field)
  if (input.dataMedicao) {
    result.data_medicao = input.dataMedicao;
  } else if (input.data_medicao) {
    result.data_medicao = input.data_medicao;
  } else {
    throw new Error('Campo data_medicao ou dataMedicao é obrigatório');
  }
  
  // Copy all other valid fields
  if (input.user_id !== undefined) result.user_id = input.user_id;
  if (input.altura_cernelha !== undefined) result.altura_cernelha = input.altura_cernelha;
  if (input.comprimento_corpo !== undefined) result.comprimento_corpo = input.comprimento_corpo;
  if (input.circunferencia_toracica !== undefined) result.circunferencia_toracica = input.circunferencia_toracica;
  if (input.perimetro_toracico !== undefined) result.circunferencia_toracica = input.perimetro_toracico;
  if (input.comprimento_cabeca !== undefined) result.comprimento_cabeca = input.comprimento_cabeca;
  if (input.largura_cabeca !== undefined) result.largura_cabeca = input.largura_cabeca;
  if (input.circunferencia_canela !== undefined) result.circunferencia_canela = input.circunferencia_canela;
  if (input.perimetro_canela !== undefined) result.circunferencia_canela = input.perimetro_canela;
  if (input.angulo_casco !== undefined) result.angulo_casco = input.angulo_casco;
  if (input.indice_cefalico !== undefined) result.indice_cefalico = input.indice_cefalico;
  if (input.pontuacao_cabeca !== undefined) result.pontuacao_cabeca = input.pontuacao_cabeca;
  if (input.pontuacao_pescoco !== undefined) result.pontuacao_pescoco = input.pontuacao_pescoco;
  if (input.pontuacao_espalda !== undefined) result.pontuacao_espalda = input.pontuacao_espalda;
  if (input.pontuacao_dorso !== undefined) result.pontuacao_dorso = input.pontuacao_dorso;
  if (input.pontuacao_garupa !== undefined) result.pontuacao_garupa = input.pontuacao_garupa;
  if (input.pontuacao_membros !== undefined) result.pontuacao_membros = input.pontuacao_membros;
  if (input.pontuacao_aprumos !== undefined) result.pontuacao_aprumos = input.pontuacao_aprumos;
  if (input.pontuacao_andamento !== undefined) result.pontuacao_andamento = input.pontuacao_andamento;
  if (input.pontuacao_harmonia !== undefined) result.pontuacao_harmonia = input.pontuacao_harmonia;
  if (input.pontuacao_total !== undefined) result.pontuacao_total = input.pontuacao_total;
  if (input.observacoes !== undefined) result.observacoes = input.observacoes;
  
  return result;
});

export const insertMedidasMorfologicasSchema = insertMorfologiaSchema;

export const updateMorfologiaSchema = baseMorfologiaSchema.partial().extend({
  id: z.number().int().positive(),
}).transform((input) => {
  // Transform horse_id to cavalo_id if provided
  if (input.horse_id && !input.cavalo_id) {
    input.cavalo_id = input.horse_id;
  }
  // Transform dataMedicao to data_medicao if provided
  if (input.dataMedicao && !input.data_medicao) {
    input.data_medicao = input.dataMedicao;
  }
  // Remove the frontend-only fields
  const { horse_id, dataMedicao, ...backendData } = input;
  return backendData;
});

export const updateMedidasMorfologicasSchema = updateMorfologiaSchema;

export const insertMorfologiaArquivoSchema = z.object({
  medida_id: z.number(),
  arquivo_id: z.number(),
  tipo_arquivo: z.string().optional(),
  descricao: z.string().optional(),
  user_id: z.number(),
});

export const updateMorfologiaArquivoSchema = insertMorfologiaArquivoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertDesempenhoHistoricoSchema = z.object({
  cavalo_id: z.number(),
  evento: z.string(),
  data_evento: z.string(),
  local_evento: z.string().optional(),
  colocacao: z.number().optional(),
  tempo: z.string().optional(),
  pontuacao: z.number().optional(),
  observacoes: z.string().optional(),
  categoria: z.string().optional(),
  modalidade: z.string().optional(),
  user_id: z.number(),
});

export const updateDesempenhoHistoricoSchema = insertDesempenhoHistoricoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertSugestoesCruzamentoSchema = z.object({
  cavalo_id: z.number(),
  padreiro_sugerido_id: z.number(),
  score_genetico: z.number().optional(),
  compatibilidade: z.string().optional(),
  objetivos_cruzamento: z.string().optional(),
  observacoes: z.string().optional(),
  data_sugestao: z.string(),
  status: z.string().default('ativa'),
  prioridade: z.string().optional(),
  user_id: z.number(),
});

export const updateSugestoesCruzamentoSchema = insertSugestoesCruzamentoSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertFeedTemplateSchema = z.object({
  nome: z.string(),
  descricao: z.string().optional(),
  categoria_animal: z.string(),
  idade_minima: z.number().optional(),
  idade_maxima: z.number().optional(),
  peso_minimo: z.number().optional(),
  peso_maximo: z.number().optional(),
  ativo: z.boolean().default(true),
  user_id: z.number(),
});

export const updateFeedTemplateSchema = insertFeedTemplateSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertFeedPlanItemSchema = z.object({
  template_id: z.number(),
  alimento: z.string(),
  quantidade_kg: z.number(),
  frequencia_diaria: z.number(),
  horarios: z.string().optional(),
  observacoes: z.string().optional(),
  custo_unitario: z.number().optional(),
  fornecedor: z.string().optional(),
  user_id: z.number(),
});

export const updateFeedPlanItemSchema = insertFeedPlanItemSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertStockBatchSchema = z.object({
  alimento: z.string(),
  quantidade_kg: z.number(),
  data_entrada: z.string(),
  data_validade: z.string().optional(),
  fornecedor: z.string().optional(),
  preco_total: z.number().optional(),
  lote: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateStockBatchSchema = insertStockBatchSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertStockAlertSchema = z.object({
  alimento: z.string(),
  quantidade_minima: z.number(),
  quantidade_atual: z.number(),
  status: z.string().default('ativo'),
  data_ultimo_alerta: z.string().optional(),
  observacoes: z.string().optional(),
  user_id: z.number(),
});

export const updateStockAlertSchema = insertStockAlertSchema.partial().extend({
  id: z.number().int().positive(),
});

// Additional missing schemas from database
export const insertAbcccTokenSchema = z.object({
  token: z.string(),
  expires_at: z.string(),
  is_active: z.boolean().optional(),
  user_id: z.number(),
  sucessos: z.number().optional(),
  ultimo_uso: z.string().optional(),
});

export const updateAbcccTokenSchema = insertAbcccTokenSchema.partial().extend({
  id: z.number().int().positive(),
});

export const insertVeterinarioSchema = z.object({
  cavalo_id: z.number(),
  tipo_procedimento: z.string(),
  data_procedimento: z.string(),
  veterinario_responsavel: z.string().optional(),
  diagnostico: z.string().optional(),
  tratamento: z.string().optional(),
  medicamentos: z.string().optional(),
  observacoes: z.string().optional(),
  custo: z.number().optional(),
  proxima_consulta: z.string().optional(),
  user_id: z.number(),
  status: z.string().optional(),
});

export const updateVeterinarioSchema = insertVeterinarioSchema.partial().extend({
  id: z.number().int().positive(),
});

// Type exports
export type InsertUserType = z.infer<typeof insertUserSchema>;
export type UpdateUserType = z.infer<typeof updateUserSchema>;
export type InsertCavaloType = z.infer<typeof insertCavaloSchema>;
export type UpdateCavaloType = z.infer<typeof updateCavaloSchema>;
export type InsertGenealogiaType = z.infer<typeof insertGenealogiaSchema>;
export type UpdateGenealogiaType = z.infer<typeof updateGenealogiaSchema>;
export type InsertManejoType = z.infer<typeof insertManejoSchema>;
export type UpdateManejoType = z.infer<typeof updateManejoSchema>;
export type InsertTaskType = z.infer<typeof insertTaskSchema>;
export type UpdateTaskType = z.infer<typeof updateTaskSchema>;
export type InsertArquivoType = z.infer<typeof insertArquivoSchema>;
export type UpdateArquivoType = z.infer<typeof updateArquivoSchema>;
export type InsertEventoType = z.infer<typeof insertEventoSchema>;
export type UpdateEventoType = z.infer<typeof updateEventoSchema>;
export type InsertProcedimentoVetType = z.infer<typeof insertProcedimentoVetSchema>;
export type UpdateProcedimentoVetType = z.infer<typeof updateProcedimentoVetSchema>;
export type InsertReproducaoType = z.infer<typeof insertReproducaoSchema>;
export type UpdateReproducaoType = z.infer<typeof updateReproducaoSchema>;
export type InsertNutricaoType = z.infer<typeof insertNutricaoSchema>;
export type UpdateNutricaoType = z.infer<typeof updateNutricaoSchema>;
export type InsertPelagemType = z.infer<typeof insertPelagemSchema>;
export type UpdatePelagemType = z.infer<typeof updatePelagemSchema>;
export type InsertMedidasMorfologicasType = z.infer<typeof insertMedidasMorfologicasSchema>;
export type UpdateMedidasMorfologicasType = z.infer<typeof updateMedidasMorfologicasSchema>;
export type InsertMorfologiaArquivoType = z.infer<typeof insertMorfologiaArquivoSchema>;
export type UpdateMorfologiaArquivoType = z.infer<typeof updateMorfologiaArquivoSchema>;
export type InsertDesempenhoHistoricoType = z.infer<typeof insertDesempenhoHistoricoSchema>;
export type UpdateDesempenhoHistoricoType = z.infer<typeof updateDesempenhoHistoricoSchema>;
export type InsertSugestoesCruzamentoType = z.infer<typeof insertSugestoesCruzamentoSchema>;
export type UpdateSugestoesCruzamentoType = z.infer<typeof updateSugestoesCruzamentoSchema>;
export type InsertFeedTemplateType = z.infer<typeof insertFeedTemplateSchema>;
export type UpdateFeedTemplateType = z.infer<typeof updateFeedTemplateSchema>;
export type InsertFeedPlanItemType = z.infer<typeof insertFeedPlanItemSchema>;
export type UpdateFeedPlanItemType = z.infer<typeof updateFeedPlanItemSchema>;
export type InsertStockBatchType = z.infer<typeof insertStockBatchSchema>;
export type UpdateStockBatchType = z.infer<typeof updateStockBatchSchema>;
export type InsertStockAlertType = z.infer<typeof insertStockAlertSchema>;
export type UpdateStockAlertType = z.infer<typeof updateStockAlertSchema>;
export type InsertAbcccTokenType = z.infer<typeof insertAbcccTokenSchema>;
export type UpdateAbcccTokenType = z.infer<typeof updateAbcccTokenSchema>;
export type InsertVeterinarioType = z.infer<typeof insertVeterinarioSchema>;
export type UpdateVeterinarioType = z.infer<typeof updateVeterinarioSchema>;