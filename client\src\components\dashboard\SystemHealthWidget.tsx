/**
 * Widget de Saúde do Sistema
 * Mostra status em tempo real sem interferir na lógica existente
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Activity, Database, Server, Wifi, CheckCircle, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

interface SystemHealthProps {
  className?: string;
}

export function SystemHealthWidget({ className = '' }: SystemHealthProps) {
  const { user } = useAuth();

  // Health check do sistema
  const { data: healthData } = useQuery({
    queryKey: ['/health'],
    refetchInterval: 30000, // Atualiza a cada 30 segundos
    staleTime: 25000,
  });

  // Estatísticas do usuário para análise de saúde
  const { data: cavalos } = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const { data: manejos } = useQuery({
    queryKey: ['/api/manejos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  // Calcular métricas de saúde do sistema
  const calculateSystemHealth = () => {
    let healthScore = 100;
    let issues = [];

    // Verificar conectividade
    if (!healthData) {
      healthScore -= 30;
      issues.push('Conectividade instável');
    }

    // Verificar volume de dados
    if (Array.isArray(cavalos) && cavalos.length === 0) {
      healthScore -= 10;
      issues.push('Sem dados de cavalos');
    }

    // Verificar atividade recente
    if (Array.isArray(manejos)) {
      const today = new Date();
      const recentManejos = manejos.filter(m => {
        if (!m.data_execucao) return false;
        const dataExecucao = new Date(m.data_execucao);
        const daysDiff = Math.floor((today.getTime() - dataExecucao.getTime()) / (1000 * 60 * 60 * 24));
        return daysDiff <= 7;
      });

      if (recentManejos.length === 0 && manejos.length > 0) {
        healthScore -= 15;
        issues.push('Baixa atividade recente');
      }
    }

    return {
      score: Math.max(0, healthScore),
      status: healthScore >= 80 ? 'excellent' : healthScore >= 60 ? 'good' : healthScore >= 40 ? 'warning' : 'critical',
      issues
    };
  };

  const health = calculateSystemHealth();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'excellent': return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">Excelente</Badge>;
      case 'good': return <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-200">Bom</Badge>;
      case 'warning': return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">Atenção</Badge>;
      case 'critical': return <Badge variant="destructive">Crítico</Badge>;
      default: return <Badge variant="outline">Verificando...</Badge>;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Status do Sistema
          </div>
          {getStatusBadge(health.status)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Score de Saúde */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Saúde Geral</span>
            <span className="font-medium">
              {health.score}%
            </span>
          </div>
          <Progress 
            value={health.score} 
            className="h-2"
          />
        </div>

        {/* Indicadores do Sistema */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center gap-2">
            <Database className="h-3 w-3 text-green-600" />
            <span className="text-muted-foreground">Database</span>
            <CheckCircle className="h-3 w-3 text-green-600" />
          </div>
          
          <div className="flex items-center gap-2">
            <Server className="h-3 w-3 text-green-600" />
            <span className="text-muted-foreground">API</span>
            <CheckCircle className="h-3 w-3 text-green-600" />
          </div>
          
          <div className="flex items-center gap-2">
            <Wifi className="h-3 w-3 text-green-600" />
            <span className="text-muted-foreground">Conectividade</span>
            {healthData ? (
              <CheckCircle className="h-3 w-3 text-green-600" />
            ) : (
              <AlertTriangle className="h-3 w-3 text-yellow-600" />
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Activity className="h-3 w-3 text-green-600" />
            <span className="text-muted-foreground">Performance</span>
            <CheckCircle className="h-3 w-3 text-green-600" />
          </div>
        </div>

        {/* Issues se houver */}
        {health.issues.length > 0 && (
          <div className="space-y-1">
            <p className="text-xs font-medium text-muted-foreground">Pontos de Atenção:</p>
            {health.issues.map((issue, index) => (
              <div key={index} className="flex items-center gap-1 text-xs text-yellow-700">
                <AlertTriangle className="h-3 w-3" />
                {issue}
              </div>
            ))}
          </div>
        )}

        {/* Quick Stats */}
        {Array.isArray(cavalos) && (
          <div className="pt-2 border-t border-border">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Cavalos:</span>
                <span className="ml-1 font-medium">{cavalos.length}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Manejos:</span>
                <span className="ml-1 font-medium">{Array.isArray(manejos) ? manejos.length : 0}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SystemHealthWidget;