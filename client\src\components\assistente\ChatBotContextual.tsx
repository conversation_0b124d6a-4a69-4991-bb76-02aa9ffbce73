import React, { useState, useRef, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { 
  useAssistenteContextual, 
  ASSISTANT_TOPICS, 
  AssistantTopic 
} from "@/hooks/use-assistente-contextual";
import { 
  Loader2, 
  Send, 
  ChevronDown, 
  ChevronUp, 
  X, 
  Bot, 
  Info, 
  Plus 
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON> } from "@shared/schema";
import { useCavalo } from "@/hooks/use-cavalo";

interface ChatBotProps {
  initialOpen?: boolean;
}

function ChatBotContextual({ initialOpen = false }: ChatBotProps) {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [message, setMessage] = useState("");
  const [selectedTab, setSelectedTab] = useState<string>("chat");
  const [selectedHorseId, setSelectedHorseId] = useState<string | null>(null);
  
  const { 
    messages, 
    sendMessage, 
    isLoading, 
    clearMessages,
    selectTopic,
    topics,
    contextData
  } = useAssistenteContextual(selectedHorseId);
  
  const { cavalosFiltrados: cavalos } = useCavalo();
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Rolar para o final das mensagens quando novas mensagens forem adicionadas
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focar no input quando o chatbot for aberto
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(focusInput, 100);
    }
  }, [isOpen]);

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (message.trim() === "") return;
    
    try {
      console.log("Enviando mensagem:", message);
      
      // Tentar enviar a mensagem e definir um timeout de 15 segundos
      const messagePromise = sendMessage(message);
      
      // Criar uma promise de timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Tempo limite de conexão excedido")), 15000);
      });
      
      // Qual promise resolver primeiro
      await Promise.race([messagePromise, timeoutPromise]);
      
      setMessage("");
      setTimeout(focusInput, 100);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      let errorMsg = "Não foi possível conectar ao assistente. Tente novamente mais tarde.";
      
      if (error instanceof Error) {
        errorMsg = error.message;
      }
      
      toast({
        title: "Erro ao enviar mensagem",
        description: errorMsg,
        variant: "destructive",
      });
    }
  };

  const handleChatToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClearChat = () => {
    clearMessages();
    toast({
      title: "Chat limpo",
      description: "Todas as mensagens foram removidas.",
    });
  };
  
  const handleTopicSelect = (topicId: string) => {
    selectTopic(topicId);
    setSelectedTab("chat"); // Voltar para a aba de chat após selecionar um tópico
  };

  // Renderizar o estado de coleta atual
  const renderCollectionState = () => {
    if (contextData.collectionMode === "idle") {
      return null;
    }
    
    let statusText = "";
    let statusColor = "";
    
    switch (contextData.collectionMode) {
      case "collecting_horse":
        statusText = "Coletando informações sobre o cavalo";
        statusColor = "blue";
        break;
      case "collecting_reason":
        statusText = "Coletando detalhes da pergunta";
        statusColor = "yellow";
        break;
      case "analyzing":
        statusText = "Analisando informações";
        statusColor = "green";
        break;
    }
    
    return (
      <div className="absolute top-0 left-0 right-0 z-10 p-2 text-xs text-center" 
           style={{ backgroundColor: `${statusColor}20`, borderBottom: `1px solid ${statusColor}60` }}>
        <Badge variant="outline" className={`bg-${statusColor}-100 text-${statusColor}-800 border-${statusColor}-300`}>
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          {statusText}
        </Badge>
      </div>
    );
  };

  return (
    <>
      {/* Botão flutuante para abrir o chat */}
      <Button
        onClick={handleChatToggle}
        className="fixed bottom-6 right-6 rounded-full p-3 w-14 h-14 shadow-lg bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099] z-50"
        aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Bot className="h-6 w-6" />
        )}
      </Button>

      {/* Janela do chat */}
      <div
        className={`fixed bottom-24 right-6 z-50 transition-all duration-300 ease-in-out ${
          isOpen
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-8 pointer-events-none"
        }`}
      >
        <Card className="w-[350px] md:w-[450px] h-[500px] shadow-xl border border-gray-200 flex flex-col">
          <CardHeader className="bg-gradient-to-r from-[#0A3364] to-[#134282] text-white py-4 rounded-t-lg flex flex-row items-center justify-between space-y-0">
            <div className="flex flex-col space-y-1.5">
              <CardTitle className="text-lg font-bold">Assistente EquiGestor</CardTitle>
              <CardDescription className="text-gray-200 text-sm">
                Tire suas dúvidas sobre cuidados equinos
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClearChat}
                className="h-8 w-8 text-white hover:bg-blue-600"
                aria-label="Limpar conversa"
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleChatToggle}
                className="h-8 w-8 text-white hover:bg-blue-600"
                aria-label="Fechar assistente"
              >
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </CardHeader>

          <Tabs 
            defaultValue="chat" 
            value={selectedTab} 
            onValueChange={setSelectedTab}
            className="flex-1 flex flex-col"
          >
            <div className="px-4 pt-3 flex items-center justify-between">
              <TabsList>
                <TabsTrigger value="chat">Chat</TabsTrigger>
                <TabsTrigger value="topics">Tópicos</TabsTrigger>
              </TabsList>
              
              <Select
                value={selectedHorseId || "nenhum"}
                onValueChange={(value) => setSelectedHorseId(value === "nenhum" ? null : value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Selecione um cavalo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nenhum">Nenhum cavalo</SelectItem>
                  {cavalos.map((cavalo: Cavalo) => (
                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                      {cavalo.name} - {cavalo.breed}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <TabsContent value="chat" className="flex-1 flex flex-col px-0 pt-0 pb-0 mt-2 relative">
              {renderCollectionState()}
              
              <CardContent className="flex-1 overflow-hidden p-4">
                <ScrollArea className="h-full pr-4" role="log">
                  {messages.length === 0 ? (
                    <div className="h-full flex flex-col items-center justify-center text-center p-4 text-gray-500">
                      <Bot className="h-12 w-12 mb-4 text-gray-400" />
                      <p className="text-sm">
                        Olá! Sou o assistente virtual do EquiGestor AI. Como posso ajudar você com seus cavalos hoje?
                      </p>
                      <p className="text-xs mt-2 text-gray-400">
                        Tente perguntar sobre nutrição, saúde, vacinação ou reprodução equina!
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-4" 
                        onClick={() => setSelectedTab("topics")}
                      >
                        <Info className="h-4 w-4 mr-2" />
                        Ver tópicos sugeridos
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((msg) => (
                        <div
                          key={msg.id}
                          className={`flex ${
                            msg.type === "user" ? "justify-end" : 
                            msg.type === "system" ? "justify-center" : "justify-start"
                          }`}
                        >
                          {msg.type === "system" ? (
                            <div className="max-w-[90%] bg-gray-100 dark:bg-gray-800 rounded-lg p-2 text-xs text-center text-gray-500">
                              <Info className="h-3 w-3 inline mr-1" />
                              {msg.content}
                            </div>
                          ) : (
                            <div
                              className={`max-w-[80%] rounded-lg p-3 ${
                                msg.type === "user"
                                  ? "bg-blue-600 text-white ml-4"
                                  : "bg-gray-100 dark:bg-gray-800 mr-4"
                              }`}
                            >
                              {msg.type === "assistant" && (
                                <div className="flex items-center mb-2">
                                  <Avatar className="h-6 w-6 mr-2">
                                    <AvatarImage src="/assets/eq-icon.png" alt="Assistente" />
                                    <AvatarFallback>EQ</AvatarFallback>
                                  </Avatar>
                                  <span className="text-xs font-semibold">Assistente EquiGestor</span>
                                </div>
                              )}
                              <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                              <div className="mt-1 text-right">
                                <span
                                  className={`text-xs ${
                                    msg.type === "user" ? "text-blue-200" : "text-gray-500"
                                  }`}
                                >
                                  {new Intl.DateTimeFormat("pt-BR", {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }).format(msg.timestamp)}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>
              </CardContent>

              <CardFooter className="border-t p-3">
                <form onSubmit={handleSendMessage} className="flex space-x-2 w-full">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Digite sua mensagem..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                    aria-label="Mensagem para o assistente"
                  />
                  <Button
                    type="submit"
                    disabled={isLoading || message.trim() === ""}
                    className="bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099]"
                    aria-label="Enviar mensagem"
                  >
                    {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                  </Button>
                </form>
              </CardFooter>
            </TabsContent>

            <TabsContent value="topics" className="flex-1 flex flex-col px-4 pt-0 pb-4 mt-2">
              <div className="mb-3 text-sm text-gray-600">
                <h3 className="font-medium">Tópicos sugeridos</h3>
                <p className="text-xs mt-1">Selecione um tópico para iniciar uma conversa guiada</p>
              </div>
              
              <ScrollArea className="flex-1 pr-4">
                <div className="space-y-2">
                  {ASSISTANT_TOPICS.map((topic) => (
                    <Card 
                      key={topic.id} 
                      className="p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => handleTopicSelect(topic.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium text-sm">{topic.title}</h4>
                          <p className="text-xs text-gray-500">{topic.description}</p>
                        </div>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
              
              <div className="mt-4">
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={() => setSelectedTab("chat")}
                >
                  Voltar para o chat
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </>
  );
}

export default ChatBotContextual;