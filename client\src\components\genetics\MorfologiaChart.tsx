import React, { useState } from 'react';
import { AvaliacaoMorfologica } from '@/types/genetica';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { File, User, Calendar, Medal, FileText, Info, ChevronRight, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// Implementação avançada de gráfico de radar com CSS
// Suporta: temas, animação, interatividade e responsividade
const EnhancedRadarChart: React.FC<{ 
  data: {[key: string]: number},
  maxValue?: number,
  labels?: {[key: string]: string},
  descriptions?: {[key: string]: string},
  theme?: 'primary' | 'secondary' | 'gradient',
  animate?: boolean
}> = ({ 
  data, 
  maxValue = 10, 
  labels = {}, 
  descriptions = {},
  theme = 'primary',
  animate = true 
}) => {
  const [highlightedPoint, setHighlightedPoint] = useState<string | null>(null);
  
  // Calcular ângulos e posições para os pontos do gráfico
  const points = Object.keys(data);
  const angleStep = (2 * Math.PI) / points.length;
  const chartSize = 150; // raio do gráfico
  const center = chartSize;
  
  // Configurações de tema
  const themeConfig = {
    primary: {
      polygonFill: 'rgba(59, 130, 246, 0.3)',
      polygonStroke: '#3b82f6',
      pointHighlight: 'rgba(59, 130, 246, 0.5)',
    },
    secondary: {
      polygonFill: 'rgba(99, 102, 241, 0.3)',
      polygonStroke: '#6366f1',
      pointHighlight: 'rgba(99, 102, 241, 0.5)',
    },
    gradient: {
      polygonFill: 'url(#radarGradient)',
      polygonStroke: '#8b5cf6',
      pointHighlight: 'rgba(139, 92, 246, 0.5)',
    }
  };
  
  // Cores baseadas na pontuação e no tema
  const getPointColor = (value: number, isHighlighted: boolean) => {
    // Cores base para os pontos (independente do tema)
    const baseColor = isHighlighted ? {
      low: '#ef4444', // Vermelho mais brilhante
      mid: '#f59e0b', // Âmbar mais brilhante
      high: '#0ea5e9', // Azul mais brilhante
      excellent: '#10b981', // Verde mais brilhante
    } : {
      low: '#dc2626', // Vermelho
      mid: '#d97706', // Âmbar
      high: '#0284c7', // Azul
      excellent: '#059669', // Verde
    };
    
    if (value <= 3) return baseColor.low;
    if (value <= 6) return baseColor.mid;
    if (value <= 8) return baseColor.high;
    return baseColor.excellent;
  };
  
  // Gerar os pontos do polígono para a área do gráfico
  const polygonPoints = points.map((key, i) => {
    const angle = angleStep * i - Math.PI / 2; // Iniciar no topo (-90º)
    const value = data[key] / maxValue;
    const x = center + chartSize * value * Math.cos(angle);
    const y = center + chartSize * value * Math.sin(angle);
    return `${x},${y}`;
  }).join(' ');
  
  return (
    <div className="flex flex-col items-center">
      <div className="w-full max-w-[320px] h-[320px] relative">
        <svg 
          viewBox={`0 0 ${center * 2} ${center * 2}`} 
          className="w-full h-full"
        >
          {/* Gradiente para o tema gradient */}
          <defs>
            <radialGradient id="radarGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
              <stop offset="0%" stopColor="rgba(139, 92, 246, 0.8)" />
              <stop offset="100%" stopColor="rgba(79, 70, 229, 0.2)" />
            </radialGradient>
          </defs>
          
          {/* Círculos de fundo - níveis com rótulos */}
          {[0.2, 0.4, 0.6, 0.8, 1].map((level, i) => (
            <React.Fragment key={i}>
              <circle 
                cx={center}
                cy={center}
                r={chartSize * level}
                fill="none"
                stroke={i === 4 ? "#d1d5db" : "#e5e7eb"}
                strokeWidth={i === 4 ? "1.5" : "1"}
                strokeDasharray={i === 4 ? "none" : "4,4"}
                className={animate ? "opacity-0 animate-radar-circles" : ""}
                style={animate ? { animationDelay: `${i * 0.1}s` } : {}}
              />
              {/* Rótulos de nível */}
              {i % 2 === 0 && (
                <text 
                  x={center}
                  y={center - (chartSize * level) - 5}
                  textAnchor="middle"
                  fontSize="8"
                  fill="#9ca3af"
                >
                  {Math.round(level * maxValue)}
                </text>
              )}
            </React.Fragment>
          ))}
          
          {/* Linhas para cada característica */}
          {points.map((key, i) => {
            const angle = angleStep * i - Math.PI / 2;
            const x = center + chartSize * Math.cos(angle);
            const y = center + chartSize * Math.sin(angle);
            return (
              <line 
                key={key}
                x1={center}
                y1={center}
                x2={x}
                y2={y}
                stroke={highlightedPoint === key ? "#9ca3af" : "#e5e7eb"}
                strokeWidth={highlightedPoint === key ? "1.5" : "1"}
                className={animate ? "opacity-0 animate-radar-lines" : ""}
                style={animate ? { animationDelay: `${i * 0.05}s` } : {}}
              />
            );
          })}
          
          {/* Polígono de valores */}
          <polygon 
            points={polygonPoints}
            fill={themeConfig[theme].polygonFill}
            stroke={themeConfig[theme].polygonStroke}
            strokeWidth="2.5"
            strokeLinejoin="round"
            className={animate ? "opacity-0 animate-radar-polygon" : ""}
            style={{ 
              transition: "all 0.3s ease",
              filter: "drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1))"
            }}
          />
          
          {/* Pontos para cada valor com interatividade */}
          {points.map((key, i) => {
            const angle = angleStep * i - Math.PI / 2;
            const value = data[key] / maxValue;
            const x = center + chartSize * value * Math.cos(angle);
            const y = center + chartSize * value * Math.sin(angle);
            const isHighlighted = highlightedPoint === key;
            
            return (
              <g 
                key={key}
                onMouseEnter={() => setHighlightedPoint(key)}
                onMouseLeave={() => setHighlightedPoint(null)}
                style={animate ? { 
                  cursor: "pointer",
                  animationDelay: `${0.5 + i * 0.05}s` 
                } : { cursor: "pointer" }}
                className={animate ? "opacity-0 animate-radar-points" : ""}
              >
                {/* Círculo de fundo para hover */}
                {isHighlighted && (
                  <circle 
                    cx={x}
                    cy={y}
                    r="10"
                    fill={themeConfig[theme].pointHighlight}
                    className="animate-pulse"
                  />
                )}
                
                {/* Ponto principal */}
                <circle 
                  cx={x}
                  cy={y}
                  r={isHighlighted ? "6" : "5"}
                  fill={getPointColor(data[key], isHighlighted)}
                  stroke="white"
                  strokeWidth="1.5"
                  style={{ transition: "all 0.2s ease" }}
                />
                
                {/* Valor ao lado do ponto quando destacado */}
                {isHighlighted && (
                  <text 
                    x={x}
                    y={y - 15}
                    textAnchor="middle"
                    fontSize="11"
                    fontWeight="bold"
                    fill="#4b5563"
                    className="animate-fade-in"
                  >
                    {data[key]}
                  </text>
                )}
              </g>
            );
          })}
          
          {/* Etiquetas das características */}
          {points.map((key, i) => {
            const angle = angleStep * i - Math.PI / 2;
            const labelDistance = chartSize * 1.15; // Distância extra para as labels
            const x = center + labelDistance * Math.cos(angle);
            const y = center + labelDistance * Math.sin(angle);
            
            // Ajustar alinhamento de texto com base no ângulo
            const textAnchor = 
              angle === -Math.PI / 2 ? "middle" : 
              angle > -Math.PI / 2 && angle < Math.PI / 2 ? "start" : "end";
            
            const labelText = labels[key] || key;
            const isHighlighted = highlightedPoint === key;
            
            return (
              <text 
                key={key}
                x={x}
                y={y}
                textAnchor={textAnchor}
                dominantBaseline="middle"
                fontSize={isHighlighted ? "11" : "10"}
                fontWeight={isHighlighted ? "700" : "500"}
                fill={isHighlighted ? "#4b5563" : "#6b7280"}
                onMouseEnter={() => setHighlightedPoint(key)}
                onMouseLeave={() => setHighlightedPoint(null)}
                className={animate ? "opacity-0 animate-radar-labels" : ""}
                style={{
                  transition: "all 0.2s ease",
                  ...(animate && { animationDelay: `${0.3 + i * 0.05}s` })
                }}
              >
                {labelText}
              </text>
            );
          })}
        </svg>
      </div>
      
      {/* Legenda interativa para os valores */}
      <div className="flex flex-wrap gap-3 justify-center mt-4">
        {points.map(key => {
          const isHighlighted = highlightedPoint === key;
          const description = descriptions[key] || '';
          
          return (
            <TooltipProvider key={key}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className={`flex items-center space-x-1.5 p-1 rounded-full ${isHighlighted ? 'bg-gray-100' : ''}`}
                    onMouseEnter={() => setHighlightedPoint(key)}
                    onMouseLeave={() => setHighlightedPoint(null)}
                    style={{ cursor: "pointer", transition: "all 0.2s ease" }}
                  >
                    <span className="h-3.5 w-3.5 rounded-full" style={{ 
                      backgroundColor: getPointColor(data[key], isHighlighted),
                      boxShadow: isHighlighted ? '0 0 0 2px rgba(0,0,0,0.05)' : 'none',
                      transition: "all 0.2s ease"
                    }}></span>
                    <span className={`text-xs ${isHighlighted ? 'font-semibold' : ''}`}>
                      {labels[key] || key}: <strong>{data[key]}</strong>
                    </span>
                  </div>
                </TooltipTrigger>
                {description && (
                  <TooltipContent side="bottom" className="max-w-[200px] text-xs">
                    {description}
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    </div>
  );
};

interface MorfologiaChartProps {
  avaliacao: AvaliacaoMorfologica;
}

export default function MorfologiaChart({ avaliacao }: MorfologiaChartProps) {
  // Verificar se a avaliação está vazia ou tem valores inválidos
  const avaliacaoVazia = !avaliacao || 
    Object.values({
      pontuacaoCabeca: avaliacao.pontuacaoCabeca,
      pontuacaoPescoco: avaliacao.pontuacaoPescoco,
      pontuacaoEspadua: avaliacao.pontuacaoEspadua,
      pontuacaoDorso: avaliacao.pontuacaoDorso,
      pontuacaoGarupa: avaliacao.pontuacaoGarupa,
      pontuacaoMembrosDianteiros: avaliacao.pontuacaoMembrosDianteiros,
      pontuacaoMembrosPosteriores: avaliacao.pontuacaoMembrosPosteriores,
      pontuacaoAndamento: avaliacao.pontuacaoAndamento
    }).some(v => v === undefined || v === null);
  
  if (avaliacaoVazia) {
    return (
      <div className="flex flex-col items-center justify-center h-60 border rounded-lg bg-muted/20">
        <FileText className="h-10 w-10 text-muted-foreground mb-3 opacity-60" />
        <p className="text-muted-foreground text-sm">Nenhuma avaliação morfológica disponível</p>
        <Button variant="outline" size="sm" className="mt-4">
          Nova Avaliação
        </Button>
      </div>
    );
  }
  // Estado para controlar as abas
  const [activeTab, setActiveTab] = useState("grafico");
  const [isCollapsed, setIsCollapsed] = useState(true);
  
  // Converter os dados para o formato do gráfico
  const chartData = {
    'cabeca': avaliacao.pontuacaoCabeca,
    'pescoco': avaliacao.pontuacaoPescoco,
    'espadua': avaliacao.pontuacaoEspadua,
    'dorso': avaliacao.pontuacaoDorso,
    'garupa': avaliacao.pontuacaoGarupa,
    'membrosDianteiros': avaliacao.pontuacaoMembrosDianteiros,
    'membrosPosteriores': avaliacao.pontuacaoMembrosPosteriores,
    'andamento': avaliacao.pontuacaoAndamento,
  };
  
  // Tradução das etiquetas para o gráfico
  const labels = {
    'cabeca': 'Cabeça',
    'pescoco': 'Pescoço',
    'espadua': 'Espádua',
    'dorso': 'Dorso',
    'garupa': 'Garupa',
    'membrosDianteiros': 'M. Dianteiros',
    'membrosPosteriores': 'M. Posteriores',
    'andamento': 'Andamento',
  };
  
  // Descrições detalhadas para cada característica
  const descriptions = {
    'cabeca': 'Formato, proporções e características específicas da raça. Inclui avaliação dos olhos, narinas e expressão geral.',
    'pescoco': 'Forma, comprimento, inserção e musculatura. Um bom pescoço deve apresentar curvatura adequada e inserção harmoniosa.',
    'espadua': 'Inclinação, comprimento e musculatura. Espáduas bem inclinadas permitem maior amplitude de movimento.',
    'dorso': 'Comprimento, largura e conformação. Um dorso forte e bem proporcionado é importante para o equilíbrio.',
    'garupa': 'Comprimento, largura e inclinação. A garupa influencia diretamente a propulsão e potência do cavalo.',
    'membrosDianteiros': 'Conformação, aprumos e articulações. Membros dianteiros corretos garantem estabilidade e absorção de impacto.',
    'membrosPosteriores': 'Conformação, aprumos e potência. Os posteriores fornecem a principal força de propulsão.',
    'andamento': 'Qualidade, regularidade, amplitude e correção dos movimentos. Um bom andamento reflete a funcionalidade global.',
  };
  
  // Identificar pontos fortes (pontuação >= 8)
  const pontosFortes: string[] = [];
  if (avaliacao.pontuacaoCabeca >= 8) pontosFortes.push("Cabeça");
  if (avaliacao.pontuacaoPescoco >= 8) pontosFortes.push("Pescoço");
  if (avaliacao.pontuacaoEspadua >= 8) pontosFortes.push("Espádua");
  if (avaliacao.pontuacaoDorso >= 8) pontosFortes.push("Dorso");
  if (avaliacao.pontuacaoGarupa >= 8) pontosFortes.push("Garupa");
  if (avaliacao.pontuacaoMembrosDianteiros >= 8) pontosFortes.push("Membros Dianteiros");
  if (avaliacao.pontuacaoMembrosPosteriores >= 8) pontosFortes.push("Membros Posteriores");
  if (avaliacao.pontuacaoAndamento >= 8) pontosFortes.push("Andamento");
  
  // Identificar pontos fracos (pontuação <= 3)
  const pontosFracos: string[] = [];
  if (avaliacao.pontuacaoCabeca <= 3) pontosFracos.push("Cabeça");
  if (avaliacao.pontuacaoPescoco <= 3) pontosFracos.push("Pescoço");
  if (avaliacao.pontuacaoEspadua <= 3) pontosFracos.push("Espádua");
  if (avaliacao.pontuacaoDorso <= 3) pontosFracos.push("Dorso");
  if (avaliacao.pontuacaoGarupa <= 3) pontosFracos.push("Garupa");
  if (avaliacao.pontuacaoMembrosDianteiros <= 3) pontosFracos.push("Membros Dianteiros");
  if (avaliacao.pontuacaoMembrosPosteriores <= 3) pontosFracos.push("Membros Posteriores");
  if (avaliacao.pontuacaoAndamento <= 3) pontosFracos.push("Andamento");
  
  // Calcular nota média
  const valoresTotais = Object.values(chartData);
  const media = valoresTotais.reduce((a, b) => a + b, 0) / valoresTotais.length;
  
  // Classificação geral com base na média
  const getClassificacao = (media: number) => {
    if (media >= 9) return { texto: "Excelente", cor: "text-green-600" };
    if (media >= 7.5) return { texto: "Muito Bom", cor: "text-blue-600" };
    if (media >= 6) return { texto: "Bom", cor: "text-blue-500" };
    if (media >= 5) return { texto: "Regular", cor: "text-amber-500" };
    if (media >= 3) return { texto: "Insatisfatório", cor: "text-amber-600" };
    return { texto: "Crítico", cor: "text-red-600" };
  };
  
  const classificacao = getClassificacao(media);
  
  // Recomendações específicas
  const getRecomendacoes = (): string[] => {
    const recomendacoes: string[] = [];
    
    // Recomendações com base nos pontos fracos
    if (pontosFracos.length > 0) {
      pontosFracos.forEach(ponto => {
        if (ponto === "Membros Dianteiros" || ponto === "Membros Posteriores") {
          recomendacoes.push("Avaliação ortopédica detalhada recomendada");
        }
        if (ponto === "Andamento") {
          recomendacoes.push("Considerar sessões de treinamento específicas para melhorar o andamento");
        }
      });
    }
    
    // Recomendações gerais com base na média
    if (media < 5) {
      recomendacoes.push("Considerar programa de condicionamento físico específico");
    }
    
    // Se não houver pontos fracos específicos
    if (recomendacoes.length === 0 && media >= 7) {
      recomendacoes.push("Manter o manejo atual, excelente condição morfológica");
    } else if (recomendacoes.length === 0) {
      recomendacoes.push("Monitorar condição física regularmente");
    }
    
    return recomendacoes;
  };
  
  const recomendacoes = getRecomendacoes();
  
  // Temas disponíveis para o gráfico (pode ser expandido para personalização)
  const temas = [
    { id: 'primary', nome: 'Padrão' },
    { id: 'secondary', nome: 'Alternativo' },
    { id: 'gradient', nome: 'Gradiente' }
  ];
  
  // Estado para controlar o tema ativo
  const [temaAtivo, setTemaAtivo] = useState('primary');
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/2">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div>
                  <CardTitle>Análise Morfológica</CardTitle>
                  <CardDescription>
                    Visualização detalhada (escala 1-10)
                  </CardDescription>
                </div>
                
                <div className="mt-2 sm:mt-0">
                  <Tabs 
                    value={temaAtivo} 
                    onValueChange={setTemaAtivo}
                    className="w-[220px]"
                  >
                    <TabsList className="grid grid-cols-3 h-8">
                      {temas.map(tema => (
                        <TabsTrigger 
                          key={tema.id} 
                          value={tema.id}
                          className="text-xs px-2"
                        >
                          {tema.nome}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </Tabs>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <EnhancedRadarChart 
                data={chartData} 
                labels={labels}
                descriptions={descriptions}
                maxValue={10}
                theme={temaAtivo as 'primary' | 'secondary' | 'gradient'}
                animate={true}
              />
            </CardContent>
          </Card>
        </div>
        
        <div className="md:w-1/2">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Detalhes da Avaliação</CardTitle>
              <CardDescription>
                Informações completas sobre a avaliação morfológica
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {avaliacao.dataMedicao ? 
                      format(new Date(avaliacao.dataMedicao), "PPP", { locale: ptBR }) : 
                      "Data não informada"}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Avaliador: <span className="font-medium">{avaliacao.avaliador}</span></span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Medal className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Pontuação total: <span className="font-medium">{avaliacao.pontuacaoTotal}/80</span> 
                    <span className="ml-1 text-xs text-muted-foreground">(Média: {media.toFixed(1)}/10)</span>
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Classificação: <span className={`font-medium ${classificacao.cor}`}>{classificacao.texto}</span>
                  </span>
                </div>
                
                {avaliacao.observacoes && (
                  <div className="flex items-start gap-2 mt-2">
                    <FileText className="h-4 w-4 text-muted-foreground shrink-0 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium mb-1">Observações:</p>
                      <p className="text-muted-foreground">{avaliacao.observacoes}</p>
                    </div>
                  </div>
                )}
              </div>
              
              <Separator />
              
              <Collapsible
                open={!isCollapsed}
                onOpenChange={(open) => setIsCollapsed(!open)}
                className="space-y-2"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Análise Detalhada</h3>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="w-9 p-0">
                      {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                    </Button>
                  </CollapsibleTrigger>
                </div>
                
                <CollapsibleContent className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Pontos Fortes</h4>
                    <div className="flex flex-wrap gap-1">
                      {pontosFortes.length > 0 ? (
                        pontosFortes.map((ponto) => (
                          <Badge key={ponto} variant="default">
                            {ponto}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-sm text-muted-foreground">
                          Nenhum ponto forte significativo identificado
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Pontos de Melhoria</h4>
                    <div className="flex flex-wrap gap-1">
                      {pontosFracos.length > 0 ? (
                        pontosFracos.map((ponto) => (
                          <Badge key={ponto} variant="destructive">
                            {ponto}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-sm text-muted-foreground">
                          Nenhum ponto fraco significativo identificado
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Recomendações</h4>
                    <ul className="text-sm text-muted-foreground list-disc list-inside">
                      {recomendacoes.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </CardContent>
            <CardFooter className="pt-0">
              <Button variant="outline" size="sm" className="w-full text-xs" onClick={() => window.print()}>
                Exportar Avaliação
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}