import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// Debugging flag
const DEBUG = true;

// Logger function
const log = (...args: any[]) => {
  if (DEBUG) {
    console.log('[Firebase]', ...args);
  }
};

// Verifica se as variáveis de ambiente necessárias existem
const hasFirebaseConfig = import.meta.env.VITE_FIREBASE_API_KEY && 
                          import.meta.env.VITE_FIREBASE_PROJECT_ID && 
                          import.meta.env.VITE_FIREBASE_APP_ID;

// Firebase configuration
const firebaseConfig = hasFirebaseConfig ? {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: `${import.meta.env.VITE_FIREBASE_PROJECT_ID}.firebaseapp.com`,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: `${import.meta.env.VITE_FIREBASE_PROJECT_ID}.firebasestorage.app`,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
} : null;

// Create a mock app and services if Firebase is not configured
let app;
let auth;
let db;

// Inicializa o Firebase apenas se a configuração existir
if (firebaseConfig) {
  log('Initializing Firebase with config:', JSON.stringify(firebaseConfig, (key, value) => 
    key === 'apiKey' ? '*****' : value
  ));
  
  try {
    app = initializeApp(firebaseConfig);
    auth = getAuth(app);
    db = getFirestore(app);
  } catch (error) {
    console.error("Erro ao inicializar Firebase:", error);
    app = null;
    auth = null;
    db = null;
  }
} else {
  console.warn("Firebase não configurado. Usando autenticação local.");
  app = null;
  auth = null;
  db = null;
}

// Export the Firebase services
export { auth, db };
export default app;
