/**
 * Dedicated API server that bypasses Vite middleware completely
 */
import express from 'express';
import cors from 'cors';
import { addApiRoutes } from './routes.js';

const apiApp = express();

// CORS configuration
apiApp.use(
  cors({
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'user-id'],
  }),
);

// Body parsing
apiApp.use(express.json({ limit: '2mb' }));
apiApp.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Add API routes
await addApiRoutes(apiApp);

// Start API server on a different port
const API_PORT = 3001;
apiApp.listen(API_PORT, '0.0.0.0', () => {
  console.log(`🚀 API Server running on port ${API_PORT}`);
});

export { apiApp };
