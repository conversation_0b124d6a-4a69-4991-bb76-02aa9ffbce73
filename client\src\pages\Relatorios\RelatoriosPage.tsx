import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectGroup, 
  SelectItem, 
  SelectLabel, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  FileText, 
  Pie<PERSON>hart, 
  BarChart, 
  LineChart, 
  Download, 
  Calendar,
  ArrowRightLeft,
  DollarSign,
  FileSpreadsheet,
  FileIcon,
  ChevronRight,
  List,
  ListFilter,
  Settings2
} from 'lucide-react';
import { LayoutWrapper } from '@/components/Layout';
import { DatePicker } from '@/components/DatePicker';

/**
 * Interface para definir a estrutura de um relatório
 */
interface Relatorio {
  id: string;
  categoria: string;
  nome: string;
  descricao: string;
  formato: 'pdf' | 'excel' | 'csv';
  icone: JSX.Element;
}

/**
 * Componente principal da página de Relatórios
 */
export function RelatoriosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoriaFilter, setCategoriaFilter] = useState('todos');
  const [dataInicio, setDataInicio] = useState<Date | undefined>(undefined);
  const [dataFim, setDataFim] = useState<Date | undefined>(undefined);
  const [activeTab, setActiveTab] = useState('relatorios');

  // Filtrar relatórios com base nos filtros aplicados
  const filteredRelatorios = relatoriosData.filter(relatorio => {
    const matchesSearch = 
      relatorio.nome.toLowerCase().includes(searchTerm.toLowerCase()) || 
      relatorio.descricao.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategoria = categoriaFilter === 'todos' || relatorio.categoria === categoriaFilter;
    
    return matchesSearch && matchesCategoria;
  });

  // Função para renderizar o ícone do formato do relatório
  const renderFormatoIcon = (formato: 'pdf' | 'excel' | 'csv') => {
    switch (formato) {
      case 'pdf':
        return <FileIcon className="h-4 w-4 text-red-600" />;
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
      case 'csv':
        return <FileText className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <LayoutWrapper pageTitle="Relatórios">
      <div className="flex flex-col gap-6">
        {/* Cabeçalho com ações principais */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Relatórios e Análises</h1>
            <p className="text-muted-foreground">
              Gere relatórios personalizados e visualize dados importantes do sistema
            </p>
          </div>
          
          <Button variant="outline" className="flex items-center gap-2">
            <Settings2 className="h-4 w-4" />
            <span>Configurar Relatórios</span>
          </Button>
        </div>
        
        {/* Tabs para alternar entre relatórios e dashboards */}
        <Tabs 
          defaultValue="relatorios" 
          value={activeTab} 
          onValueChange={setActiveTab} 
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
            <TabsTrigger value="relatorios" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Relatórios
            </TabsTrigger>
            <TabsTrigger value="dashboards" className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              Dashboards
            </TabsTrigger>
          </TabsList>
          
          {/* Conteúdo de Relatórios */}
          <TabsContent value="relatorios">
            <div className="flex flex-col gap-6">
              {/* Filtros de relatórios */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Filtros</CardTitle>
                  <CardDescription>
                    Encontre e gere relatórios específicos
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="relative">
                      <Input
                        type="search"
                        placeholder="Buscar relatórios..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    
                    <div className="flex gap-4">
                      <Select 
                        value={categoriaFilter} 
                        onValueChange={setCategoriaFilter}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Categoria" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="todos">Todas as Categorias</SelectItem>
                          <SelectItem value="animais">Animais</SelectItem>
                          <SelectItem value="financeiro">Financeiro</SelectItem>
                          <SelectItem value="veterinario">Veterinário</SelectItem>
                          <SelectItem value="reproducao">Reprodução</SelectItem>
                          <SelectItem value="estoque">Estoque</SelectItem>
                          <SelectItem value="movimentacoes">Movimentações</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">Período:</span>
                      <DatePicker
                        date={dataInicio}
                        setDate={setDataInicio}
                        placeholder="Data inicial"
                      />
                      <span className="text-muted-foreground">até</span>
                      <DatePicker
                        date={dataFim}
                        setDate={setDataFim}
                        placeholder="Data final"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Lista de relatórios */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredRelatorios.length === 0 ? (
                  <div className="md:col-span-2 lg:col-span-3 text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">Nenhum relatório encontrado</h3>
                    <p className="text-muted-foreground">
                      Tente ajustar os critérios de busca ou selecionar outra categoria
                    </p>
                  </div>
                ) : (
                  filteredRelatorios.map(relatorio => (
                    <Card key={relatorio.id} className="hover:shadow-md transition-shadow">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="bg-primary/10 p-2 rounded-md">
                            {relatorio.icone}
                          </div>
                          <div className="flex items-center gap-2">
                            {renderFormatoIcon(relatorio.formato)}
                            <span className="text-xs text-muted-foreground uppercase">
                              {relatorio.formato}
                            </span>
                          </div>
                        </div>
                        <CardTitle className="text-lg mt-4">{relatorio.nome}</CardTitle>
                        <CardDescription>{relatorio.descricao}</CardDescription>
                      </CardHeader>
                      <CardFooter className="flex justify-between">
                        <span className="text-xs text-muted-foreground">
                          Categoria: {relatorio.categoria}
                        </span>
                        <Link href={`/relatorios/gerar/${relatorio.id}`}>
                          <Button variant="default" size="sm" className="flex items-center gap-2">
                            <Download className="h-4 w-4" />
                            <span>Gerar</span>
                          </Button>
                        </Link>
                      </CardFooter>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
          
          {/* Conteúdo de Dashboards */}
          <TabsContent value="dashboards">
            {/* Cards de dashboards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <LineChart className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Evolução do Plantel</CardTitle>
                  <CardDescription>
                    Acompanhe o crescimento do plantel ao longo do tempo
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <BarChart className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do gráfico</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado diariamente
                  </span>
                  <Link href="/dashboard/evolucao-plantel">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <DollarSign className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Indicadores Financeiros</CardTitle>
                  <CardDescription>
                    Monitore receitas, despesas e rentabilidade
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <PieChart className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do gráfico</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado diariamente
                  </span>
                  <Link href="/dashboard/financeiro">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Calendário de Atividades</CardTitle>
                  <CardDescription>
                    Visualize todas as atividades programadas
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <Calendar className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do calendário</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado em tempo real
                  </span>
                  <Link href="/dashboard/calendario">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <List className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Inventário e Estoque</CardTitle>
                  <CardDescription>
                    Acompanhe o nível de estoque de insumos e medicamentos
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <BarChart className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do gráfico</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado diariamente
                  </span>
                  <Link href="/dashboard/estoque">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <ArrowRightLeft className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Movimentações de Animais</CardTitle>
                  <CardDescription>
                    Acompanhe entradas, saídas e transferências
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <LineChart className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do gráfico</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado diariamente
                  </span>
                  <Link href="/dashboard/movimentacoes">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <ListFilter className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                  <CardTitle className="text-lg mt-4">Métricas de Desempenho</CardTitle>
                  <CardDescription>
                    Monitore indicadores chave de performance
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-48 flex items-center justify-center bg-muted/50 rounded-md">
                  <div className="text-center text-muted-foreground">
                    <PieChart className="mx-auto h-8 w-8 mb-2" />
                    <p>Visualização do gráfico</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <span className="text-xs text-muted-foreground">
                    Atualizado diariamente
                  </span>
                  <Link href="/dashboard/metricas">
                    <Button variant="outline" size="sm">
                      <ChevronRight className="h-4 w-4" />
                      <span>Visualizar</span>
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}

// Dados de exemplo para relatórios (apenas para demonstração)
const relatoriosData: Relatorio[] = [
  {
    id: 'animais-plantel',
    categoria: 'animais',
    nome: 'Relatório de Plantel',
    descricao: 'Lista completa de todos os animais com informações detalhadas',
    formato: 'pdf',
    icone: <FileText className="h-5 w-5 text-primary" />
  },
  {
    id: 'financeiro-mensal',
    categoria: 'financeiro',
    nome: 'Balanço Financeiro Mensal',
    descricao: 'Resumo de receitas e despesas do mês com categorização',
    formato: 'excel',
    icone: <DollarSign className="h-5 w-5 text-primary" />
  },
  {
    id: 'veterinario-procedimentos',
    categoria: 'veterinario',
    nome: 'Procedimentos Veterinários',
    descricao: 'Histórico de todos os procedimentos realizados no período',
    formato: 'pdf',
    icone: <FileText className="h-5 w-5 text-primary" />
  },
  {
    id: 'reproducao-estatisticas',
    categoria: 'reproducao',
    nome: 'Estatísticas de Reprodução',
    descricao: 'Dados de coberturas, nascimentos e índices reprodutivos',
    formato: 'excel',
    icone: <PieChart className="h-5 w-5 text-primary" />
  },
  {
    id: 'estoque-insumos',
    categoria: 'estoque',
    nome: 'Inventário de Insumos',
    descricao: 'Lista completa de todos os insumos em estoque com níveis atuais',
    formato: 'csv',
    icone: <FileText className="h-5 w-5 text-primary" />
  },
  {
    id: 'estoque-medicamentos',
    categoria: 'estoque',
    nome: 'Inventário de Medicamentos',
    descricao: 'Lista de medicamentos com prazo de validade e níveis críticos',
    formato: 'excel',
    icone: <FileSpreadsheet className="h-5 w-5 text-primary" />
  },
  {
    id: 'movimentacoes-periodo',
    categoria: 'movimentacoes',
    nome: 'Movimentações de Animais',
    descricao: 'Registro de entradas, saídas e transferências no período',
    formato: 'pdf',
    icone: <ArrowRightLeft className="h-5 w-5 text-primary" />
  },
  {
    id: 'financeiro-centro-custo',
    categoria: 'financeiro',
    nome: 'Análise por Centro de Custo',
    descricao: 'Despesas detalhadas por centro de custo com comparativos',
    formato: 'excel',
    icone: <BarChart className="h-5 w-5 text-primary" />
  },
  {
    id: 'animais-biometria',
    categoria: 'animais',
    nome: 'Evolução Biométrica',
    descricao: 'Histórico de medições de peso, altura e condição corporal',
    formato: 'excel',
    icone: <LineChart className="h-5 w-5 text-primary" />
  }
];

export default RelatoriosPage;