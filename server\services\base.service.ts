// Base service class with common functionality
import { DatabaseService } from '../core/database';
import { ServiceResponse, PaginationOptions, FilterOptions } from '../types';
import { ValidationError, NotFoundError, AuthorizationError } from '../core/errors';

export abstract class BaseService<T> {
  protected db: DatabaseService;
  protected tableName: string;

  constructor(db: DatabaseService, tableName: string) {
    this.db = db;
    this.tableName = tableName;
  }

  protected abstract validateCreate(data: any): T;
  protected abstract validateUpdate(data: any): Partial<T>;

  protected validateUserAccess(userId: number, resourceUserId: number): void {
    if (userId !== resourceUserId) {
      throw new AuthorizationError('Access denied to this resource');
    }
  }

  protected buildWhereClause(filters: FilterOptions, userIdField: string = 'user_id'): { query: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (filters.userId) {
      conditions.push(`${userIdField} = $${paramIndex}`);
      params.push(filters.userId);
      paramIndex++;
    }

    if (filters.search) {
      conditions.push(`name ILIKE $${paramIndex}`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    if (filters.status) {
      conditions.push(`status = $${paramIndex}`);
      params.push(filters.status);
      paramIndex++;
    }

    if (filters.dateFrom) {
      conditions.push(`created_at >= $${paramIndex}`);
      params.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters.dateTo) {
      conditions.push(`created_at <= $${paramIndex}`);
      params.push(filters.dateTo);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { query: whereClause, params };
  }

  protected buildOrderClause(pagination: PaginationOptions): string {
    const { sortBy = 'created_at', sortOrder = 'desc' } = pagination;
    return `ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
  }

  protected buildLimitClause(pagination: PaginationOptions): { query: string; params: any[] } {
    const { page = 1, limit = 10 } = pagination;
    const offset = (page - 1) * limit;
    return {
      query: `LIMIT $${1} OFFSET $${2}`,
      params: [limit, offset]
    };
  }

  async findMany(
    filters: FilterOptions = {},
    pagination: PaginationOptions = {}
  ): Promise<ServiceResponse<T[]>> {
    try {
      const { query: whereClause, params: whereParams } = this.buildWhereClause(filters);
      const orderClause = this.buildOrderClause(pagination);
      const { query: limitClause, params: limitParams } = this.buildLimitClause(pagination);

      const query = `
        SELECT * FROM ${this.tableName} 
        ${whereClause} 
        ${orderClause} 
        ${limitClause}
      `;

      const params = [...whereParams, ...limitParams];
      const result = await this.db.query<T>(query, params);

      return {
        data: result.data,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'QUERY_ERROR',
          message: 'Failed to fetch records',
          statusCode: 500
        }
      };
    }
  }

  async findById(id: number, userId?: number): Promise<ServiceResponse<T>> {
    try {
      const query = userId 
        ? `SELECT * FROM ${this.tableName} WHERE id = $1 AND user_id = $2`
        : `SELECT * FROM ${this.tableName} WHERE id = $1`;
      
      const params = userId ? [id, userId] : [id];
      const result = await this.db.query<T>(query, params);

      if (result.count === 0) {
        throw new NotFoundError(`${this.tableName} with id ${id}`);
      }

      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'NOT_FOUND',
          message: error.message,
          statusCode: 404
        } : {
          code: 'QUERY_ERROR',
          message: 'Failed to fetch record',
          statusCode: 500
        }
      };
    }
  }

  async create(data: any): Promise<ServiceResponse<T>> {
    try {
      const validatedData = this.validateCreate(data);
      
      const fields = Object.keys(validatedData).join(', ');
      const placeholders = Object.keys(validatedData).map((_, index) => `$${index + 1}`).join(', ');
      const values = Object.values(validatedData);

      const query = `
        INSERT INTO ${this.tableName} (${fields})
        VALUES (${placeholders})
        RETURNING *
      `;

      const result = await this.db.query<T>(query, values);

      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'CREATE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'CREATE_ERROR',
          message: 'Failed to create record',
          statusCode: 500
        }
      };
    }
  }

  async update(id: number, data: any, userId?: number): Promise<ServiceResponse<T>> {
    try {
      const validatedData = this.validateUpdate(data);
      
      // Remove undefined values
      const cleanedData = Object.entries(validatedData)
        .filter(([_, value]) => value !== undefined)
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

      if (Object.keys(cleanedData).length === 0) {
        throw new ValidationError('No valid fields to update');
      }

      const fields = Object.keys(cleanedData).map((key, index) => `${key} = $${index + 1}`).join(', ');
      const values = Object.values(cleanedData);

      const whereClause = userId 
        ? `WHERE id = $${values.length + 1} AND user_id = $${values.length + 2}`
        : `WHERE id = $${values.length + 1}`;
      
      const params = userId ? [...values, id, userId] : [...values, id];

      const query = `
        UPDATE ${this.tableName} 
        SET ${fields}, updated_at = NOW() 
        ${whereClause}
        RETURNING *
      `;

      const result = await this.db.query<T>(query, params);

      if (result.count === 0) {
        throw new NotFoundError(`${this.tableName} with id ${id}`);
      }

      return {
        data: result.data[0],
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'UPDATE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'UPDATE_ERROR',
          message: 'Failed to update record',
          statusCode: 500
        }
      };
    }
  }

  async delete(id: number, userId?: number): Promise<ServiceResponse<boolean>> {
    try {
      const whereClause = userId 
        ? `WHERE id = $1 AND user_id = $2`
        : `WHERE id = $1`;
      
      const params = userId ? [id, userId] : [id];

      const query = `DELETE FROM ${this.tableName} ${whereClause}`;
      const result = await this.db.query(query, params);

      if (result.count === 0) {
        throw new NotFoundError(`${this.tableName} with id ${id}`);
      }

      return {
        data: true,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? {
          code: 'DELETE_ERROR',
          message: error.message,
          statusCode: 400
        } : {
          code: 'DELETE_ERROR',
          message: 'Failed to delete record',
          statusCode: 500
        }
      };
    }
  }

  async count(filters: FilterOptions = {}): Promise<ServiceResponse<number>> {
    try {
      const { query: whereClause, params } = this.buildWhereClause(filters);
      
      const query = `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`;
      const result = await this.db.query<{ count: string }>(query, params);

      return {
        data: parseInt(result.data[0].count),
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'COUNT_ERROR',
          message: 'Failed to count records',
          statusCode: 500
        }
      };
    }
  }
}