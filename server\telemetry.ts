/**
 * Configuração e instrumentação do OpenTelemetry
 * Rastreia requisições HTTP e exporta dados para o coletor
 */
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';
import { logger } from './logger';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';

// URL do coletor OpenTelemetry
const OTEL_COLLECTOR_URL = process.env.OTEL_COLLECTOR_URL || 'http://otel-collector:4317';

/**
 * Inicializa o SDK do OpenTelemetry com as configurações apropriadas
 */
export const initTelemetry = () => {
  try {
    // Cria o exportador para enviar dados ao coletor
    const traceExporter = new OTLPTraceExporter({
      url: `${OTEL_COLLECTOR_URL}/v1/traces`,
    });

    // Configuração do SDK
    const sdk = new NodeSDK({
      resource: resourceFromAttributes({
        [SemanticResourceAttributes.SERVICE_NAME]: 'equigestor-api',
        [SemanticResourceAttributes.SERVICE_VERSION]: process.env.npm_package_version || '1.0.0',
        [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV || 'development',
      }),
      traceExporter,
      instrumentations: [
        // Instrumentação automática para Node.js
        getNodeAutoInstrumentations({
          // Desativa instrumentações que não precisamos
          '@opentelemetry/instrumentation-fs': { enabled: false },
        }),
        // Instrumentação específica para Express
        new ExpressInstrumentation({
          // Configuração para instrumentar apenas rotas /api/*
          ignoreLayersType: ['middleware', 'request_handler'],
          ignoreLayers: ['^(?!/api/).*$'],
        }),
        // Instrumentação específica para HTTP
        new HttpInstrumentation({
          // Ignora requisições para recursos estáticos
          ignoreIncomingRequestHook: (req) => {
            const url = req.url || '';
            // Ignora requisições para arquivos estáticos
            return url.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/) !== null;
          },
        }),
      ],
    });

    // Inicializa o SDK
    sdk.start();

    logger.info('OpenTelemetry inicializado com sucesso');

    // Registra função para encerrar o SDK quando a aplicação for encerrada
    process.on('SIGTERM', () => {
      sdk.shutdown()
        .then(() => logger.info('OpenTelemetry encerrado com sucesso'))
        .catch((error) => logger.error({
          msg: 'Erro ao encerrar OpenTelemetry',
          error: error instanceof Error ? error.message : String(error)
        }))
        .finally(() => process.exit(0));
    });

    return sdk;
  } catch (error) {
    logger.error({
      msg: 'Erro ao inicializar OpenTelemetry',
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
};

export default { initTelemetry };
