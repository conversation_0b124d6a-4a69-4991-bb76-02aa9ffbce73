import { pgTable, text, serial, integer, timestamp, real, date, pgEnum, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { horses } from './cavalos';
import { 
  observacoesSchema,
  optionalDateSchema,
  currencySchema,
  UserContext 
} from './core';

// ============================================================================
// ENUMS
// ============================================================================

export const tipoAlimentoEnum = pgEnum('tipo_alimento', [
  'feno', 'capim', 'racao', 'aveia', 'milho', 'farelo_trigo', 
  'farelo_soja', 'suplemento', 'vitamina', 'mineral', 'outro'
]);

export const unidadeMedidaEnum = pgEnum('unidade_medida', [
  'kg', 'g', 'litros', 'ml', 'copos', 'punhados', 'fardos'
]);

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const planosNutricionais = pgTable("planos_nutricionais", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  user_id: integer("user_id").notNull(),
  
  // Dados do plano
  nome: text("nome").notNull(),
  descricao: text("descricao"),
  dataInicio: date("data_inicio").notNull(),
  dataFim: date("data_fim"),
  
  // Objetivos nutricionais
  objetivo: text("objetivo"), // "manutencao", "ganho_peso", "perda_peso", "gestacao", "lactacao", "competicao"
  pesoAlvo: real("peso_alvo"),
  condicaoCorporal: real("condicao_corporal"), // Escala 1-9
  
  // Necessidades calóricas
  necessidadeEnergetica: real("necessidade_energetica"), // Mcal/dia
  necessidadeProteica: real("necessidade_proteica"), // g/dia
  
  // Status
  ativo: boolean("ativo").default(true),
  
  // Responsável técnico
  nutricionista: text("nutricionista"),
  veterinario: text("veterinario"),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const alimentacao = pgTable("alimentacao", {
  id: serial("id").primaryKey(),
  planoId: integer("plano_id"),
  horse_id: integer("horse_id").notNull(),
  
  // Dados da alimentação
  dataAlimentacao: date("data_alimentacao").notNull(),
  refeicao: text("refeicao"), // "manha", "tarde", "noite", "madrugada"
  
  // Alimento
  tipoAlimento: tipoAlimentoEnum("tipo_alimento").notNull(),
  nomeAlimento: text("nome_alimento").notNull(),
  marca: text("marca"),
  
  // Quantidade
  quantidade: real("quantidade").notNull(),
  unidade: unidadeMedidaEnum("unidade").notNull(),
  
  // Valores nutricionais (por porção fornecida)
  calorias: real("calorias"), // kcal
  proteinas: real("proteinas"), // g
  carboidratos: real("carboidratos"), // g
  gorduras: real("gorduras"), // g
  fibras: real("fibras"), // g
  
  // Custo
  custoUnitario: real("custo_unitario"),
  custoTotal: real("custo_total"),
  
  // Observações
  aceitacao: text("aceitacao"), // "total", "parcial", "rejeitou"
  observacoes: text("observacoes"),
  
  // Responsável
  responsavel: text("responsavel"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const suplementacao = pgTable("suplementacao", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  
  // Dados do suplemento
  nomeProduto: text("nome_produto").notNull(),
  fabricante: text("fabricante"),
  principioAtivo: text("principio_ativo"),
  
  // Dosagem
  dosagem: text("dosagem").notNull(),
  frequencia: text("frequencia"), // "diaria", "2x_dia", "semanal", etc.
  viaAdministracao: text("via_administracao"), // "oral", "injetavel", "topica"
  
  // Período
  dataInicio: date("data_inicio").notNull(),
  dataFim: date("data_fim"),
  duracaoTratamento: text("duracao_tratamento"),
  
  // Indicação
  indicacao: text("indicacao").notNull(),
  prescritoPor: text("prescrito_por"),
  
  // Monitoramento
  efeitosObservados: text("efeitos_observados"),
  efeitosColaterais: text("efeitos_colaterais"),
  
  // Status
  ativo: boolean("ativo").default(true),
  
  // Custo
  custoMensal: real("custo_mensal"),
  
  observacoes: text("observacoes"),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const planosNutricionaisRelations = relations(planosNutricionais, ({ one, many }) => ({
  horse: one(horses, {
    fields: [planosNutricionais.horse_id],
    references: [horses.id],
  }),
  alimentacoes: many(alimentacao),
}));

export const alimentacaoRelations = relations(alimentacao, ({ one }) => ({
  horse: one(horses, {
    fields: [alimentacao.horse_id],
    references: [horses.id],
  }),
  plano: one(planosNutricionais, {
    fields: [alimentacao.planoId],
    references: [planosNutricionais.id],
  }),
}));

export const suplementacaoRelations = relations(suplementacao, ({ one }) => ({
  horse: one(horses, {
    fields: [suplementacao.horse_id],
    references: [horses.id],
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const planoNutricionalSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  user_id: z.number().int().positive(),
  
  nome: z.string().min(1, 'Nome do plano é obrigatório').max(100),
  descricao: z.string().max(500).optional().nullable(),
  dataInicio: z.string().datetime().or(z.date()),
  dataFim: optionalDateSchema,
  
  objetivo: z.enum([
    'manutencao', 'ganho_peso', 'perda_peso', 'gestacao', 
    'lactacao', 'competicao', 'recuperacao'
  ]).optional().nullable(),
  pesoAlvo: z.number().positive().max(2000).optional().nullable(),
  condicaoCorporal: z.number().min(1).max(9).optional().nullable(),
  
  necessidadeEnergetica: z.number().positive().max(50).optional().nullable(), // Mcal/dia
  necessidadeProteica: z.number().positive().max(5000).optional().nullable(), // g/dia
  
  ativo: z.boolean().default(true),
  nutricionista: z.string().max(100).optional().nullable(),
  veterinario: z.string().max(100).optional().nullable(),
  
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: data fim deve ser posterior ao início
  if (data.dataFim && data.dataInicio) {
    const fim = new Date(data.dataFim);
    const inicio = new Date(data.dataInicio);
    return fim >= inicio;
  }
  return true;
}, {
  message: "Data fim deve ser posterior à data de início",
  path: ["dataFim"]
});

export const alimentacaoSchema = z.object({
  id: z.number().int().positive().optional(),
  planoId: z.number().int().positive().optional().nullable(),
  horse_id: z.number().int().positive(),
  
  dataAlimentacao: z.string().datetime().or(z.date()),
  refeicao: z.enum(['manha', 'tarde', 'noite', 'madrugada']).optional().nullable(),
  
  tipoAlimento: z.enum([
    'feno', 'capim', 'racao', 'aveia', 'milho', 'farelo_trigo',
    'farelo_soja', 'suplemento', 'vitamina', 'mineral', 'outro'
  ]),
  nomeAlimento: z.string().min(1, 'Nome do alimento é obrigatório').max(100),
  marca: z.string().max(50).optional().nullable(),
  
  quantidade: z.number().positive().max(100), // kg máximo por refeição
  unidade: z.enum(['kg', 'g', 'litros', 'ml', 'copos', 'punhados', 'fardos']),
  
  // Nutritional values validation
  calorias: z.number().nonnegative().max(10000).optional().nullable(),
  proteinas: z.number().nonnegative().max(1000).optional().nullable(),
  carboidratos: z.number().nonnegative().max(1000).optional().nullable(),
  gorduras: z.number().nonnegative().max(500).optional().nullable(),
  fibras: z.number().nonnegative().max(1000).optional().nullable(),
  
  custoUnitario: currencySchema.optional().nullable(),
  custoTotal: currencySchema.optional().nullable(),
  
  aceitacao: z.enum(['total', 'parcial', 'rejeitou']).optional().nullable(),
  observacoes: observacoesSchema,
  responsavel: z.string().max(100).optional().nullable(),
});

export const suplementacaoSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  
  nomeProduto: z.string().min(1, 'Nome do produto é obrigatório').max(100),
  fabricante: z.string().max(100).optional().nullable(),
  principioAtivo: z.string().max(200).optional().nullable(),
  
  dosagem: z.string().min(1, 'Dosagem é obrigatória').max(100),
  frequencia: z.string().max(50).optional().nullable(),
  viaAdministracao: z.enum(['oral', 'injetavel', 'topica', 'inalatoria']).optional().nullable(),
  
  dataInicio: z.string().datetime().or(z.date()),
  dataFim: optionalDateSchema,
  duracaoTratamento: z.string().max(100).optional().nullable(),
  
  indicacao: z.string().min(1, 'Indicação é obrigatória').max(200),
  prescritoPor: z.string().max(100).optional().nullable(),
  
  efeitosObservados: z.string().max(500).optional().nullable(),
  efeitosColaterais: z.string().max(500).optional().nullable(),
  
  ativo: z.boolean().default(true),
  custoMensal: currencySchema.optional().nullable(),
  
  observacoes: observacoesSchema,
}).refine((data) => {
  // Business rule: data fim deve ser posterior ao início
  if (data.dataFim && data.dataInicio) {
    const fim = new Date(data.dataFim);
    const inicio = new Date(data.dataInicio);
    return fim >= inicio;
  }
  return true;
}, {
  message: "Data fim deve ser posterior à data de início",
  path: ["dataFim"]
});

// Insert schemas
export const insertPlanoNutricionalSchema = createInsertSchema(planosNutricionais).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertAlimentacaoSchema = createInsertSchema(alimentacao).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertSuplementacaoSchema = createInsertSchema(suplementacao).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type PlanoNutricional = typeof planosNutricionais.$inferSelect;
export type NewPlanoNutricional = typeof planosNutricionais.$inferInsert;
export type PlanoNutricionalData = z.infer<typeof planoNutricionalSchema>;

export type Alimentacao = typeof alimentacao.$inferSelect;
export type NewAlimentacao = typeof alimentacao.$inferInsert;
export type AlimentacaoData = z.infer<typeof alimentacaoSchema>;

export type Suplementacao = typeof suplementacao.$inferSelect;
export type NewSuplementacao = typeof suplementacao.$inferInsert;
export type SuplemencaoData = z.infer<typeof suplementacaoSchema>;

// ============================================================================
// BUSINESS LOGIC
// ============================================================================

/**
 * Calculates daily nutritional requirements based on horse characteristics
 */
export function calcularNecessidadesNutricionais(
  peso: number, 
  atividade: 'sedentario' | 'leve' | 'moderado' | 'intenso',
  estado: 'manutencao' | 'gestacao' | 'lactacao' | 'crescimento'
): { energia: number; proteina: number } {
  
  // Base energy requirement (Mcal/day)
  let energiaBase = 1.4 + (0.03 * peso);
  
  // Activity multipliers
  const multiplicadorAtividade = {
    sedentario: 1.0,
    leve: 1.25,
    moderado: 1.5,
    intenso: 2.0
  };
  
  // State multipliers
  const multiplicadorEstado = {
    manutencao: 1.0,
    gestacao: 1.15, // Last trimester
    lactacao: 1.8,  // Peak lactation
    crescimento: 1.5
  };
  
  const energiaTotal = energiaBase * 
    multiplicadorAtividade[atividade] * 
    multiplicadorEstado[estado];
  
  // Protein requirement (g/day) - 10% of energy requirement
  const proteinaTotal = (energiaTotal * 1000 * 0.10) / 4; // 4 kcal/g protein
  
  return {
    energia: Math.round(energiaTotal * 100) / 100,
    proteina: Math.round(proteinaTotal)
  };
}

/**
 * Validates feed nutritional balance
 */
export function validarBalanceNutricional(alimentacoes: AlimentacaoData[]): string[] {
  const warnings: string[] = [];
  
  // Calculate daily totals
  const totais = alimentacoes.reduce((acc, alim) => ({
    calorias: acc.calorias + (alim.calorias || 0),
    proteinas: acc.proteinas + (alim.proteinas || 0),
    fibras: acc.fibras + (alim.fibras || 0),
  }), { calorias: 0, proteinas: 0, fibras: 0 });
  
  // Minimum fiber requirement (25% of dry matter)
  if (totais.fibras < (totais.calorias * 0.25 / 4)) {
    warnings.push("Fibra insuficiente na dieta (mínimo 25% da matéria seca)");
  }
  
  // Protein percentage validation (8-14% is typical)
  const percentualProteina = (totais.proteinas * 4) / totais.calorias * 100;
  if (percentualProteina < 8) {
    warnings.push("Proteína abaixo do recomendado (mínimo 8%)");
  } else if (percentualProteina > 16) {
    warnings.push("Excesso de proteína na dieta (máximo 16%)");
  }
  
  return warnings;
}

/**
 * Monitors supplement interactions and contraindications
 */
export function verificarInteracoesSuplementos(suplementos: SuplemencaoData[]): string[] {
  const warnings: string[] = [];
  
  // Common contraindications
  const interacoes: Record<string, string[]> = {
    'ferro': ['calcio', 'zinco'],
    'calcio': ['ferro', 'magnesio'],
    'vitamina_e': ['ferro'],
    'selenio': ['vitamina_c']
  };
  
  const ativos = suplementos
    .filter(s => s.ativo)
    .map(s => s.principioAtivo?.toLowerCase() || s.nomeProduto.toLowerCase());
  
  for (const ativo of ativos) {
    const contraindications = interacoes[ativo];
    if (contraindications) {
      for (const contra of contraindications) {
        if (ativos.some(a => a.includes(contra))) {
          warnings.push(`Possível interação entre ${ativo} e ${contra}`);
        }
      }
    }
  }
  
  return warnings;
}