import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Settings,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  useCategorias, 
  useCreateCategoria, 
  useUpdateCategoria, 
  useDeleteCategoria,
  type CategoriaFinanceira
} from '@/hooks/use-financeiro';

/**
 * Página de categorias financeiras refatorada
 * 
 * Funcionalidades MVP:
 * - Listagem de categorias
 * - Criação e edição de categorias
 * - Ativação/desativação de categorias
 * - Exclusão de categorias (se não tiver lançamentos)
 */
export default function CategoriasPageRefactored() {
  const { toast } = useToast();
  
  // Estados locais
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCategoria, setEditingCategoria] = useState<CategoriaFinanceira | null>(null);

  // Hooks para dados
  const { data: categorias, isLoading } = useCategorias();
  
  // Mutations
  const createCategoria = useCreateCategoria();
  const updateCategoria = useUpdateCategoria();
  const deleteCategoria = useDeleteCategoria();

  // Estados do formulário
  const [formData, setFormData] = useState({
    nome: '',
    tipo: 'despesa' as 'receita' | 'despesa',
    descricao: '',
    ativo: true,
  });

  // Filtrar categorias por tipo
  const categoriasReceita = categorias?.filter(c => c.tipo === 'receita') || [];
  const categoriasDespesa = categorias?.filter(c => c.tipo === 'despesa') || [];

  // Handlers
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.nome.trim()) {
      toast({
        title: "Erro",
        description: "Nome da categoria é obrigatório",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingCategoria) {
        await updateCategoria.mutateAsync({ 
          id: editingCategoria.id, 
          data: formData 
        });
        toast({
          title: "Sucesso",
          description: "Categoria atualizada com sucesso",
        });
      } else {
        await createCategoria.mutateAsync(formData);
        toast({
          title: "Sucesso",
          description: "Categoria criada com sucesso",
        });
      }

      handleCloseDialog();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao salvar categoria",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (categoria: CategoriaFinanceira) => {
    setEditingCategoria(categoria);
    setFormData({
      nome: categoria.nome,
      tipo: categoria.tipo,
      descricao: categoria.descricao || '',
      ativo: categoria.ativo,
    });
    setIsDialogOpen(true);
  };

  const handleToggleActive = async (categoria: CategoriaFinanceira) => {
    try {
      await updateCategoria.mutateAsync({
        id: categoria.id,
        data: { ativo: !categoria.ativo }
      });
      toast({
        title: "Sucesso",
        description: `Categoria ${categoria.ativo ? 'desativada' : 'ativada'} com sucesso`,
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao alterar status da categoria",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number, nome: string) => {
    if (!confirm(`Tem certeza que deseja excluir a categoria "${nome}"?`)) return;
    
    try {
      await deleteCategoria.mutateAsync(id);
      toast({
        title: "Sucesso",
        description: "Categoria excluída com sucesso",
      });
    } catch (error: any) {
      const message = error.message || "Erro ao excluir categoria";
      toast({
        title: "Erro",
        description: message,
        variant: "destructive",
      });
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingCategoria(null);
    setFormData({
      nome: '',
      tipo: 'despesa',
      descricao: '',
      ativo: true,
    });
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="nome">Nome da Categoria *</Label>
        <Input
          id="nome"
          value={formData.nome}
          onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
          placeholder="Ex: Alimentação, Medicamentos, Pensão..."
          required
        />
      </div>

      <div>
        <Label htmlFor="tipo">Tipo *</Label>
        <Select value={formData.tipo} onValueChange={(value: 'receita' | 'despesa') => 
          setFormData(prev => ({ ...prev, tipo: value }))
        }>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="receita">Receita</SelectItem>
            <SelectItem value="despesa">Despesa</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="descricao">Descrição</Label>
        <Textarea
          id="descricao"
          value={formData.descricao}
          onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
          placeholder="Descrição detalhada da categoria (opcional)"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="ativo"
          checked={formData.ativo}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, ativo: checked }))}
        />
        <Label htmlFor="ativo">Categoria ativa</Label>
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" disabled={createCategoria.isPending || updateCategoria.isPending}>
          {editingCategoria ? 'Atualizar' : 'Criar'} Categoria
        </Button>
        <Button type="button" variant="outline" onClick={handleCloseDialog}>
          Cancelar
        </Button>
      </div>
    </form>
  );

  const renderCategoriaTable = (categorias: CategoriaFinanceira[], titulo: string, icon: React.ReactNode) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {icon}
          {titulo}
        </CardTitle>
        <CardDescription>
          {categorias.length} categoria(s) cadastrada(s)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {categorias.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nenhuma categoria de {titulo.toLowerCase()} cadastrada
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-center">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categorias.map((categoria) => (
                  <TableRow key={categoria.id}>
                    <TableCell className="font-medium">
                      {categoria.nome}
                    </TableCell>
                    <TableCell>
                      {categoria.descricao || '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={categoria.ativo}
                          onCheckedChange={() => handleToggleActive(categoria)}
                          disabled={updateCategoria.isPending}
                        />
                        <Badge variant={categoria.ativo ? 'default' : 'secondary'}>
                          {categoria.ativo ? 'Ativa' : 'Inativa'}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex gap-2 justify-center">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(categoria)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(categoria.id, categoria.nome)}
                          disabled={deleteCategoria.isPending}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Categorias Financeiras</h2>
          <p className="text-gray-600">Organize suas receitas e despesas por categorias</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingCategoria(null)}>
              <Plus className="mr-2 h-4 w-4" />
              Nova Categoria
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingCategoria ? 'Editar' : 'Nova'} Categoria
              </DialogTitle>
            </DialogHeader>
            {renderForm()}
          </DialogContent>
        </Dialog>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="text-center py-8">Carregando categorias...</div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderCategoriaTable(
            categoriasReceita, 
            'Receitas',
            <TrendingUp className="h-5 w-5 text-green-600" />
          )}
          {renderCategoriaTable(
            categoriasDespesa, 
            'Despesas',
            <TrendingDown className="h-5 w-5 text-red-600" />
          )}
        </div>
      )}
    </div>
  );
}