import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Check, ChevronsUpDown, Search, Heart } from "lucide-react";
import { cn } from "@/lib/utils";

interface Horse {
  id: number;
  name: string;
  breed?: string;
  sexo?: string;
  birth_date?: string;
  cor?: string;
}

interface HorseSelectorProps {
  selectedHorseId: number | null;
  onHorseSelect: (horse_id: number | null) => void;
  placeholder?: string;
  className?: string;
}

export default function HorseSelector({ 
  selectedHorseId, 
  onHorseSelect, 
  placeholder = "Selecione um cavalo...",
  className 
}: HorseSelectorProps) {
  const [open, setOpen] = useState(false);

  const { data: cavalos, isLoading } = useQuery<Horse[]>({
    queryKey: ['/api/cavalos'],
  });

  const selectedHorse = cavalos?.find(cavalo => cavalo.id === selectedHorseId);

  const getHorseAge = (birth_date?: string) => {
    if (!birth_date) return null;
    const birth = new Date(birth_date);
    const today = new Date();
    const age = today.getFullYear() - birth.getFullYear();
    return age;
  };

  const getHorseInitials = (name: string) => {
    return name
      .split(' ')
      .slice(0, 2)
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  const getSexBadgeColor = (sexo?: string) => {
    switch (sexo?.toLowerCase()) {
      case 'macho':
      case 'm':
        return 'bg-blue-100 text-blue-800';
      case 'fêmea':
      case 'femea':
      case 'f':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto min-h-[3rem] p-3"
          >
            {selectedHorse ? (
              <div className="flex items-center gap-3 flex-1 text-left">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={`/api/cavalos/${selectedHorse.id}/photo`} />
                  <AvatarFallback className="text-xs">
                    {getHorseInitials(selectedHorse.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{selectedHorse.name}</div>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    {selectedHorse.breed && (
                      <span>{selectedHorse.breed}</span>
                    )}
                    {selectedHorse.sexo && (
                      <Badge 
                        variant="secondary" 
                        className={cn("text-xs px-1.5 py-0.5", getSexBadgeColor(selectedHorse.sexo))}
                      >
                        {selectedHorse.sexo === 'Macho' || selectedHorse.sexo === 'Garanhão' ? 'Macho' : 'Fêmea'}
                      </Badge>
                    )}
                    {selectedHorse.birth_date && (
                      <span>{getHorseAge(selectedHorse.birth_date)} anos</span>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <Heart className="h-4 w-4" />
                {placeholder}
              </div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" style={{ width: 'var(--radix-popover-trigger-width)' }}>
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput placeholder="Buscar cavalo..." className="border-0 focus:ring-0" />
            </div>
            <CommandList className="max-h-96">
              <CommandEmpty>
                {isLoading ? "Carregando cavalos..." : "Nenhum cavalo encontrado."}
              </CommandEmpty>
              <CommandGroup>
                {cavalos?.map((cavalo) => (
                  <CommandItem
                    key={cavalo.id}
                    value={`${cavalo.name} ${cavalo.breed || ''}`}
                    onSelect={() => {
                      onHorseSelect(cavalo.id === selectedHorseId ? null : cavalo.id);
                      setOpen(false);
                    }}
                    className="p-0"
                  >
                    <Card className="w-full border-0 shadow-none hover:bg-accent transition-colors">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={`/api/cavalos/${cavalo.id}/photo`} />
                            <AvatarFallback className="text-sm">
                              {getHorseInitials(cavalo.name)}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium truncate">{cavalo.name}</h4>
                              <Check
                                className={cn(
                                  "h-4 w-4 shrink-0",
                                  selectedHorseId === cavalo.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                            </div>
                            
                            <div className="flex items-center gap-2 mt-1">
                              {cavalo.breed && (
                                <span className="text-sm text-gray-600">{cavalo.breed}</span>
                              )}
                              
                              {cavalo.sexo && (
                                <Badge 
                                  variant="secondary" 
                                  className={cn("text-xs px-1.5 py-0.5", getSexBadgeColor(cavalo.sexo))}
                                >
                                  {cavalo.sexo === 'Macho' || cavalo.sexo === 'Garanhão' ? 'Macho' : 'Fêmea'}
                                </Badge>
                              )}
                              
                              {cavalo.cor && (
                                <span className="text-xs text-gray-500">{cavalo.cor}</span>
                              )}
                            </div>
                            
                            {cavalo.birth_date && (
                              <div className="text-xs text-gray-500 mt-1">
                                {getHorseAge(cavalo.birth_date)} anos • {new Date(cavalo.birth_date).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}