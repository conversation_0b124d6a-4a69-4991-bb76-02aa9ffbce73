import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle2, XCircle } from 'lucide-react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Definir variantes para o indicador de salvamento
const savingIndicatorVariants = cva(
  "fixed z-50 flex items-center gap-2 px-4 py-2 rounded-md shadow-md transition-all duration-300",
  {
    variants: {
      position: {
        "top-right": "top-4 right-4",
        "top-left": "top-4 left-4",
        "bottom-right": "bottom-4 right-4",
        "bottom-left": "bottom-4 left-4",
        "top-center": "top-4 left-1/2 -translate-x-1/2",
        "bottom-center": "bottom-4 left-1/2 -translate-x-1/2",
      },
      status: {
        saving: "bg-blue-50 text-blue-700 border border-blue-200",
        success: "bg-green-50 text-green-700 border border-green-200",
        error: "bg-red-50 text-red-700 border border-red-200",
      }
    },
    defaultVariants: {
      position: "bottom-center",
      status: "saving"
    }
  }
);

// Props para o componente
export interface SavingIndicatorProps extends VariantProps<typeof savingIndicatorVariants> {
  show: boolean;
  status: "saving" | "success" | "error";
  message?: string;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center";
  autoHideDuration?: number;
  className?: string;
  onHide?: () => void;
}

/**
 * Componente que mostra um indicador de salvamento
 * 
 * Útil para dar feedback visual ao usuário durante operações de salvamento
 */
export function SavingIndicator({
  show,
  status,
  message,
  position,
  autoHideDuration = 3000,
  className,
  onHide
}: SavingIndicatorProps) {
  const [visible, setVisible] = useState(show);
  
  // Atualizar visibilidade quando a prop show mudar
  useEffect(() => {
    setVisible(show);
    
    // Se show for true e status for success ou error, configurar timer para esconder
    if (show && (status === "success" || status === "error") && autoHideDuration > 0) {
      const timer = setTimeout(() => {
        setVisible(false);
        if (onHide) onHide();
      }, autoHideDuration);
      
      return () => clearTimeout(timer);
    }
  }, [show, status, autoHideDuration, onHide]);
  
  // Não renderizar nada se não estiver visível
  if (!visible) return null;
  
  // Determinar ícone e mensagem padrão com base no status
  let icon = <Loader2 className="h-5 w-5 animate-spin" />;
  let defaultMessage = "Salvando...";
  
  if (status === "success") {
    icon = <CheckCircle2 className="h-5 w-5" />;
    defaultMessage = "Salvo com sucesso!";
  } else if (status === "error") {
    icon = <XCircle className="h-5 w-5" />;
    defaultMessage = "Erro ao salvar";
  }
  
  return (
    <div 
      className={cn(savingIndicatorVariants({ position, status }), className)}
      role="status"
      aria-live={status === "error" ? "assertive" : "polite"}
    >
      {icon}
      <span>{message || defaultMessage}</span>
    </div>
  );
}

/**
 * Hook para gerenciar o estado do indicador de salvamento
 */
export function useSavingIndicator(initialState: boolean = false) {
  const [show, setShow] = useState(initialState);
  const [status, setStatus] = useState<"saving" | "success" | "error">("saving");
  const [message, setMessage] = useState<string | undefined>(undefined);
  
  // Função para mostrar o indicador de salvamento
  const showSaving = (customMessage?: string) => {
    setStatus("saving");
    setMessage(customMessage);
    setShow(true);
  };
  
  // Função para mostrar sucesso
  const showSuccess = (customMessage?: string) => {
    setStatus("success");
    setMessage(customMessage);
    setShow(true);
  };
  
  // Função para mostrar erro
  const showError = (customMessage?: string) => {
    setStatus("error");
    setMessage(customMessage);
    setShow(true);
  };
  
  // Função para esconder o indicador
  const hide = () => {
    setShow(false);
  };
  
  return {
    show,
    status,
    message,
    showSaving,
    showSuccess,
    showError,
    hide,
    // Props para passar diretamente para o componente
    indicatorProps: {
      show,
      status,
      message,
      onHide: hide
    }
  };
}

export default SavingIndicator;
