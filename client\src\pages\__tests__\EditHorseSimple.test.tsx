/**
 * 8. Testes para EditHorseSimple refatorado
 * Vitest + Testing Library para cobertura ≥ 90%
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Router } from 'wouter';
import EditHorseSimple from '../EditHorseSimple';
import { apiRequest } from '@/lib/queryClient';
import { sexoMapping } from '@/utils/form-helpers';

// Mock apiRequest
vi.mock('@/lib/queryClient', () => ({
  apiRequest: vi.fn(),
  queryClient: {
    setQueryData: vi.fn(),
    invalidateQueries: vi.fn(),
  }
}));

// Mock hooks
vi.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({ user: { id: 1, name: 'Test User' } })
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() })
}));

vi.mock('@/hooks/use-pelagens', () => ({
  usePelagens: () => ({ 
    pelagens: [
      { id: 1, nome: 'Tostado', slug: 'tostado' },
      { id: 2, nome: 'Zaino', slug: 'zaino' }
    ], 
    isLoading: false 
  }),
  usePelagemById: () => ({ pelagem: null })
}));

vi.mock('@/hooks/use-racas', () => ({
  useRacas: () => ({ 
    racas: [
      { id: 1, nome: 'Crioulo' },
      { id: 2, nome: 'Árabe' }
    ]
  })
}));

const mockHorse = {
  id: 95,
  name: 'FADA DE QUARACI',
  breed: 'Crioulo',
  birth_date: '2021-08-02',
  sexo: 'Fêmea',
  cor: 'TOSTADA BRAGADA',
  pelagem_id: 32,
  status: 'ativo',
  user_id: 1,
  peso: null,
  altura: null,
  numero_registro: 'B632663',
  criador: 'ROSALIE TAVARES NEGRINI JONES',
  proprietario: 'CAMILA WEBER',
  inspetor: 'FELIPE CACCIA MACIEL',
  origem: null,
  notes: null,
  data_entrada: null,
  data_saida: null,
  motivo_saida: null,
  valor_compra: null,
  data_compra: null,
  is_external: false,
  created_at: '2025-06-13T23:33:47.578Z'
};

function renderWithProviders(ui: React.ReactElement, { route = '/cavalos/95/editar' } = {}) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <Router hook={() => [route, () => {}]}>
        {ui}
      </Router>
    </QueryClientProvider>
  );
}

describe('EditHorseSimple - Refatoração Robusta', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock das queries principais
    (apiRequest as any).mockImplementation((method: string, url: string) => {
      if (url.includes('/api/cavalos/95') && method === 'GET') {
        return Promise.resolve(mockHorse);
      }
      if (url.includes('/api/cavalos') && method === 'GET') {
        return Promise.resolve([]);
      }
      if (url.includes('/api/cavalos/95/genealogia') && method === 'GET') {
        return Promise.resolve({});
      }
      return Promise.resolve({});
    });
  });

  describe('1. Unificação de roteamento', () => {
    it('deve extrair horseId corretamente da URL', async () => {
      renderWithProviders(<EditHorseSimple />);
      
      await waitFor(() => {
        expect(apiRequest).toHaveBeenCalledWith('/api/cavalos/95', 'GET');
      }, { timeout: 3000 });
    });
  });

  describe('2. Conversão de sexo para snake_case', () => {
    it('deve converter sexo display para snake_case', () => {
      expect(sexoMapping['Fêmea']).toBe('femea');
      expect(sexoMapping['Macho']).toBe('macho');
      expect(sexoMapping['Garanhão']).toBe('garanhao');
    });
  });

  describe('3. Validação de formulário', () => {
    it('deve renderizar componente corretamente', async () => {
      renderWithProviders(<EditHorseSimple />);
      
      await waitFor(() => {
        expect(screen.getByText(/editar cavalo/i)).toBeInTheDocument();
      });
    });
  });
});