import * as React from "react";
import { cn } from "@/lib/utils";
import { getSafeSelectValue, getOriginalValue, isValidSelectValue } from "@/lib/select-utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * SafeSelectItem
 *
 * Um componente SelectItem que garante que o valor nunca será vazio,
 * evitando o erro "A <Select.Item /> must have a value prop that is not an empty string"
 */
interface SafeSelectItemProps extends React.ComponentPropsWithoutRef<typeof SelectItem> {
  originalValue?: string | null;
  fallbackValue?: string;
  prefix?: string;
}

export const SafeSelectItem = React.forwardRef<
  React.ElementRef<typeof SelectItem>,
  SafeSelectItemProps
>(({
  originalValue,
  value,
  fallbackValue = 'nao_informado',
  prefix = '',
  children,
  ...props
}, ref) => {
  // Determinar o valor seguro a ser usado
  const safeValue = React.useMemo(() => {
    // Se originalValue for fornecido, usá-lo
    if (originalValue !== undefined) {
      return getSafeSelectValue(originalValue, fallbackValue, prefix);
    }

    // Caso contrário, usar o value fornecido
    const result = getSafeSelectValue(value, fallbackValue, prefix);

    // Verificar novamente se o valor é válido (não vazio)
    if (!result || result.trim() === '') {
      console.warn('SafeSelectItem recebeu um valor inválido mesmo após processamento:', { value, result });
      return fallbackValue; // Garantir que sempre temos um valor válido
    }

    return result;
  }, [originalValue, value, fallbackValue, prefix]);

  return (
    <SelectItem
      ref={ref}
      value={safeValue}
      {...props}
    >
      {children}
    </SelectItem>
  );
});

SafeSelectItem.displayName = "SafeSelectItem";

/**
 * SafeSelect
 *
 * Um componente Select que lida automaticamente com valores vazios/nulos
 */
interface SafeSelectProps extends Omit<React.ComponentPropsWithoutRef<typeof Select>, 'onValueChange'> {
  onValueChange?: (value: string | null) => void;
  emptyValue?: string | null;
  fallbackValue?: string;
  prefix?: string;
}

export const SafeSelect = React.forwardRef<
  React.ElementRef<typeof Select>,
  SafeSelectProps
>(({
  onValueChange,
  value,
  emptyValue = null,
  fallbackValue = 'nao_informado',
  prefix = '',
  children,
  ...props
}, ref) => {
  // Converter o valor original para um valor seguro para o Select
  const safeValue = React.useMemo(() => {
    const result = getSafeSelectValue(value, fallbackValue, prefix);

    // Verificar novamente se o valor é válido (não vazio)
    if (!result || result.trim() === '') {
      console.warn('SafeSelect recebeu um valor inválido mesmo após processamento:', { value, result });
      return fallbackValue; // Garantir que sempre temos um valor válido
    }

    return result;
  }, [value, fallbackValue, prefix]);

  // Handler para converter o valor seguro de volta para o valor original
  const handleValueChange = React.useCallback((newValue: string) => {
    if (onValueChange) {
      const originalValue = getOriginalValue(newValue, emptyValue);
      onValueChange(originalValue);
    }
  }, [onValueChange, emptyValue]);

  return (
    <Select
      ref={ref}
      value={safeValue}
      onValueChange={handleValueChange}
      {...props}
    >
      {children}
    </Select>
  );
});

SafeSelect.displayName = "SafeSelect";

/**
 * Hook para trabalhar com valores de select de forma segura
 */
export function useSafeSelect(
  value: any,
  onChange: (value: any) => void,
  options: {
    emptyValue?: string | null;
    fallbackValue?: string;
    prefix?: string;
    convertToNumber?: boolean;
  } = {}
) {
  const {
    emptyValue = null,
    fallbackValue = 'nao_informado',
    prefix = '',
    convertToNumber = false
  } = options;

  // Valor seguro para o Select
  const safeValue = React.useMemo(() => {
    const result = getSafeSelectValue(value, fallbackValue, prefix);

    // Verificar novamente se o valor é válido (não vazio)
    if (!result || result.trim() === '') {
      console.warn('useSafeSelect recebeu um valor inválido mesmo após processamento:', { value, result });
      return fallbackValue; // Garantir que sempre temos um valor válido
    }

    return result;
  }, [value, fallbackValue, prefix]);

  // Handler para converter o valor seguro de volta para o valor original
  const handleChange = React.useCallback((newValue: string) => {
    const originalValue = getOriginalValue(newValue, emptyValue);

    // Se for para converter para número e o valor não for nulo
    if (convertToNumber && originalValue !== null && originalValue !== '') {
      const numValue = Number(originalValue);
      onChange(isNaN(numValue) ? originalValue : numValue);
    } else {
      onChange(originalValue);
    }
  }, [onChange, emptyValue, convertToNumber]);

  return {
    value: safeValue,
    onChange: handleChange
  };
}
