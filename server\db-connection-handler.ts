import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

// Configure Neon for better compatibility
neonConfig.webSocketConstructor = ws;
neonConfig.useSecureWebSocket = true;
neonConfig.pipelineConnect = false;
neonConfig.pipelineTLS = false;

export class DatabaseConnectionHandler {
  private static instance: DatabaseConnectionHandler;
  private pool: Pool | null = null;
  private db: any = null;
  private connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error' = 'disconnected';
  private retryCount = 0;
  private maxRetries = 3;

  private constructor() {}

  static getInstance(): DatabaseConnectionHandler {
    if (!DatabaseConnectionHandler.instance) {
      DatabaseConnectionHandler.instance = new DatabaseConnectionHandler();
    }
    return DatabaseConnectionHandler.instance;
  }

  async connect(): Promise<boolean> {
    if (this.connectionStatus === 'connected' && this.db) {
      return true;
    }

    if (this.connectionStatus === 'connecting') {
      // Wait for existing connection attempt
      return new Promise((resolve) => {
        const checkConnection = () => {
          if (this.connectionStatus === 'connected') {
            resolve(true);
          } else if (this.connectionStatus === 'error') {
            resolve(false);
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    if (!process.env.DATABASE_URL) {
      console.error('DATABASE_URL environment variable is not set');
      this.connectionStatus = 'error';
      return false;
    }

    this.connectionStatus = 'connecting';

    try {
      console.log('Attempting database connection...');
      
      this.pool = new Pool({ 
        connectionString: process.env.DATABASE_URL,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000,
      });

      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();

      this.db = drizzle({ client: this.pool, schema });
      this.connectionStatus = 'connected';
      this.retryCount = 0;
      
      console.log('✅ Database connected successfully');
      return true;

    } catch (error) {
      console.error(`❌ Database connection failed (attempt ${this.retryCount + 1}/${this.maxRetries}):`, error.message);
      
      this.connectionStatus = 'error';
      this.retryCount++;

      if (this.retryCount < this.maxRetries) {
        console.log(`Retrying connection in 2 seconds...`);
        setTimeout(() => this.connect(), 2000);
      } else {
        console.error('Max database connection retries reached. Running in offline mode.');
      }

      return false;
    }
  }

  getDB() {
    if (this.connectionStatus === 'connected' && this.db) {
      return this.db;
    }
    throw new Error('Database not connected. Check connection status first.');
  }

  getPool() {
    if (this.connectionStatus === 'connected' && this.pool) {
      return this.pool;
    }
    throw new Error('Database pool not available. Check connection status first.');
  }

  isConnected(): boolean {
    return this.connectionStatus === 'connected';
  }

  getStatus(): string {
    return this.connectionStatus;
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }
    this.db = null;
    this.connectionStatus = 'disconnected';
  }
}

export const dbHandler = DatabaseConnectionHandler.getInstance();

// Legacy exports for backward compatibility
export const db = {
  get connection() {
    try {
      return dbHandler.getDB();
    } catch {
      return null;
    }
  }
};

export const pool = {
  get connection() {
    try {
      return dbHandler.getPool();
    } catch {
      return null;
    }
  }
};