import { useEffect } from 'react';

/**
 * Hook para bloquear o scroll do body quando um elemento estiver aberto (como um menu móvel)
 * @param isLocked Indica se o scroll deve ser bloqueado
 */
export function useLockBody(isLocked: boolean = true) {
  useEffect(() => {
    if (!isLocked) {
      return;
    }

    // Salva a posição atual de scroll
    const originalStyle = window.getComputedStyle(document.body).overflow;
    const scrollY = window.scrollY;

    // Impede o scroll do body
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollY}px`;
    document.body.style.width = '100%';

    return () => {
      // Restaura o scroll ao desmontar o componente ou quando o bloqueio for removido
      document.body.style.overflow = originalStyle;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      window.scrollTo(0, scrollY);
    };
  }, [isLocked]);
}