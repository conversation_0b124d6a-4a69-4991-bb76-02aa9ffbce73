import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Database, Users, Activity, Shield, AlertCircle, CheckCircle } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Link } from "wouter";

export default function AdminSimples() {
  const { user } = useAuth();

  // Verificar se é admin
  if (user?.id !== 1) {
    return (
      <div className="container mx-auto p-6 max-w-2xl">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
              <p className="text-muted-foreground">
                Você não tem permissão para acessar esta área.
              </p>
              <Button asChild className="mt-4">
                <Link href="/dashboard">Voltar ao Dashboard</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Buscar estatísticas do sistema
  const { data: stats } = useQuery({
    queryKey: ['/api/system-status'],
    enabled: user?.id === 1,
    retry: false
  });

  const systemModules = [
    { name: "Cavalos", status: "online", description: "Gestão do plantel" },
    { name: "Manejos", status: "online", description: "Procedimentos e cuidados" },
    { name: "Nutrição", status: "online", description: "Alimentação e custos" },
    { name: "Veterinário", status: "online", description: "Cuidados médicos" },
    { name: "Reprodução", status: "online", description: "Controle reprodutivo" },
    { name: "Morfologia", status: "online", description: "Medidas e avaliações" },
    { name: "Genética", status: "online", description: "Genealogia e herança" },
    { name: "Financeiro", status: "online", description: "Controle de custos" },
  ];

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Shield className="h-8 w-8" />
          Administração do Sistema
        </h1>
        <p className="text-muted-foreground mt-2">
          Painel administrativo simplificado do RS Horse
        </p>
      </div>

      <div className="grid gap-6">
        {/* Status Geral do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Status do Sistema
            </CardTitle>
            <CardDescription>
              Visão geral do funcionamento do sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">Online</div>
                <div className="text-sm text-muted-foreground">Sistema</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">v1.0.0</div>
                <div className="text-sm text-muted-foreground">Versão</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{(stats as any)?.totalHorses || '0'}</div>
                <div className="text-sm text-muted-foreground">Cavalos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{(stats as any)?.totalUsers || '0'}</div>
                <div className="text-sm text-muted-foreground">Usuários</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Módulos do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Módulos do Sistema
            </CardTitle>
            <CardDescription>
              Status dos principais módulos funcionais
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {systemModules.map((module) => (
                <div
                  key={module.name}
                  className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{module.name}</h4>
                    <Badge variant={module.status === 'online' ? 'default' : 'destructive'}>
                      {module.status === 'online' ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <AlertCircle className="h-3 w-3 mr-1" />
                      )}
                      {module.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">{module.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Usuários do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Usuários do Sistema
            </CardTitle>
            <CardDescription>
              Informações básicas sobre os usuários
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">admin</div>
                  <div className="text-sm text-muted-foreground">ID: 1 • Administrador</div>
                </div>
                <Badge variant="default">Ativo</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">samuel</div>
                  <div className="text-sm text-muted-foreground">ID: 2 • Usuário padrão</div>
                </div>
                <Badge variant="secondary">Ativo</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Ações Administrativas */}
        <Card>
          <CardHeader>
            <CardTitle>Ações Rápidas</CardTitle>
            <CardDescription>
              Ferramentas administrativas básicas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => window.location.reload()}
              >
                <Activity className="h-5 w-5" />
                Atualizar Sistema
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                asChild
              >
                <Link href="/configuracoes">
                  <Database className="h-5 w-5" />
                  Configurações
                </Link>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                asChild
              >
                <Link href="/dashboard">
                  <Shield className="h-5 w-5" />
                  Dashboard
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Informações de Suporte */}
        <Card>
          <CardHeader>
            <CardTitle>Informações de Suporte</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-2">
              <p><strong>Sistema:</strong> RS Horse - Sistema de Gestão Equina</p>
              <p><strong>Versão:</strong> 1.0.0 MVP</p>
              <p><strong>Ambiente:</strong> Produção</p>
              <p><strong>Banco de Dados:</strong> PostgreSQL</p>
              <p><strong>Última Atualização:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}