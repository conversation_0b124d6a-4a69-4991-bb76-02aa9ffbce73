import { 
  categorias_financeiras, 
  lancamentos_financeiros, 
  cavalos,
  insertCategoriaFinanceiraSchema,
  insertLancamentoFinanceiroSchema,
  CategoriaFinanceira,
  InsertCategoriaFinanceira,
  LancamentoFinanceiro,
  InsertLancamentoFinanceiro,
} from "../../shared/schema";
import { db } from "../db";
import { eq, and, desc, asc, sum, sql, gte, lte, count } from "drizzle-orm";
import { getModuleLogger } from '../logger';

const logger = getModuleLogger('FinanceiroService');

class FinanceiroService {
  // ===== CATEGORIAS =====
  
  /**
   * Lista todas as categorias do usuário
   */
  async getCategorias(user_id: number): Promise<CategoriaFinanceira[]> {
    try {
      return await db
        .select()
        .from(categorias_financeiras)
        .where(eq(categorias_financeiras.user_id, user_id))
        .orderBy(asc(categorias_financeiras.nome));
    } catch (error) {
      logger.error('Erro ao buscar categorias:', error);
      throw error;
    }
  }

  /**
   * Busca categoria por ID
   */
  async getCategoria(id: number, user_id: number): Promise<CategoriaFinanceira | null> {
    try {
      const result = await db
        .select()
        .from(categorias_financeiras)
        .where(and(
          eq(categorias_financeiras.id, id),
          eq(categorias_financeiras.user_id, user_id)
        ))
        .limit(1);
      
      return result[0] || null;
    } catch (error) {
      logger.error('Erro ao buscar categoria:', error);
      throw error;
    }
  }

  /**
   * Cria nova categoria
   */
  async createCategoria(data: InsertCategoriaFinanceira): Promise<CategoriaFinanceira> {
    try {
      const validatedData = insertCategoriaFinanceiraSchema.parse(data);
      
      const result = await db
        .insert(categorias_financeiras)
        .values(validatedData)
        .returning();
      
      logger.info('Categoria criada:', { id: result[0].id, nome: result[0].nome });
      return result[0];
    } catch (error) {
      logger.error('Erro ao criar categoria:', error);
      throw error;
    }
  }

  /**
   * Atualiza categoria
   */
  async updateCategoria(id: number, user_id: number, data: Partial<InsertCategoriaFinanceira>): Promise<CategoriaFinanceira | null> {
    try {
      const result = await db
        .update(categorias_financeiras)
        .set(data)
        .where(and(
          eq(categorias_financeiras.id, id),
          eq(categorias_financeiras.user_id, user_id)
        ))
        .returning();
      
      if (result.length === 0) {
        return null;
      }
      
      logger.info('Categoria atualizada:', { id, nome: result[0].nome });
      return result[0];
    } catch (error) {
      logger.error('Erro ao atualizar categoria:', error);
      throw error;
    }
  }

  /**
   * Deleta categoria (apenas se não tiver lançamentos)
   */
  async deleteCategoria(id: number, user_id: number): Promise<boolean> {
    try {
      // Verificar se existem lançamentos desta categoria
      const lancamentosCount = await db
        .select({ count: count() })
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.categoria_id, id),
          eq(lancamentos_financeiros.user_id, user_id)
        ));

      if (lancamentosCount[0].count > 0) {
        throw new Error('Não é possível excluir categoria que possui lançamentos');
      }

      const result = await db
        .delete(categorias_financeiras)
        .where(and(
          eq(categorias_financeiras.id, id),
          eq(categorias_financeiras.user_id, user_id)
        ))
        .returning();
      
      logger.info('Categoria deletada:', { id });
      return result.length > 0;
    } catch (error) {
      logger.error('Erro ao deletar categoria:', error);
      throw error;
    }
  }

  // ===== LANÇAMENTOS =====

  /**
   * Lista lançamentos do usuário com filtros opcionais
   */
  async getLancamentos(
    user_id: number, 
    filters?: {
      dataInicio?: string;
      dataFim?: string;
      tipo?: 'receita' | 'despesa';
      categoria_id?: number;
      cavalo_id?: number;
    }
  ): Promise<LancamentoFinanceiro[]> {
    try {
      let query = db
        .select({
          id: lancamentos_financeiros.id,
          data: lancamentos_financeiros.data,
          tipo: lancamentos_financeiros.tipo,
          categoria_id: lancamentos_financeiros.categoria_id,
          descricao: lancamentos_financeiros.descricao,
          valor: lancamentos_financeiros.valor,
          cavalo_id: lancamentos_financeiros.cavalo_id,
          observacoes: lancamentos_financeiros.observacoes,
          user_id: lancamentos_financeiros.user_id,
          created_at: lancamentos_financeiros.created_at,
          categoria_nome: categorias_financeiras.nome,
          cavalo_nome: cavalos.name
        })
        .from(lancamentos_financeiros)
        .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
        .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
        .where(eq(lancamentos_financeiros.user_id, user_id));

      // Aplicar filtros
      if (filters?.dataInicio) {
        query = query.where(gte(lancamentos_financeiros.data, filters.dataInicio));
      }
      if (filters?.dataFim) {
        query = query.where(lte(lancamentos_financeiros.data, filters.dataFim));
      }
      if (filters?.tipo) {
        query = query.where(eq(lancamentos_financeiros.tipo, filters.tipo));
      }
      if (filters?.categoria_id) {
        query = query.where(eq(lancamentos_financeiros.categoria_id, filters.categoria_id));
      }
      if (filters?.cavalo_id) {
        query = query.where(eq(lancamentos_financeiros.cavalo_id, filters.cavalo_id));
      }

      const result = await query.orderBy(desc(lancamentos_financeiros.data));
      
      return result.map(row => ({
        ...row,
        categoria_nome: row.categoria_nome || '',
        cavalo_nome: row.cavalo_nome || null
      })) as LancamentoFinanceiro[];
    } catch (error) {
      logger.error('Erro ao buscar lançamentos:', error);
      throw error;
    }
  }

  /**
   * Busca lançamento por ID
   */
  async getLancamento(id: number, user_id: number): Promise<LancamentoFinanceiro | null> {
    try {
      const result = await db
        .select()
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.id, id),
          eq(lancamentos_financeiros.user_id, user_id)
        ))
        .limit(1);
      
      return result[0] || null;
    } catch (error) {
      logger.error('Erro ao buscar lançamento:', error);
      throw error;
    }
  }

  /**
   * Cria novo lançamento
   */
  async createLancamento(data: InsertLancamentoFinanceiro): Promise<LancamentoFinanceiro> {
    try {
      const validatedData = insertLancamentoFinanceiroSchema.parse(data);
      
      const result = await db
        .insert(lancamentos_financeiros)
        .values(validatedData)
        .returning();
      
      logger.info('Lançamento criado:', { id: result[0].id, tipo: result[0].tipo, valor: result[0].valor });
      return result[0];
    } catch (error) {
      logger.error('Erro ao criar lançamento:', error);
      throw error;
    }
  }

  /**
   * Atualiza lançamento
   */
  async updateLancamento(id: number, user_id: number, data: Partial<InsertLancamentoFinanceiro>): Promise<LancamentoFinanceiro | null> {
    try {
      const result = await db
        .update(lancamentos_financeiros)
        .set(data)
        .where(and(
          eq(lancamentos_financeiros.id, id),
          eq(lancamentos_financeiros.user_id, user_id)
        ))
        .returning();
      
      if (result.length === 0) {
        return null;
      }
      
      logger.info('Lançamento atualizado:', { id, tipo: result[0].tipo, valor: result[0].valor });
      return result[0];
    } catch (error) {
      logger.error('Erro ao atualizar lançamento:', error);
      throw error;
    }
  }

  /**
   * Deleta lançamento
   */
  async deleteLancamento(id: number, user_id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.id, id),
          eq(lancamentos_financeiros.user_id, user_id)
        ))
        .returning();
      
      logger.info('Lançamento deletado:', { id });
      return result.length > 0;
    } catch (error) {
      logger.error('Erro ao deletar lançamento:', error);
      throw error;
    }
  }

  // ===== RELATÓRIOS =====

  /**
   * Gera relatório mensal agregado
   */
  async getRelatorioMensal(user_id: number, mes: number, ano: number) {
    try {
      const dataInicio = `${ano}-${mes.toString().padStart(2, '0')}-01`;
      const ultimoDia = new Date(ano, mes, 0).getDate();
      const dataFim = `${ano}-${mes.toString().padStart(2, '0')}-${ultimoDia}`;

      // Totais gerais
      const totaisReceitas = await db
        .select({ total: sum(lancamentos_financeiros.valor) })
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          eq(lancamentos_financeiros.tipo, 'receita'),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim)
        ));

      const totaisDespesas = await db
        .select({ total: sum(lancamentos_financeiros.valor) })
        .from(lancamentos_financeiros)
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          eq(lancamentos_financeiros.tipo, 'despesa'),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim)
        ));

      // Totais por categoria
      const totalsPorCategoria = await db
        .select({
          categoria_id: lancamentos_financeiros.categoria_id,
          categoria_nome: categorias_financeiras.nome,
          tipo: lancamentos_financeiros.tipo,
          total: sum(lancamentos_financeiros.valor)
        })
        .from(lancamentos_financeiros)
        .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim)
        ))
        .groupBy(lancamentos_financeiros.categoria_id, categorias_financeiras.nome, lancamentos_financeiros.tipo);

      // Totais por cavalo
      const totalsPorCavalo = await db
        .select({
          cavalo_id: lancamentos_financeiros.cavalo_id,
          cavalo_nome: cavalos.name,
          tipo: lancamentos_financeiros.tipo,
          total: sum(lancamentos_financeiros.valor)
        })
        .from(lancamentos_financeiros)
        .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
        .where(and(
          eq(lancamentos_financeiros.user_id, user_id),
          gte(lancamentos_financeiros.data, dataInicio),
          lte(lancamentos_financeiros.data, dataFim),
          sql`${lancamentos_financeiros.cavalo_id} IS NOT NULL`
        ))
        .groupBy(lancamentos_financeiros.cavalo_id, cavalos.name, lancamentos_financeiros.tipo);

      const receitas = Number(totaisReceitas[0]?.total || 0);
      const despesas = Number(totaisDespesas[0]?.total || 0);
      const saldo = receitas - despesas;

      return {
        periodo: { mes, ano },
        resumo: {
          receitas,
          despesas,
          saldo
        },
        porCategoria: totalsPorCategoria.map(cat => ({
          categoria_id: cat.categoria_id,
          categoria_nome: cat.categoria_nome,
          tipo: cat.tipo,
          total: Number(cat.total || 0)
        })),
        porCavalo: totalsPorCavalo.map(cavalo => ({
          cavalo_id: cavalo.cavalo_id,
          cavalo_nome: cavalo.cavalo_nome,
          tipo: cavalo.tipo,
          total: Number(cavalo.total || 0)
        }))
      };
    } catch (error) {
      logger.error('Erro ao gerar relatório mensal:', error);
      throw error;
    }
  }

  /**
   * Inicializa categorias padrão para novo usuário
   */
  async initializeCategoriasPadrao(user_id: number): Promise<void> {
    try {
      const categoriasPadrao = [
        // Receitas
        { nome: 'Pensão', tipo: 'receita' as const, descricao: 'Receitas de pensionamento de cavalos', user_id },
        { nome: 'Serviços', tipo: 'receita' as const, descricao: 'Serviços diversos prestados', user_id },
        { nome: 'Venda de Animais', tipo: 'receita' as const, descricao: 'Receitas com venda de cavalos', user_id },
        
        // Despesas
        { nome: 'Alimentação', tipo: 'despesa' as const, descricao: 'Ração, feno, suplementos e outros alimentos', user_id },
        { nome: 'Medicamentos', tipo: 'despesa' as const, descricao: 'Remédios, vacinas e outros medicamentos', user_id },
        { nome: 'Serviços Veterinários', tipo: 'despesa' as const, descricao: 'Consultas e procedimentos veterinários', user_id },
        { nome: 'Ferrageamento', tipo: 'despesa' as const, descricao: 'Serviços de ferrageamento e casqueamento', user_id },
        { nome: 'Estrutura', tipo: 'despesa' as const, descricao: 'Manutenção de instalações e equipamentos', user_id },
      ];

      await db.insert(categorias_financeiras).values(categoriasPadrao);
      
      logger.info('Categorias padrão inicializadas para usuário:', { user_id });
    } catch (error) {
      logger.error('Erro ao inicializar categorias padrão:', error);
      throw error;
    }
  }
}

const financeiroService = new FinanceiroService();

export { financeiroService, FinanceiroService };