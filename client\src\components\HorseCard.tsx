import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  CardDescription, 
  <PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, Edit, Trash, Activity } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from 'wouter';

interface HorseCardProps {
  horse: {
    id: number;
    name: string;
    breed: string;
    birth_date: string;
    notes?: string;
  };
  onDelete?: (id: number) => void;
  onManage?: (id: number) => void;
}

const breedNames: { [key: string]: string } = {
  arabian: "Árabe",
  quarter: "Quarto de Milha",
  thoroughbred: "Puro-Sangue",
  andalusian: "Andaluz",
  friesian: "Frisão",
  appaloosa: "Appaloosa",
  mangalarga: "Mangalarga",
  crioulo: "Crioulo",
  other: "Outra"
};

const HorseCard: React.FC<HorseCardProps> = ({ horse, onDelete, onManage }) => {
  const { toast } = useToast();
  const [_, navigate] = useLocation();

  const formatDate = (dateString: string) => {
    if (!dateString) return "Data desconhecida";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR').format(date);
  };

  const calculateAge = (birth_date_string: string) => {
    if (!birth_date_string) return "Idade desconhecida";
    
    const birth_date = new Date(birth_date_string);
    const today = new Date();
    
    let age = today.getFullYear() - birth_date.getFullYear();
    const m = today.getMonth() - birth_date.getMonth();
    
    if (m < 0 || (m === 0 && today.getDate() < birth_date.getDate())) {
      age--;
    }
    
    return `${age} ${age === 1 ? 'ano' : 'anos'}`;
  };

  const handleViewManejos = () => {
    if (onManage) {
      onManage(horse.id);
    } else {
      navigate(`/manejos?horse_id=${horse.id}`);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      if (window.confirm(`Tem certeza que deseja excluir ${horse.name}?`)) {
        onDelete(horse.id);
        toast({
          title: "Cavalo removido",
          description: `${horse.name} foi removido do seu estábulo.`,
          variant: "default",
        });
      }
    }
  };

  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
      <CardHeader className="bg-primary/10 pb-2">
        <CardTitle className="text-primary-700 flex items-center justify-between">
          <span>{horse.name}</span>
          <Badge variant="outline" className="ml-2 bg-primary/5">
            {breedNames[horse.breed] || horse.breed}
          </Badge>
        </CardTitle>
        <CardDescription className="flex items-center mt-1">
          <CalendarDays className="h-4 w-4 mr-1 text-muted-foreground" />
          <span>
            Nascimento: {formatDate(horse.birth_date)} ({calculateAge(horse.birth_date)})
          </span>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-4">
        {horse.notes && (
          <p className="text-sm text-gray-600 mb-4">
            {horse.notes.length > 150 
              ? `${horse.notes.substring(0, 150)}...` 
              : horse.notes}
          </p>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between gap-2 pt-0">
        <Button 
          variant="default" 
          size="sm" 
          className="flex-1"
          onClick={handleViewManejos}
        >
          <Activity className="mr-2 h-4 w-4" />
          Manejos
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          className="flex-1"
          onClick={handleDelete}
        >
          <Trash className="mr-2 h-4 w-4" />
          Excluir
        </Button>
      </CardFooter>
    </Card>
  );
};

export default HorseCard;