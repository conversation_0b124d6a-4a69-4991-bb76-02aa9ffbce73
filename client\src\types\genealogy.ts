/**
 * Tipos para sistema de genealogia refatorado
 */

export type GenealogyLevel = 
  | 'pai' 
  | 'mae' 
  | 'avo_paterno' 
  | 'avo_paterna' 
  | 'avo_materno' 
  | 'avo_materna'
  | 'bisavo_paterno_paterno'
  | 'bisavo_paterno_paterna'
  | 'bisavo_materno_paterno'
  | 'bisavo_materno_paterna';

export interface GenealogyEntry {
  tipo: 'sistema' | 'externo' | 'nenhum';
  cavaloSistemaId?: string | null;
  cavaloNome?: string | null;
}

export type TipoEntradaGenealogica = 'sistema' | 'externo' | 'nenhum' | 'nao_informado';

// Interface para entrada de genealogia com campos obrigatórios
export interface EntradaGenealogica {
  tipo: TipoEntradaGenealogica;
  cavaloSistemaId?: string | null;
  cavaloNome?: string | null;
  // Campos extras para compatibilidade com dados da ABCCC
  sexo?: string;
  nascimento?: string;
  pelagem?: string;
  criador?: string;
  proprietario?: string;
  registro?: string;
}

export type GenealogyState = Record<GenealogyLevel, GenealogyEntry>;

// Função para limpar genealogia antes do submit
export function cleanGenealogy(genealogy: GenealogyState): Partial<GenealogyState> {
  const cleaned: Partial<GenealogyState> = {};
  
  Object.entries(genealogy).forEach(([level, entry]) => {
    if (entry.tipo !== 'nenhum') {
      cleaned[level as GenealogyLevel] = entry;
    }
  });
  
  return cleaned;
}

// Inicializar genealogia com valores padrão
export function initializeGenealogy(): GenealogyState {
  const levels: GenealogyLevel[] = [
    'pai', 'mae', 'avo_paterno', 'avo_paterna', 
    'avo_materno', 'avo_materna', 'bisavo_paterno_paterno',
    'bisavo_paterno_paterna', 'bisavo_materno_paterno', 'bisavo_materno_paterna'
  ];
  
  const genealogy: GenealogyState = {} as GenealogyState;
  
  levels.forEach(level => {
    genealogy[level] = {
      tipo: 'nenhum',
      cavaloSistemaId: null,
      cavaloNome: null
    };
  });
  
  return genealogy;
}