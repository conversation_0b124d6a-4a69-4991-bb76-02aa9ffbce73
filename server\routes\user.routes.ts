// User routes with improved architecture
import { Router } from 'express';
import { UserService } from '../services/user.service';
import { getDatabaseService } from '../core/database';
import { authenticateUser, requireAdmin, AuthenticatedRequest } from '../middleware/auth.middleware';
import { asyncHandler } from '../core/errors';
import { validate, authValidation } from '../validation';

const router = Router();
const db = getDatabaseService();
const userService = new UserService(db);

// POST /api/auth/login - User login
router.post('/login', validate(authValidation.login), asyncHandler(async (req, res) => {
  const result = await userService.authenticateUser(req.body);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data,
    message: 'Login successful'
  });
}));

// POST /api/auth/register - User registration
router.post('/register', validate(authValidation.signup), asyncHandler(async (req, res) => {
  const result = await userService.createUser(req.body);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.status(201).json({
    success: true,
    data: result.data,
    message: 'User created successfully'
  });
}));

// GET /api/users/profile - Get current user profile
router.get('/profile', authenticateUser, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  const result = await userService.findById(userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// PUT /api/users/profile - Update user profile
router.put('/profile', authenticateUser, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  const result = await userService.updateProfile(userId, req.body);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// PUT /api/users/password - Change password
router.put('/password', authenticateUser, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  const result = await userService.changePassword(userId, req.body);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data,
    message: 'Password changed successfully'
  });
}));

// GET /api/users/statistics - Get user statistics
router.get('/statistics', authenticateUser, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  
  const result = await userService.getUserStatistics(userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// GET /api/users - Get all users (admin only)
router.get('/', authenticateUser, requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const adminUserId = req.user!.id;
  
  const result = await userService.getAllUsers(adminUserId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// GET /api/users/:id - Get specific user (admin only)
router.get('/:id', authenticateUser, requireAdmin, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.params.id as unknown as number;
  
  const result = await userService.findById(userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// PUT /api/users/:id - Update user (admin only)
router.put('/:id', authenticateUser, requireAdmin, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.params.id as unknown as number;
  
  const result = await userService.update(userId, req.body);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

// DELETE /api/users/:id - Delete user (admin only)
router.delete('/:id', authenticateUser, requireAdmin, validate(authValidation.idParam, 'params'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.params.id as unknown as number;
  
  const result = await userService.delete(userId);
  
  if (!result.success) {
    return res.status(result.error!.statusCode).json(result);
  }

  res.json({
    success: true,
    data: result.data
  });
}));

export default router;