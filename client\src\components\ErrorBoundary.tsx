/**
 * Sistema de Error Boundaries Estratégicos - EquiGestor AI
 * Previne crashes completos da aplicação com recuperação inteligente
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { logger } from '@/lib/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  level?: 'page' | 'component' | 'critical';
  componentName?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { level = 'component', componentName = 'Unknown' } = this.props;
    const errorId = this.state.errorId;

    // Log estruturado do erro
    const errorData = {
      errorId,
      componentName,
      level,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      retryCount: this.retryCount,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.error('ErrorBoundary captured error:', errorData);
    
    // Log para monitoramento se disponível
    if (logger?.error) {
      logger.error(`Error Boundary [${level}] - ${componentName}: ${error.message}`);
    }

    // Tentar recovery automático para erros de componente
    if (level === 'component' && this.retryCount < this.maxRetries) {
      setTimeout(() => {
        this.retryCount++;
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
      }, 1000 * this.retryCount); // Backoff exponencial
    }

    this.setState({ errorInfo });
  }

  handleManualRetry = () => {
    this.retryCount++;
    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined 
    });
  };

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;
    const { componentName } = this.props;
    
    // Enviar relatório de erro (implementar conforme necessário)
    const errorReport = {
      errorId,
      componentName,
      error: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    };

    console.log('Error report generated:', errorReport);
    
    // Aqui poderia enviar para serviço de monitoramento
    // await sendErrorReport(errorReport);
  };

  override render() {
    if (this.state.hasError) {
      const { level = 'component', componentName = 'Componente', fallback } = this.props;
      const { error, errorId } = this.state;

      // Fallback customizado se fornecido
      if (fallback) {
        return fallback;
      }

      // Fallback baseado no nível do erro
      switch (level) {
        case 'critical':
          return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle className="text-red-600">Sistema Indisponível</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <AlertDescription>
                      Ocorreu um erro crítico no sistema. Nossa equipe foi notificada automaticamente.
                    </AlertDescription>
                  </Alert>
                  <div className="text-sm text-gray-600">
                    <p>ID do Erro: {errorId}</p>
                    <p>Horário: {new Date().toLocaleString()}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={() => window.location.reload()} className="flex-1">
                      Recarregar Página
                    </Button>
                    <Button variant="outline" onClick={this.handleReportError}>
                      Reportar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          );

        case 'page':
          return (
            <div className="container mx-auto p-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-orange-600">Página Indisponível</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert>
                    <AlertDescription>
                      Esta página encontrou um problema inesperado. Você pode tentar novamente ou navegar para outra seção.
                    </AlertDescription>
                  </Alert>
                  <div className="text-sm text-gray-600">
                    <p>Componente: {componentName}</p>
                    <p>Tentativas: {this.retryCount}/{this.maxRetries}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={this.handleManualRetry} disabled={this.retryCount >= this.maxRetries}>
                      Tentar Novamente
                    </Button>
                    <Button variant="outline" onClick={() => window.history.back()}>
                      Voltar
                    </Button>
                    <Button variant="outline" onClick={() => window.location.href = '/dashboard'}>
                      Dashboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          );

        default: // component
          return (
            <div className="p-4 border border-red-200 rounded-lg bg-red-50">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-red-800">
                  Erro no {componentName}
                </h3>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={this.handleManualRetry}
                  disabled={this.retryCount >= this.maxRetries}
                >
                  Tentar Novamente
                </Button>
              </div>
              <p className="text-sm text-red-600">
                {error?.message || 'Ocorreu um erro inesperado neste componente.'}
              </p>
              {this.retryCount >= this.maxRetries && (
                <p className="text-xs text-red-500 mt-2">
                  Máximo de tentativas atingido. Recarregue a página se necessário.
                </p>
              )}
            </div>
          );
      }
    }

    return this.props.children;
  }
}

// Hook para capturar erros em componentes funcionais
export const useErrorHandler = () => {
  const handleError = React.useCallback((error: Error, errorInfo?: any) => {
    console.error('Unhandled error:', error, errorInfo);
    
    if (logger?.error) {
      logger.error(`Unhandled component error: ${error.message}`);
    }
  }, []);

  return handleError;
};

// HOC para wrap componentes automaticamente
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};