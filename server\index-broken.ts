import express from "express";
import cors from "cors";
import { createServer } from "http";
import { setupVite, serveStatic, log } from "./vite";


const app = express();

// Basic middleware
app.use(cors());
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({ 
    status: 'EquiGestor AI Online!', 
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Port configuration for external deployment
function getPort() {
  // Priority 1: Environment PORT variable (for external deployments)
  if (process.env.PORT) {
    const envPort = parseInt(process.env.PORT, 10);
    if (!isNaN(envPort) && envPort > 0) {
      console.log(`🌐 Using environment PORT: ${envPort}`);
      return envPort;
    }
  }

  // Priority 2: REPLIT_PORT for Replit environment
  if (process.env.REPLIT_PORT) {
    const replitPort = parseInt(process.env.REPLIT_PORT, 10);
    if (!isNaN(replitPort) && replitPort > 0) {
      console.log(`📱 Using REPLIT_PORT: ${replitPort}`);
      return replitPort;
    }
  }

  // Priority 3: Detect specific platforms
  if (process.env.REPL_ID || process.env.REPLIT_DB_URL) {
    console.log('📱 Replit detected, using port 5000');
    return 5000;
  }

  if (process.env.K_SERVICE || process.env.GOOGLE_CLOUD_PROJECT) {
    console.log('☁️ Google Cloud Run detected, using port 8080');
    return 8080;
  }

  // Default for development
  console.log('🏠 Development mode, using port 5000');
  return 5000;
}

const port = getPort();
const host = '0.0.0.0';

const server = createServer(app);

(async () => {
  try {
    console.log('🚀 Starting EquiGestor AI server...');
    
    // Initialize database connection first
    try {
      console.log('Initializing database connection...');
      const { dbHandler } = await import("./db-connection-handler");
      await dbHandler.connect();
      console.log('✅ Database connection initialized successfully');
    } catch (dbError: any) {
      console.error('❌ Database connection failed:', dbError?.message);
      console.log('🔄 Server will continue in offline mode');
    }
    
    // Add authentication routes BEFORE Vite setup
    try {
      console.log('🔄 Loading authentication routes...');
      const { addAuthRoutes } = await import("./auth-routes");
      addAuthRoutes(app);
      console.log('✅ Authentication routes loaded successfully');
    } catch (authError: any) {
      console.error('❌ Authentication routes failed to load:', authError?.message);
    }

    // Add API routes BEFORE Vite setup
    try {
      console.log('🔄 Loading API routes...');
      const { addApiRoutes } = await import("./routes");
      await addApiRoutes(app);
      console.log('✅ API routes loaded successfully');
    } catch (routeError: any) {
      console.warn('⚠️  API routes failed to load:', routeError?.message);
      console.log('🔄 Server running in limited mode');
    }

    // Add admin user management routes
    try {
      console.log('🔄 Loading admin user management routes...');
      const { db } = await import("./db");
      const { eq } = await import("drizzle-orm");
      const { users } = await import("../shared/schema");
      const { authenticateUser } = await import("./auth");

      // Admin middleware function
      const isAdmin = (req: any, res: any, next: any) => {
        if (!req.user || req.user.role !== 'ADMIN') {
          return res.status(403).json({ error: 'Forbidden' });
        }
        next();
      };

      // GET /api/users - Lista todos os usuários (apenas admin)
      app.get('/api/users', authenticateUser, isAdmin, async (req: any, res: any) => {
        try {
          const allUsers = await db
            .select({
              id: users.id,
              name: users.name,
              username: users.username,
              email: users.email,
              role: users.role,
              created_at: users.created_at,
              updated_at: users.updated_at
            })
            .from(users)
            .orderBy(users.created_at);

          res.json(allUsers);
        } catch (error) {
          console.error('Erro ao buscar usuários:', error);
          res.status(500).json({ error: 'Erro interno do servidor' });
        }
      });

      console.log('✅ Admin user management routes loaded successfully');
    } catch (adminRoutesError: any) {
      console.warn('⚠️  Admin user management routes failed to load:', adminRoutesError?.message);
    }
      
      // Add financial routes directly inline as fallback
      try {
        console.log('📊 Loading financial routes inline...');
        const { db } = await import("./db");
        const { eq, and, desc, asc, sum, count, gte, lte } = await import("drizzle-orm");
        const { categorias_financeiras, lancamentos_financeiros, cavalos } = await import("../shared/schema");
        const { authenticateUser } = await import("./auth");

        // Listar categorias financeiras
        app.get('/api/financeiro/categorias', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            
            const categorias = await db
              .select()
              .from(categorias_financeiras)
              .where(eq(categorias_financeiras.user_id, user_id))
              .orderBy(asc(categorias_financeiras.nome));
            
            res.json(categorias);
          } catch (error) {
            console.error('Erro ao buscar categorias:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Criar categoria financeira
        app.post('/api/financeiro/categorias', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const { nome, tipo, descricao = '', ativo = true } = req.body;
            
            if (!nome || !tipo) {
              return res.status(400).json({ error: 'Nome e tipo são obrigatórios' });
            }
            
            if (!['receita', 'despesa'].includes(tipo)) {
              return res.status(400).json({ error: 'Tipo deve ser receita ou despesa' });
            }
            
            const [categoria] = await db
              .insert(categorias_financeiras)
              .values({ nome, tipo, descricao, ativo, user_id })
              .returning();
            
            res.status(201).json(categoria);
          } catch (error) {
            console.error('Erro ao criar categoria:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Excluir categoria financeira
        app.delete('/api/financeiro/categorias/:id', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const { id } = req.params;
            
            if (!id || isNaN(parseInt(id))) {
              return res.status(400).json({ error: 'ID da categoria inválido' });
            }
            
            const categoriaId = parseInt(id);
            
            // Verificar se a categoria existe e pertence ao usuário
            const existente = await db
              .select({ id: categorias_financeiras.id })
              .from(categorias_financeiras)
              .where(and(
                eq(categorias_financeiras.id, categoriaId),
                eq(categorias_financeiras.user_id, user_id)
              ));
            
            if (existente.length === 0) {
              return res.status(404).json({ error: 'Categoria não encontrada' });
            }
            
            // Verificar se há lançamentos associados a esta categoria
            const lancamentosAssociados = await db
              .select({ id: lancamentos_financeiros.id })
              .from(lancamentos_financeiros)
              .where(and(
                eq(lancamentos_financeiros.categoria_id, categoriaId),
                eq(lancamentos_financeiros.user_id, user_id)
              ));
            
            if (lancamentosAssociados.length > 0) {
              return res.status(400).json({ 
                error: 'Não é possível excluir categoria com lançamentos associados',
                lancamentosAssociados: lancamentosAssociados.length
              });
            }
            
            // Excluir a categoria
            await db
              .delete(categorias_financeiras)
              .where(and(
                eq(categorias_financeiras.id, categoriaId),
                eq(categorias_financeiras.user_id, user_id)
              ));
            
            res.json({ message: 'Categoria excluída com sucesso' });
          } catch (error) {
            console.error('Erro ao excluir categoria:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Listar lançamentos financeiros
        app.get('/api/financeiro/lancamentos', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const { dataInicio, dataFim, tipo, categoria_id, cavalo_id } = req.query;
            
            // Adicionar filtros opcionais
            const conditions = [eq(lancamentos_financeiros.user_id, user_id)];
            
            if (dataInicio) {
              conditions.push(gte(lancamentos_financeiros.data, dataInicio as string));
            }
            
            if (dataFim) {
              conditions.push(lte(lancamentos_financeiros.data, dataFim as string));
            }
            
            if (tipo) {
              conditions.push(eq(lancamentos_financeiros.tipo, tipo as 'receita' | 'despesa'));
            }
            
            if (categoria_id) {
              conditions.push(eq(lancamentos_financeiros.categoria_id, parseInt(categoria_id as string)));
            }
            
            if (cavalo_id) {
              conditions.push(eq(lancamentos_financeiros.cavalo_id, parseInt(cavalo_id as string)));
            }
            
            const lancamentos = await db
              .select({
                id: lancamentos_financeiros.id,
                data: lancamentos_financeiros.data,
                tipo: lancamentos_financeiros.tipo,
                categoria_id: lancamentos_financeiros.categoria_id,
                categoria_nome: categorias_financeiras.nome,
                descricao: lancamentos_financeiros.descricao,
                valor: lancamentos_financeiros.valor,
                cavalo_id: lancamentos_financeiros.cavalo_id,
                cavalo_nome: cavalos.name,
                observacoes: lancamentos_financeiros.observacoes,
                created_at: lancamentos_financeiros.created_at
              })
              .from(lancamentos_financeiros)
              .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
              .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
              .where(and(...conditions))
              .orderBy(desc(lancamentos_financeiros.data), desc(lancamentos_financeiros.created_at));
            
            res.json(lancamentos);
          } catch (error) {
            console.error('Erro ao buscar lançamentos:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Criar lançamento financeiro
        app.post('/api/financeiro/lancamentos', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const { data, tipo, categoria_id, descricao, valor, cavalo_id, observacoes } = req.body;
            
            if (!data || !tipo || !categoria_id || !descricao || valor === undefined) {
              return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
            }
            
            if (!['receita', 'despesa'].includes(tipo)) {
              return res.status(400).json({ error: 'Tipo deve ser receita ou despesa' });
            }
            
            const [lancamento] = await db
              .insert(lancamentos_financeiros)
              .values({
                data,
                tipo,
                categoria_id: parseInt(categoria_id),
                descricao,
                valor: parseFloat(valor),
                cavalo_id: cavalo_id ? parseInt(cavalo_id) : null,
                observacoes,
                user_id
              })
              .returning();
            
            res.status(201).json(lancamento);
          } catch (error) {
            console.error('Erro ao criar lançamento:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Excluir lançamento financeiro
        app.delete('/api/financeiro/lancamentos/:id', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const { id } = req.params;
            
            if (!id || isNaN(parseInt(id))) {
              return res.status(400).json({ error: 'ID do lançamento inválido' });
            }
            
            const lancamentoId = parseInt(id);
            
            // Verificar se o lançamento existe e pertence ao usuário
            const existente = await db
              .select({ id: lancamentos_financeiros.id })
              .from(lancamentos_financeiros)
              .where(and(
                eq(lancamentos_financeiros.id, lancamentoId),
                eq(lancamentos_financeiros.user_id, user_id)
              ));
            
            if (existente.length === 0) {
              return res.status(404).json({ error: 'Lançamento não encontrado' });
            }
            
            // Excluir o lançamento
            await db
              .delete(lancamentos_financeiros)
              .where(and(
                eq(lancamentos_financeiros.id, lancamentoId),
                eq(lancamentos_financeiros.user_id, user_id)
              ));
            
            res.json({ message: 'Lançamento excluído com sucesso' });
          } catch (error) {
            console.error('Erro ao excluir lançamento:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Inicializar categorias padrão
        app.post('/api/financeiro/init-categorias', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            
            const categoriasPadrao = [
              // Receitas
              { nome: 'Pensão', tipo: 'receita' as const, descricao: 'Receitas de pensão de cavalos', user_id, ativo: true },
              { nome: 'Serviços', tipo: 'receita' as const, descricao: 'Receitas de serviços prestados', user_id, ativo: true },
              { nome: 'Vendas', tipo: 'receita' as const, descricao: 'Vendas de cavalos e produtos', user_id, ativo: true },
              
              // Despesas
              { nome: 'Alimentação', tipo: 'despesa' as const, descricao: 'Ração, feno, suplementos', user_id, ativo: true },
              { nome: 'Medicamentos', tipo: 'despesa' as const, descricao: 'Medicamentos e vacinas', user_id, ativo: true },
              { nome: 'Veterinário', tipo: 'despesa' as const, descricao: 'Consultas e tratamentos veterinários', user_id, ativo: true },
              { nome: 'Ferrageamento', tipo: 'despesa' as const, descricao: 'Serviços de ferrageamento', user_id, ativo: true },
              { nome: 'Manutenção', tipo: 'despesa' as const, descricao: 'Manutenção de instalações', user_id, ativo: true }
            ];
            
            // Verificar categorias existentes
            const existentes = await db
              .select({ nome: categorias_financeiras.nome })
              .from(categorias_financeiras)
              .where(eq(categorias_financeiras.user_id, user_id));
            
            const nomesExistentes = existentes.map(c => c.nome);
            const novasCategorias = categoriasPadrao.filter(c => !nomesExistentes.includes(c.nome));
            
            if (novasCategorias.length > 0) {
              await db.insert(categorias_financeiras).values(novasCategorias);
            }
            
            res.json({ 
              message: 'Categorias padrão inicializadas com sucesso',
              criadas: novasCategorias.length,
              existentes: nomesExistentes.length
            });
          } catch (error) {
            console.error('Erro ao inicializar categorias padrão:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        // Relatório mensal endpoint
        app.get('/api/financeiro/relatorio-mensal', authenticateUser, async (req: any, res: any) => {
          try {
            const user_id = req.user?.id || 1;
            const mes = parseInt(req.query.mes as string) || new Date().getMonth() + 1;
            const ano = parseInt(req.query.ano as string) || new Date().getFullYear();
            
            if (mes < 1 || mes > 12) {
              return res.status(400).json({ error: 'Mês deve estar entre 1 e 12' });
            }
            
            if (ano < 2000 || ano > 2100) {
              return res.status(400).json({ error: 'Ano inválido' });
            }
            
            // Data inicial e final do mês
            const dataInicial = new Date(ano, mes - 1, 1);
            const dataFinal = new Date(ano, mes, 0);
            
            // Buscar lançamentos do mês
            const lancamentos = await db
              .select({
                id: lancamentos_financeiros.id,
                data: lancamentos_financeiros.data,
                tipo: lancamentos_financeiros.tipo,
                categoria_id: lancamentos_financeiros.categoria_id,
                categoria_nome: categorias_financeiras.nome,
                descricao: lancamentos_financeiros.descricao,
                valor: lancamentos_financeiros.valor,
                cavalo_id: lancamentos_financeiros.cavalo_id,
                cavalo_nome: cavalos.name
              })
              .from(lancamentos_financeiros)
              .leftJoin(categorias_financeiras, eq(lancamentos_financeiros.categoria_id, categorias_financeiras.id))
              .leftJoin(cavalos, eq(lancamentos_financeiros.cavalo_id, cavalos.id))
              .where(and(
                eq(lancamentos_financeiros.user_id, user_id),
                gte(lancamentos_financeiros.data, dataInicial.toISOString().split('T')[0]),
                lte(lancamentos_financeiros.data, dataFinal.toISOString().split('T')[0])
              ))
              .orderBy(desc(lancamentos_financeiros.data));
            
            // Calcular totais
            const totalReceitas = lancamentos
              .filter(l => l.tipo === 'receita')
              .reduce((acc, l) => acc + parseFloat(l.valor.toString()), 0);
            
            const totalDespesas = lancamentos
              .filter(l => l.tipo === 'despesa')
              .reduce((acc, l) => acc + parseFloat(l.valor.toString()), 0);
            
            const saldoLiquido = totalReceitas - totalDespesas;
            
            // Agrupar por categoria
            const porCategoria = lancamentos.reduce((acc, lancamento) => {
              const categoria = lancamento.categoria_nome || 'Sem categoria';
              if (!acc[categoria]) {
                acc[categoria] = {
                  nome: categoria,
                  tipo: lancamento.tipo,
                  total: 0,
                  quantidade: 0
                };
              }
              acc[categoria].total += parseFloat(lancamento.valor.toString());
              acc[categoria].quantidade += 1;
              return acc;
            }, {} as any);
            
            const relatorio = {
              mes,
              ano,
              periodo: {
                inicio: dataInicial.toISOString().split('T')[0],
                fim: dataFinal.toISOString().split('T')[0]
              },
              resumo: {
                totalReceitas,
                totalDespesas,
                saldoLiquido,
                totalLancamentos: lancamentos.length
              },
              porCategoria: Object.values(porCategoria),
              lancamentos
            };
            
            res.json(relatorio);
          } catch (error) {
            console.error('Erro ao gerar relatório mensal:', error);
            res.status(500).json({ error: 'Erro interno do servidor' });
          }
        });

        console.log('✅ Financial routes loaded inline successfully');
      } catch (financeError: any) {
        console.error('❌ Financial routes failed to load:', financeError?.message);
      }
      
      // Try minimal routes as final fallback
      try {
        const { addApiRoutes: addMinimalRoutes } = await import("./routes-minimal");
        await addMinimalRoutes(app);
        console.log('✅ Minimal API routes loaded as fallback');
      } catch (minimalError: any) {
        console.error('❌ All route loading attempts failed:', minimalError?.message);
      }
    }

    // Setup Vite for development AFTER API routes
    const nodeEnv = process.env.NODE_ENV || "development";
    if (nodeEnv === "development") {
      await setupVite(app, server);
    } else {
      serveStatic(app);
    }

    // Start server
    server.listen(port, host, () => {
      console.log(`🎉 EquiGestor AI server running on ${host}:${port}`);
      console.log(`⚙️  Environment: ${nodeEnv}`);
      console.log(`🔗 API Documentation: http://${host}:${port}/api/health`);
      console.log(`📡 External access: ${process.env.REPL_SLUG ? `https://${process.env.REPL_SLUG}.${process.env.REPL_OWNER}.repl.co` : 'localhost'}`);
      log(`serving on port ${port}`);
    });

    // Error handling
    server.on('error', (error: any) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`💥 Port ${port} is already in use`);
        process.exit(1);
      }
    });

  } catch (error) {
    console.error('❌ Critical startup error:', error);
    process.exit(1);
  }
})();



export default app;
