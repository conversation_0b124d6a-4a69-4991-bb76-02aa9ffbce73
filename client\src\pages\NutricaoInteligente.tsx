import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Calculator, 
  TrendingUp, 
  AlertTriangle, 
  Heart, 
  Scale, 
  Target,
  Zap,
  DollarSign,
  Leaf,
  BarChart3,
  Filter,
  Search,
  X
} from 'lucide-react';

interface AnaliseNutricional {
  cavalo_id: number;
  nome: string;
  peso: number;
  idade_meses: number;
  necessidades_energeticas: number;
  necessidades_proteina: number;
  score_nutricional: number;
  custo_mensal: number;
  alertas: string[];
}

export default function NutricaoInteligente() {
  const [modoVisualizacao, setModoVisualizacao] = useState<'dashboard' | 'analise'>('dashboard');
  const [cavaloSelecionado, setCavaloSelecionado] = useState<number | null>(null);
  
  // Estados dos filtros de nutrição
  const [filtros, setFiltros] = useState({
    busca: '',
    scoreMin: 0,
    scoreMax: 100,
    comPlanoNutricional: 'todos', // 'todos', 'com_plano', 'sem_plano'
    faixaIdade: 'todas', // 'todas', 'potros', 'adultos', 'idosos'
    nivelAlerta: 'todos', // 'todos', 'sem_alertas', 'alertas_baixos', 'alertas_criticos'
    ordenacao: 'nome' // 'nome', 'score', 'custo', 'idade'
  });
  const [mostrarFiltros, setMostrarFiltros] = useState(false);

  // Buscar cavalos
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery({
    queryKey: ['/api/cavalos'],
    staleTime: 5 * 60 * 1000
  });

  // Buscar dados de nutrição existentes
  const { data: nutricaoExistente = [], isLoading: loadingNutricao } = useQuery({
    queryKey: ['/api/nutricao'],
    staleTime: 2 * 60 * 1000
  });

  // Garantir que os dados são arrays tipados
  const cavalosArray: any[] = Array.isArray(cavalos) ? cavalos : [];
  const nutricaoArray: any[] = Array.isArray(nutricaoExistente) ? nutricaoExistente : [];

  // Sistema inteligente de cálculo nutricional
  const calcularAnaliseNutricional = (cavalo: any): AnaliseNutricional => {
    const peso = cavalo.peso || 450; // kg
    const dataNascimento = new Date(cavalo.birth_date || '2020-01-01');
    const idadeMeses = Math.floor((Date.now() - dataNascimento.getTime()) / (1000 * 60 * 60 * 24 * 30));
    
    // Cálculos científicos baseados em necessidades equinas
    const necessidadesEnergeticas = peso * 0.03 * 1000; // kcal/dia
    const necessidadesProteina = peso * 0.7; // gramas/dia
    
    // Buscar registros de nutrição específicos do cavalo
    const nutricaoAtual = nutricaoArray.filter((n: any) => n.cavalo_id === cavalo.id);
    
    // Cálculo inteligente de custo mensal
    let custoMensalTotal = 0;
    let temPlanoNutricional = false;
    
    if (nutricaoAtual.length > 0) {
      // Somar todos os custos mensais reais dos registros
      custoMensalTotal = nutricaoAtual.reduce((total, item) => {
        const custo = parseFloat(item.custo_mensal) || 0;
        return total + custo;
      }, 0);
      temPlanoNutricional = true;
    } else {
      // Estimativa inteligente baseada no peso para cavalos sem plano
      // Custo básico: R$ 8-12 por kg de peso vivo por mês
      const custoBasicoPorKg = 10; // R$ por kg/mês
      custoMensalTotal = peso * custoBasicoPorKg;
    }
    
    // Sistema de score nutricional inteligente
    let score = 60; // Score base mais realista
    
    // Pontuação por ter plano nutricional definido
    if (temPlanoNutricional) {
      score += 20;
      
      // Análise da qualidade do plano nutricional
      const variedadeAlimentos = new Set(nutricaoAtual.map(item => item.tipo_alimento)).size;
      if (variedadeAlimentos >= 3) score += 10; // Dieta variada
      if (variedadeAlimentos >= 5) score += 5; // Dieta muito variada
      
      // Análise de custo vs necessidades
      const custoRecomendado = peso * 12; // R$ 12/kg é um bom custo
      if (custoMensalTotal >= custoRecomendado * 0.8 && custoMensalTotal <= custoRecomendado * 1.5) {
        score += 10; // Custo adequado
      }
    }
    
    // Pontuação por condições físicas
    if (peso >= 400 && peso <= 550) score += 5; // Peso ideal
    if (idadeMeses >= 36 && idadeMeses <= 180) score += 5; // Idade adulta ideal
    
    // Sistema de alertas inteligente
    const alertas: string[] = [];
    
    if (!temPlanoNutricional) {
      alertas.push('Sem plano nutricional registrado');
    }
    
    if (peso < 350) {
      alertas.push('Peso abaixo da média - revisar nutrição');
    } else if (peso > 600) {
      alertas.push('Peso elevado - controlar alimentação');
    }
    
    if (idadeMeses <= 24) {
      alertas.push('Potro - necessidades nutricionais especiais');
    } else if (idadeMeses > 240) {
      alertas.push('Animal idoso - dieta adaptada necessária');
    }
    
    if (temPlanoNutricional) {
      const custoRecomendado = peso * 12;
      if (custoMensalTotal < custoRecomendado * 0.6) {
        alertas.push('Investimento nutricional pode ser insuficiente');
      } else if (custoMensalTotal > custoRecomendado * 2) {
        alertas.push('Custo nutricional muito elevado');
      }
    }

    return {
      cavalo_id: cavalo.id,
      nome: cavalo.nome || cavalo.name,
      peso,
      idade_meses: idadeMeses,
      necessidades_energeticas: necessidadesEnergeticas,
      necessidades_proteina: necessidadesProteina,
      score_nutricional: Math.min(Math.max(score, 0), 100),
      custo_mensal: custoMensalTotal,
      alertas
    };
  };

  // Sistema de filtros aplicado aos cavalos
  const cavalosComAnalise = useMemo(() => {
    return cavalosArray.map(calcularAnaliseNutricional);
  }, [cavalosArray, nutricaoArray]);

  // Aplicar filtros aos cavalos
  const cavalosFiltrados = useMemo(() => {
    let resultado = [...cavalosComAnalise];

    // Filtro de busca por nome
    if (filtros.busca) {
      resultado = resultado.filter(cavalo => 
        cavalo.nome.toLowerCase().includes(filtros.busca.toLowerCase())
      );
    }

    // Filtro por score nutricional
    resultado = resultado.filter(cavalo => 
      cavalo.score_nutricional >= filtros.scoreMin && 
      cavalo.score_nutricional <= filtros.scoreMax
    );

    // Filtro por plano nutricional
    if (filtros.comPlanoNutricional === 'com_plano') {
      resultado = resultado.filter(cavalo => cavalo.custo_mensal > 0);
    } else if (filtros.comPlanoNutricional === 'sem_plano') {
      resultado = resultado.filter(cavalo => cavalo.custo_mensal === 0);
    }

    // Filtro por faixa etária
    if (filtros.faixaIdade === 'potros') {
      resultado = resultado.filter(cavalo => cavalo.idade_meses <= 36); // até 3 anos
    } else if (filtros.faixaIdade === 'adultos') {
      resultado = resultado.filter(cavalo => cavalo.idade_meses > 36 && cavalo.idade_meses <= 180); // 3-15 anos
    } else if (filtros.faixaIdade === 'idosos') {
      resultado = resultado.filter(cavalo => cavalo.idade_meses > 180); // acima de 15 anos
    }

    // Filtro por nível de alerta
    if (filtros.nivelAlerta === 'sem_alertas') {
      resultado = resultado.filter(cavalo => cavalo.alertas.length === 0);
    } else if (filtros.nivelAlerta === 'alertas_baixos') {
      resultado = resultado.filter(cavalo => cavalo.alertas.length > 0 && cavalo.score_nutricional >= 60);
    } else if (filtros.nivelAlerta === 'alertas_criticos') {
      resultado = resultado.filter(cavalo => cavalo.score_nutricional < 50);
    }

    // Aplicar ordenação
    resultado.sort((a, b) => {
      switch (filtros.ordenacao) {
        case 'score':
          return b.score_nutricional - a.score_nutricional;
        case 'custo':
          return b.custo_mensal - a.custo_mensal;
        case 'idade':
          return b.idade_meses - a.idade_meses;
        case 'nome':
        default:
          return a.nome.localeCompare(b.nome);
      }
    });

    return resultado;
  }, [cavalosComAnalise, filtros]);

  // Calcular métricas do dashboard baseadas em dados filtrados
  const metricas = useMemo(() => {
    if (!cavalosFiltrados.length) return null;

    // Debug dos dados de nutrição
    console.log('🔍 Debug Nutrição:', {
      totalNutricaoRegistros: nutricaoArray.length,
      nutricaoSample: nutricaoArray.slice(0, 3),
      cavalosComAnalise: cavalosFiltrados.length
    });

    const scoreNutricionalMedio = cavalosFiltrados.reduce((sum, analise) => sum + analise.score_nutricional, 0) / cavalosFiltrados.length;
    const alertasAtivos = cavalosFiltrados.reduce((total, analise) => total + analise.alertas.length, 0);
    const alertasCriticos = cavalosFiltrados.filter(analise => analise.score_nutricional < 50).length;
    
    // Cálculo detalhado do custo mensal total
    const custoMensalTotal = cavalosFiltrados.reduce((total, analise) => {
      console.log(`💰 Cavalo ${analise.nome}: R$ ${analise.custo_mensal.toFixed(2)}`);
      return total + analise.custo_mensal;
    }, 0);

    // Separar cavalos com/sem planos nutricionais reais
    const cavalosComPlanoReal = cavalosFiltrados.filter(cavalo => {
      const nutricaoReal = nutricaoArray.filter(n => n.cavalo_id === cavalo.cavalo_id);
      return nutricaoReal.length > 0;
    }).length;

    const cavalosComEstimativa = cavalosFiltrados.length - cavalosComPlanoReal;

    console.log('📊 Métricas calculadas:', {
      custoMensalTotal: custoMensalTotal.toFixed(2),
      cavalosComPlanoReal,
      cavalosComEstimativa
    });

    return {
      totalCavalos: cavalosFiltrados.length,
      cavalosComNutricao: cavalosComPlanoReal,
      cavalosComEstimativa,
      alertasAtivos,
      alertasCriticos,
      custoMensalTotal,
      scoreNutricionalMedio,
      coberturaNutricional: cavalosFiltrados.length > 0 ? (cavalosComPlanoReal / cavalosFiltrados.length) * 100 : 0
    };
  }, [cavalosFiltrados, nutricaoArray]);

  // Componente de filtros avançados
  const renderFiltrosAvancados = () => (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados de Nutrição
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setMostrarFiltros(!mostrarFiltros)}
          >
            {mostrarFiltros ? <X className="h-4 w-4" /> : <Filter className="h-4 w-4" />}
            {mostrarFiltros ? 'Fechar' : 'Filtrar'}
          </Button>
        </div>
      </CardHeader>
      {mostrarFiltros && (
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Busca por nome */}
            <div className="space-y-2">
              <Label htmlFor="busca">Buscar por nome</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="busca"
                  placeholder="Nome do cavalo..."
                  value={filtros.busca}
                  onChange={(e) => setFiltros(prev => ({ ...prev, busca: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filtro por plano nutricional */}
            <div className="space-y-2">
              <Label>Plano Nutricional</Label>
              <Select
                value={filtros.comPlanoNutricional}
                onValueChange={(value) => setFiltros(prev => ({ ...prev, comPlanoNutricional: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os cavalos</SelectItem>
                  <SelectItem value="com_plano">Com plano nutricional</SelectItem>
                  <SelectItem value="sem_plano">Sem plano nutricional</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filtro por faixa etária */}
            <div className="space-y-2">
              <Label>Faixa Etária</Label>
              <Select
                value={filtros.faixaIdade}
                onValueChange={(value) => setFiltros(prev => ({ ...prev, faixaIdade: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todas">Todas as idades</SelectItem>
                  <SelectItem value="potros">Potros (até 3 anos)</SelectItem>
                  <SelectItem value="adultos">Adultos (3-15 anos)</SelectItem>
                  <SelectItem value="idosos">Idosos (15+ anos)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filtro por nível de alerta */}
            <div className="space-y-2">
              <Label>Nível de Alerta</Label>
              <Select
                value={filtros.nivelAlerta}
                onValueChange={(value) => setFiltros(prev => ({ ...prev, nivelAlerta: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos os níveis</SelectItem>
                  <SelectItem value="sem_alertas">Sem alertas</SelectItem>
                  <SelectItem value="alertas_baixos">Alertas baixos</SelectItem>
                  <SelectItem value="alertas_criticos">Alertas críticos</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Ordenação */}
            <div className="space-y-2">
              <Label>Ordenar por</Label>
              <Select
                value={filtros.ordenacao}
                onValueChange={(value) => setFiltros(prev => ({ ...prev, ordenacao: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nome">Nome</SelectItem>
                  <SelectItem value="score">Score nutricional</SelectItem>
                  <SelectItem value="custo">Custo mensal</SelectItem>
                  <SelectItem value="idade">Idade</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Range de score nutricional */}
            <div className="space-y-2">
              <Label>Score Nutricional</Label>
              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder="Min"
                  min="0"
                  max="100"
                  value={filtros.scoreMin}
                  onChange={(e) => setFiltros(prev => ({ ...prev, scoreMin: parseInt(e.target.value) || 0 }))}
                  className="w-20"
                />
                <span className="flex items-center">até</span>
                <Input
                  type="number"
                  placeholder="Max"
                  min="0"
                  max="100"
                  value={filtros.scoreMax}
                  onChange={(e) => setFiltros(prev => ({ ...prev, scoreMax: parseInt(e.target.value) || 100 }))}
                  className="w-20"
                />
              </div>
            </div>
          </div>

          {/* Botões de ação */}
          <div className="flex gap-2 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFiltros({
                busca: '',
                scoreMin: 0,
                scoreMax: 100,
                comPlanoNutricional: 'todos',
                faixaIdade: 'todas',
                nivelAlerta: 'todos',
                ordenacao: 'nome'
              })}
            >
              Limpar Filtros
            </Button>
            <Badge variant="secondary" className="flex items-center gap-1">
              <span>{cavalosFiltrados.length} cavalos encontrados</span>
            </Badge>
          </div>
        </CardContent>
      )}
    </Card>
  );

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Métricas Principais baseadas em dados reais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Cavalos</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metricas?.totalCavalos || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metricas?.cavalosComNutricao || 0} com plano nutricional
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Score Nutricional</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metricas?.scoreNutricionalMedio?.toFixed(1) || '0'}%
            </div>
            <Progress value={metricas?.scoreNutricionalMedio || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alertas Ativos</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metricas?.alertasAtivos || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metricas?.alertasCriticos || 0} críticos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custo Mensal Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              R$ {metricas?.custoMensalTotal?.toLocaleString('pt-BR', { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2 
              }) || '0,00'}
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>{metricas?.cavalosComNutricao || 0} com planos reais</p>
              {metricas?.cavalosComEstimativa > 0 && (
                <p>{metricas.cavalosComEstimativa} com estimativas</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Card de explicação do sistema de custos */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Calculator className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-blue-900 mb-2">Como calculamos os custos nutricionais</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Planos reais:</strong> Soma dos custos registrados no sistema</p>
                <p><strong>Estimativas:</strong> R$ 10/kg de peso corporal/mês (padrão da indústria)</p>
                <p><strong>Custo recomendado:</strong> R$ 12/kg para nutrição de qualidade</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Análise por Cavalo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Análise Individual dos Cavalos
          </CardTitle>
          <CardDescription>
            Análise nutricional baseada em dados reais do plantel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {cavalosFiltrados.slice(0, 10).map((analise) => {
              const corScore = analise.score_nutricional >= 80 ? 'text-green-600' : 
                             analise.score_nutricional >= 60 ? 'text-orange-600' : 'text-red-600';
              
              return (
                <div key={analise.cavalo_id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-1">
                    <h4 className="font-medium">{analise.nome}</h4>
                    <p className="text-sm text-muted-foreground">
                      {analise.peso}kg • {Math.floor(analise.idade_meses / 12)} anos
                    </p>
                    {analise.alertas.length > 0 && (
                      <div className="flex gap-1 mt-1">
                        {analise.alertas.map((alerta, idx) => (
                          <Badge key={idx} variant="destructive" className="text-xs">
                            {alerta}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className={`text-lg font-bold ${corScore}`}>
                      {analise.score_nutricional}%
                    </div>
                    <div className="text-sm space-y-1">
                      <div className="font-medium">
                        R$ {analise.custo_mensal.toLocaleString('pt-BR', { 
                          minimumFractionDigits: 2, 
                          maximumFractionDigits: 2 
                        })}/mês
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {nutricaoArray.filter(n => n.cavalo_id === analise.cavalo_id).length > 0 
                          ? '✓ Plano real' 
                          : '~ Estimativa'
                        }
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setCavaloSelecionado(analise.cavalo_id);
                        setModoVisualizacao('analise');
                      }}
                      className="mt-1 h-6 text-xs"
                    >
                      Ver detalhes
                    </Button>
                  </div>
                </div>
              );
            })}
            {cavalosFiltrados.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nenhum cavalo encontrado com os filtros aplicados.</p>
                <p className="text-sm">Tente ajustar os critérios de busca.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderAnaliseDetalhada = () => {
    if (!cavaloSelecionado) {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Análise Detalhada</CardTitle>
            <CardDescription>Selecione um cavalo para ver a análise completa</CardDescription>
          </CardHeader>
          <CardContent>
            <Select onValueChange={(value) => setCavaloSelecionado(parseInt(value))}>
              <SelectTrigger>
                <SelectValue placeholder="Escolha um cavalo" />
              </SelectTrigger>
              <SelectContent>
                {cavalosArray.map((cavalo: any) => (
                  <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                    {cavalo.nome || cavalo.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      );
    }

    const cavalo = cavalosArray.find((c: any) => c.id === cavaloSelecionado);
    if (!cavalo) return null;
    
    const analise = calcularAnaliseNutricional(cavalo);
    const nutricaoAtual = nutricaoArray.filter((n: any) => n.cavalo_id === cavaloSelecionado);

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Análise Detalhada - {analise.nome}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <Scale className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <div className="text-2xl font-bold">{analise.peso}kg</div>
                <div className="text-sm text-muted-foreground">Peso Atual</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <Zap className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                <div className="text-2xl font-bold">{analise.necessidades_energeticas.toFixed(0)}</div>
                <div className="text-sm text-muted-foreground">kcal/dia</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <Leaf className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <div className="text-2xl font-bold">{analise.necessidades_proteina.toFixed(0)}g</div>
                <div className="text-sm text-muted-foreground">Proteína/dia</div>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="font-semibold mb-2">Plano Nutricional Atual</h4>
              {nutricaoAtual.length > 0 ? (
                <div className="space-y-2">
                  {nutricaoAtual.map((item: any, idx: number) => (
                    <div key={idx} className="flex justify-between items-center p-2 bg-muted rounded">
                      <span>{item.tipo_alimento}</span>
                      <span>{item.quantidade}kg • R$ {item.custo_mensal || 0}/mês</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">Nenhum plano nutricional registrado</p>
              )}
            </div>

            {analise.alertas.length > 0 && (
              <div className="p-4 border-l-4 border-orange-500 bg-orange-50">
                <h4 className="font-semibold text-orange-800 mb-2">Alertas Nutricionais</h4>
                <ul className="space-y-1">
                  {analise.alertas.map((alerta, idx) => (
                    <li key={idx} className="text-orange-700">• {alerta}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  if (loadingCavalos || loadingNutricao) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-[100px]" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-[60px]" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sistema de Nutrição Inteligente</h1>
          <p className="text-muted-foreground">
            Análise científica e recomendações personalizadas baseadas em dados reais
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant={modoVisualizacao === 'dashboard' ? 'default' : 'outline'}
            onClick={() => setModoVisualizacao('dashboard')}
          >
            Dashboard
          </Button>
          <Button 
            variant={modoVisualizacao === 'analise' ? 'default' : 'outline'}
            onClick={() => setModoVisualizacao('analise')}
          >
            Análise Detalhada
          </Button>
        </div>
      </div>

      {/* Filtros Avançados */}
      {renderFiltrosAvancados()}

      {/* Conteúdo Principal */}
      {modoVisualizacao === 'dashboard' ? renderDashboard() : renderAnaliseDetalhada()}
    </div>
  );
}