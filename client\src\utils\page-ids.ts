/**
 * Mapeamento de IDs únicos para todas as páginas do sistema EquiGestor AI
 * Usado para identificação durante debugging e navegação
 */

export const PAGE_IDS = {
  // Páginas principais
  DASHBOARD: 'dashboard-main',
  LOGIN: 'auth-login',
  SIGNUP: 'auth-signup',
  NOT_FOUND: 'error-404',

  // <PERSON><PERSON><PERSON><PERSON>
  CAVALOS_LIST: 'cavalos-lista',
  CAVALO_DETAILS: 'cavalo-detalhes',
  CAVALO_FORM: 'cavalo-formulario',
  CAVALO_EDIT: 'cavalo-edicao',
  CAVALO_EDIT_SIMPLE: 'cavalo-edicao-simples',
  CAVALO_PHOTOS: 'cavalo-fotos',
  CAVALO_UPLOAD: 'cavalo-upload',
  CADASTRO_SIMPLES: 'cavalos-cadastro-simples',
  BIOMETRIA: 'cavalos-biometria',
  SAIDA_RETORNO: 'cavalos-saida-retorno',

  // Módulo Genética
  GENETICS_MAIN: 'genetica-principal',
  GENEALOGIA: 'genetica-genealogia',
  GENEALOGIA_EDIT: 'genetica-genealogia-edicao',
  GENEALOGIA_EDIT_COMPLETE: 'genetica-genealogia-edicao-completa',
  MORFOLOGIA: 'genetica-morfologia',
  DESEMPENHO: 'genetica-desempenho',
  SUGESTOES_CRUZAMENTO: 'genetica-sugestoes-cruzamento',
  HERANCA_GENETICA: 'genetica-heranca-simulador',
  IMPORTACAO_ABCCC: 'genetica-importacao-abccc',

  // Módulo Manejos
  MANEJOS_MAIN: 'manejos-principal',
  MANEJOS_TIPO: 'manejos-tipo',

  // Módulo Veterinário
  VETERINARIO_MAIN: 'veterinario-principal',
  VETERINARIO_REGISTROS: 'veterinario-registros',
  VETERINARIO_VACINACOES: 'veterinario-vacinacoes',
  VETERINARIO_VERMIFUGACOES: 'veterinario-vermifugacoes',
  PROCEDIMENTOS_VET: 'veterinario-procedimentos',

  // Módulo Reprodução
  REPRODUCAO_MAIN: 'reproducao-principal',
  REPRODUCAO_ESTATISTICAS: 'reproducao-estatisticas',
  REPRODUCAO_COLETA_SEMEN: 'reproducao-coleta-semen',
  REPRODUCAO_DETALHES: 'reproducao-detalhes',

  // Módulo Nutrição
  NUTRICAO_MAIN: 'nutricao-principal',
  NUTRICAO_LOGISTICA: 'nutricao-logistica',

  // Módulo Financeiro
  FINANCEIRO_MAIN: 'financeiro-principal',
  FINANCEIRO_LANCAMENTOS: 'financeiro-lancamentos',
  FINANCEIRO_CENTROS_CUSTO: 'financeiro-centros-custo',
  FINANCEIRO_CONTAS: 'financeiro-contas',
  FINANCEIRO_DEMONSTRATIVOS: 'financeiro-demonstrativos',

  // Módulo Administrativo
  ADMIN_MAIN: 'admin-principal',
  ADMIN_ABCCC_TOKENS: 'admin-abccc-tokens',
  USUARIOS: 'admin-usuarios',
  CONFIGURACOES: 'admin-configuracoes',

  // Outras páginas
  ESTATISTICAS: 'estatisticas-gerais',
  ALERTAS: 'alertas-sistema',
  AGENDA: 'agenda-principal',
  EVENTOS: 'eventos-principal',
  DOCUMENTOS: 'documentos-principal',
  ARQUIVOS: 'arquivos-principal',
  ASSISTENTE: 'assistente-virtual',
  INSUMOS: 'insumos-principal',
  MEDICAMENTOS: 'medicamentos-principal',
  MOVIMENTACOES: 'movimentacoes-principal',
  RELATORIOS: 'relatorios-principal'
} as const;

/**
 * Mapeamento de nomes descritivos para cada página
 */
export const PAGE_NAMES = {
  [PAGE_IDS.DASHBOARD]: 'Dashboard - Painel de Controle',
  [PAGE_IDS.LOGIN]: 'Login - Autenticação',
  [PAGE_IDS.SIGNUP]: 'Cadastro - Novo Usuário',
  [PAGE_IDS.NOT_FOUND]: 'Página Não Encontrada',

  // Cavalos
  [PAGE_IDS.CAVALOS_LIST]: 'Cavalos - Lista Principal',
  [PAGE_IDS.CAVALO_DETAILS]: 'Cavalo - Detalhes',
  [PAGE_IDS.CAVALO_FORM]: 'Cavalo - Formulário de Cadastro',
  [PAGE_IDS.CAVALO_EDIT]: 'Cavalo - Edição Completa',
  [PAGE_IDS.CAVALO_EDIT_SIMPLE]: 'Cavalo - Edição Simples',
  [PAGE_IDS.CAVALO_PHOTOS]: 'Cavalo - Fotos',
  [PAGE_IDS.CAVALO_UPLOAD]: 'Cavalo - Upload de Arquivos',
  [PAGE_IDS.CADASTRO_SIMPLES]: 'Cavalos - Cadastro Simplificado',
  [PAGE_IDS.BIOMETRIA]: 'Cavalos - Biometria',
  [PAGE_IDS.SAIDA_RETORNO]: 'Cavalos - Saída e Retorno',

  // Genética
  [PAGE_IDS.GENETICS_MAIN]: 'Genética - Página Principal',
  [PAGE_IDS.GENEALOGIA]: 'Genética - Genealogia',
  [PAGE_IDS.GENEALOGIA_EDIT]: 'Genética - Edição de Genealogia',
  [PAGE_IDS.GENEALOGIA_EDIT_COMPLETE]: 'Genética - Edição Completa de Genealogia',
  [PAGE_IDS.MORFOLOGIA]: 'Genética - Morfologia',
  [PAGE_IDS.DESEMPENHO]: 'Genética - Desempenho',
  [PAGE_IDS.SUGESTOES_CRUZAMENTO]: 'Genética - Sugestões de Cruzamento',
  [PAGE_IDS.HERANCA_GENETICA]: 'Genética - Simulador de Herança',
  [PAGE_IDS.IMPORTACAO_ABCCC]: 'Genética - Importação ABCCC',

  // Manejos
  [PAGE_IDS.MANEJOS_MAIN]: 'Manejos - Página Principal',
  [PAGE_IDS.MANEJOS_TIPO]: 'Manejos - Por Tipo',

  // Veterinário
  [PAGE_IDS.VETERINARIO_MAIN]: 'Veterinário - Página Principal',
  [PAGE_IDS.VETERINARIO_REGISTROS]: 'Veterinário - Registros Clínicos',
  [PAGE_IDS.VETERINARIO_VACINACOES]: 'Veterinário - Vacinações',
  [PAGE_IDS.VETERINARIO_VERMIFUGACOES]: 'Veterinário - Vermifugações',
  [PAGE_IDS.PROCEDIMENTOS_VET]: 'Veterinário - Procedimentos',

  // Reprodução
  [PAGE_IDS.REPRODUCAO_MAIN]: 'Reprodução - Página Principal',
  [PAGE_IDS.REPRODUCAO_ESTATISTICAS]: 'Reprodução - Estatísticas',
  [PAGE_IDS.REPRODUCAO_COLETA_SEMEN]: 'Reprodução - Coleta de Sêmen',
  [PAGE_IDS.REPRODUCAO_DETALHES]: 'Reprodução - Detalhes',

  // Nutrição
  [PAGE_IDS.NUTRICAO_MAIN]: 'Nutrição - Página Principal',
  [PAGE_IDS.NUTRICAO_LOGISTICA]: 'Nutrição - Logística',

  // Financeiro
  [PAGE_IDS.FINANCEIRO_MAIN]: 'Financeiro - Página Principal',
  [PAGE_IDS.FINANCEIRO_LANCAMENTOS]: 'Financeiro - Lançamentos',
  [PAGE_IDS.FINANCEIRO_CENTROS_CUSTO]: 'Financeiro - Centros de Custo',
  [PAGE_IDS.FINANCEIRO_CONTAS]: 'Financeiro - Contas',
  [PAGE_IDS.FINANCEIRO_DEMONSTRATIVOS]: 'Financeiro - Demonstrativos',

  // Administrativo
  [PAGE_IDS.ADMIN_MAIN]: 'Administração - Página Principal',
  [PAGE_IDS.ADMIN_ABCCC_TOKENS]: 'Administração - Tokens ABCCC',
  [PAGE_IDS.USUARIOS]: 'Administração - Usuários',
  [PAGE_IDS.CONFIGURACOES]: 'Administração - Configurações',

  // Outras
  [PAGE_IDS.ESTATISTICAS]: 'Estatísticas Gerais',
  [PAGE_IDS.ALERTAS]: 'Alertas do Sistema',
  [PAGE_IDS.AGENDA]: 'Agenda - Página Principal',
  [PAGE_IDS.EVENTOS]: 'Eventos - Página Principal',
  [PAGE_IDS.DOCUMENTOS]: 'Documentos - Página Principal',
  [PAGE_IDS.ARQUIVOS]: 'Arquivos - Página Principal',
  [PAGE_IDS.ASSISTENTE]: 'Assistente Virtual',
  [PAGE_IDS.INSUMOS]: 'Insumos - Página Principal',
  [PAGE_IDS.MEDICAMENTOS]: 'Medicamentos - Página Principal',
  [PAGE_IDS.MOVIMENTACOES]: 'Movimentações - Página Principal',
  [PAGE_IDS.RELATORIOS]: 'Relatórios - Página Principal'
} as const;

/**
 * Função helper para obter o nome da página pelo ID
 */
export function getPageName(pageId: string): string {
  return PAGE_NAMES[pageId as keyof typeof PAGE_NAMES] || 'Página Desconhecida';
}

/**
 * Função para validar se um ID de página existe
 */
export function isValidPageId(pageId: string): boolean {
  return Object.values(PAGE_IDS).includes(pageId as any);
}