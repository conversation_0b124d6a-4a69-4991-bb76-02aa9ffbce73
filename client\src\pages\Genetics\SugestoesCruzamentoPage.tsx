import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Loader2, Search, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Interfaces genéticas
import { SugestaoCruzamento } from "@/types/genetica";

export default function SugestoesCruzamentoPage() {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [objetivo, setObjetivo] = useState<string>("geral");
  const { toast } = useToast();

  // Buscar cavalos
  const {
    data: cavalos,
    isLoading: isLoadingCavalos,
    error: cavalosError,
  } = useQuery({
    queryKey: ["/api/cavalos"],
  });

  // Buscar sugestões de cruzamento
  const {
    data: sugestoes,
    isLoading: isLoadingSugestoes,
    error: sugestoesError,
    refetch,
  } = useQuery({
    queryKey: ["/api/cavalos", selectedHorseId, "sugestoes-cruzamento", objetivo],
    enabled: selectedHorseId !== null,
    queryFn: async ({ queryKey }) => {
      console.log(`Buscando sugestões para cavalo ${selectedHorseId} com objetivo ${objetivo}`);
      try {
        const response = await fetch(`/api/cavalos/${selectedHorseId}/sugestoes-cruzamento?objetivo=${objetivo}`, {
          headers: {
            'Content-Type': 'application/json',
            'User-ID': '1' // Deve ser substituído pelo ID do usuário autenticado em produção
          }
        });
        
        if (!response.ok) {
          throw new Error('Erro ao buscar sugestões de cruzamento');
        }
        
        const data = await response.json();
        return data;
      } catch (error) {
        console.error("Erro na requisição de sugestões:", error);
        // Em caso de erro, retorna dados simulados como fallback apenas durante o desenvolvimento
        // Em produção, esse comportamento deve ser removido
        if (process.env.NODE_ENV === 'development') {
          return [
            {
              id: 1,
              horseIdBase: selectedHorseId,
              horseIdSugerido: 2,
              pontuacaoCompatibilidade: 85,
              consanguinidade: 6.25,
              pontosFortes: ["Andamento", "Temperamento", "Resistência"],
              recomendacoes: "Cruzamento recomendado para melhorar características de andamento e temperamento."
            },
            {
              id: 2,
              horseIdBase: selectedHorseId,
              horseIdSugerido: 3,
              pontuacaoCompatibilidade: 78,
              consanguinidade: 3.125,
              pontosFortes: ["Conformação", "Altura"],
              recomendacoes: "Bom cruzamento para potencializar características físicas como conformação e estatura."
            },
            {
              id: 3,
              horseIdBase: selectedHorseId,
              horseIdSugerido: 4,
              pontuacaoCompatibilidade: 92,
              consanguinidade: null,
              pontosFortes: ["Velocidade", "Estrutura Óssea", "Andamento"],
              recomendacoes: "Cruzamento excelente para características atléticas e morfológicas."
            }
          ];
        }
        throw error;
      }
    },
  });

  // Atualizar visualização quando mudar o cavalo
  useEffect(() => {
    if (cavalos && Array.isArray(cavalos) && cavalos.length > 0 && !selectedHorseId) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Mostrar erros
  useEffect(() => {
    if (cavalosError) {
      toast({
        title: "Erro ao carregar cavalos",
        description: "Não foi possível carregar a lista de cavalos.",
        variant: "destructive",
      });
    }
    if (sugestoesError) {
      toast({
        title: "Erro ao carregar sugestões",
        description: "Não foi possível carregar as sugestões de cruzamento.",
        variant: "destructive",
      });
    }
  }, [cavalosError, sugestoesError, toast]);

  // Manipulador para buscar sugestões
  const handleBuscarSugestoes = () => {
    if (selectedHorseId) {
      refetch();
      toast({
        title: "Buscando sugestões",
        description: `Analisando compatibilidade para objetivo: ${objetivo}`,
      });
    }
  };

  // Classificar compatibilidade para cor
  const getCompatibilidadeClass = (valor: number) => {
    if (valor >= 85) return "text-green-600";
    if (valor >= 70) return "text-emerald-600";
    if (valor >= 50) return "text-amber-600";
    return "text-red-600";
  };

  // Classificar consanguinidade para alerta
  const alertaConsanguinidade = (valor: number | null) => {
    if (valor === null) return null;
    if (valor > 12.5)
      return {
        nivel: "alto",
        mensagem:
          "Consanguinidade alta pode aumentar o risco de problemas genéticos.",
        icone: <AlertCircle className="h-4 w-4 text-red-600" />,
      };
    if (valor > 6.25)
      return {
        nivel: "médio",
        mensagem:
          "Consanguinidade moderada. Recomenda-se monitorar características nas próximas gerações.",
        icone: <AlertCircle className="h-4 w-4 text-amber-600" />,
      };
    return {
      nivel: "baixo",
      mensagem: "Nível seguro de consanguinidade.",
      icone: <CheckCircle2 className="h-4 w-4 text-green-600" />,
    };
  };

  // Renderizar carregamento
  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando cavalos...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-primary">
          Sugestões de Cruzamento
        </h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Selecione um Cavalo e Objetivo</CardTitle>
          <CardDescription>
            O sistema analisará compatibilidade genética e sugerirá os melhores
            cruzamentos
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-1 block">
              Cavalo Base para Análise
            </label>
            <Select
              value={selectedHorseId?.toString() || ""}
              onValueChange={(value) => setSelectedHorseId(Number(value))}
            >
              <SelectTrigger className="w-full md:w-[300px]">
                <SelectValue placeholder="Selecione um cavalo" />
              </SelectTrigger>
              <SelectContent>
                {Array.isArray(cavalos) && cavalos?.map((cavalo: any) => (
                  <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                    {cavalo.name} ({cavalo.breed})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="border rounded-md p-4">
            <h3 className="font-medium mb-2">Objetivo do Cruzamento</h3>
            <RadioGroup
              value={objetivo}
              onValueChange={setObjetivo}
              className="gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="geral" id="geral" />
                <Label htmlFor="geral">Melhoramento Geral</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="conformacao" id="conformacao" />
                <Label htmlFor="conformacao">Conformação Física</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="velocidade" id="velocidade" />
                <Label htmlFor="velocidade">Velocidade/Desempenho</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="temperamento" id="temperamento" />
                <Label htmlFor="temperamento">Temperamento</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleBuscarSugestoes} className="w-full md:w-auto">
            <Search className="mr-2 h-4 w-4" />
            Buscar Sugestões de Cruzamento
          </Button>
        </CardFooter>
      </Card>

      {selectedHorseId && (
        <>
          {isLoadingSugestoes ? (
            <div className="flex items-center justify-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Analisando compatibilidades...</span>
            </div>
          ) : sugestoes && sugestoes.length > 0 ? (
            <div className="space-y-6">
              <h3 className="text-lg font-medium">
                Sugestões para{" "}
                {Array.isArray(cavalos) && cavalos?.find((c: any) => c.id === selectedHorseId)?.name}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {sugestoes.map((sugestao: SugestaoCruzamento) => {
                  const alerta = alertaConsanguinidade(sugestao.consanguinidade);
                  return (
                    <Card key={sugestao.horseIdSugerido} className="relative">
                      {sugestao.pontuacaoCompatibilidade >= 85 && (
                        <div className="absolute top-0 right-0 bg-green-600 text-white px-2 py-1 text-xs font-bold rounded-bl-md">
                          TOP
                        </div>
                      )}
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          {Array.isArray(cavalos) && cavalos?.find((c: any) => c.id === sugestao.horseIdSugerido)?.name || `Cavalo #${sugestao.horseIdSugerido}`}
                          {alerta?.nivel === "alto" && alerta.icone}
                        </CardTitle>
                        <CardDescription>
                          {Array.isArray(cavalos) && cavalos?.find((c: any) => c.id === sugestao.horseIdSugerido)?.breed || ""}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-1">
                            Compatibilidade
                          </h4>
                          <div
                            className={`text-2xl font-bold ${getCompatibilidadeClass(
                              sugestao.pontuacaoCompatibilidade
                            )}`}
                          >
                            {sugestao.pontuacaoCompatibilidade}%
                          </div>
                        </div>

                        {sugestao.consanguinidade !== null && (
                          <div>
                            <h4 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1">
                              Consanguinidade
                              {alerta?.icone}
                            </h4>
                            <div className="text-base">
                              {sugestao.consanguinidade ? `${sugestao.consanguinidade.toFixed(2)}%` : 'N/A'}
                            </div>
                            {alerta && (
                              <p className="text-xs mt-1 text-muted-foreground">
                                {alerta.mensagem}
                              </p>
                            )}
                          </div>
                        )}

                        {sugestao.pontosFortes &&
                          sugestao.pontosFortes.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-muted-foreground mb-1">
                                Pontos Fortes Compatíveis
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {sugestao.pontosFortes.map(
                                  (ponto, idx) => (
                                    <Badge
                                      key={idx}
                                      variant="secondary"
                                      className="mr-1 mb-1"
                                    >
                                      {ponto}
                                    </Badge>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-1">
                            Análise
                          </h4>
                          <p className="text-sm">
                            {sugestao.recomendacoes || "Sem análise disponível"}
                          </p>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() =>
                            toast({
                              title: "Função em implementação",
                              description: "Detalhes avançados disponíveis em breve",
                            })
                          }
                        >
                          Ver Detalhes Completos
                        </Button>
                      </CardFooter>
                    </Card>
                  );
                })}
              </div>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Sem Sugestões Disponíveis</CardTitle>
                <CardDescription>
                  Não foi possível encontrar sugestões de cruzamento para os
                  critérios selecionados.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Tente selecionar outro objetivo de cruzamento ou verificar se
                  existem outros cavalos compatíveis no sistema.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={handleBuscarSugestoes}
                >
                  <Search className="mr-2 h-4 w-4" />
                  Tentar Novamente
                </Button>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}