import { 
  users, cavalos, manejos, procedimentosVet, desempenhoHistorico, sugestoesCruzamento, genealogia, nutricao, medidas_morfologicas, reproducao, pelagens, arquivos, eventos, veterinario
} from "@shared/schema";
import { db } from "./db";
import { eq, and, asc } from "drizzle-orm";
import { getModuleLogger } from './logger';

export interface IStorage {
  // User methods
  getUserByUsername(username: string): Promise<any>;
  getUserById(id: any): Promise<any>;
  createUser(user: any): Promise<any>;
  updateUser(id: any, user: any): Promise<any>;
  deleteUser(id: any): Promise<boolean>;
  getAllUsers(): Promise<any[]>;
  
  // Horse methods
  getCavalos(user_id: number): Promise<any[]>;
  getCavalo(id: number, user_id: number): Promise<any>;
  createCavalo(cavalo: any): Promise<any>;
  updateCavalo(id: number, user_id: number, cavalo: any): Promise<any>;
  deleteCavalo(id: number, user_id: number): Promise<boolean>;
  
  // Manejo methods
  getManejos(user_id: number): Promise<any[]>;
  getManejosByHorse(horse_id: number, user_id: number): Promise<any[]>;
  getManejo(id: number, user_id: number): Promise<any>;
  createManejo(manejo: any): Promise<any>;
  updateManejo(id: number, user_id: number, manejo: any): Promise<any>;
  deleteManejo(id: number, user_id: number): Promise<boolean>;

  // Nutrition methods
  getNutricaoByUserId?(user_id: number): Promise<any[]>;
  getNutricaoByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  createNutricao?(nutricao: any): Promise<any>;
  updateNutricao?(id: number, user_id: number, nutricao: any): Promise<any>;
  deleteNutricao?(id: number, user_id: number): Promise<boolean>;

  // Horse status methods
  getInactiveHorses?(user_id: number): Promise<any[]>;
  getAllHorses?(user_id: number): Promise<any[]>;
  reactivateHorse?(id: number, user_id: number): Promise<boolean>;

  // Veterinary methods
  getProcedimentosVet?(user_id: number): Promise<any[]>;
  getProcedimentosVetByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getProcedimentoVet?(id: number, user_id: number): Promise<any>;
  createProcedimentoVet?(procedimento: any): Promise<any>;
  updateProcedimentoVet?(id: number, user_id: number, procedimento: any): Promise<any>;
  deleteProcedimentoVet?(id: number, user_id: number): Promise<boolean>;

  // Morphology methods
  getMorfologias?(user_id: number): Promise<any[]>;
  getMorfologiasByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getMorfologia?(id: number, user_id: number): Promise<any>;
  createMorfologia?(morfologia: any): Promise<any>;
  updateMorfologia?(id: number, user_id: number, morfologia: any): Promise<any>;
  deleteMorfologia?(id: number, user_id: number): Promise<boolean>;

  // Performance methods
  getDesempenhoHistorico?(user_id: number): Promise<any[]>;
  getDesempenhoHistoricoByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getDesempenho?(id: number, user_id: number): Promise<any>;
  createDesempenhoHistorico?(desempenho: any): Promise<any>;
  updateDesempenhoHistorico?(id: number, user_id: number, desempenho: any): Promise<any>;
  deleteDesempenhoHistorico?(id: number, user_id: number): Promise<boolean>;

  // Reproduction methods
  getReproducoesByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getReproducoesByProcedimento?(procedimento_id: number, user_id: number): Promise<any[]>;
  getReproducoesByEvento?(evento_id: number, user_id: number): Promise<any[]>;
  getReproducao?(id: number, user_id: number): Promise<any>;
  createReproducao?(reproducao: any): Promise<any>;
  updateReproducao?(id: number, user_id: number, reproducao: any): Promise<any>;
  deleteReproducao?(id: number, user_id: number): Promise<boolean>;

  // Placeholder methods for other routes
  getArquivos?(user_id: number): Promise<any[]>;
  getArquivosByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getArquivo?(id: number, user_id: number): Promise<any>;
  createArquivo?(arquivo: any): Promise<any>;
  deleteArquivo?(id: number, user_id: number): Promise<boolean>;
  
  getEventos?(user_id: number): Promise<any[]>;
  getEventosByDate?(date: string, user_id: number): Promise<any[]>;
  getEventosByHorse?(horse_id: number, user_id: number): Promise<any[]>;
  getEventosByManejo?(manejoId: number, user_id: number): Promise<any[]>;
  getEvento?(id: number, user_id: number): Promise<any>;
  createEvento?(evento: any): Promise<any>;
  updateEvento?(id: number, user_id: number, evento: any): Promise<any>;
  deleteEvento?(id: number, user_id: number): Promise<boolean>;

  [key: string]: any; // Allow any other methods for compatibility
}

export class Storage implements IStorage {
  private logger = getModuleLogger('Storage');

  // User methods
  async getUserByUsername(username: string): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao buscar usuário:', error);
      return null;
    }
  }

  async getUserById(id: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM users WHERE id = $1', [id]);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('getUserById error:', error);
      return null;
    }
  }

  async createUser(user: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const { name, username, email, password_hash, role = 'USER', avatar_url, flags = {} } = user;
      
      const result = await pool.query(
        `INSERT INTO users (name, username, email, password_hash, role, avatar_url, flags, created_at, updated_at) 
         VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW()) 
         RETURNING *`,
        [name, username, email, password_hash, role, avatar_url, JSON.stringify(flags)]
      );
      
      return result.rows[0];
    } catch (error) {
      this.logger.error('createUser error:', error);
      throw error;
    }
  }

  async updateUser(id: any, updates: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const { name, email, role, avatar_url, flags, password_hash } = updates;
      
      let query = 'UPDATE users SET updated_at = NOW()';
      const values = [];
      let paramCount = 1;

      if (name !== undefined) {
        query += `, name = $${paramCount}`;
        values.push(name);
        paramCount++;
      }
      if (email !== undefined) {
        query += `, email = $${paramCount}`;
        values.push(email);
        paramCount++;
      }
      if (role !== undefined) {
        query += `, role = $${paramCount}`;
        values.push(role);
        paramCount++;
      }
      if (avatar_url !== undefined) {
        query += `, avatar_url = $${paramCount}`;
        values.push(avatar_url);
        paramCount++;
      }
      if (flags !== undefined) {
        query += `, flags = $${paramCount}`;
        values.push(JSON.stringify(flags));
        paramCount++;
      }
      if (password_hash !== undefined) {
        query += `, password_hash = $${paramCount}`;
        values.push(password_hash);
        paramCount++;
      }

      query += ` WHERE id = $${paramCount} RETURNING *`;
      values.push(id);

      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      this.logger.error('updateUser error:', error);
      throw error;
    }
  }

  async deleteUser(id: any): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('DELETE FROM users WHERE id = $1', [id]);
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('deleteUser error:', error);
      return false;
    }
  }

  async getAllUsers(): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM users ORDER BY created_at DESC');
      return result.rows || [];
    } catch (error) {
      this.logger.error('getAllUsers error:', error);
      return [];
    }
  }

  // Horse methods
  async getCavalos(user_id: number): Promise<any[]> {
    try {
      console.log(`[Storage] Iniciando busca de cavalos para user_id: ${user_id}, tipo: ${typeof user_id}`);
      
      // Validate user_id
      if (!user_id || isNaN(user_id) || user_id <= 0) {
        console.error(`[Storage] UserId inválido: ${user_id}`);
        return [];
      }
      
      // Use direct SQL query to avoid Drizzle ORM issues
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      console.log(`[Storage] Database helper importado, executando query...`);
      
      const result = await DatabaseQueryHelper.executeQuery(
        'SELECT * FROM cavalos WHERE user_id = $1 AND (is_external = false OR is_external IS NULL) AND status = $2 ORDER BY name ASC', 
        [user_id, 'ativo']
      );
      console.log(`[Storage] Query executada. Result:`, { rowCount: result.rowCount, rows: result.rows?.length });
      
      const horses = result.rows || [];
      console.log(`[Storage] Encontrados ${horses.length} cavalos para user_id ${user_id}`);
      
      if (horses.length > 0) {
        console.log(`[Storage] Primeiro cavalo:`, horses[0]);
      } else {
        // Test if there are any horses in the table at all - using db instead of pool
        console.log(`[Storage] Sem cavalos encontrados para user_id ${user_id}, verificando tabela completa...`);
      }
      
      return horses.map(horse => ({
        ...horse,
        nome: horse.name, // Compatibility mapping
        pelagem: horse.cor
      }));
    } catch (error) {
      this.logger.error('Erro ao buscar cavalos:', error);
      console.error('[Storage] Erro detalhado:', error);
      return [];
    }
  }

  async getCavalo(id: number, user_id: number): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM cavalos WHERE id = $1 AND user_id = $2 AND status = $3', [id, user_id, 'ativo']);
      
      if (!result.rows || result.rows.length === 0) return null;
      
      const horse = result.rows[0];
      
      return {
        ...horse,
        nome: horse.name,
        pelagem: horse.cor
      };
    } catch (error) {
      this.logger.error('Erro ao buscar cavalo:', error);
      return null;
    }
  }

  async createCavalo(cavalo: any): Promise<any> {
    try {
      this.logger.info('Iniciando criação de cavalo', { 
        dados: cavalo,
        campos: Object.keys(cavalo)
      });

      const { pool } = await import('./db');
      
      // Data should already be sanitized by the route handlers
      // Preparar query de insert com os campos fornecidos
      const validFields = Object.keys(cavalo);
      const values = validFields.map(key => cavalo[key]);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      const fieldNames = validFields.map(field => `"${field}"`).join(', ');
      
      this.logger.info('Executando insert', {
        campos: validFields,
        valores: values.map(v => typeof v === 'string' ? v.substring(0, 50) : v)
      });
      
      const query = `INSERT INTO cavalos (${fieldNames}) VALUES (${placeholders}) RETURNING *`;
      const result = await pool.query(query, values);
      
      const newHorse = result.rows && result.rows.length > 0 ? result.rows[0] : null;
      
      this.logger.info('Cavalo criado com sucesso', {
        id: newHorse?.id,
        name: newHorse?.name
      });
      
      return newHorse;
    } catch (error) {
      this.logger.error('Erro ao criar cavalo:', {
        error: error instanceof Error ? error.message : String(error),
        dados: cavalo,
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  async updateCavalo(id: number, user_id: number, cavalo: any): Promise<any> {
    try {
      this.logger.info(`🔄 Iniciando atualização do cavalo ${id} para usuário ${user_id}`, {
        cavaloId: id,
        user_id,
        dadosRecebidos: Object.keys(cavalo)
      });

      // Log detalhado dos dados que serão atualizados
      this.logger.debug('Dados para atualização:', cavalo);

      const { pool } = await import('./db');

      // Data should already be sanitized by the route handlers
      const fields = Object.keys(cavalo);

      if (fields.length === 0) {
        this.logger.warn('Nenhum campo fornecido para atualização');
        // Retorna registro atual sem executar UPDATE
        return this.getCavalo(id, user_id);
      }

      const values = Object.values(cavalo);
      const setClause = fields.map((field, index) => `"${field}" = $${index + 1}`).join(', ');

      const query = `UPDATE cavalos SET ${setClause} WHERE id = $${fields.length + 1} AND user_id = $${fields.length + 2} RETURNING *`;
      const result = await pool.query(query, [...values, id, user_id]);

      const updatedHorse = result.rows && result.rows.length > 0 ? result.rows[0] : null;

      if (!updatedHorse) {
        this.logger.warn(`⚠️ Cavalo ${id} não encontrado para usuário ${user_id}`);
        return null;
      }

      this.logger.info(`✅ Cavalo ${id} atualizado com sucesso`, {
        cavaloId: updatedHorse.id,
        nome: updatedHorse.name
      });

      return updatedHorse;
    } catch (error) {
      this.logger.error(`❌ Erro ao atualizar cavalo ${id}:`, error);
      throw error; // Propagar erro em vez de retornar null
    }
  }

  async deleteCavalo(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      
      this.logger.info(`🔄 Iniciando soft delete do cavalo ${id} para usuário ${user_id}`);
      
      // Primeiro verificar se o cavalo existe e está ativo
      const checkResult = await pool.query(
        'SELECT id, name, status FROM cavalos WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      
      if (checkResult.rows.length === 0) {
        this.logger.warn(`⚠️ Cavalo ${id} não encontrado para usuário ${user_id}`);
        return false;
      }
      
      const cavalo = checkResult.rows[0];
      this.logger.info(`📋 Cavalo encontrado: ${cavalo.name}, status atual: ${cavalo.status}`);
      
      if (cavalo.status !== 'ativo') {
        this.logger.warn(`⚠️ Cavalo ${id} já está inativo (status: ${cavalo.status})`);
        return false;
      }
      
      // Soft delete: marcar como inativo em vez de deletar fisicamente
      const result = await pool.query(
        'UPDATE cavalos SET status = $1, data_saida = CURRENT_DATE, motivo_saida = $2 WHERE id = $3 AND user_id = $4 AND status = $5 RETURNING *', 
        ['inativo', 'Removido pelo usuário', id, user_id, 'ativo']
      );
      
      if (result.rowCount && result.rowCount > 0) {
        this.logger.info(`✅ Cavalo ${id} marcado como inativo (soft delete)`, { 
          cavaloId: id, 
          user_id,
          nomeCompleto: result.rows[0]?.name,
          rowsAffected: result.rowCount
        });
        return true;
      } else {
        this.logger.error(`❌ Falha ao atualizar cavalo ${id} - nenhuma linha afetada`);
        return false;
      }
    } catch (error) {
      this.logger.error(`❌ Erro ao marcar cavalo ${id} como inativo:`, error);
      return false;
    }
  }

  // Manejo methods
  async getManejos(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM manejos WHERE user_id = $1 ORDER BY data_execucao ASC', [user_id]);
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar manejos:', error);
      return [];
    }
  }

  async getManejosByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM manejos WHERE cavalo_id = $1 AND user_id = $2 ORDER BY data_execucao ASC', [horse_id, user_id]);
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar manejos por cavalo:', error);
      return [];
    }
  }

  async getManejo(id: number, user_id: number): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM manejos WHERE id = $1 AND user_id = $2', [id, user_id]);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao buscar manejo:', error);
      return null;
    }
  }

  async createManejo(manejo: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      
      // Prepare insert query with all fields
      const fields = Object.keys(manejo);
      const values = Object.values(manejo);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      const fieldNames = fields.map(field => `"${field}"`).join(', ');
      
      const query = `INSERT INTO manejos (${fieldNames}) VALUES (${placeholders}) RETURNING *`;
      const result = await pool.query(query, values);
      
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao criar manejo:', error);
      throw error;
    }
  }

  async updateManejo(id: number, user_id: number, manejo: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      
      // Prepare update query with all fields
      const fields = Object.keys(manejo);
      const values = Object.values(manejo);
      const setClause = fields.map((field, index) => `"${field}" = $${index + 1}`).join(', ');
      
      const query = `UPDATE manejos SET ${setClause} WHERE id = $${fields.length + 1} AND user_id = $${fields.length + 2} RETURNING *`;
      const result = await pool.query(query, [...values, id, user_id]);
      
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao atualizar manejo:', error);
      return null;
    }
  }

  async deleteManejo(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('DELETE FROM manejos WHERE id = $1 AND user_id = $2', [id, user_id]);
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Erro ao deletar manejo:', error);
      return false;
    }
  }

  // Nutrition methods
  async getNutricaoByUserId(user_id: number): Promise<any[]> {
    try {
      console.log(`[Storage] Buscando nutrição para user_id: ${user_id}`);
      
      // Use direct SQL query to avoid schema issues
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM nutricao WHERE user_id = $1 ORDER BY data DESC', 
        [user_id]
      );
      
      console.log(`[Storage] Encontrados ${result.rows?.length || 0} registros de nutrição`);
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar registros de nutrição:', error);
      return [];
    }
  }

  async getNutricaoByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM nutricao WHERE cavalo_id = $1 AND user_id = $2 ORDER BY data_inicio DESC', 
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar nutrição por cavalo:', error);
      return [];
    }
  }



  // Placeholder methods for compatibility
  async getArquivos(user_id: number): Promise<any[]> { return []; }
  async getArquivosByHorse(horse_id: number, user_id: number): Promise<any[]> { return []; }
  async getArquivo(id: number, user_id: number): Promise<any> { return null; }
  async createArquivo(arquivo: any): Promise<any> { return null; }
  async deleteArquivo(id: number, user_id: number): Promise<boolean> { return true; }
  
  // Horse status methods implementation
  async getInactiveHorses(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM cavalos WHERE user_id = $1 AND status = $2 ORDER BY data_saida DESC', [user_id, 'inativo']);
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar cavalos inativos:', error);
      return [];
    }
  }

  async getAllHorses(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query('SELECT * FROM cavalos WHERE user_id = $1 ORDER BY status ASC, name ASC', [user_id]);
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar todos os cavalos:', error);
      return [];
    }
  }

  async reactivateHorse(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'UPDATE cavalos SET status = $1, data_saida = NULL, motivo_saida = NULL WHERE id = $2 AND user_id = $3 AND status = $4 RETURNING *',
        ['ativo', id, user_id, 'inativo']
      );
      
      if (result.rowCount > 0) {
        this.logger.info(`✅ Cavalo ${id} reativado com sucesso`, { 
          cavaloId: id, 
          user_id,
          nomeCompleto: result.rows[0]?.name 
        });
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Erro ao reativar cavalo:', error);
      return false;
    }
  }

  async getEventos(user_id: number): Promise<any[]> { return []; }
  async getEventosByDate(date: string, user_id: number): Promise<any[]> { return []; }
  async getEventosByHorse(horse_id: number, user_id: number): Promise<any[]> { return []; }
  async getEventosByManejo(manejoId: number, user_id: number): Promise<any[]> { return []; }
  async getEvento(id: number, user_id: number): Promise<any> { return null; }
  async createEvento(evento: any): Promise<any> { return null; }
  async updateEvento(id: number, user_id: number, evento: any): Promise<any> { return null; }
  async deleteEvento(id: number, user_id: number): Promise<boolean> { return true; }

  // Veterinary methods
  async getProcedimentosVet(user_id: number): Promise<any[]> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      const result = await DatabaseQueryHelper.executeQuery(
        'SELECT id, tipo_procedimento as tipo, data_procedimento as data, cavalo_id, veterinario_responsavel as veterinario, diagnostico, tratamento, medicamentos, observacoes, custo, proxima_consulta, status FROM veterinario WHERE user_id = $1 ORDER BY data_procedimento DESC',
        [user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar procedimentos veterinários:', error);
      return [];
    }
  }

  async getProcedimentosVetByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      const result = await DatabaseQueryHelper.executeQuery(
        'SELECT id, tipo_procedimento as tipo, data_procedimento as data, cavalo_id as horse_id, veterinario_responsavel as veterinario, diagnostico as resultado FROM veterinario WHERE cavalo_id = $1 AND user_id = $2 ORDER BY data_procedimento DESC',
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar procedimentos veterinários por cavalo:', error);
      return [];
    }
  }

  async getProcedimentoVet(id: number, user_id: number): Promise<any> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      const result = await DatabaseQueryHelper.executeQuery(
        'SELECT * FROM veterinario WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao buscar procedimento veterinário:', error);
      return null;
    }
  }

  async createProcedimentoVet(procedimento: any): Promise<any> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      
      // Filtrar apenas campos válidos da tabela veterinario
      const validFields: Record<string, any> = {
        cavalo_id: procedimento.cavalo_id || procedimento.horse_id,
        tipo_procedimento: procedimento.tipo_procedimento || procedimento.tipo,
        data_procedimento: procedimento.data_procedimento || procedimento.data,
        veterinario_responsavel: procedimento.veterinario_responsavel || procedimento.veterinario,
        diagnostico: procedimento.diagnostico,
        tratamento: procedimento.tratamento,
        medicamentos: procedimento.medicamentos,
        observacoes: procedimento.observacoes,
        custo: procedimento.custo,
        proxima_consulta: procedimento.proxima_consulta,
        user_id: procedimento.user_id,
        status: procedimento.status || 'concluido'
      };

      // Remover campos undefined, null ou string vazia (especialmente para datas)
      Object.keys(validFields).forEach(key => {
        const typedFields = validFields as Record<string, any>;
        if (typedFields[key] === undefined || typedFields[key] === null || typedFields[key] === '') {
          delete typedFields[key];
        }
      });
      
      const fields = Object.keys(validFields);
      const values = Object.values(validFields);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      const fieldNames = fields.join(', ');

      const query = `INSERT INTO veterinario (${fieldNames}) VALUES (${placeholders}) RETURNING *`;
      const result = await DatabaseQueryHelper.executeQuery(query, values);
      
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao criar procedimento veterinário:', error);
      throw error;
    }
  }

  async updateProcedimentoVet(id: number, user_id: number, procedimento: any): Promise<any> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      
      const updates = Object.keys(procedimento)
        .map((key, index) => `${key} = $${index + 3}`)
        .join(', ');
      
      const values = [id, user_id, ...Object.values(procedimento)];
      const query = `UPDATE veterinario SET ${updates} WHERE id = $1 AND user_id = $2 RETURNING *`;
      
      const result = await DatabaseQueryHelper.executeQuery(query, values);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao atualizar procedimento veterinário:', error);
      throw error;
    }
  }

  async deleteProcedimentoVet(id: number, user_id: number): Promise<boolean> {
    try {
      const { DatabaseQueryHelper } = await import('./db-query-helper');
      const result = await DatabaseQueryHelper.executeQuery(
        'DELETE FROM veterinario WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Erro ao deletar procedimento veterinário:', error);
      return false;
    }
  }

  // Genealogy methods
  async getGenealogias(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      // Note: genealogia table doesn't have user_id column
      // User access control is handled via horse ownership
      const result = await pool.query('SELECT * FROM genealogia ORDER BY created_at DESC');
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar genealogias:', error);
      return [];
    }
  }

  async getGenealogiaByHorse(horse_id: number, user_id: number): Promise<any> {
    try {
      const { pool } = await import('./db');
      // Note: genealogia table doesn't have user_id column
      // User access control is handled via horse ownership
      const result = await pool.query('SELECT * FROM genealogia WHERE horse_id = $1', [horse_id]);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao buscar genealogia por cavalo:', error);
      return null;
    }
  }

  async createGenealogia(genealogiaData: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      
      // Convert camelCase to snake_case for database fields (sem user_id - não existe na tabela)
      const dbData: Record<string, any> = {
        horse_id: genealogiaData.horse_id,
        pai: genealogiaData.pai,
        mae: genealogiaData.mae,
        avo_paterno_id: genealogiaData.avo_paterno_id,
        avo_paterno: genealogiaData.avo_paterno,
        avo_paterna_id: genealogiaData.avo_paterna_id,
        avo_paterna: genealogiaData.avo_paterna,
        avo_materno_id: genealogiaData.avo_materno_id,
        avo_materno: genealogiaData.avo_materno,
        avo_materna_id: genealogiaData.avo_materna_id,
        avo_materna: genealogiaData.avo_materna,
        bisavo_paterno_paterno: genealogiaData.bisavo_paterno_paterno,
        bisavo_paterno_paterno_id: genealogiaData.bisavo_paterno_paterno_id,
        bisavo_paterna_paterno: genealogiaData.bisavo_paterna_paterno,
        bisavo_paterna_paterno_id: genealogiaData.bisavo_paterna_paterno_id,
        bisavo_materno_paterno: genealogiaData.bisavo_materno_paterno,
        bisavo_materno_paterno_id: genealogiaData.bisavo_materno_paterno_id,
        bisavo_materna_paterno: genealogiaData.bisavo_materna_paterno,
        bisavo_materna_paterno_id: genealogiaData.bisavo_materna_paterno_id
      };

      // Remove undefined values
      Object.keys(dbData).forEach(key => {
        if (dbData[key] === undefined) {
          delete dbData[key];
        }
      });

      // Build insert query dynamically
      const fields = Object.keys(dbData);
      const values = Object.values(dbData);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      const fieldNames = fields.join(', ');

      const query = `INSERT INTO genealogia (${fieldNames}) VALUES (${placeholders}) RETURNING *`;
      
      console.log('createGenealogia - Query:', query);
      console.log('createGenealogia - Values:', values);
      
      const result = await pool.query(query, values);
      
      if (result.rows && result.rows.length > 0) {
        console.log('createGenealogia - Success:', result.rows[0]);
        return result.rows[0];
      }
      
      throw new Error('Nenhum resultado retornado da inserção');
    } catch (error) {
      this.logger.error('Erro ao criar genealogia:', error);
      console.error('createGenealogia - Erro detalhado:', error);
      throw error;
    }
  }

  async updateGenealogia(id: number, user_id: number, genealogiaData: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      
      // Convert camelCase to snake_case for database fields
      const dbData: Record<string, any> = {
        pai: genealogiaData.pai,
        mae: genealogiaData.mae,
        avo_paterno_id: genealogiaData.avoPaternoId,
        avo_paterno: genealogiaData.avoPaterno,
        avo_paterna_id: genealogiaData.avoPaternaId,
        avo_paterna: genealogiaData.avoPaterna,
        avo_materno_id: genealogiaData.avoMaternoId,
        avo_materno: genealogiaData.avoMaterno,
        avo_materna_id: genealogiaData.avoMaternaId,
        avo_materna: genealogiaData.avoMaterna,
        bisavo_paterno_paterno: genealogiaData.bisavoPaternoPaterno,
        bisavo_paterno_paterno_id: genealogiaData.bisavoPaternoPaterno_id,
        bisavo_paterna_paterno: genealogiaData.bisavoPaternaPaterno,
        bisavo_paterna_paterno_id: genealogiaData.bisavoPaternaPaterno_id,
        bisavo_materno_paterno: genealogiaData.bisavoMaternoPaterno,
        bisavo_materno_paterno_id: genealogiaData.bisavoMaternoPaterno_id,
        bisavo_materna_paterno: genealogiaData.bisavoMaternaPaterno,
        bisavo_materna_paterno_id: genealogiaData.bisavoMaternaPaterno_id,
        bisavo_paterno_materno: genealogiaData.bisavoPaternoMaterno,
        bisavo_paterno_materno_id: genealogiaData.bisavoPaternoMaterno_id,
        bisavo_paterna_materno: genealogiaData.bisavoPaternaMaterno,
        bisavo_paterna_materno_id: genealogiaData.bisavoPaternaMaterno_id,
        bisavo_materno_materno: genealogiaData.bisavoMaternoMaterno,
        bisavo_materno_materno_id: genealogiaData.bisavoMaternoMaterno_id,
        bisavo_materna_materno: genealogiaData.bisavoMaternaMaterno,
        bisavo_materna_materno_id: genealogiaData.bisavoMaternaMaterno_id,
        observacoes: genealogiaData.observacoes
      };

      // Remove undefined values
      Object.keys(dbData).forEach(key => {
        if (dbData[key] === undefined) {
          delete dbData[key];
        }
      });

      // Build update query dynamically
      const fields = Object.keys(dbData);
      const values = Object.values(dbData);
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');

      const query = `UPDATE genealogia SET ${setClause} WHERE id = $${fields.length + 1} RETURNING *`;
      
      console.log('updateGenealogia - Query:', query);
      console.log('updateGenealogia - Values:', [...values, id]);
      
      const result = await pool.query(query, [...values, id]);
      
      if (result.rows && result.rows.length > 0) {
        console.log('updateGenealogia - Success:', result.rows[0]);
        return result.rows[0];
      }
      
      return null;
    } catch (error) {
      this.logger.error('Erro ao atualizar genealogia:', error);
      console.error('updateGenealogia - Erro detalhado:', error);
      throw error;
    }
  }

  async deleteGenealogia(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      // Note: genealogia table doesn't have user_id column
      // User access control is handled via horse ownership
      const result = await pool.query('DELETE FROM genealogia WHERE id = $1', [id]);
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Erro ao deletar genealogia:', error);
      return false;
    }
  }

  // Reproduction methods
  async getReproducoesByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM reproducao WHERE egua_id = $1 AND user_id = $2 ORDER BY created_at DESC',
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar reproduções por cavalo:', error);
      return [];
    }
  }

  async getReproducoesByProcedimento(procedimento_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM reproducao WHERE procedimento_vet_id = $1 AND user_id = $2 ORDER BY created_at DESC',
        [procedimento_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar reproduções por procedimento:', error);
      return [];
    }
  }

  async getReproducoesByEvento(evento_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM reproducao WHERE evento_id = $1 AND user_id = $2 ORDER BY created_at DESC',
        [evento_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar reproduções por evento:', error);
      return [];
    }
  }

  async getReproducao(id: number, user_id: number): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM reproducao WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao buscar reprodução:', error);
      return null;
    }
  }

  async createReproducao(reproducao: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const query = `
        INSERT INTO reproducao (
          egua_id, user_id, garanhao_id, garanhao_nome, data_cobertura, 
          data_parto_previsto, data_parto_real, tipo_cobertura,
          resultado, observacoes, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `;
      
      const values = [
        reproducao.egua_id || reproducao.horse_id, // Compatibility with legacy field name
        reproducao.user_id,
        reproducao.garanhao_id || reproducao.padreiro_id, // Compatibility with legacy field name
        reproducao.garanhao_nome,
        reproducao.data_cobertura,
        reproducao.data_parto_previsto || reproducao.data_prevista_parto, // Compatibility with legacy field name
        reproducao.data_parto_real,
        reproducao.tipo_cobertura,
        reproducao.resultado || reproducao.estado, // Compatibility with legacy field name
        reproducao.observacoes,
        reproducao.status || 'planejado'
      ];
      
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      this.logger.error('Erro ao criar reprodução:', error);
      throw error;
    }
  }

  async updateReproducao(id: number, user_id: number, reproducao: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      
      // Build dynamic update query with correct column names
      const updates = [];
      const values = [];
      let paramCount = 1;
      
      if (reproducao.egua_id !== undefined || reproducao.horse_id !== undefined) {
        updates.push(`egua_id = $${paramCount++}`);
        values.push(reproducao.egua_id || reproducao.horse_id);
      }
      if (reproducao.garanhao_id !== undefined || reproducao.padreiro_id !== undefined) {
        updates.push(`garanhao_id = $${paramCount++}`);
        values.push(reproducao.garanhao_id || reproducao.padreiro_id);
      }
      if (reproducao.garanhao_nome !== undefined) {
        updates.push(`garanhao_nome = $${paramCount++}`);
        values.push(reproducao.garanhao_nome);
      }
      if (reproducao.data_cobertura !== undefined) {
        updates.push(`data_cobertura = $${paramCount++}`);
        values.push(reproducao.data_cobertura);
      }
      if (reproducao.data_parto_previsto !== undefined || reproducao.data_prevista_parto !== undefined) {
        updates.push(`data_parto_previsto = $${paramCount++}`);
        values.push(reproducao.data_parto_previsto || reproducao.data_prevista_parto);
      }
      if (reproducao.data_parto_real !== undefined) {
        updates.push(`data_parto_real = $${paramCount++}`);
        values.push(reproducao.data_parto_real);
      }
      if (reproducao.tipo_cobertura !== undefined) {
        updates.push(`tipo_cobertura = $${paramCount++}`);
        values.push(reproducao.tipo_cobertura);
      }
      if (reproducao.resultado !== undefined || reproducao.estado !== undefined) {
        updates.push(`resultado = $${paramCount++}`);
        values.push(reproducao.resultado || reproducao.estado);
      }
      if (reproducao.status !== undefined) {
        updates.push(`status = $${paramCount++}`);
        values.push(reproducao.status);
      }
      if (reproducao.observacoes !== undefined) {
        updates.push(`observacoes = $${paramCount++}`);
        values.push(reproducao.observacoes);
      }
      
      if (updates.length === 0) {
        throw new Error('Nenhum campo para atualizar');
      }
      
      const query = `UPDATE reproducao SET ${updates.join(', ')} WHERE id = $${paramCount} AND user_id = $${paramCount + 1} RETURNING *`;
      values.push(id, user_id);
      
      const result = await pool.query(query, values);
      return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      this.logger.error('Erro ao atualizar reprodução:', error);
      throw error;
    }
  }

  async deleteReproducao(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'DELETE FROM reproducao WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Erro ao deletar reprodução:', error);
      return false;
    }
  }

  // Morphology methods
  async getMorfologias(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM medidas_morfologicas WHERE user_id = $1 ORDER BY data_medicao DESC',
        [user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar morfologias:', error);
      return [];
    }
  }

  async getMorfologiasByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM medidas_morfologicas WHERE cavalo_id = $1 AND user_id = $2 ORDER BY data_medicao DESC',
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar morfologias por cavalo:', error);
      return [];
    }
  }

  async createMorfologia(morfologia: any): Promise<any> {
    try {
      const { pool } = await import('./db');

      // Filter only fields that exist in the actual medidas_morfologicas table
      const validFields = {
        cavalo_id: morfologia.cavalo_id,
        user_id: morfologia.user_id,
        data_medicao: morfologia.data_medicao,
        altura_cernelha: morfologia.altura_cernelha,
        comprimento_corpo: morfologia.comprimento_corpo,
        circunferencia_toracica: morfologia.perimetro_toracico, // Map to correct column name
        comprimento_cabeca: morfologia.comprimento_cabeca,
        largura_cabeca: morfologia.largura_cabeca,
        circunferencia_canela: morfologia.perimetro_canela, // Map to correct column name
        angulo_casco: morfologia.angulo_casco,
        indice_cefalico: morfologia.indice_cefalico,
        pontuacao_cabeca: morfologia.pontuacao_cabeca,
        pontuacao_pescoco: morfologia.pontuacao_pescoco,
        pontuacao_espalda: morfologia.pontuacao_espalda,
        pontuacao_dorso: morfologia.pontuacao_dorso,
        pontuacao_garupa: morfologia.pontuacao_garupa,
        pontuacao_membros: morfologia.pontuacao_membros,
        pontuacao_aprumos: morfologia.pontuacao_aprumos,
        pontuacao_andamento: morfologia.pontuacao_andamento,
        pontuacao_harmonia: morfologia.pontuacao_harmonia,
        pontuacao_total: morfologia.pontuacao_total,
        observacoes: morfologia.observacoes
      };

      if (validFields.pontuacao_total === undefined) {
        const scores = [
          validFields.pontuacao_cabeca,
          validFields.pontuacao_pescoco,
          validFields.pontuacao_espalda,
          validFields.pontuacao_dorso,
          validFields.pontuacao_garupa,
          validFields.pontuacao_membros,
          validFields.pontuacao_aprumos,
          validFields.pontuacao_andamento,
          validFields.pontuacao_harmonia
        ].filter(v => v !== undefined && v !== null) as number[];
        if (scores.length > 0) {
          const media = scores.reduce((a, b) => a + b, 0) / scores.length;
          validFields.pontuacao_total = parseFloat(media.toFixed(2));
        }
      }
      
      // Remove undefined/null values
      const cleanedFields = Object.entries(validFields)
        .filter(([key, value]) => value !== undefined && value !== null)
        .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});
      
      const columns = Object.keys(cleanedFields);
      const values = Object.values(cleanedFields);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');
      
      const result = await pool.query(`
        INSERT INTO medidas_morfologicas (${columns.join(', ')})
        VALUES (${placeholders})
        RETURNING *
      `, values);
      
      return result.rows[0];
    } catch (error) {
      this.logger.error('Erro ao criar morfologia:', error);
      throw error;
    }
  }

  async updateMorfologia(id: number, user_id: number, morfologia: any): Promise<any> {
    try {
      const { pool } = await import('./db');

      const validFields = {
        cavalo_id: morfologia.cavalo_id,
        data_medicao: morfologia.data_medicao,
        altura_cernelha: morfologia.altura_cernelha,
        comprimento_corpo: morfologia.comprimento_corpo,
        circunferencia_toracica: morfologia.perimetro_toracico,
        comprimento_cabeca: morfologia.comprimento_cabeca,
        largura_cabeca: morfologia.largura_cabeca,
        circunferencia_canela: morfologia.perimetro_canela,
        angulo_casco: morfologia.angulo_casco,
        indice_cefalico: morfologia.indice_cefalico,
        pontuacao_cabeca: morfologia.pontuacao_cabeca,
        pontuacao_pescoco: morfologia.pontuacao_pescoco,
        pontuacao_espalda: morfologia.pontuacao_espalda,
        pontuacao_dorso: morfologia.pontuacao_dorso,
        pontuacao_garupa: morfologia.pontuacao_garupa,
        pontuacao_membros: morfologia.pontuacao_membros,
        pontuacao_aprumos: morfologia.pontuacao_aprumos,
        pontuacao_andamento: morfologia.pontuacao_andamento,
        pontuacao_harmonia: morfologia.pontuacao_harmonia,
        pontuacao_total: morfologia.pontuacao_total,
        observacoes: morfologia.observacoes
      } as Record<string, any>;

      const scoreKeys = [
        'pontuacao_cabeca','pontuacao_pescoco','pontuacao_espalda','pontuacao_dorso',
        'pontuacao_garupa','pontuacao_membros','pontuacao_aprumos','pontuacao_andamento','pontuacao_harmonia'
      ];

      if (scoreKeys.some(k => k in morfologia)) {
        const current = await pool.query(
          'SELECT * FROM medidas_morfologicas WHERE id = $1 AND user_id = $2',
          [id, user_id]
        );
        const base = current.rows[0] || {};
        const scores = scoreKeys.map(k =>
          validFields[k] !== undefined ? validFields[k] : base[k]
        ).filter(v => v !== undefined && v !== null) as number[];
        if (scores.length > 0) {
          const total = scores.reduce((a,b) => a + b, 0) / scores.length;
          validFields.pontuacao_total = parseFloat(total.toFixed(2));
        }
      }

      const cleaned = Object.entries(validFields)
        .filter(([_, v]) => v !== undefined && v !== null)
        .reduce((o,[k,v]) => ({ ...o, [k]: v }), {} as Record<string, any>);

      if (Object.keys(cleaned).length === 0) {
        const res = await pool.query(
          'SELECT * FROM medidas_morfologicas WHERE id = $1 AND user_id = $2',
          [id, user_id]
        );
        return res.rows[0] || null;
      }

      const fields = Object.keys(cleaned);
      const values = Object.values(cleaned);
      const setClause = fields.map((f,i) => `"${f}" = $${i+1}`).join(', ');
      const query = `UPDATE medidas_morfologicas SET ${setClause} WHERE id = $${fields.length+1} AND user_id = $${fields.length+2} RETURNING *`;

      const result = await pool.query(query, [...values, id, user_id]);
      return result.rows[0] || null;
    } catch (error) {
      this.logger.error('Erro ao atualizar morfologia:', error);
      throw error;
    }
  }

  // Performance methods
  async getDesempenhoHistorico(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM desempenho_historico WHERE user_id = $1 ORDER BY data DESC',
        [user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar desempenho histórico:', error);
      return [];
    }
  }

  async getDesempenhoHistoricoByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM desempenho_historico WHERE horse_id = $1 AND user_id = $2 ORDER BY data DESC',
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar desempenho por cavalo:', error);
      return [];
    }
  }

  async createDesempenhoHistorico(desempenho: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(`
        INSERT INTO desempenho_historico (
          horse_id, user_id, data, atividade, tempo, distancia, velocidade, observacoes
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8
        ) RETURNING *
      `, [
        desempenho.horse_id, desempenho.user_id, desempenho.data,
        desempenho.atividade, desempenho.tempo, desempenho.distancia,
        desempenho.velocidade, desempenho.observacoes
      ]);
      return result.rows[0];
    } catch (error) {
      this.logger.error('Erro ao criar desempenho histórico:', error);
      throw error;
    }
  }

  // Nutrition methods implementation  
  async getNutricoesByUserId(user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM nutricao WHERE user_id = $1 ORDER BY created_at DESC',
        [user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar nutrições por usuário:', error);
      return [];
    }
  }

  async getNutricoesByHorse(horse_id: number, user_id: number): Promise<any[]> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'SELECT * FROM nutricao WHERE cavalo_id = $1 AND user_id = $2 ORDER BY data_inicio DESC',
        [horse_id, user_id]
      );
      return result.rows || [];
    } catch (error) {
      this.logger.error('Erro ao buscar nutrições por cavalo:', error);
      return [];
    }
  }

  async createNutricao(nutricao: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const validFields = [
        'cavalo_id', 'user_id', 'tipo_alimento', 'quantidade', 'unidade',
        'frequencia', 'horario_alimentacao', 'observacoes', 'custo_mensal',
        'data_inicio', 'data_fim', 'status'
      ];
      
      // Limpar dados de entrada - converter strings vazias em null para campos de data
      const cleanedNutricao = { ...nutricao };
      if (cleanedNutricao.data_fim === '' || cleanedNutricao.data_fim === undefined) {
        cleanedNutricao.data_fim = null;
      }
      if (cleanedNutricao.data_inicio === '' || cleanedNutricao.data_inicio === undefined) {
        cleanedNutricao.data_inicio = null;
      }
      
      const fields = Object.keys(cleanedNutricao).filter(field => 
        validFields.includes(field) && cleanedNutricao[field] !== undefined
      );
      const values = fields.map(field => cleanedNutricao[field]);
      const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
      
      const query = `INSERT INTO nutricao (${fields.join(', ')}, created_at) VALUES (${placeholders}, NOW()) RETURNING *`;
      const result = await pool.query(query, values);
      
      return result.rows[0];
    } catch (error) {
      this.logger.error('Erro ao criar nutrição:', error);
      throw error;
    }
  }

  async updateNutricao(id: number, user_id: number, nutricao: any): Promise<any> {
    try {
      const { pool } = await import('./db');
      const validFields = [
        'tipo_alimento', 'quantidade', 'unidade', 'frequencia',
        'horario_alimentacao', 'observacoes', 'custo_mensal',
        'data_inicio', 'data_fim', 'status'
      ];
      
      // Limpar dados de entrada - converter strings vazias em null para campos de data
      const cleanedNutricao = { ...nutricao };
      if (cleanedNutricao.data_fim === '' || cleanedNutricao.data_fim === undefined) {
        cleanedNutricao.data_fim = null;
      }
      if (cleanedNutricao.data_inicio === '' || cleanedNutricao.data_inicio === undefined) {
        cleanedNutricao.data_inicio = null;
      }
      
      const fields = Object.keys(cleanedNutricao).filter(field => 
        validFields.includes(field) && cleanedNutricao[field] !== undefined
      );
      
      if (fields.length === 0) {
        throw new Error('Nenhum campo válido para atualização');
      }
      
      const values = fields.map(field => cleanedNutricao[field]);
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
      
      const query = `UPDATE nutricao SET ${setClause} WHERE id = $${fields.length + 1} AND user_id = $${fields.length + 2} RETURNING *`;
      const result = await pool.query(query, [...values, id, user_id]);
      
      return result.rows[0];
    } catch (error) {
      this.logger.error('Erro ao atualizar nutrição:', error);
      throw error;
    }
  }

  async deleteNutricao(id: number, user_id: number): Promise<boolean> {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(
        'DELETE FROM nutricao WHERE id = $1 AND user_id = $2',
        [id, user_id]
      );
      return result.rowCount > 0;
    } catch (error) {
      this.logger.error('Erro ao deletar nutrição:', error);
      return false;
    }
  }

  async criarProcedimentoViaIA(user_id: number, data: any): Promise<any> {
    try {
      console.log("[VETERINARIO] Criando procedimento via IA para user_id:", user_id, data);
      
      // Buscar cavalo se nome foi fornecido
      let horse_id = data.horse_id;
      if (!horse_id && data.horseName) {
        const cavalos = await this.getCavalos(user_id);
        const cavalo = cavalos.find(c => 
          c.name.toLowerCase().includes(data.horseName.toLowerCase())
        );
        if (cavalo) {
          horse_id = cavalo.id;
        } else {
          throw new Error(`Cavalo "${data.horseName}" não encontrado`);
        }
      }
      
      if (!horse_id) {
        throw new Error("ID do cavalo é obrigatório");
      }
      
      const procedimento = {
        cavalo_id: horse_id,
        tipo_procedimento: data.tipo || 'consulta',
        data_procedimento: data.dataExecucao || new Date().toISOString().split('T')[0],
        veterinario_responsavel: data.veterinario || 'A ser definido',
        observacoes: data.observacoes || '',
        status: 'pendente',
        user_id
      };
      
      const result = await db.insert(procedimentosVet).values(procedimento).returning();
      
      console.log("[VETERINARIO] Procedimento criado via IA com sucesso:", result[0]);
      return result[0];
    } catch (error) {
      console.error("[VETERINARIO] Erro ao criar procedimento via IA:", error);
      throw error;
    }
  }

  // Handle any other method calls gracefully
  [key: string]: any;
}

export const storage = new Storage();