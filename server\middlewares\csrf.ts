import { doubleCsrf } from 'csrf-csrf';
import { Request, Response, NextFunction } from 'express';
import { createError } from '../error-handler';

// Configuração para proteção CSRF
const csrfConfig = {
  // Valor secreto para gerar e validar tokens (deve ser gerado aleatoriamente em produção)
  getSecret: () => process.env.CSRF_SECRET || 'default-csrf-secret-equigestor',
  
  // Tempo de validade do token (2 horas)
  cookieOptions: {
    httpOnly: true,
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 2 * 60 * 60 * 1000 // 2 horas em milissegundos
  },
  
  // Nome do cookie que armazenará o token
  cookieName: 'x-csrf-token',
  
  // Nome do token no cabeçalho
  headerName: 'x-csrf-token',
  
  // Método de verificação de tokens em cada requisição
  checkOrigin: process.env.NODE_ENV === 'production',
  
  // Configurações de validação
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'],
  size: 64 // Tamanho do token em bytes (maior = mais seguro)
};

// Criar a instância de proteção CSRF
const { generateToken, validateRequest, doubleCsrfProtection } = doubleCsrf(csrfConfig);

/**
 * Middleware para injetar o token CSRF na resposta
 */
export const csrfInjection = (req: Request, res: Response, next: NextFunction) => {
  // Apenas para roteamento a partir do servidor
  if (req.path === '/' || req.path.startsWith('/api/csrf-token')) {
    const csrfToken = generateToken(res, req);
    // Adicionamos o token ao objeto de resposta para usá-lo nos templates
    (res as any).locals = (res as any).locals || {};
    (res as any).locals.csrfToken = csrfToken;
  }
  next();
};

/**
 * Middleware para fornecer o token CSRF via API
 */
export const getCsrfToken = (req: Request, res: Response) => {
  const csrfToken = generateToken(res, req);
  res.json({ csrfToken });
};

/**
 * Middleware customizado para tratamento de erros CSRF
 */
export const csrfErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err && err.code === 'EBADCSRFTOKEN') {
    return next(createError.authorization(
      'Token CSRF inválido. Possível ataque de Cross-Site Request Forgery.',
      { 
        path: req.path, 
        method: req.method,
        origin: req.headers.origin || 'unknown'
      }
    ));
  }
  
  // Passar outros erros para o próximo middleware
  next(err);
};

export { doubleCsrfProtection as csrfProtection };