/**
 * Configuração global para testes
 * Este arquivo é executado antes de cada teste
 */

// Configurar variáveis de ambiente para testes
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgres://postgres:postgres@localhost:5432/equigestor_test';

// Silenciar logs durante os testes
jest.mock('../logger', () => {
  const mockLogger = {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    child: jest.fn().mockReturnThis(),
  };
  
  return {
    __esModule: true,
    logger: mockLogger,
    default: mockLogger,
    log: jest.fn(),
    createRequestLogger: jest.fn().mockReturnValue(mockLogger),
    extractUserId: jest.fn().mockReturnValue('test-user'),
    extractRoute: jest.fn().mockReturnValue('GET /test'),
  };
});

// Mock para o Sentry
jest.mock('@sentry/node', () => {
  return {
    init: jest.fn(),
    captureException: jest.fn(),
    captureMessage: jest.fn(),
    configureScope: jest.fn((callback) => callback({
      setUser: jest.fn(),
      setTag: jest.fn(),
      setExtra: jest.fn(),
    })),
    Handlers: {
      requestHandler: jest.fn(() => (req, res, next) => next()),
      errorHandler: jest.fn(() => (err, req, res, next) => next(err)),
      tracingHandler: jest.fn(() => (req, res, next) => next()),
    },
    Integrations: {
      Http: jest.fn(),
      Express: jest.fn(),
    },
    withScope: jest.fn((callback) => callback({
      setTag: jest.fn(),
      setUser: jest.fn(),
      setExtra: jest.fn(),
    })),
  };
});

// Limpar todos os mocks após cada teste
afterEach(() => {
  jest.clearAllMocks();
});
