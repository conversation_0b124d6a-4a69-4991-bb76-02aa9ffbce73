import { useState, useEffect } from 'react';
import { Link } from 'wouter';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Calendar, X, Filter, List, Grid3X3, Plus, Search, MoreVertical, Clock, MapPin, CheckCircle2, AlertCircle } from 'lucide-react';
import { LayoutWrapper } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/DatePicker';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

// Interface para Cavalo
interface Cavalo {
  id: number;
  name: string;
  breed: string;
  /* Outros campos que podem ser necessários */
}

// Interface para Evento
interface Evento {
  id: number;
  titulo: string;
  descricao?: string;
  data: string;
  horaInicio: string;
  horaFim: string;
  tipo: string;
  status: 'pendente' | 'concluido' | 'cancelado';
  prioridade: 'baixa' | 'media' | 'alta';
  manejoId?: number;
  horse_id?: number;
  responsavel?: string;
  telefoneResponsavel?: string;
  local?: string;
  user_id: number;
}

/**
 * Página de Eventos
 * 
 * Esta página permite ao usuário visualizar, filtrar e gerenciar todos os eventos
 * registrados no sistema, com diferentes visualizações e opções de filtragem.
 */
export function EventosPage() {
  // Estados
  const [eventos, setEventos] = useState<Evento[]>([]);
  const [cavalos, setCavalos] = useState<Cavalo[]>([]);
  const [loading, setLoading] = useState(true);
  const [tipoEvento, setTipoEvento] = useState<string | null>(null);
  const [statusEvento, setStatusEvento] = useState<string | null>(null);
  const [prioridadeEvento, setPrioridadeEvento] = useState<string | null>(null);
  const [cavaloId, setCavaloId] = useState<number | null>(null);
  const [dataInicio, setDataInicio] = useState<Date | undefined>(undefined);
  const [dataFim, setDataFim] = useState<Date | undefined>(undefined);
  const [visualizacao, setVisualizacao] = useState<'lista' | 'grade'>('lista');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTab, setCurrentTab] = useState('todos');
  
  const { toast } = useToast();

  // Buscar eventos da API
  const fetchEventos = async () => {
    setLoading(true);
    try {
      const user_id = localStorage.getItem("user_id");
      if (!user_id) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch("/api/eventos", {
        headers: {
          "user-id": user_id,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao carregar eventos");
      }

      const data = await response.json();
      setEventos(data);
    } catch (error) {
      console.error("Erro ao buscar eventos:", error);
      toast({
        title: "Erro ao carregar eventos",
        description: "Não foi possível carregar os eventos. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Buscar cavalos da API
  const fetchCavalos = async () => {
    try {
      const user_id = localStorage.getItem("user_id");
      if (!user_id) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch("/api/cavalos", {
        headers: {
          "user-id": user_id,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao carregar cavalos");
      }

      const data = await response.json();
      setCavalos(data);
    } catch (error) {
      console.error("Erro ao buscar cavalos:", error);
    }
  };

  // Carregar dados na inicialização
  useEffect(() => {
    fetchEventos();
    fetchCavalos();
  }, []);

  // Remover um evento
  const handleRemoveEvento = async (eventoId: number) => {
    if (!confirm('Tem certeza que deseja excluir este evento?')) {
      return;
    }
    
    try {
      const user_id = localStorage.getItem("user_id");
      if (!user_id) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch(`/api/eventos/${eventoId}`, {
        method: "DELETE",
        headers: {
          "user-id": user_id,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao excluir evento");
      }

      toast({
        title: "Evento excluído com sucesso",
        description: "O evento foi removido com sucesso.",
      });

      fetchEventos();
    } catch (error) {
      console.error("Erro ao excluir evento:", error);
      toast({
        title: "Erro ao excluir evento",
        description: "Não foi possível excluir o evento. Tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };

  // Atualizar status do evento
  const handleChangeStatus = async (eventoId: number, novoStatus: string) => {
    try {
      const user_id = localStorage.getItem("user_id");
      if (!user_id) {
        throw new Error("Usuário não autenticado");
      }

      const response = await fetch(`/api/eventos/${eventoId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "user-id": user_id,
        },
        body: JSON.stringify({
          status: novoStatus,
          user_id: Number(user_id)
        }),
      });

      if (!response.ok) {
        throw new Error("Falha ao atualizar status do evento");
      }

      toast({
        title: "Status atualizado",
        description: `O evento foi marcado como "${novoStatus}".`,
      });

      fetchEventos();
    } catch (error) {
      console.error("Erro ao atualizar status:", error);
      toast({
        title: "Erro ao atualizar status",
        description: "Não foi possível atualizar o status do evento.",
        variant: "destructive",
      });
    }
  };

  // Mapear nomes de cavalos pelo ID
  const cavaloNameMap = new Map<number, string>();
  cavalos.forEach((cavalo: Cavalo) => {
    cavaloNameMap.set(cavalo.id, cavalo.name);
  });

  // Filtrar eventos com base nos critérios selecionados
  const eventosFiltrados = eventos.filter(evento => {
    // Filtrar por pesquisa de texto
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const eventoMatches = 
        evento.titulo.toLowerCase().includes(searchLower) ||
        (evento.descricao && evento.descricao.toLowerCase().includes(searchLower)) ||
        (evento.responsavel && evento.responsavel.toLowerCase().includes(searchLower)) ||
        (evento.local && evento.local.toLowerCase().includes(searchLower));
      
      if (!eventoMatches) return false;
    }
    
    // Filtrar por tipo de evento na tab atual
    if (currentTab !== 'todos' && evento.tipo !== currentTab) {
      return false;
    }
    
    // Filtrar por tipo de evento (filtro adicional)
    if (tipoEvento && tipoEvento !== 'todos' && evento.tipo !== tipoEvento) {
      return false;
    }
    
    // Filtrar por status do evento
    if (statusEvento && statusEvento !== 'todos' && evento.status !== statusEvento) {
      return false;
    }
    
    // Filtrar por prioridade
    if (prioridadeEvento && prioridadeEvento !== 'todos' && evento.prioridade !== prioridadeEvento) {
      return false;
    }
    
    // Filtrar por cavalo
    if (cavaloId && cavaloId.toString() !== 'todos' && evento.horse_id !== cavaloId) {
      return false;
    }
    
    // Filtrar por período de data (início)
    if (dataInicio) {
      const eventoDate = new Date(evento.data);
      const startDate = new Date(dataInicio);
      startDate.setHours(0, 0, 0, 0);
      
      if (eventoDate < startDate) {
        return false;
      }
    }
    
    // Filtrar por período de data (fim)
    if (dataFim) {
      const eventoDate = new Date(evento.data);
      const endDate = new Date(dataFim);
      endDate.setHours(23, 59, 59, 999);
      
      if (eventoDate > endDate) {
        return false;
      }
    }
    
    return true;
  });

  // Agrupar eventos por data para visualização em lista
  const eventosPorData = eventosFiltrados.reduce((acc, evento) => {
    const dataEvento = new Date(evento.data);
    const dataKey = dataEvento.toISOString().split('T')[0];
    
    if (!acc[dataKey]) {
      acc[dataKey] = [];
    }
    
    acc[dataKey].push(evento);
    return acc;
  }, {} as Record<string, Evento[]>);

  // Ordenar as datas para visualização em lista
  const datasOrdenadas = Object.keys(eventosPorData).sort();
  
  // Cor de fundo conforme o tipo de evento
  const getEventTypeBgColor = (tipo: string) => {
    const colors: Record<string, string> = {
      'veterinario': 'bg-blue-500',
      'ferrageamento': 'bg-amber-500',
      'odontologia': 'bg-green-500',
      'treinamento': 'bg-purple-500',
      'vacina': 'bg-pink-500',
      'reprodutivo': 'bg-red-500',
      'transporte': 'bg-cyan-500',
      'competicao': 'bg-indigo-500'
    };
    
    return colors[tipo] || 'bg-gray-500';
  };
  
  // Cores de status
  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pendente': 'bg-yellow-100 text-yellow-800 border-yellow-300',
      'concluido': 'bg-green-100 text-green-800 border-green-300',
      'cancelado': 'bg-red-100 text-red-800 border-red-300'
    };
    
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-300';
  };
  
  // Cores de prioridade
  const getPriorityColor = (prioridade: string) => {
    const colors: Record<string, string> = {
      'baixa': 'bg-blue-100 text-blue-800 border-blue-300',
      'media': 'bg-yellow-100 text-yellow-800 border-yellow-300', 
      'alta': 'bg-red-100 text-red-800 border-red-300'
    };
    
    return colors[prioridade] || 'bg-gray-100 text-gray-800 border-gray-300';
  };
  
  // Função para limpar todos os filtros
  const limparFiltros = () => {
    setTipoEvento(null);
    setStatusEvento(null);
    setPrioridadeEvento(null);
    setCavaloId(null);
    setDataInicio(undefined);
    setDataFim(undefined);
    setSearchTerm('');
  };

  // Verificação se há filtros ativos
  const filtrosAtivos = tipoEvento || statusEvento || prioridadeEvento || cavaloId || dataInicio || dataFim || searchTerm;

  return (
    <div className="space-y-6">
      {/* Cabeçalho e filtros */}
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Eventos</h1>
            <p className="text-muted-foreground">
              Gerencie todos os eventos do sistema em um só lugar
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" onClick={() => setVisualizacao(visualizacao === 'lista' ? 'grade' : 'lista')}>
              {visualizacao === 'lista' ? <Grid3X3 className="h-4 w-4 mr-2" /> : <List className="h-4 w-4 mr-2" />}
              {visualizacao === 'lista' ? 'Visualização em Grade' : 'Visualização em Lista'}
            </Button>
            
            <Link href="/agenda/evento/novo">
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Novo Evento
              </Button>
            </Link>
          </div>
        </div>
        
        {/* Barra de pesquisa e filtros */}
        <div className="grid gap-4 md:grid-cols-4">
          <div className="md:col-span-2 relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Buscar eventos..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div>
            <DatePicker 
              date={dataInicio} 
              setDate={setDataInicio}
              placeholder="Data inicial"
            />
          </div>
          
          <div>
            <DatePicker 
              date={dataFim} 
              setDate={setDataFim}
              placeholder="Data final"
            />
          </div>
        </div>
        
        {/* Filtros avançados */}
        <div className="flex flex-wrap gap-2">
          <Select value={tipoEvento || ''} onValueChange={(value) => setTipoEvento(value || null)}>
            <SelectTrigger className="w-[150px]">
              <span className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>{tipoEvento ? 'Tipo: ' + tipoEvento : 'Tipo de Evento'}</span>
              </span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos os tipos</SelectItem>
              <SelectItem value="veterinario">Veterinário</SelectItem>
              <SelectItem value="ferrageamento">Ferrageamento</SelectItem>
              <SelectItem value="odontologia">Odontologia</SelectItem>
              <SelectItem value="treinamento">Treinamento</SelectItem>
              <SelectItem value="vacina">Vacinação</SelectItem>
              <SelectItem value="reprodutivo">Reprodutivo</SelectItem>
              <SelectItem value="transporte">Transporte</SelectItem>
              <SelectItem value="competicao">Competição</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusEvento || ''} onValueChange={(value) => setStatusEvento(value || null)}>
            <SelectTrigger className="w-[150px]">
              <span className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>{statusEvento ? 'Status: ' + statusEvento : 'Status'}</span>
              </span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos os status</SelectItem>
              <SelectItem value="pendente">Pendente</SelectItem>
              <SelectItem value="concluido">Concluído</SelectItem>
              <SelectItem value="cancelado">Cancelado</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={prioridadeEvento || ''} onValueChange={(value) => setPrioridadeEvento(value || null)}>
            <SelectTrigger className="w-[150px]">
              <span className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>{prioridadeEvento ? 'Prioridade: ' + prioridadeEvento : 'Prioridade'}</span>
              </span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todas as prioridades</SelectItem>
              <SelectItem value="baixa">Baixa</SelectItem>
              <SelectItem value="media">Média</SelectItem>
              <SelectItem value="alta">Alta</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={cavaloId?.toString() || ''} onValueChange={(value) => setCavaloId(value ? parseInt(value) : null)}>
            <SelectTrigger className="w-[150px]">
              <span className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <span>{cavaloId ? 'Cavalo: ' + (cavaloNameMap.get(cavaloId) || 'ID: ' + cavaloId) : 'Cavalo'}</span>
              </span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos os cavalos</SelectItem>
              {cavalos.map(cavalo => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {filtrosAtivos && (
            <Button variant="ghost" size="sm" onClick={limparFiltros}>
              <X className="h-4 w-4 mr-2" />
              Limpar filtros
            </Button>
          )}
        </div>
        
        {/* Badges de filtros ativos */}
        {filtrosAtivos && (
          <div className="flex flex-wrap gap-2">
            {searchTerm && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100 border-blue-200">
                Busca: {searchTerm}
                <button 
                  className="ml-1 hover:text-red-500" 
                  onClick={() => setSearchTerm('')}
                >
                  <X size={12} />
                </button>
              </Badge>
            )}
            
            {dataInicio && (
              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100 border-green-200">
                A partir de: {format(dataInicio, 'dd/MM/yyyy')}
                <button 
                  className="ml-1 hover:text-red-500" 
                  onClick={() => setDataInicio(undefined)}
                >
                  <X size={12} />
                </button>
              </Badge>
            )}
            
            {dataFim && (
              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100 border-green-200">
                Até: {format(dataFim, 'dd/MM/yyyy')}
                <button 
                  className="ml-1 hover:text-red-500" 
                  onClick={() => setDataFim(undefined)}
                >
                  <X size={12} />
                </button>
              </Badge>
            )}
          </div>
        )}
      </div>
      
      {/* Guias de tipos de eventos */}
      <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
        <ScrollArea className="w-full">
          <div className="flex w-max pb-2">
            <TabsList className="flex">
              <TabsTrigger value="todos">Todos</TabsTrigger>
              <TabsTrigger value="veterinario">Veterinário</TabsTrigger>
              <TabsTrigger value="ferrageamento">Ferrageamento</TabsTrigger>
              <TabsTrigger value="odontologia">Odontologia</TabsTrigger>
              <TabsTrigger value="treinamento">Treinamento</TabsTrigger>
              <TabsTrigger value="vacina">Vacinação</TabsTrigger>
              <TabsTrigger value="reprodutivo">Reprodutivo</TabsTrigger>
              <TabsTrigger value="transporte">Transporte</TabsTrigger>
              <TabsTrigger value="competicao">Competição</TabsTrigger>
            </TabsList>
          </div>
        </ScrollArea>

        {/* Conteúdo principal - Visualização dos eventos */}
        <TabsContent value={currentTab} className="pt-4 mt-0 space-y-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Carregando eventos...</span>
            </div>
          ) : eventosFiltrados.length === 0 ? (
            <div className="bg-muted/40 rounded-lg py-10 text-center">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
              <h3 className="text-lg font-medium mb-1">Nenhum evento encontrado</h3>
              <p className="text-muted-foreground">
                Não há eventos que correspondam aos critérios selecionados.
              </p>
              {filtrosAtivos && (
                <Button variant="link" onClick={limparFiltros} className="mt-2">
                  Limpar todos os filtros
                </Button>
              )}
            </div>
          ) : visualizacao === 'lista' ? (
            // Visualização em lista (agrupada por data)
            <div className="space-y-6">
              {datasOrdenadas.map(data => {
                const eventosNaData = eventosPorData[data];
                const dataFormatada = format(new Date(data), "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR });
                
                return (
                  <div key={data} className="space-y-3">
                    <div className="sticky top-0 bg-background z-10 py-2">
                      <h3 className="font-semibold text-lg capitalize">{dataFormatada}</h3>
                      <Separator className="mt-1" />
                    </div>
                    
                    <div className="space-y-3">
                      {eventosNaData.map(evento => (
                        <Card key={evento.id} className="overflow-hidden">
                          <div className="flex">
                            <div className={`w-1.5 ${getEventTypeBgColor(evento.tipo)}`}></div>
                            <div className="flex-1 p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="flex items-center space-x-2 mb-1">
                                    <Badge variant="outline" className={getPriorityColor(evento.prioridade)}>
                                      {evento.prioridade.charAt(0).toUpperCase() + evento.prioridade.slice(1)}
                                    </Badge>
                                    <Badge variant="outline" className={getStatusColor(evento.status)}>
                                      {evento.status.charAt(0).toUpperCase() + evento.status.slice(1)}
                                    </Badge>
                                  </div>
                                  
                                  <h4 className="font-semibold text-lg mb-1">{evento.titulo}</h4>
                                  
                                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm text-muted-foreground mt-2">
                                    <div className="flex items-center">
                                      <Clock className="h-3.5 w-3.5 mr-1" />
                                      <span>{evento.horaInicio} - {evento.horaFim}</span>
                                    </div>
                                    
                                    {evento.local && (
                                      <div className="flex items-center">
                                        <MapPin className="h-3.5 w-3.5 mr-1" />
                                        <span>{evento.local}</span>
                                      </div>
                                    )}
                                    
                                    {evento.horse_id && (
                                      <div>
                                        <span>Cavalo: {cavaloNameMap.get(evento.horse_id) || 'Não especificado'}</span>
                                      </div>
                                    )}
                                    
                                    {evento.responsavel && (
                                      <div>
                                        <span>Responsável: {evento.responsavel}</span>
                                      </div>
                                    )}
                                  </div>
                                  
                                  {evento.descricao && (
                                    <p className="text-sm mt-2 text-gray-600">
                                      {evento.descricao}
                                    </p>
                                  )}
                                </div>
                                
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreVertical className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Ações</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem asChild>
                                      <Link href={`/agenda/evento/${evento.id}`}>Ver detalhes</Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                      <Link href={`/agenda/evento/${evento.id}/editar`}>Editar</Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    {evento.status !== 'concluido' && (
                                      <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'concluido')}>
                                        <CheckCircle2 className="h-4 w-4 mr-2" />
                                        Marcar como concluído
                                      </DropdownMenuItem>
                                    )}
                                    {evento.status !== 'cancelado' && (
                                      <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'cancelado')}>
                                        <AlertCircle className="h-4 w-4 mr-2" />
                                        Marcar como cancelado
                                      </DropdownMenuItem>
                                    )}
                                    {evento.status !== 'pendente' && (
                                      <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'pendente')}>
                                        <Clock className="h-4 w-4 mr-2" />
                                        Marcar como pendente
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem 
                                      className="text-red-600"
                                      onClick={() => handleRemoveEvento(evento.id)}
                                    >
                                      Excluir
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            // Visualização em grade (cards)
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {eventosFiltrados.map(evento => (
                <Card key={evento.id} className="overflow-hidden">
                  <div className={`h-1.5 w-full ${getEventTypeBgColor(evento.tipo)}`}></div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between">
                      <div className="space-y-1">
                        <CardTitle>{evento.titulo}</CardTitle>
                        <CardDescription>
                          {format(new Date(evento.data), "dd/MM/yyyy")} • {evento.horaInicio} - {evento.horaFim}
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Ações</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/agenda/evento/${evento.id}`}>Ver detalhes</Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/agenda/evento/${evento.id}/editar`}>Editar</Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {evento.status !== 'concluido' && (
                            <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'concluido')}>
                              <CheckCircle2 className="h-4 w-4 mr-2" />
                              Marcar como concluído
                            </DropdownMenuItem>
                          )}
                          {evento.status !== 'cancelado' && (
                            <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'cancelado')}>
                              <AlertCircle className="h-4 w-4 mr-2" />
                              Marcar como cancelado
                            </DropdownMenuItem>
                          )}
                          {evento.status !== 'pendente' && (
                            <DropdownMenuItem onClick={() => handleChangeStatus(evento.id, 'pendente')}>
                              <Clock className="h-4 w-4 mr-2" />
                              Marcar como pendente
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => handleRemoveEvento(evento.id)}
                          >
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="outline" className={getPriorityColor(evento.prioridade)}>
                          {evento.prioridade.charAt(0).toUpperCase() + evento.prioridade.slice(1)}
                        </Badge>
                        <Badge variant="outline" className={getStatusColor(evento.status)}>
                          {evento.status.charAt(0).toUpperCase() + evento.status.slice(1)}
                        </Badge>
                        <Badge variant="outline">
                          {evento.tipo.charAt(0).toUpperCase() + evento.tipo.slice(1)}
                        </Badge>
                      </div>
                      
                      {evento.descricao && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {evento.descricao}
                        </p>
                      )}
                      
                      {evento.horse_id && (
                        <div className="flex items-center text-sm">
                          <span className="font-medium">Cavalo:</span>
                          <span className="ml-1">{cavaloNameMap.get(evento.horse_id) || 'Não especificado'}</span>
                        </div>
                      )}
                      
                      {evento.local && (
                        <div className="flex items-center text-sm">
                          <MapPin className="h-3.5 w-3.5 mr-1" />
                          <span>{evento.local}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <div className="w-full flex justify-between">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/agenda/evento/${evento.id}`}>
                          Ver detalhes
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/agenda/evento/${evento.id}/editar`}>
                          Editar
                        </Link>
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default EventosPage;