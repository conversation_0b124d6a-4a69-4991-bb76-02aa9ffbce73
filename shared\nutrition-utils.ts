/**
 * Utilitários para cálculo de dieta - EquiGestor AI
 * Funções para calcular volumoso e concentrado baseado no peso vivo
 */

/** Cálculo de volumoso (kg MS) = peso vivo * pct_MS */
export const calcForageKg = (weightKg: number, pctMS: number): number => {
  return +(weightKg * pctMS).toFixed(2);
};

/** Concentrado máx (kg) = peso vivo / 100 * kg/100kg */
export const calcConcentrateKg = (
  weightKg: number,
  kgPer100: number
): number => {
  return +((weightKg / 100) * kgPer100).toFixed(2);
};

/**
 * Calcula o plano alimentar completo para um cavalo
 * @param weightKg Peso do cavalo em kg
 * @param category Categoria da dieta
 * @param template Template de alimentação
 */
export const calculateFeedPlan = (
  weightKg: number,
  template: {
    forageMnPctMs: number;
    concentrateMaxKgPer100kg: number;
  }
) => {
  const forageKg = calcForageKg(weightKg, template.forageMnPctMs / 100);
  const concentrateKg = calcConcentrateKg(weightKg, template.concentrateMaxKgPer100kg);
  
  return {
    forageKg,
    concentrateKg,
    totalKg: forageKg + concentrateKg
  };
};

/**
 * Valida se uma sobra é significativa (>10%)
 * @param leftoverPct Percentual de sobra
 */
export const isSignificantLeftover = (leftoverPct: number): boolean => {
  return leftoverPct > 10;
};

/**
 * Calcula o custo diário de alimentação
 * @param forageKg Quantidade de volumoso
 * @param concentrateKg Quantidade de concentrado
 * @param forageCostPerKg Custo do volumoso por kg
 * @param concentrateCostPerKg Custo do concentrado por kg
 */
export const calculateDailyCost = (
  forageKg: number,
  concentrateKg: number,
  forageCostPerKg: number,
  concentrateCostPerKg: number
): number => {
  const forageCost = forageKg * forageCostPerKg;
  const concentrateCost = concentrateKg * concentrateCostPerKg;
  return +(forageCost + concentrateCost).toFixed(2);
};

/**
 * Templates padrão de dieta
 */
export const DEFAULT_FEED_TEMPLATES = [
  {
    category: "manutenção",
    forageMnPctMs: 2.0, // 2% do peso vivo em MS
    concentrateMaxKgPer100kg: 0.5, // 0.5kg por 100kg de peso vivo
    notes: "Dieta básica para cavalos em manutenção, sem trabalho intenso"
  },
  {
    category: "atleta_leve",
    forageMnPctMs: 2.2, // 2.2% do peso vivo em MS
    concentrateMaxKgPer100kg: 1.0, // 1kg por 100kg de peso vivo
    notes: "Para cavalos em trabalho leve a moderado"
  },
  {
    category: "gestacao",
    forageMnPctMs: 2.5, // 2.5% do peso vivo em MS
    concentrateMaxKgPer100kg: 1.2, // 1.2kg por 100kg de peso vivo
    notes: "Éguas gestantes - últimos 3 meses de gestação"
  },
  {
    category: "lactacao",
    forageMnPctMs: 3.0, // 3% do peso vivo em MS
    concentrateMaxKgPer100kg: 1.5, // 1.5kg por 100kg de peso vivo
    notes: "Éguas em lactação - primeiros 3 meses"
  },
  {
    category: "potro",
    forageMnPctMs: 3.5, // 3.5% do peso vivo em MS
    concentrateMaxKgPer100kg: 2.0, // 2kg por 100kg de peso vivo
    notes: "Potros em crescimento - 6 a 24 meses"
  }
];