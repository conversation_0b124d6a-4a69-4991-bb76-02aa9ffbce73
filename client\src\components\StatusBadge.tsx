import { Badge } from "@/components/ui/badge";

interface StatusBadgeProps {
  estado: string | null;
}

/**
 * Componente para exibir badges de status coloridos
 * Padroniza a exibição de estados em diferentes módulos
 */
export function StatusBadge({ estado }: StatusBadgeProps) {
  if (!estado) {
    return <Badge variant="outline">Não informado</Badge>;
  }

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status.toLowerCase()) {
      case 'gestante':
      case 'prenhez_confirmada':
      case 'confirmada':
        return 'default';
      case 'em_observacao':
      case 'vazia':
      case 'pendente':
        return 'secondary';
      case 'aborto':
      case 'falha_embriao':
      case 'falha':
        return 'destructive';
      case 'parto_realizado':
      case 'realizado':
      case 'finalizado':
        return 'default';
      default:
        return 'outline';
    }
  };

  const formatStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'gestante': 'Gestante',
      'prenhez_confirmada': 'Prenhez Confirmada',
      'em_observacao': 'Em Observação',
      'vazia': 'Vazia',
      'aborto': 'Aborto',
      'falha_embriao': 'Falha de Embrião',
      'parto_realizado': 'Parto Realizado',
      'pendente': 'Pendente',
      'realizado': 'Realizado',
      'finalizado': 'Finalizado'
    };

    return statusMap[status.toLowerCase()] || status;
  };

  return (
    <Badge variant={getStatusVariant(estado)}>
      {formatStatus(estado)}
    </Badge>
  );
}