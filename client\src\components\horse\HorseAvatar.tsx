/**
 * Avatar simples do cavalo para listagens e visualizações
 */
import React from 'react';
import { User } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface HorseAvatarProps {
  horse_id: number;
  horseName: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function HorseAvatar({ 
  horse_id, 
  horseName, 
  size = 'md', 
  className = '' 
}: HorseAvatarProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };

  const getHorseInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase();
  };

  return (
    <Avatar className={`${sizeClasses[size]} ${className}`}>
      <AvatarImage 
        src={`/api/cavalos/${horse_id}/photo`}
        alt={`Foto de ${horseName}`}
        className="object-cover"
        onError={(e) => {
          // Se não conseguir carregar a imagem, não fazer nada
          // O AvatarFallback será mostrado automaticamente
        }}
      />
      <AvatarFallback className="bg-blue-100 text-blue-700 text-xs font-medium">
        {getHorseInitials(horseName)}
      </AvatarFallback>
    </Avatar>
  );
}

export default HorseAvatar;