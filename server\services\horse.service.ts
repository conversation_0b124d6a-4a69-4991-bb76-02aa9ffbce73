// Horse service with improved architecture
import { BaseService } from './base.service';
import { Horse, ServiceResponse, FilterOptions, PaginationOptions } from '../types';
import { ValidationError, NotFoundError } from '../core/errors';
import { validateRequest } from '../core/validation';
import { z } from 'zod';

const horseCreateSchema = z.object({
  name: z.string().min(1).max(255),
  breed: z.string().max(100).optional(),
  birth_date: z.string().transform(str => new Date(str)).optional(),
  sexo: z.enum(['<PERSON><PERSON>', 'Fêmea', 'Macho (Castrado)', 'Garanhão', 'Égua']).optional(),
  cor: z.string().max(100).optional(),
  pelagem_id: z.number().int().positive().optional(),
  status: z.enum(['ativo', 'inativo', 'vendido', 'falecido', 'nao_informado', 'competicao', 'reproducao']).default('ativo'),
  user_id: z.number().int().positive(),
  notes: z.string().optional(),
  peso: z.number().positive().optional(),
  altura: z.number().positive().optional(),
  data_entrada: z.string().transform(str => new Date(str)).optional(),
  data_saida: z.string().transform(str => new Date(str)).optional(),
  motivo_saida: z.string().optional(),
  numero_registro: z.string().max(50).optional(),
  origem: z.string().max(255).optional(),
  criador: z.string().max(255).optional(),
  proprietario: z.string().max(255).optional(),
  valor_compra: z.string().optional(),
  data_compra: z.string().transform(str => new Date(str)).optional(),
  inspetor: z.string().max(255).optional(),
  is_external: z.boolean().default(false),
  pai_id: z.number().int().positive().optional(),
  mae_id: z.number().int().positive().optional()
});

const horseUpdateSchema = horseCreateSchema.partial().omit({ user_id: true });

export class HorseService extends BaseService<Horse> {
  constructor(db: any) {
    super(db, 'cavalos');
  }

  protected validateCreate(data: any): Horse {
    return validateRequest(horseCreateSchema, data);
  }

  protected validateUpdate(data: any): Partial<Horse> {
    return validateRequest(horseUpdateSchema, data);
  }

  async findByUserId(userId: number, filters: FilterOptions = {}, pagination: PaginationOptions = {}): Promise<ServiceResponse<Horse[]>> {
    console.log(`[CAVALOS] Buscando cavalos do plantel para user_id: ${userId}`);
    
    const fullFilters = { ...filters, userId };
    return await this.findMany(fullFilters, pagination);
  }

  async findActiveHorses(userId: number, pagination: PaginationOptions = {}): Promise<ServiceResponse<Horse[]>> {
    const filters = { userId, status: 'ativo' };
    return await this.findMany(filters, pagination);
  }

  async findInactiveHorses(userId: number, pagination: PaginationOptions = {}): Promise<ServiceResponse<Horse[]>> {
    const filters = { userId, status: 'inativo' };
    return await this.findMany(filters, pagination);
  }

  async updateStatus(id: number, userId: number, newStatus: string, reason?: string): Promise<ServiceResponse<Horse>> {
    const updateData: any = { status: newStatus };
    
    if (newStatus === 'inativo' && reason) {
      updateData.motivo_saida = reason;
      updateData.data_saida = new Date();
    }
    
    if (newStatus === 'ativo') {
      updateData.data_entrada = new Date();
      updateData.motivo_saida = null;
      updateData.data_saida = null;
    }

    return await this.update(id, updateData, userId);
  }

  async getHorseStatistics(userId: number): Promise<ServiceResponse<any>> {
    try {
      const query = `
        SELECT 
          status,
          COUNT(*) as count,
          AVG(peso) as peso_medio,
          AVG(altura) as altura_media
        FROM cavalos 
        WHERE user_id = $1 
        GROUP BY status
      `;

      const result = await this.db.query(query, [userId]);
      
      const statistics = {
        total: 0,
        active: 0,
        inactive: 0,
        sold: 0,
        deceased: 0,
        averageWeight: 0,
        averageHeight: 0
      };

      result.data.forEach((row: any) => {
        statistics.total += parseInt(row.count);
        
        switch (row.status) {
          case 'ativo':
            statistics.active = parseInt(row.count);
            break;
          case 'inativo':
            statistics.inactive = parseInt(row.count);
            break;
          case 'vendido':
            statistics.sold = parseInt(row.count);
            break;
          case 'falecido':
            statistics.deceased = parseInt(row.count);
            break;
        }
        
        if (row.peso_medio) {
          statistics.averageWeight = parseFloat(row.peso_medio);
        }
        if (row.altura_media) {
          statistics.averageHeight = parseFloat(row.altura_media);
        }
      });

      return {
        data: statistics,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STATISTICS_ERROR',
          message: 'Failed to get horse statistics',
          statusCode: 500
        }
      };
    }
  }

  async validateHorseOwnership(horseId: number, userId: number): Promise<boolean> {
    const result = await this.findById(horseId, userId);
    return result.success;
  }

  async searchHorses(userId: number, searchTerm: string, pagination: PaginationOptions = {}): Promise<ServiceResponse<Horse[]>> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      const query = `
        SELECT * FROM cavalos 
        WHERE user_id = $1 
        AND (
          name ILIKE $2 OR 
          breed ILIKE $2 OR 
          numero_registro ILIKE $2 OR
          criador ILIKE $2 OR
          proprietario ILIKE $2
        )
        ORDER BY name ASC
        LIMIT $3 OFFSET $4
      `;

      const searchPattern = `%${searchTerm}%`;
      const result = await this.db.query<Horse>(query, [userId, searchPattern, limit, offset]);

      return {
        data: result.data,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'SEARCH_ERROR',
          message: 'Failed to search horses',
          statusCode: 500
        }
      };
    }
  }

  async getHorseGenealogy(horseId: number, userId: number): Promise<ServiceResponse<any>> {
    try {
      // Validate horse ownership
      const horseResult = await this.findById(horseId, userId);
      if (!horseResult.success) {
        return horseResult as any;
      }

      const horse = horseResult.data;
      const genealogy: any = {
        horse: horse,
        pai: null,
        mae: null,
        avo_paterno: null,
        avo_paterna: null,
        avo_materno: null,
        avo_materna: null
      };

      // Get parents
      if (horse?.pai_id) {
        const paiResult = await this.findById(horse.pai_id);
        if (paiResult.success) {
          genealogy.pai = paiResult.data;
        }
      }

      if (horse?.mae_id) {
        const maeResult = await this.findById(horse.mae_id);
        if (maeResult.success) {
          genealogy.mae = maeResult.data;
        }
      }

      // Get grandparents
      if (genealogy.pai) {
        if (genealogy.pai.pai_id) {
          const avoPaternoResult = await this.findById(genealogy.pai.pai_id);
          if (avoPaternoResult.success) {
            genealogy.avo_paterno = avoPaternoResult.data;
          }
        }
        if (genealogy.pai.mae_id) {
          const avoPaternaResult = await this.findById(genealogy.pai.mae_id);
          if (avoPaternaResult.success) {
            genealogy.avo_paterna = avoPaternaResult.data;
          }
        }
      }

      if (genealogy.mae) {
        if (genealogy.mae.pai_id) {
          const avoMaternoResult = await this.findById(genealogy.mae.pai_id);
          if (avoMaternoResult.success) {
            genealogy.avo_materno = avoMaternoResult.data;
          }
        }
        if (genealogy.mae.mae_id) {
          const avoMaternaResult = await this.findById(genealogy.mae.mae_id);
          if (avoMaternaResult.success) {
            genealogy.avo_materna = avoMaternaResult.data;
          }
        }
      }

      return {
        data: genealogy,
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GENEALOGY_ERROR',
          message: 'Failed to get horse genealogy',
          statusCode: 500
        }
      };
    }
  }
}