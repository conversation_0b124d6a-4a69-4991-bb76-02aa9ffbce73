// Improved logging utility
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  module?: string;
  userId?: number;
  context?: Record<string, any>;
  stack?: string;
}

export class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  log(level: LogLevel, message: string, context?: Record<string, any>): void {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      module: context?.module,
      userId: context?.userId,
      context: context ? { ...context } : undefined,
      stack: context?.error?.stack
    };

    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output
    const logMessage = `[${entry.timestamp}] [${level.toUpperCase()}] ${message}`;
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(logMessage, context?.error || '');
        break;
      case LogLevel.WARN:
        console.warn(logMessage);
        break;
      case LogLevel.INFO:
        console.info(logMessage);
        break;
      case LogLevel.DEBUG:
        console.debug(logMessage);
        break;
    }
  }

  error(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context);
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  getLogs(filters?: {
    level?: LogLevel;
    module?: string;
    userId?: number;
    limit?: number;
  }): LogEntry[] {
    let filteredLogs = this.logs;

    if (filters?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filters.level);
    }

    if (filters?.module) {
      filteredLogs = filteredLogs.filter(log => log.module === filters.module);
    }

    if (filters?.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters?.limit) {
      filteredLogs = filteredLogs.slice(-filters.limit);
    }

    return filteredLogs;
  }

  getStats(): { total: number; byLevel: Record<LogLevel, number> } {
    const stats = {
      total: this.logs.length,
      byLevel: {
        [LogLevel.ERROR]: 0,
        [LogLevel.WARN]: 0,
        [LogLevel.INFO]: 0,
        [LogLevel.DEBUG]: 0
      }
    };

    this.logs.forEach(log => {
      stats.byLevel[log.level]++;
    });

    return stats;
  }

  clear(): void {
    this.logs = [];
  }
}

// Global logger instance
export const logger = new Logger();

// Module-specific logger factory
export const createModuleLogger = (module: string) => {
  return {
    error: (message: string, context?: Record<string, any>) => 
      logger.error(message, { ...context, module }),
    warn: (message: string, context?: Record<string, any>) => 
      logger.warn(message, { ...context, module }),
    info: (message: string, context?: Record<string, any>) => 
      logger.info(message, { ...context, module }),
    debug: (message: string, context?: Record<string, any>) => 
      logger.debug(message, { ...context, module })
  };
};