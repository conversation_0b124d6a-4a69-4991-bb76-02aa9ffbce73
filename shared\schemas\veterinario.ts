import { pgTable, text, serial, integer, timestamp, real, date, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { horses } from './cavalos';
import { 
  observacoesSchema,
  optionalDateSchema,
  currencySchema,
  UserContext 
} from './core';

// ============================================================================
// ENUMS
// ============================================================================

export const tipoConsultaEnum = pgEnum('tipo_consulta', [
  'rotina', 'emergencia', 'vacinacao', 'exame', 'cirurgia', 'acompanhamento'
]);

export const statusConsultaEnum = pgEnum('status_consulta', [
  'agendada', 'em_andamento', 'concluida', 'cancelada'
]);

export const tipoExameEnum = pgEnum('tipo_exame', [
  'sangue', 'urina', 'fezes', 'radiografia', 'ultrassom', 'endoscopia', 'biopsia'
]);

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const consultasVeterinarias = pgTable("consultas_veterinarias", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  user_id: integer("user_id").notNull(),
  
  // Dados da consulta
  tipo: tipoConsultaEnum("tipo").notNull(),
  dataConsulta: timestamp("data_consulta").notNull(),
  veterinario: text("veterinario").notNull(),
  clinica: text("clinica"),
  
  // Motivo e diagnóstico
  motivoConsulta: text("motivo_consulta").notNull(),
  sintomas: text("sintomas"),
  diagnostico: text("diagnostico"),
  prognostico: text("prognostico"),
  
  // Exame físico
  temperatura: real("temperatura"), // °C
  frequenciaCardiaca: integer("frequencia_cardiaca"), // bpm
  frequenciaRespiratoria: integer("frequencia_respiratoria"), // rpm
  peso: real("peso"), // kg
  
  // Tratamento
  tratamento: text("tratamento"),
  medicamentos: text("medicamentos"),
  dosagem: text("dosagem"),
  duracaoTratamento: text("duracao_tratamento"),
  
  // Follow-up
  proximaConsulta: date("proxima_consulta"),
  recomendacoes: text("recomendacoes"),
  restricoes: text("restricoes"),
  
  // Financeiro
  valorConsulta: real("valor_consulta"),
  
  // Status
  status: statusConsultaEnum("status").default('agendada'),
  
  observacoes: text("observacoes"),
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const examesVeterinarios = pgTable("exames_veterinarios", {
  id: serial("id").primaryKey(),
  consultaId: integer("consulta_id"),
  horse_id: integer("horse_id").notNull(),
  
  // Dados do exame
  tipoExame: tipoExameEnum("tipo_exame").notNull(),
  dataExame: date("data_exame").notNull(),
  laboratorio: text("laboratorio"),
  veterinarioSolicitante: text("veterinario_solicitante"),
  
  // Resultados
  resultados: text("resultados"),
  valoresReferencia: text("valores_referencia"),
  interpretacao: text("interpretacao"),
  alteracoes: text("alteracoes"),
  
  // Status
  status: text("status").default('solicitado'), // solicitado, coletado, processando, concluido
  
  // Custos
  valorExame: real("valor_exame"),
  
  observacoes: text("observacoes"),
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const vacinacoes = pgTable("vacinacoes", {
  id: serial("id").primaryKey(),
  horse_id: integer("horse_id").notNull(),
  user_id: integer("user_id").notNull(),
  
  // Dados da vacina
  nomeVacina: text("nome_vacina").notNull(),
  fabricante: text("fabricante"),
  lote: text("lote"),
  dataVencimento: date("data_vencimento"),
  
  // Aplicação
  dataAplicacao: date("data_aplicacao").notNull(),
  veterinario: text("veterinario").notNull(),
  localAplicacao: text("local_aplicacao"),
  dose: text("dose"),
  
  // Reações e acompanhamento
  reacoesAdversas: text("reacoes_adversas"),
  proximaDose: date("proxima_dose"),
  reforcoNecessario: text("reforco_necessario"),
  
  // Certificação
  certificado: text("certificado"),
  
  observacoes: text("observacoes"),
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const consultasVeterinariasRelations = relations(consultasVeterinarias, ({ one, many }) => ({
  horse: one(horses, {
    fields: [consultasVeterinarias.horse_id],
    references: [horses.id],
  }),
  exames: many(examesVeterinarios),
}));

export const examesVeterinariosRelations = relations(examesVeterinarios, ({ one }) => ({
  consulta: one(consultasVeterinarias, {
    fields: [examesVeterinarios.consultaId],
    references: [consultasVeterinarias.id],
  }),
  horse: one(horses, {
    fields: [examesVeterinarios.horse_id],
    references: [horses.id],
  }),
}));

export const vacinacoesRelations = relations(vacinacoes, ({ one }) => ({
  horse: one(horses, {
    fields: [vacinacoes.horse_id],
    references: [horses.id],
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export const consultaVeterinariaSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  user_id: z.number().int().positive(),
  
  tipo: z.enum(['rotina', 'emergencia', 'vacinacao', 'exame', 'cirurgia', 'acompanhamento']),
  dataConsulta: z.string().datetime().or(z.date()),
  veterinario: z.string().min(1, 'Nome do veterinário é obrigatório').max(100),
  clinica: z.string().max(100).optional().nullable(),
  
  motivoConsulta: z.string().min(1, 'Motivo da consulta é obrigatório').max(500),
  sintomas: z.string().max(1000).optional().nullable(),
  diagnostico: z.string().max(1000).optional().nullable(),
  prognostico: z.string().max(500).optional().nullable(),
  
  // Vital signs validation
  temperatura: z.number().min(35).max(45).optional().nullable(), // Normal horse temp: 37.2-38.3°C
  frequenciaCardiaca: z.number().int().min(20).max(100).optional().nullable(), // Normal: 28-44 bpm
  frequenciaRespiratoria: z.number().int().min(5).max(50).optional().nullable(), // Normal: 8-16 rpm
  peso: z.number().positive().max(2000).optional().nullable(),
  
  tratamento: z.string().max(1000).optional().nullable(),
  medicamentos: z.string().max(1000).optional().nullable(),
  dosagem: z.string().max(500).optional().nullable(),
  duracaoTratamento: z.string().max(200).optional().nullable(),
  
  proximaConsulta: optionalDateSchema,
  recomendacoes: z.string().max(1000).optional().nullable(),
  restricoes: z.string().max(500).optional().nullable(),
  
  valorConsulta: currencySchema.optional().nullable(),
  status: z.enum(['agendada', 'em_andamento', 'concluida', 'cancelada']).default('agendada'),
  
  observacoes: observacoesSchema,
  arquivoIds: z.array(z.string()).optional().nullable(),
});

export const exameVeterinarioSchema = z.object({
  id: z.number().int().positive().optional(),
  consultaId: z.number().int().positive().optional().nullable(),
  horse_id: z.number().int().positive(),
  
  tipoExame: z.enum(['sangue', 'urina', 'fezes', 'radiografia', 'ultrassom', 'endoscopia', 'biopsia']),
  dataExame: z.string().datetime().or(z.date()),
  laboratorio: z.string().max(100).optional().nullable(),
  veterinarioSolicitante: z.string().max(100).optional().nullable(),
  
  resultados: z.string().max(2000).optional().nullable(),
  valoresReferencia: z.string().max(1000).optional().nullable(),
  interpretacao: z.string().max(1000).optional().nullable(),
  alteracoes: z.string().max(1000).optional().nullable(),
  
  status: z.enum(['solicitado', 'coletado', 'processando', 'concluido']).default('solicitado'),
  valorExame: currencySchema.optional().nullable(),
  
  observacoes: observacoesSchema,
  arquivoIds: z.array(z.string()).optional().nullable(),
});

export const vacinacaoSchema = z.object({
  id: z.number().int().positive().optional(),
  horse_id: z.number().int().positive(),
  user_id: z.number().int().positive(),
  
  nomeVacina: z.string().min(1, 'Nome da vacina é obrigatório').max(100),
  fabricante: z.string().max(100).optional().nullable(),
  lote: z.string().max(50).optional().nullable(),
  dataVencimento: optionalDateSchema,
  
  dataAplicacao: z.string().datetime().or(z.date()),
  veterinario: z.string().min(1, 'Nome do veterinário é obrigatório').max(100),
  localAplicacao: z.string().max(100).optional().nullable(),
  dose: z.string().max(50).optional().nullable(),
  
  reacoesAdversas: z.string().max(500).optional().nullable(),
  proximaDose: optionalDateSchema,
  reforcoNecessario: z.string().max(200).optional().nullable(),
  
  certificado: z.string().max(100).optional().nullable(),
  observacoes: observacoesSchema,
  arquivoIds: z.array(z.string()).optional().nullable(),
}).refine((data) => {
  // Business rule: data de vencimento deve ser posterior à aplicação
  if (data.dataVencimento && data.dataAplicacao) {
    const vencimento = new Date(data.dataVencimento);
    const aplicacao = new Date(data.dataAplicacao);
    return vencimento >= aplicacao;
  }
  return true;
}, {
  message: "Data de vencimento deve ser posterior à data de aplicação",
  path: ["dataVencimento"]
});

// Insert schemas
export const insertConsultaVeterinariaSchema = createInsertSchema(consultasVeterinarias).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertExameVeterinarioSchema = createInsertSchema(examesVeterinarios).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

export const insertVacinacaoSchema = createInsertSchema(vacinacoes).omit({
  id: true,
  created_at: true,
  updatedAt: true,
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type ConsultaVeterinaria = typeof consultasVeterinarias.$inferSelect;
export type NewConsultaVeterinaria = typeof consultasVeterinarias.$inferInsert;
export type ConsultaVeterinariaData = z.infer<typeof consultaVeterinariaSchema>;

export type ExameVeterinario = typeof examesVeterinarios.$inferSelect;
export type NewExameVeterinario = typeof examesVeterinarios.$inferInsert;
export type ExameVeterinarioData = z.infer<typeof exameVeterinarioSchema>;

export type Vacinacao = typeof vacinacoes.$inferSelect;
export type NewVacinacao = typeof vacinacoes.$inferInsert;
export type VacinacaoData = z.infer<typeof vacinacaoSchema>;

// ============================================================================
// BUSINESS LOGIC
// ============================================================================

/**
 * Validates vital signs are within normal ranges
 */
export function validarSinaisVitais(consulta: ConsultaVeterinariaData): string[] {
  const warnings: string[] = [];
  
  // Temperature validation (normal: 37.2-38.3°C)
  if (consulta.temperatura) {
    if (consulta.temperatura < 37.0) {
      warnings.push("Temperatura abaixo do normal (hipotermia)");
    } else if (consulta.temperatura > 39.0) {
      warnings.push("Temperatura acima do normal (febre)");
    }
  }
  
  // Heart rate validation (normal: 28-44 bpm)
  if (consulta.frequenciaCardiaca) {
    if (consulta.frequenciaCardiaca < 25) {
      warnings.push("Frequência cardíaca baixa (bradicardia)");
    } else if (consulta.frequenciaCardiaca > 50) {
      warnings.push("Frequência cardíaca alta (taquicardia)");
    }
  }
  
  // Respiratory rate validation (normal: 8-16 rpm)
  if (consulta.frequenciaRespiratoria) {
    if (consulta.frequenciaRespiratoria < 6) {
      warnings.push("Frequência respiratória baixa");
    } else if (consulta.frequenciaRespiratoria > 20) {
      warnings.push("Frequência respiratória alta");
    }
  }
  
  return warnings;
}

/**
 * Calculates next vaccination date based on vaccine type
 */
export function calcularProximaVacinacao(nomeVacina: string, dataAplicacao: string): string | null {
  const data = new Date(dataAplicacao);
  
  // Standard vaccination intervals for horses
  const intervalos: Record<string, number> = {
    'Tétano': 365, // Annual
    'Influenza': 180, // Bi-annual
    'Rinoneumonite': 180, // Bi-annual
    'Encefalomielite': 365, // Annual
    'Raiva': 365, // Annual
    'Antraz': 365, // Annual
  };
  
  // Find matching vaccine (case insensitive partial match)
  const vacina = Object.keys(intervalos).find(v => 
    nomeVacina.toLowerCase().includes(v.toLowerCase())
  );
  
  if (vacina) {
    data.setDate(data.getDate() + intervalos[vacina]);
    return data.toISOString().split('T')[0];
  }
  
  return null; // Unknown vaccine type
}

/**
 * Determines examination urgency based on type and symptoms
 */
export function determinarUrgenciaExame(
  tipoExame: string, 
  sintomas?: string
): 'baixa' | 'media' | 'alta' | 'urgente' {
  const urgencias: Record<string, 'baixa' | 'media' | 'alta'> = {
    'sangue': 'media',
    'urina': 'baixa',
    'fezes': 'baixa',
    'radiografia': 'alta',
    'ultrassom': 'media',
    'endoscopia': 'alta',
    'biopsia': 'alta'
  };
  
  // Check for emergency keywords in symptoms
  if (sintomas) {
    const emergencyKeywords = [
      'cólica', 'dor intensa', 'sangramento', 'convulsão', 
      'dificuldade respiratória', 'paralisia', 'trauma'
    ];
    
    const hasEmergencySymptom = emergencyKeywords.some(keyword =>
      sintomas.toLowerCase().includes(keyword)
    );
    
    if (hasEmergencySymptom) {
      return 'urgente';
    }
  }
  
  return urgencias[tipoExame] || 'media';
}