// Main routes aggregator with improved architecture
import { Router } from 'express';
import { errorHandler } from '../core/errors';
import horseRoutes from './horse.routes';
import userRoutes from './user.routes';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'EquiGestor AI API is running',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// System status endpoint
router.get('/system-status', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'online',
      database: 'connected',
      services: [
        { name: 'horse-service', status: 'running' },
        { name: 'user-service', status: 'running' },
        { name: 'auth-service', status: 'running' }
      ]
    }
  });
});

// API routes
router.use('/cavalos', horseRoutes);
router.use('/users', userRoutes);

// Legacy auth routes for backward compatibility
router.use('/auth', userRoutes);

// Error handling middleware
router.use(errorHandler);

export default router;