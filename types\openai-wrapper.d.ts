declare module 'openai-wrapper' {
  export interface OpenAIOptions {
    apiKey: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
  }

  export interface CompletionRequest {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  }

  export interface CompletionResponse {
    text: string;
    usage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
  }

  export class OpenAIWrapper {
    constructor(options: OpenAIOptions);
    createCompletion(request: CompletionRequest): Promise<CompletionResponse>;
    createJsonCompletion(request: CompletionRequest): Promise<any>;
  }
}