import { CreditCard, Wallet, Building } from 'lucide-react';
import type { JSX } from 'react';

export function formatMoney(value: number): string {
  return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
}

export function formatDate(dateString: string | null, emptyLabel: string = ''): string {
  if (!dateString) return emptyLabel;
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? emptyLabel : date.toLocaleDateString('pt-BR');
}

export function getContaIcon(tipo: string): JSX.Element {
  const normalized = tipo.toLowerCase();
  if (['conta corrente', 'poupança', 'cartão'].includes(normalized)) {
    return <CreditCard className="h-4 w-4" />;
  }
  if (normalized === 'investimento') {
    return <Building className="h-4 w-4" />;
  }
  return <Wallet className="h-4 w-4" />;
}
