import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { LayoutWrapper } from '@/components/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Eye, Edit, Download } from 'lucide-react';
import { Reproducao, Cavalo } from '@shared/schema';

/**
 * Página de Controle de Coleta de Sêmen
 * Exibe e gerencia os registros de coletas de sêmen e armazenamento
 */
export default function ColetaSemenPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPadreador, setFilterPadreador] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  // Buscar dados de reproduções
  const { data: reproducoes = [], isLoading: loadingReproducoes } = useQuery<Reproducao[]>({
    queryKey: ['/api/reproducao'],
  });

  // Buscar dados de cavalos para o filtro
  const { data: cavalos = [], isLoading: loadingCavalos } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
  });

  // Filtrar apenas coletas de sêmen
  const coletas = reproducoes
    .filter(rep => 
      rep.tipoEvento?.toLowerCase().includes('coleta') || 
      rep.observacoes?.toLowerCase().includes('coleta de sêmen') ||
      rep.observacoes?.toLowerCase().includes('coleta de semen')
    )
    .filter(rep => 
      (searchTerm === '' || 
        rep.observacoes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        getCavaloName(rep.padreiroId).toLowerCase().includes(searchTerm.toLowerCase())
      ) &&
      (filterPadreador === '' || rep.padreiroId.toString() === filterPadreador) &&
      (filterStatus === '' || getStatusColeta(rep) === filterStatus)
    );

  // Obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    const cavalo = cavalos.find(c => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };

  // Função para determinar o status da coleta de sêmen
  function getStatusColeta(coleta: Reproducao): string {
    if (coleta.status === 'utilizando') return 'em_uso';
    if (coleta.status === 'congelado') return 'armazenado';
    if (coleta.status === 'descartado') return 'descartado';
    
    // Status padrão
    return 'disponível';
  }

  // Função para calcular a quantidade de paletas/palhetas
  function getQuantidadePalhetas(coleta: Reproducao): number {
    // Tenta extrair das observações
    const match = coleta.observacoes?.match(/(\d+)\s*(?:palh?etas|doses)/i);
    if (match) {
      return parseInt(match[1]);
    }
    
    // Valor padrão estimado
    return 0;
  }

  // Filtrar apenas garanhões (machos reprodutores)
  const garanhoes = cavalos.filter(cavalo => 
    cavalo.sexo?.toLowerCase() === 'macho' || 
    cavalo.sexo?.toLowerCase() === 'm'
  );

  return (
    <LayoutWrapper pageTitle="Controle de Coleta de Sêmen" showBackButton backUrl="/reproducao">
      <div className="container mx-auto py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filtros</CardTitle>
            <CardDescription>Filtre as coletas por garanhão, status ou data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Buscar</label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar em coletas..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Garanhão</label>
                <Select value={filterPadreador} onValueChange={setFilterPadreador}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um garanhão" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    {garanhoes.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="armazenado">Armazenado</SelectItem>
                    <SelectItem value="em_uso">Em Uso</SelectItem>
                    <SelectItem value="disponível">Disponível</SelectItem>
                    <SelectItem value="descartado">Descartado</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Coletas de Sêmen</h2>
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Nova Coleta
          </Button>
        </div>

        {loadingReproducoes ? (
          <div className="text-center py-10">Carregando coletas...</div>
        ) : coletas.length > 0 ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Garanhão</TableHead>
                  <TableHead>Método</TableHead>
                  <TableHead>Palhetas</TableHead>
                  <TableHead>Qualidade</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {coletas.map((coleta) => {
                  const status = getStatusColeta(coleta);
                  const palhetas = getQuantidadePalhetas(coleta);
                  return (
                    <TableRow key={coleta.id}>
                      <TableCell>{formato.data(coleta.dataCobertura || coleta.dataEvento || '')}</TableCell>
                      <TableCell>{getCavaloName(coleta.padreiroId)}</TableCell>
                      <TableCell>{getMetodoColeta(coleta)}</TableCell>
                      <TableCell>{palhetas > 0 ? palhetas : 'N/A'}</TableCell>
                      <TableCell>{getQualidadeSemen(coleta)}</TableCell>
                      <TableCell>
                        <StatusBadge status={status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Download className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-muted-foreground">Nenhuma coleta de sêmen encontrada.</p>
            <Button variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> Registrar Primeira Coleta
            </Button>
          </div>
        )}

        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Inventário de Sêmen</CardTitle>
              <CardDescription>Visão geral do sêmen armazenado por garanhão</CardDescription>
            </CardHeader>
            <CardContent>
              {garanhoes.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Garanhão</TableHead>
                      <TableHead>Palhetas disponíveis</TableHead>
                      <TableHead>Última coleta</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {garanhoes
                      .filter(garanhao => {
                        // Garanhões que têm pelo menos uma coleta
                        return coletas.some(c => c.padreiroId === garanhao.id);
                      })
                      .map((garanhao) => {
                        // Calcular total de palhetas disponíveis
                        const palhetasDisponiveis = coletas
                          .filter(c => 
                            c.padreiroId === garanhao.id && 
                            getStatusColeta(c) !== 'descartado' &&
                            getStatusColeta(c) !== 'em_uso'
                          )
                          .reduce((total, c) => total + getQuantidadePalhetas(c), 0);
                          
                        // Obter data da última coleta
                        const coletasGaranhao = coletas.filter(c => c.padreiroId === garanhao.id);
                        const ultimaColeta = coletasGaranhao.length > 0 
                          ? coletasGaranhao.reduce((ultima, atual) => {
                              const dataAtual = new Date(atual.dataCobertura || atual.dataEvento || '');
                              const dataUltima = new Date(ultima.dataCobertura || ultima.dataEvento || '');
                              return dataAtual > dataUltima ? atual : ultima;
                            })
                          : null;
                          
                        return (
                          <TableRow key={garanhao.id}>
                            <TableCell className="font-medium">{garanhao.name}</TableCell>
                            <TableCell>{palhetasDisponiveis}</TableCell>
                            <TableCell>
                              {ultimaColeta 
                                ? formato.data(ultimaColeta.dataCobertura || ultimaColeta.dataEvento || '') 
                                : 'N/A'}
                            </TableCell>
                            <TableCell>
                              {palhetasDisponiveis > 0 
                                ? <Badge>Disponível</Badge> 
                                : <Badge variant="outline">Sem estoque</Badge>}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-center py-4 text-muted-foreground">
                  Nenhum garanhão com coletas de sêmen registradas
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </LayoutWrapper>
  );
}

// Componente para mostrar o status com cor apropriada
function StatusBadge({ status }: { status: string }) {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let label = status.charAt(0).toUpperCase() + status.slice(1);
  
  switch (status) {
    case 'armazenado':
      variant = "default";
      break;
    case 'em_uso':
      variant = "secondary";
      break;
    case 'disponível':
      variant = "default";
      break;
    case 'descartado':
      variant = "destructive";
      break;
  }
  
  return <Badge variant={variant}>{label}</Badge>;
}

// Funções auxiliares
const formato = {
  data: (dataString: string) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  }
};

// Extrair método de coleta das observações
function getMetodoColeta(coleta: Reproducao): string {
  if (!coleta.observacoes) return 'Não especificado';
  
  if (coleta.observacoes.toLowerCase().includes('vagina artificial')) {
    return 'Vagina Artificial';
  } else if (coleta.observacoes.toLowerCase().includes('eletroejaculador')) {
    return 'Eletroejaculador';
  } else if (coleta.observacoes.toLowerCase().includes('manual')) {
    return 'Manual';
  }
  
  return 'Não especificado';
}

// Extrair qualidade do sêmen das observações
function getQualidadeSemen(coleta: Reproducao): string {
  if (!coleta.observacoes) return 'Não avaliado';
  
  // Procurar por expressões comuns de qualidade
  if (coleta.observacoes.toLowerCase().includes('excelente')) {
    return 'Excelente';
  } else if (coleta.observacoes.toLowerCase().includes('boa')) {
    return 'Boa';
  } else if (coleta.observacoes.toLowerCase().includes('regular')) {
    return 'Regular';
  } else if (coleta.observacoes.toLowerCase().includes('ruim')) {
    return 'Ruim';
  }
  
  return 'Não avaliado';
}