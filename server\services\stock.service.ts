/**
 * Serviço de Estoque - EquiGestor AI
 * Gerencia estoque de alimentos, calcula dias restantes e gera alertas
 */

import { db } from "../db";
import { 
  stockBatches, 
  stockAlerts, 
  feedPlanItems,
  type StockBatch, 
  type StockAlert,
  type InsertStockAlert 
} from "@shared/schema";
import { eq, and, sql, lt, gte } from "drizzle-orm";

export class StockService {
  /**
   * Calcula o consumo diário previsto para um item
   */
  async calculateDailyConsumption(user_id: number, item: string): Promise<number> {
    try {
      // Buscar consumo dos últimos 7 dias
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const consumption = await db.select({
        totalForage: sql<number>`SUM(${feedPlanItems.forageKg})`,
        totalConcentrate: sql<number>`SUM(${feedPlanItems.concentrateKg})`
      })
      .from(feedPlanItems)
      .where(and(
        eq(feedPlanItems.user_id, user_id),
        gte(feedPlanItems.date, sevenDaysAgo.toISOString().split('T')[0])
      ));

      const result = consumption[0];
      
      // Assumir que volumoso = feno/pasto, concentrado = ração
      if (item.toLowerCase().includes('feno') || item.toLowerCase().includes('volumoso')) {
        return (result?.totalForage || 0) / 7; // Média diária
      } else if (item.toLowerCase().includes('ração') || item.toLowerCase().includes('concentrado')) {
        return (result?.totalConcentrate || 0) / 7; // Média diária
      }
      
      return 0;
    } catch (error) {
      console.error("Erro ao calcular consumo diário:", error);
      return 0;
    }
  }

  /**
   * Calcula dias restantes de estoque para um item
   */
  async calculateDaysRemaining(user_id: number, item: string): Promise<number> {
    try {
      // Buscar estoque total do item
      const stock = await db.select({
        totalQuantity: sql<number>`SUM(${stockBatches.quantityKg})`
      })
      .from(stockBatches)
      .where(and(
        eq(stockBatches.user_id, user_id),
        eq(stockBatches.item, item),
        gte(stockBatches.quantityKg, 0.1) // Apenas lotes com estoque
      ));

      const totalStock = stock[0]?.totalQuantity || 0;
      if (totalStock <= 0) return 0;

      // Calcular consumo diário
      const dailyConsumption = await this.calculateDailyConsumption(user_id, item);
      if (dailyConsumption <= 0) return 999; // Sem consumo = estoque "infinito"

      return Math.floor(totalStock / dailyConsumption);
    } catch (error) {
      console.error("Erro ao calcular dias restantes:", error);
      return 0;
    }
  }

  /**
   * Consome estoque baseado no plano do dia anterior
   */
  async consumeStockFromPlans(user_id: number, date: string): Promise<void> {
    try {
      // Buscar planos concluídos do dia
      const completedPlans = await db.select()
        .from(feedPlanItems)
        .where(and(
          eq(feedPlanItems.user_id, user_id),
          eq(feedPlanItems.date, date),
          eq(feedPlanItems.status, "done")
        ));

      for (const plan of completedPlans) {
        // Consumir volumoso (feno)
        await this.consumeFromOldestBatch(user_id, "Feno de Tifton", plan.forageKg);
        
        // Consumir concentrado (ração)
        await this.consumeFromOldestBatch(user_id, "Ração Concentrada", plan.concentrateKg);
      }
    } catch (error) {
      console.error("Erro ao consumir estoque:", error);
      throw error;
    }
  }

  /**
   * Consome quantidade de um item do lote mais antigo
   */
  private async consumeFromOldestBatch(user_id: number, item: string, quantity: number): Promise<void> {
    try {
      // Buscar lotes do item ordenados por data de criação (FIFO)
      const batches = await db.select()
        .from(stockBatches)
        .where(and(
          eq(stockBatches.user_id, user_id),
          eq(stockBatches.item, item),
          gte(stockBatches.quantityKg, 0.1)
        ))
        .orderBy(stockBatches.created_at);

      let remainingToConsume = quantity;

      for (const batch of batches) {
        if (remainingToConsume <= 0) break;

        const consumeFromBatch = Math.min(batch.quantityKg, remainingToConsume);
        const newQuantity = batch.quantityKg - consumeFromBatch;

        await db.update(stockBatches)
          .set({ quantityKg: newQuantity })
          .where(eq(stockBatches.id, batch.id));

        remainingToConsume -= consumeFromBatch;
      }
    } catch (error) {
      console.error("Erro ao consumir do lote:", error);
    }
  }

  /**
   * Verifica e cria alertas de estoque baixo e produtos vencendo
   */
  async checkAndCreateAlerts(user_id: number): Promise<StockAlert[]> {
    try {
      // Limpar alertas antigos não resolvidos
      await db.update(stockAlerts)
        .set({ resolved: true })
        .where(eq(stockAlerts.user_id, user_id));

      const alerts: InsertStockAlert[] = [];

      // Buscar todos os itens únicos do usuário
      const items = await db.select({ item: stockBatches.item })
        .from(stockBatches)
        .where(eq(stockBatches.user_id, user_id))
        .groupBy(stockBatches.item);

      // Verificar estoque baixo para cada item
      for (const { item } of items) {
        const daysRemaining = await this.calculateDaysRemaining(user_id, item);
        
        if (daysRemaining < 7 && daysRemaining > 0) {
          alerts.push({
            item,
            alertType: "low_stock",
            message: `Estoque baixo: ${item} - ${daysRemaining} dias restantes`,
            daysRemaining,
            priority: daysRemaining < 3 ? "high" : "medium",
            resolved: false,
            user_id
          });
        } else if (daysRemaining === 0) {
          alerts.push({
            item,
            alertType: "low_stock",
            message: `Estoque esgotado: ${item}`,
            daysRemaining: 0,
            priority: "high",
            resolved: false,
            user_id
          });
        }
      }

      // Verificar produtos vencendo
      const today = new Date();
      const weekFromNow = new Date();
      weekFromNow.setDate(today.getDate() + 7);

      const expiringBatches = await db.select()
        .from(stockBatches)
        .where(and(
          eq(stockBatches.user_id, user_id),
          lt(stockBatches.expiry, weekFromNow.toISOString().split('T')[0]),
          gte(stockBatches.quantityKg, 0.1)
        ));

      for (const batch of expiringBatches) {
        const expiry = new Date(batch.expiry!);
        const daysToExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysToExpiry < 0) {
          alerts.push({
            item: batch.item,
            alertType: "expired",
            message: `Produto vencido: ${batch.item} - Lote ${batch.batchNumber || 'N/A'}`,
            daysRemaining: daysToExpiry,
            priority: "high",
            resolved: false,
            user_id
          });
        } else {
          alerts.push({
            item: batch.item,
            alertType: "expiring",
            message: `Produto vencendo: ${batch.item} - ${daysToExpiry} dias para vencer`,
            daysRemaining: daysToExpiry,
            priority: daysToExpiry < 3 ? "high" : "medium",
            resolved: false,
            user_id
          });
        }
      }

      // Inserir novos alertas
      if (alerts.length > 0) {
        const result = await db.insert(stockAlerts)
          .values(alerts)
          .returning();
        return result;
      }

      return [];
    } catch (error) {
      console.error("Erro ao verificar alertas:", error);
      throw error;
    }
  }

  /**
   * Busca alertas ativos de estoque
   */
  async getActiveAlerts(user_id: number): Promise<StockAlert[]> {
    try {
      return await db.select()
        .from(stockAlerts)
        .where(and(
          eq(stockAlerts.user_id, user_id),
          eq(stockAlerts.resolved, false)
        ))
        .orderBy(sql`
          CASE ${stockAlerts.priority}
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'low' THEN 3
          END
        `);
    } catch (error) {
      console.error("Erro ao buscar alertas:", error);
      throw error;
    }
  }

  /**
   * Adiciona novo lote ao estoque
   */
  async addStockBatch(batch: any): Promise<StockBatch> {
    try {
      const result = await db.insert(stockBatches)
        .values(batch)
        .returning();
      return result[0];
    } catch (error) {
      console.error("Erro ao adicionar lote:", error);
      throw error;
    }
  }

  /**
   * Busca todo o estoque de um usuário
   */
  async getUserStock(user_id: number): Promise<StockBatch[]> {
    try {
      return await db.select()
        .from(stockBatches)
        .where(and(
          eq(stockBatches.user_id, user_id),
          gte(stockBatches.quantityKg, 0.1)
        ))
        .orderBy(stockBatches.item, stockBatches.created_at);
    } catch (error) {
      console.error("Erro ao buscar estoque:", error);
      throw error;
    }
  }

  /**
   * Calcula custo médio por animal por dia
   */
  async calculateAverageCostPerAnimal(user_id: number, days: number = 30): Promise<number> {
    try {
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      // Buscar consumo total no período
      const consumption = await db.select({
        totalForage: sql<number>`SUM(${feedPlanItems.forageKg})`,
        totalConcentrate: sql<number>`SUM(${feedPlanItems.concentrateKg})`,
        uniqueAnimals: sql<number>`COUNT(DISTINCT ${feedPlanItems.animalId})`
      })
      .from(feedPlanItems)
      .where(and(
        eq(feedPlanItems.user_id, user_id),
        gte(feedPlanItems.date, fromDate.toISOString().split('T')[0])
      ));

      const result = consumption[0];
      if (!result || !result.uniqueAnimals) return 0;

      // Calcular custo médio baseado nos lotes de estoque
      const avgForageCost = await this.getAverageItemCost(user_id, "Feno de Tifton");
      const avgConcentrateCost = await this.getAverageItemCost(user_id, "Ração Concentrada");

      const totalForageCost = (result.totalForage || 0) * avgForageCost;
      const totalConcentrateCost = (result.totalConcentrate || 0) * avgConcentrateCost;
      const totalCost = totalForageCost + totalConcentrateCost;

      const totalAnimalDays = result.uniqueAnimals * days;
      return totalAnimalDays > 0 ? totalCost / totalAnimalDays : 0;
    } catch (error) {
      console.error("Erro ao calcular custo médio por animal:", error);
      return 0;
    }
  }

  /**
   * Calcula custo médio de um item baseado nos lotes em estoque
   */
  private async getAverageItemCost(user_id: number, item: string): Promise<number> {
    try {
      const avgCost = await db.select({
        avgCost: sql<number>`AVG(${stockBatches.unitCost})`
      })
      .from(stockBatches)
      .where(and(
        eq(stockBatches.user_id, user_id),
        eq(stockBatches.item, item),
        gte(stockBatches.quantityKg, 0.1)
      ));

      return avgCost[0]?.avgCost || 0;
    } catch (error) {
      console.error("Erro ao calcular custo médio do item:", error);
      return 0;
    }
  }
}

export const stockService = new StockService();