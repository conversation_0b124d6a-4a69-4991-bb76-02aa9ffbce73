import React from 'react';
import { <PERSON> } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutWrapper } from '@/components/Layout';
import {
  Stethoscope,
  Clipboard,
  Syringe,
  FileCheck,
  ArrowRightCircle,
  Pill,
  ShoppingBag,
  Image
} from 'lucide-react';

/**
 * Página principal do módulo Veterinário
 * Apresenta cards para acesso aos submódulos veterinários
 */
export default function VeterinarioPage() {
  // Array com os dados de cada card
  const modulos = [
    {
      id: 'procedimentos',
      titulo: 'Procedimentos',
      descricao: 'Registros de procedimentos veterinários',
      href: '/procedimentos-vet',
      icon: <Stethoscope className="h-6 w-6 text-blue-600" />
    },
    {
      id: 'registros',
      titulo: 'Registros Clínicos',
      descricao: 'Histórico clínico dos animais',
      href: '/veterinario/registros',
      icon: <Clipboard className="h-6 w-6 text-blue-600" />
    },
    {
      id: 'vacinacoes',
      titulo: 'Vacinações',
      descricao: 'Controle de vacinas aplicadas',
      href: '/veterinario/vacinacoes',
      icon: <Syringe className="h-6 w-6 text-blue-600" />
    },
    {
      id: 'vermifugacoes',
      titulo: 'Vermifugações',
      descricao: 'Controle de vermífugos aplicados',
      href: '/veterinario/vermifugacoes',
      icon: <Pill className="h-6 w-6 text-blue-600" />
    },
    {
      id: 'exames',
      titulo: 'Exames Laboratoriais',
      descricao: 'Resultados de exames realizados',
      href: '/veterinario/exames',
      icon: <FileCheck className="h-6 w-6 text-blue-600" />
    },
    {
      id: 'imagens',
      titulo: 'Galeria de Imagens',
      descricao: 'Imagens de ultrassom, raio-x, etc',
      href: '/veterinario/imagens',
      icon: <Image className="h-6 w-6 text-blue-600" />
    },
  ];

  return (
    <LayoutWrapper pageTitle="Veterinário">
      <div className="container mx-auto py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {modulos.map((modulo) => (
            <Link key={modulo.id} href={modulo.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                <CardHeader className="flex flex-row items-center gap-4">
                  {modulo.icon}
                  <div>
                    <CardTitle>{modulo.titulo}</CardTitle>
                    <CardDescription>{modulo.descricao}</CardDescription>
                  </div>
                </CardHeader>
                <CardFooter className="pt-2">
                  <Button variant="ghost" className="ml-auto">
                    Acessar <ArrowRightCircle className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </LayoutWrapper>
  );
}