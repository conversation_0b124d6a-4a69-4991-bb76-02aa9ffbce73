/**
 * Módulo para validação de senhas
 * Implementa políticas de segurança para senhas de usuários
 */

// Objeto com as regras de validação e suas mensagens de erro
export const regrasValidacao = {
  comprimentoMinimo: {
    validacao: (senha: string) => senha.length >= 8,
    mensagem: "A senha deve ter pelo menos 8 caracteres."
  },
  contemLetraMinuscula: {
    validacao: (senha: string) => /[a-z]/.test(senha),
    mensagem: "A senha deve conter pelo menos uma letra minúscula."
  },
  contemLetraMaiuscula: {
    validacao: (senha: string) => /[A-Z]/.test(senha),
    mensagem: "A senha deve conter pelo menos uma letra maiúscula."
  },
  contemNumero: {
    validacao: (senha: string) => /[0-9]/.test(senha),
    mensagem: "A senha deve conter pelo menos um número."
  },
  contemCaracterEspecial: {
    validacao: (senha: string) => /[^A-Za-z0-9]/.test(senha),
    mensagem: "A senha deve conter pelo menos um caractere especial (por exemplo: !@#$%^&*)."
  },
  semSequenciasComuns: {
    validacao: (senha: string) => {
      const sequenciasComuns = [
        '123456', 'abcdef', 'qwerty', 'password', 'admin', 'senha', '111111'
      ];
      
      // Converte para minúsculo para checar
      const senhaLower = senha.toLowerCase();
      
      // Verifica se a senha contém alguma das sequências comuns
      return !sequenciasComuns.some(seq => senhaLower.includes(seq));
    },
    mensagem: "A senha contém uma sequência de caracteres muito comum e fácil de adivinhar."
  },
  semRepeticoesExcessivas: {
    validacao: (senha: string) => {
      // Verificar caracteres repetidos mais de 3 vezes seguidas
      return !/(.)\1{2,}/.test(senha);
    },
    mensagem: "A senha contém caracteres repetidos em excesso."
  }
};

/**
 * Verifica se uma senha atende aos critérios de segurança
 * @param senha A senha a ser validada
 * @returns Um objeto com o resultado da validação e erros, se houver
 */
export function validarSenha(senha: string): { valida: boolean; erros: string[] } {
  const erros: string[] = [];
  
  // Aplicar cada regra de validação
  for (const [nome, regra] of Object.entries(regrasValidacao)) {
    if (!regra.validacao(senha)) {
      erros.push(regra.mensagem);
    }
  }
  
  return {
    valida: erros.length === 0,
    erros
  };
}

/**
 * Calcula o nível de força da senha (0-100)
 * @param senha A senha a ser avaliada
 * @returns Um número de 0 a 100 representando a força da senha
 */
export function calcularForcaSenha(senha: string): number {
  if (!senha) return 0;
  
  let pontuacao = 0;
  
  // Comprimento contribui com até 25 pontos
  pontuacao += Math.min(25, Math.floor(senha.length * 2.5));
  
  // Variedade de caracteres (até 35 pontos)
  if (/[a-z]/.test(senha)) pontuacao += 10;
  if (/[A-Z]/.test(senha)) pontuacao += 10;
  if (/[0-9]/.test(senha)) pontuacao += 10;
  if (/[^A-Za-z0-9]/.test(senha)) pontuacao += 5;
  
  // Distribuição de tipos diferentes (até 15 pontos)
  const tiposPresentes = [
    /[a-z]/.test(senha),
    /[A-Z]/.test(senha),
    /[0-9]/.test(senha),
    /[^A-Za-z0-9]/.test(senha)
  ].filter(Boolean).length;
  
  pontuacao += tiposPresentes * 5;
  
  // Bônus de posicionamento (até 15 pontos)
  // Intercalar tipos de caracteres é mais seguro
  let alternancias = 0;
  let ultimoTipo = getCharTipo(senha[0]);
  
  for (let i = 1; i < senha.length; i++) {
    const tipoAtual = getCharTipo(senha[i]);
    if (tipoAtual !== ultimoTipo) {
      alternancias++;
      ultimoTipo = tipoAtual;
    }
  }
  
  pontuacao += Math.min(15, alternancias * 2);
  
  // Penalidade para sequências comuns (até -25 pontos)
  const sequenciasComuns = [
    '123', 'abc', 'qwe', 'asd', 'zxc', 'pass', 'senha', 'admin'
  ];
  
  const senhaLower = senha.toLowerCase();
  
  for (const seq of sequenciasComuns) {
    if (senhaLower.includes(seq)) {
      pontuacao -= Math.min(25, 5 + seq.length * 2);
      break;
    }
  }
  
  // Penalidade para repetições (até -15 pontos)
  const repetitionMatch = senha.match(/(.)\1{2,}/);
  if (repetitionMatch) {
    pontuacao -= Math.min(15, repetitionMatch[0].length * 3);
  }
  
  // Garantir limites entre 0 e 100
  return Math.max(0, Math.min(100, pontuacao));
}

/**
 * Retorna uma classificação da força da senha
 * @param pontuacao A pontuação da senha (0-100)
 * @returns Uma string representando a classificação
 */
export function classificarForcaSenha(pontuacao: number): string {
  if (pontuacao < 30) return "Muito fraca";
  if (pontuacao < 50) return "Fraca";
  if (pontuacao < 70) return "Média";
  if (pontuacao < 90) return "Forte";
  return "Muito forte";
}

/**
 * Identifica o tipo de um caractere
 * @param char O caractere a ser analisado
 * @returns Um código representando o tipo do caractere
 */
function getCharTipo(char: string): number {
  if (/[a-z]/.test(char)) return 1; // minúscula
  if (/[A-Z]/.test(char)) return 2; // maiúscula
  if (/[0-9]/.test(char)) return 3; // número
  return 4; // caractere especial
}