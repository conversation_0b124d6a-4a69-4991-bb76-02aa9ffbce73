/**
 * Componente wrapper para depuração de componentes React
 * Captura erros em componentes individuais e os registra no sistema de log
 */

import React, { useEffect, useState } from 'react';
import { logger, LogLevel } from '@/lib/logger';

interface DebugWrapperProps {
  componentName: string;
  module?: string;
  children: React.ReactNode;
  logProps?: boolean;
  logLifecycle?: boolean;
  logRenders?: boolean;
}

interface DebugErrorBoundaryProps {
  componentName: string;
  module: string;
  logRenders?: boolean;
  children: React.ReactNode;
}

interface DebugErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * Boundary para capturar erros em componentes
 */
class DebugErrorBoundary extends React.Component<DebugErrorBoundaryProps, DebugErrorBoundaryState> {
  constructor(props: DebugErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  // Captura erros no componente
  static override getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  // Registra o erro capturado
  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error(
      this.props.module,
      `Erro no componente ${this.props.componentName}`,
      { errorInfo: errorInfo.componentStack },
      error
    );
  }

  // Registra montagem do componente
  override componentDidMount() {
    if (this.props.logRenders) {
      logger.debug(this.props.module, `Componente ${this.props.componentName} montado`);
    }
  }

  // Registra atualização do componente
  override componentDidUpdate(prevProps: DebugErrorBoundaryProps) {
    if (this.props.logRenders && prevProps.children !== this.props.children) {
      logger.debug(this.props.module, `Componente ${this.props.componentName} atualizado`);
    }
  }

  // Registra desmontagem do componente
  override componentWillUnmount() {
    if (this.props.logRenders) {
      logger.debug(this.props.module, `Componente ${this.props.componentName} desmontado`);
    }
  }

  override render() {
    if (this.state.hasError) {
      // Falback visual para erro
      return (
        <div className="p-4 border border-red-500 bg-red-50 rounded-md">
          <h3 className="text-lg font-semibold text-red-700">
            Erro no componente: {this.props.componentName}
          </h3>
          <p className="text-sm text-red-600 mt-1">
            {this.state.error?.message || 'Erro desconhecido'}
          </p>
          <p className="text-xs text-gray-500 mt-2">
            Este erro foi registrado e será investigado.
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Componente wrapper de depuração funcional
 */
export function DebugWrapper({
  componentName,
  module = 'ui',
  children,
  logProps = false,
  logLifecycle = false,
  logRenders = false,
}: DebugWrapperProps) {
  const [instanceId] = useState(() => Math.random().toString(36).substring(2, 8));
  const fullComponentName = `${componentName}_${instanceId}`;

  // Efeito para capturar montagem/desmontagem
  useEffect(() => {
    if (logLifecycle) {
      logger.debug(module, `Componente ${fullComponentName} montado`);
      
      return () => {
        logger.debug(module, `Componente ${fullComponentName} desmontado`);
      };
    }
  }, [fullComponentName, module, logLifecycle]);

  // Efeito para capturar renderizações
  useEffect(() => {
    if (logRenders) {
      logger.debug(module, `Componente ${fullComponentName} renderizado`);
    }
  });

  // Efeito para analisar props
  useEffect(() => {
    if (logProps && React.isValidElement(children)) {
      const props = children.props;
      logger.debug(module, `Props do componente ${fullComponentName}`, props);
    }
  }, [children, fullComponentName, module, logProps]);

  return (
    <DebugErrorBoundary
      componentName={fullComponentName}
      module={module}
      logRenders={logRenders}
    >
      {children}
    </DebugErrorBoundary>
  );
}

/**
 * HOC para envolver componentes com o DebugWrapper
 * React.memo para compatibilidade com Fast Refresh
 */
export function withDebug<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    module?: string;
    componentName?: string;
    logProps?: boolean;
    logLifecycle?: boolean;
    logRenders?: boolean;
  } = {}
) {
  const componentName = options.componentName || Component.displayName || Component.name || 'UnknownComponent';
  
  // Componente funcional simples
  const WrappedComponent = (props: P) => (
    <DebugWrapper
      componentName={componentName}
      module={options.module || 'unknown'}
      logProps={options.logProps}
      logLifecycle={options.logLifecycle}
      logRenders={options.logRenders}
    >
      <Component {...props} />
    </DebugWrapper>
  );
  
  WrappedComponent.displayName = `withDebug(${componentName})`;
  return WrappedComponent;
}

export default DebugWrapper;