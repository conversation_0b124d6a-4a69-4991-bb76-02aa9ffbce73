// Database connection and query abstraction layer
import { Pool, PoolClient } from 'pg';
import { DatabaseConfig, QueryResult } from '../types';
import { DatabaseError } from './errors';

export class DatabaseService {
  private pool: Pool | null = null;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      this.pool = new Pool({
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.user,
        password: this.config.password,
        ssl: this.config.ssl
      });

      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();

      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw new DatabaseError('Failed to connect to database', { error });
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log('🔌 Database disconnected');
    }
  }

  async query<T = any>(
    text: string, 
    params?: any[]
  ): Promise<QueryResult<T>> {
    if (!this.pool) {
      throw new DatabaseError('Database not connected');
    }

    try {
      const result = await this.pool.query(text, params);
      return {
        data: result.rows,
        count: result.rowCount || 0,
        success: true
      };
    } catch (error) {
      console.error('Database query error:', { text, params, error });
      throw new DatabaseError('Database query failed', { 
        query: text, 
        params,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    if (!this.pool) {
      throw new DatabaseError('Database not connected');
    }

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  getPool(): Pool {
    if (!this.pool) {
      throw new DatabaseError('Database not connected');
    }
    return this.pool;
  }

  isConnected(): boolean {
    return this.pool !== null;
  }
}

// Singleton database instance
let databaseService: DatabaseService | null = null;

export const createDatabaseService = (config: DatabaseConfig): DatabaseService => {
  if (!databaseService) {
    databaseService = new DatabaseService(config);
  }
  return databaseService;
};

export const getDatabaseService = (): DatabaseService => {
  if (!databaseService) {
    throw new DatabaseError('Database service not initialized');
  }
  return databaseService;
};