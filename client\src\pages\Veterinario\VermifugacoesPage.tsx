import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { LayoutWrapper } from '@/components/Layout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/debug-select';
import { logger, logError, logWarn } from '@/lib/logger';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Eye, Edit, Calendar, AlertCircle } from 'lucide-react';
import { ProcedimentoVet, Cavalo } from '@shared/schema';
import { AuthenticationErrorHandler, isAuthenticationError } from '@/components/auth/AuthenticationErrorHandler';

/**
 * Página de Vermifugações
 * Exibe e gerencia os registros de vermifugações dos cavalos
 */
export default function VermifugacoesPage() {
  // Registrar inicialização do componente
  useEffect(() => {
    logger.info('veterinario.vermifugacoes', 'Página de vermifugações carregada');
    
    return () => {
      logger.info('veterinario.vermifugacoes', 'Página de vermifugações desmontada');
    };
  }, []);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterCavalo, setFilterCavalo] = useState('todos');
  const [filterStatus, setFilterStatus] = useState('todos');

  // Buscar dados de procedimentos veterinários
  const { 
    data: procedimentos = [], 
    isLoading: loadingProcedimentos, 
    error: procedimentosError 
  } = useQuery<ProcedimentoVet[]>({
    queryKey: ['/api/procedimentos-vet'],
    retry: 1,
    // Logging via useEffect abaixo para compatibilidade com React Query v5
  });

  // Buscar dados de cavalos para o filtro
  const { 
    data: cavalos = [], 
    isLoading: loadingCavalos, 
    error: cavalosError 
  } = useQuery<Cavalo[]>({
    queryKey: ['/api/cavalos'],
    retry: 1,
    // Logging via useEffect abaixo para compatibilidade com React Query v5
  });

  // Log de dados carregados
  useEffect(() => {
    if (procedimentos.length > 0) {
      logger.info('veterinario.vermifugacoes', `Carregados ${procedimentos.length} procedimentos veterinários`);
    }
    if (cavalos.length > 0) {
      logger.info('veterinario.vermifugacoes', `Carregados ${cavalos.length} cavalos`);
    }
  }, [procedimentos.length, cavalos.length]);

  // Log de erros nas queries
  useEffect(() => {
    if (procedimentosError) {
      logError('veterinario.vermifugacoes', 'Erro na consulta de procedimentos', {}, procedimentosError as Error);
    }
    if (cavalosError) {
      logError('veterinario.vermifugacoes', 'Erro na consulta de cavalos', {}, cavalosError as Error);
    }
  }, [procedimentosError, cavalosError]);

  // Filtrar apenas vermifugações
  const vermifugacoes = React.useMemo(() => {
    // Verificar se procedimentos é um array válido
    if (!Array.isArray(procedimentos)) {
      logger.warn('veterinario.vermifugacoes', 'Procedimentos não é um array válido', {
        tipoRecebido: typeof procedimentos,
        valorRecebido: procedimentos
      });
      return [];
    }

    // Primeiro filtro - tipo de procedimento
    const vermifugacoesTemp = procedimentos.filter((proc: ProcedimentoVet) => {
      if (!proc) return false;
      
      const tipoLowerCase = proc.tipo?.toLowerCase() || '';
      const descricaoLowerCase = proc.descricao?.toLowerCase() || '';
      const medicamentosLowerCase = proc.medicamentos?.toLowerCase() || '';
      
      return (
        tipoLowerCase.includes('vermifug') || 
        descricaoLowerCase.includes('vermifug') ||
        medicamentosLowerCase.includes('vermifug')
      );
    });
    
    // Segundo filtro - critérios de busca e filtros de UI
    return vermifugacoesTemp.filter((proc: ProcedimentoVet) => {
      if (!proc) return false;
      
      const matchesSearch = searchTerm === '' || 
        (proc.descricao?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (proc.medicamentos?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (proc.veterinario?.toLowerCase() || '').includes(searchTerm.toLowerCase());
      
      const matchesCavalo = filterCavalo === 'todos' || proc.horse_id?.toString() === filterCavalo;
      const matchesStatus = filterStatus === 'todos' || getStatusVermifugacao(proc) === filterStatus;
      
      return matchesSearch && matchesCavalo && matchesStatus;
    });
  }, [procedimentos, searchTerm, filterCavalo, filterStatus]);

  // Obter o nome do cavalo pelo ID
  const getCavaloName = (id: number) => {
    if (!Array.isArray(cavalos)) return 'N/A';
    
    const cavalo = cavalos.find((c: Cavalo) => c && c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  };

  // Função para determinar o status da vermifugação
  function getStatusVermifugacao(procedimento: ProcedimentoVet): string {
    if (!procedimento || !procedimento.dataProximoProcedimento) return 'completo';
    
    try {
      const dataProximo = new Date(procedimento.dataProximoProcedimento);
      const hoje = new Date();
      
      // Se a data de próxima vermifugação já passou
      if (dataProximo < hoje) return 'atrasado';
      
      // Se a data de próxima vermifugação está a menos de 15 dias
      const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
      if (diasDiferenca <= 15) return 'próximo';
      
      return 'agendado';
    } catch (e) {
      return 'completo';
    }
  }

  // Status de vermifugação para o filtro
  const statusOptions = [
    { value: 'todos', label: 'Todos' },
    { value: 'atrasado', label: 'Atrasados' },
    { value: 'próximo', label: 'Próximos' },
    { value: 'agendado', label: 'Agendados' },
    { value: 'completo', label: 'Completos' }
  ];

  // Verificar se há erros de autenticação
  if (isAuthenticationError(procedimentosError) || isAuthenticationError(cavalosError)) {
    return (
      <LayoutWrapper pageTitle="Vermifugações" showBackButton backUrl="/veterinario">
        <div className="container mx-auto py-10">
          <AuthenticationErrorHandler 
            error={procedimentosError || cavalosError} 
            message="Sua sessão expirou ou você não está autenticado. Para visualizar as vermifugações dos cavalos, faça login novamente."
          />
        </div>
      </LayoutWrapper>
    );
  }  
  
  return (
    <LayoutWrapper pageTitle="Vermifugações" showBackButton backUrl="/veterinario">
      <div className="container mx-auto py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filtros</CardTitle>
            <CardDescription>Filtre as vermifugações por cavalo, status ou medicamento</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Buscar</label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar por medicamento, veterinário..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Cavalo</label>
                <Select 
                  value={filterCavalo} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.vermifugacoes', `Filtro de cavalo alterado para: ${value}`, {
                      previousValue: filterCavalo,
                      newValue: value
                    });
                    setFilterCavalo(value);
                  }}
                  debugId="filtro-cavalo-vermifugacoes"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cavalo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos" debugId="cavalo-todos">Todos</SelectItem>
                    {Array.isArray(cavalos) && cavalos.map((cavalo: Cavalo) => (
                      cavalo && cavalo.id && (
                        <SelectItem 
                          key={`cavalo-${cavalo.id}`}
                          value={cavalo.id.toString()} 
                          debugId={`cavalo-${cavalo.id}`}
                        >
                          {cavalo.name || `Cavalo ${cavalo.id}`}
                        </SelectItem>
                      )
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select 
                  value={filterStatus} 
                  onValueChange={(value) => {
                    logger.debug('veterinario.vermifugacoes', `Filtro de status alterado para: ${value}`, {
                      previousValue: filterStatus,
                      newValue: value
                    });
                    setFilterStatus(value);
                  }}
                  debugId="filtro-status-vermifugacoes"
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem 
                        key={`status-${option.value}`} 
                        value={option.value} 
                        debugId={`status-vermif-${option.value}`}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Vermifugações</h2>
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Nova Vermifugação
          </Button>
        </div>

        {loadingProcedimentos ? (
          <div className="text-center py-10">Carregando vermifugações...</div>
        ) : vermifugacoes.length > 0 ? (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Data</TableHead>
                  <TableHead>Cavalo</TableHead>
                  <TableHead>Medicamento</TableHead>
                  <TableHead>Dosagem</TableHead>
                  <TableHead>Próxima Aplicação</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.isArray(vermifugacoes) && vermifugacoes.map((vermifugacao: ProcedimentoVet) => {
                  if (!vermifugacao || !vermifugacao.id) return null;
                  
                  const status = getStatusVermifugacao(vermifugacao);
                  return (
                    <TableRow key={`vermifugacao-${vermifugacao.id}`}>
                      <TableCell>{formato.data(vermifugacao.data)}</TableCell>
                      <TableCell>{getCavaloName(vermifugacao.horse_id)}</TableCell>
                      <TableCell>{vermifugacao.medicamentos || 'N/A'}</TableCell>
                      <TableCell>{vermifugacao.dosagem || 'N/A'}</TableCell>
                      <TableCell>
                        {vermifugacao.dataProximoProcedimento 
                          ? formato.data(vermifugacao.dataProximoProcedimento) 
                          : 'Não agendado'}
                      </TableCell>
                      <TableCell>
                        <StatusBadge status={status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Calendar className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-10">
            <p className="text-muted-foreground">Nenhuma vermifugação encontrada.</p>
            <Button variant="outline" className="mt-4">
              <Plus className="mr-2 h-4 w-4" /> Registrar Primeira Vermifugação
            </Button>
          </div>
        )}
      </div>
    </LayoutWrapper>
  );
}

// Componente para mostrar o status com cor apropriada
function StatusBadge({ status }: { status: string }) {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let label = status.charAt(0).toUpperCase() + status.slice(1);
  
  switch (status) {
    case 'atrasado':
      variant = "destructive";
      break;
    case 'próximo':
      variant = "default";
      break;
    case 'agendado':
      variant = "secondary";
      break;
    case 'completo':
      variant = "outline";
      break;
  }
  
  return <Badge variant={variant}>{label}</Badge>;
}

// Funções auxiliares
const formato = {
  data: (dataString: string) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString || 'Data inválida';
    }
  }
};