// Refactored server entry point with improved architecture
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { setupVite, serveStatic, log } from './vite';
import { createDatabaseService } from './core/database';
import { getDatabaseConfig } from './config/database.config';
import { errorHandler } from './core/errors';
import apiRoutes from './routes/index';

class EquiGestorServer {
  private app: express.Application;
  private server: any;
  private port: number;
  private host: string;

  constructor() {
    this.app = express();
    this.port = 5000;
    this.host = '0.0.0.0';
    this.server = createServer(this.app);
    
    this.initializeMiddleware();
  }

  private initializeMiddleware(): void {
    // Basic middleware
    this.app.use(cors());
    this.app.use(express.json({ limit: '2mb' }));
    this.app.use(express.urlencoded({ extended: false, limit: '2mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'EquiGestor AI Online!',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development'
      });
    });
  }

  private async initializeDatabase(): Promise<void> {
    try {
      console.log('🔄 Initializing database connection...');
      const config = getDatabaseConfig();
      const db = createDatabaseService(config);
      await db.connect();
      console.log('✅ Database connection initialized successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      console.log('🔄 Server will continue in offline mode');
    }
  }

  private async initializeRoutes(): Promise<void> {
    try {
      console.log('🔄 Loading API routes...');
      
      // API routes
      this.app.use('/api', apiRoutes);
      
      console.log('✅ API routes loaded successfully');
    } catch (error) {
      console.error('❌ API routes failed to load:', error);
      console.log('🔄 Server running in limited mode');
    }
  }

  private async initializeVite(): Promise<void> {
    try {
      const nodeEnv = process.env.NODE_ENV || 'development';
      
      if (nodeEnv === 'development') {
        console.log('🔄 Setting up Vite development server...');
        await setupVite(this.app, this.server);
        console.log('✅ Vite development server configured');
      } else {
        console.log('🔄 Setting up static file serving...');
        serveStatic(this.app);
        console.log('✅ Static file serving configured');
      }
    } catch (error) {
      console.error('❌ Vite setup failed:', error);
      console.log('🔄 Server will serve static files as fallback');
      serveStatic(this.app);
    }
  }

  private initializeErrorHandling(): void {
    // Global error handling
    this.app.use(errorHandler);
    
    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Endpoint not found'
        }
      });
    });
  }

  public async start(): Promise<void> {
    try {
      console.log('🚀 Starting EquiGestor AI server...');
      
      // Initialize components in order
      await this.initializeDatabase();
      await this.initializeRoutes();
      await this.initializeVite();
      this.initializeErrorHandling();

      // Start server
      this.server.listen(this.port, this.host, () => {
        console.log(`🎉 EquiGestor AI server running on ${this.host}:${this.port}`);
        console.log(`⚙️  Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🔗 API Documentation: http://${this.host}:${this.port}/api/health`);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      console.error('❌ Server startup failed:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    console.log('🔄 Shutting down server...');
    
    try {
      // Close database connections
      const { getDatabaseService } = await import('./core/database');
      const db = getDatabaseService();
      await db.disconnect();
    } catch (error) {
      console.error('Error during database shutdown:', error);
    }

    // Close HTTP server
    this.server.close(() => {
      console.log('✅ Server shutdown complete');
      process.exit(0);
    });
  }
}

// Start server
const server = new EquiGestorServer();
server.start().catch(error => {
  console.error('Fatal error during server startup:', error);
  process.exit(1);
});