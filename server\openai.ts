import { OpenAI } from "openai";
import { Request, Response } from "express";

// Configuração da API OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Modelo a ser usado
const MODEL = "gpt-4.1-nano"; // Alterado de gpt-3.5-turbo para gpt-4o-mini conforme solicitado pelo usuário

// Contexto do sistema para o assistente do EquiGestor
const systemPrompt = `Você é o assistente virtual do EquiGestor, um sistema de gestão para cavalos. 
Responda sempre em português e de forma natural, como se estivesse conversando com o usuário.

O EquiGestor possui os seguintes módulos e funcionalidades:

1. Gestão de Cavalos:
   - Cadastro completo de cavalos (nome, raça, nascimento, sexo, etc.)
   - Registro de genealogia e histórico
   - Acompanhamento de medidas físicas (peso, altura, etc.)

2. Manejo:
   - Agendamento de vacinações
   - Ferrageamento
   - Vermifugação
   - Treinamentos
   - Participação em competições

3. Veterinário:
   - Registro de procedimentos veterinários
   - Histórico de saúde
   - Controle de medicações
   - Exames
   
4. Reprodução:
   - Controle de ciclo reprodutivo
   - Registro de coberturas
   - Acompanhamento de gestação
   - Histórico de partos e potros

5. Nutrição:
   - Planejamento alimentar
   - Controle de dietas
   - Suplementações
   
6. Financeiro:
   - Controle de custos
   - Receitas com serviços
   - Relatórios financeiros

7. Agenda e Alertas:
   - Calendário de compromissos
   - Notificações para manejos atrasados
   - Lembretes de vacinação

8. Estatísticas:
   - Relatórios personalizados
   - Gráficos de desempenho
   - Indicadores de saúde

9. Arquivo:
   - Upload de fotos, vídeos e documentos
   - Organização por cavalo
   
10. Genética:
   - Registro de morfologia (avaliação física detalhada)
   - Análise de genealogia com cálculo de consanguinidade
   - Histórico de desempenho (competições, provas, etc.)
   - Sugestões de cruzamento baseadas em compatibilidade

Responda perguntas no contexto dessas funcionalidades e ajude o usuário a utilizar o sistema da melhor forma possível.
Seja específico quando um usuário perguntar sobre como realizar uma tarefa.

IMPORTANTE: Quando o usuário fizer perguntas sobre "meus cavalos", "meus eventos" ou quaisquer outros dados, 
responda EXCLUSIVAMENTE com base nos dados atuais que são fornecidos no contexto expandido abaixo deste sistema.
NÃO use os exemplos de cavalos a seguir como se fossem do usuário atual. Estes são apenas exemplos genéricos:

Exemplos de cavalos que podem existir em um sistema:
- Thor: garanhão, 8 anos, 520kg, Quarto de Milha, especialista em provas de velocidade
- Pegasus: macho castrado, 8 anos, 450kg, Árabe, usado principalmente para enduro
- Trovão: macho, 5 anos, 480kg, Mangalarga Marchador, em treinamento para marcha
- Zeus: garanhão, 12 anos, 500kg, Brasileiro de Hipismo, usado para saltos

IMPORTANTE: O sistema não impõe restrições de intervalo entre vacinações ou procedimentos. Os usuários podem agendar vacinações para qualquer data que desejarem. Nunca indique que não é possível agendar uma vacinação devido a uma vacinação anterior.

Ao responder, seja diretamente útil e objetivo. Se a pergunta não estiver relacionada ao sistema EquiGestor, informe educadamente que você é um assistente especializado em gestão equina.`;

// Handler para processar mensagens enviadas para a API OpenAI
export const processMessage = async (req: Request, res: Response) => {
  try {
    const { message, history = [] } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Mensagem não fornecida' });
    }
    
    // Obter user_id do corpo ou cabeçalho da requisição
    let user_id = req.body.user_id;
    
    // Se não está no corpo, tenta obter do cabeçalho
    if (!user_id && req.headers['user-id']) {
      user_id = parseInt(req.headers['user-id'] as string);
    }
    
    // Se ainda não tiver um user_id, usa um valor padrão de fallback
    if (!user_id) {
      console.warn('[OpenAI] Nenhum user_id encontrado na requisição, usando valor padrão');
      user_id = 1; // Valor padrão de fallback
    }
    
    console.log('[OpenAI] Processando mensagem para o usuário ID:', user_id);
    
    // Tentar obter informações do banco de dados para este usuário
    try {
      const { storage } = await import('./storage');
      
      // Obter dados básicos para contextualizar o assistente
      const cavalos = await storage.getCavalos(user_id);
      const manejos = await storage.getManejos(user_id);
      const eventos = await storage.getEventos(user_id);
      
      // Preparar contexto adicional com dados reais do usuário
      let databaseContext = `
      Informações atuais do sistema para este usuário:
      - Total de cavalos: ${cavalos.length}
      - Total de manejos: ${manejos.length}
      - Total de eventos: ${eventos.length}
      `;
      
      if (cavalos.length > 0) {
        databaseContext += "\nCavalos cadastrados:";
        // Limitar a 10 cavalos para não sobrecarregar o contexto
        cavalos.slice(0, 10).forEach(cavalo => {
          databaseContext += `\n- ${cavalo.name} (${cavalo.breed || 'Raça não informada'})`;
        });
      }
      
      // Próximos eventos (filtrar eventos futuros)
      const proximosEventos = eventos
        .filter(evento => new Date(evento.data) > new Date())
        .slice(0, 5); // Limitar a 5 eventos futuros
      
      if (proximosEventos.length > 0) {
        databaseContext += "\n\nPróximos eventos:";
        proximosEventos.forEach(evento => {
          databaseContext += `\n- ${evento.titulo} (${evento.data})`;
        });
      }
      
      // Atualizar o prompt do sistema com os dados do usuário
      const enhancedSystemPrompt = systemPrompt + "\n\n" + databaseContext;
      
      // Preparar as mensagens para envio com o contexto aumentado
      const messages = [
        { role: "system", content: enhancedSystemPrompt },
        ...history.map((msg: any) => ({
          role: msg.role,
          content: msg.content
        })),
        { role: "user", content: message }
      ];
      
      console.log('[OpenAI] Contexto aumentado com dados do usuário id:', user_id);
      return messages;
    } catch (error) {
      console.error('[OpenAI] Erro ao obter dados do banco:', error);
      // Em caso de erro, prossegue com o prompt original
      
      // Preparar as mensagens para envio
      const messages = [
        { role: "system", content: systemPrompt },
        ...history.map((msg: any) => ({
          role: msg.role,
          content: msg.content
        })),
        { role: "user", content: message }
      ];
      
      return messages;
    }
    
    // Função auxiliar para obter mensagens com contexto
    const getContextualMessages = async () => {
      // Tentar obter informações do banco de dados para este usuário
      try {
        const { storage } = await import('./storage');
        
        // Obter dados básicos para contextualizar o assistente
        const cavalos = await storage.getCavalos(user_id);
        const manejos = await storage.getManejos(user_id);
        const eventos = await storage.getEventos(user_id);
        
        // Preparar contexto adicional com dados reais do usuário
        let databaseContext = `
        Informações atuais do sistema para este usuário:
        - Total de cavalos: ${cavalos.length}
        - Total de manejos: ${manejos.length}
        - Total de eventos: ${eventos.length}
        `;
        
        if (cavalos.length > 0) {
          databaseContext += "\nCavalos cadastrados:";
          // Mostrar todos os cavalos para evitar inconsistências
          cavalos.forEach((cavalo: any) => {
            databaseContext += `\n- ${cavalo.name} (${cavalo.breed || 'Raça não informada'})`;
          });
        }
        
        // Próximos eventos (filtrar eventos futuros)
        const proximosEventos = eventos
          .filter((evento: any) => new Date(evento.data) > new Date())
          .slice(0, 5); // Limitar a 5 eventos futuros
        
        if (proximosEventos.length > 0) {
          databaseContext += "\n\nPróximos eventos:";
          proximosEventos.forEach((evento: any) => {
            databaseContext += `\n- ${evento.titulo} (${evento.data})`;
          });
        }
        
        // Atualizar o prompt do sistema com os dados do usuário
        const enhancedSystemPrompt = systemPrompt + "\n\n" + databaseContext;
        
        // Preparar as mensagens para envio com o contexto aumentado
        const formatMessages = [
          { role: "system", content: enhancedSystemPrompt },
          ...history.map((msg: any) => ({
            role: msg.role,
            content: msg.content
          })),
          { role: "user", content: message }
        ];
        
        console.log('[OpenAI] Contexto aumentado com dados do usuário id:', user_id);
        return formatMessages;
      } catch (error) {
        console.error('[OpenAI] Erro ao obter dados do banco:', error);
        // Em caso de erro, prossegue com o prompt original
        
        // Preparar as mensagens para envio
        const formatMessages = [
          { role: "system", content: systemPrompt },
          ...history.map((msg: any) => ({
            role: msg.role,
            content: msg.content
          })),
          { role: "user", content: message }
        ];
        
        return formatMessages;
      }
    }
    
    // Obter mensagens contextualizadas e enviar para a API
    const formattedMessages = await getContextualMessages();
    
    // Substituir o response anterior
    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: formattedMessages,
      temperature: 0.7,
      max_tokens: 800,
      function_calling: "auto",
      functions: [
        {
          name: "registrarVacina",
          description: "Registra uma nova vacinação no sistema",
          parameters: {
            type: "object",
            properties: {
              nome: {
                type: "string",
                description: "Nome do cavalo para a vacinação"
              },
              data: {
                type: "string",
                description: "Data para a vacinação (aceita formatos como DD/MM/AAAA, DD-MM-AAAA ou por extenso)"
              },
              tipo_vacina: {
                type: "string",
                description: "Tipo da vacina (exemplos: raiva, tétano, influenza)"
              },
              hora: {
                type: "string",
                description: "Hora de início da vacinação (opcional)"
              },
              hora_fim: {
                type: "string",
                description: "Hora de término da vacinação (opcional)"
              }
            },
            required: ["nome", "data", "tipo_vacina"]
          }
        }
      ]
    });
    
    // Verifica se há uma chamada de função na resposta
    const responseMessage = response.choices[0].message;
    
    if (responseMessage.function_call) {
      console.log('[OpenAI] Function call detectada:', responseMessage.function_call.name);
      console.log('[OpenAI] Argumentos da função:', responseMessage.function_call.arguments);
      
      try {
        // Processar a chamada de função com o módulo assistenteActions
        const functionCall = {
          name: responseMessage.function_call.name,
          arguments: JSON.parse(responseMessage.function_call.arguments)
        };
        
        const { processarAcaoAssistente } = await import('./assistenteActions');
        const actionResult = await processarAcaoAssistente(functionCall);
        
        console.log('[OpenAI] Resultado da ação:', JSON.stringify(actionResult));
        
        // Se a ação foi bem-sucedida, envia uma resposta combinada
        if (actionResult && actionResult.success) {
          // Segunda chamada para o modelo para formatar melhor a resposta
          const followUpResponse = await openai.chat.completions.create({
            model: MODEL,
            messages: [
              ...formattedMessages,
              { 
                role: "assistant", 
                content: null, 
                function_call: {
                  name: responseMessage.function_call.name,
                  arguments: responseMessage.function_call.arguments
                }
              },
              { 
                role: "function", 
                name: responseMessage.function_call.name, 
                content: JSON.stringify(actionResult)
              }
            ],
            temperature: 0.7,
            max_tokens: 800
          });
          
          return res.status(200).json({
            content: followUpResponse.choices[0].message.content,
            action: {
              name: responseMessage.function_call.name,
              result: actionResult
            }
          });
        } else {
          // Se a ação falhou, retornar a mensagem de erro
          return res.status(200).json({
            content: actionResult.message || "Não foi possível completar a ação solicitada.",
            action: {
              name: responseMessage.function_call.name,
              result: actionResult
            }
          });
        }
      } catch (error: any) {
        console.error('[OpenAI] Erro ao processar função:', error);
        return res.status(200).json({
          content: `Desculpe, ocorreu um erro ao processar sua solicitação: ${error.message}`,
          error: true
        });
      }
    } else {
      // Se não houver chamada de função, retorna o conteúdo da mensagem
      return res.status(200).json({
        content: responseMessage.content
      });
    }
  } catch (error: any) {
    console.error('Erro ao processar mensagem:', error);
    return res.status(500).json({ 
      error: 'Erro ao processar mensagem',
      details: error.message 
    });
  }
};

// Handler para detectar intenção das mensagens do usuário
export const detectIntent = async (req: Request, res: Response) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Mensagem não fornecida' });
    }
    
    const intentPrompt = `Analise a seguinte mensagem do usuário relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Identifique a intenção principal e classifique-a em uma das seguintes categorias:
    - Registro de peso
    - Vacinação
    - Treinamento
    - Alimentação
    - Ferrageamento
    - Consulta veterinária
    - Cadastro de cavalo
    - Reprodução
    - Morfologia (avaliação de características físicas)
    - Genealogia (histórico familiar e consanguinidade)
    - Desempenho (resultados em competições)
    - Cruzamento (sugestões para procriação)
    - Genética (qualquer tema relacionado à genética equina)
    - Consulta de informação
    
    Além disso, classifique se é uma CONSULTA (o usuário só quer informação) ou uma AÇÃO (o usuário quer executar algo no sistema).
    
    Responda em formato JSON:
    {
      "intent": "categoria escolhida",
      "type": "query ou action"
    }`;
    
    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: intentPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });
    
    const result = JSON.parse(response.choices[0].message.content || '{"intent": "Consulta de informação", "type": "query"}');
    
    return res.status(200).json({
      intent: result.intent,
      type: result.type
    });
  } catch (error: any) {
    console.error('Erro ao detectar intenção:', error);
    return res.status(500).json({ 
      error: 'Erro ao detectar intenção',
      details: error.message 
    });
  }
};

// Handler para extrair entidades das mensagens do usuário
export const extractEntities = async (req: Request, res: Response) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Mensagem não fornecida' });
    }
    
    const entityPrompt = `Extraia as informações relevantes da seguinte mensagem relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Extraia as seguintes entidades, se presentes:
    - nome_cavalo: nome do cavalo mencionado
    - tipo_acao: tipo de ação a ser realizada (vacinar, pesar, treinar, etc.)
    - data: qualquer data mencionada
    - valor_peso: valor numérico de peso, se mencionado (somente o número)
    - valor_altura: valor numérico de altura, se mencionado (somente o número)
    - tipo_vacina: nome da vacina mencionada
    
    Entidades relacionadas à genética:
    - tipo_morfologia: tipo de avaliação morfológica mencionada (ex: cabeça, pescoço, garupa)
    - valor_pontuacao: pontuação numérica mencionada (1-10)
    - nome_ancestral: nome de ancestral mencionado (pai, mãe, avô, avó)
    - tipo_desempenho: tipo de competição ou prova mencionada
    - objetivo_cruzamento: objetivo desejado para cruzamento (ex: velocidade, conformação)
    
    Responda em formato JSON, incluindo apenas os campos encontrados:
    {
      "nome_cavalo": "nome encontrado",
      "tipo_acao": "ação encontrada",
      etc.
    }`;
    
    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: entityPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });
    
    const entities = JSON.parse(response.choices[0].message.content || '{}');
    
    return res.status(200).json(entities);
  } catch (error: any) {
    console.error('Erro ao extrair entidades:', error);
    return res.status(500).json({ 
      error: 'Erro ao extrair entidades',
      details: error.message 
    });
  }
};