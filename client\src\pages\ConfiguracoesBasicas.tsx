import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Settings, User, Shield, Database } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export default function ConfiguracoesBasicas() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    // Simular salvamento
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
    
    toast({
      title: "Configurações salvas",
      description: "Suas configurações foram atualizadas com sucesso.",
    });
  };

  const isAdmin = user?.id === 1;

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Settings className="h-8 w-8" />
          Configurações
        </h1>
        <p className="text-muted-foreground mt-2">
          Gerencie as configurações básicas do seu sistema
        </p>
      </div>

      <div className="grid gap-6">
        {/* Informações do Usuário */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Informações do Usuário
            </CardTitle>
            <CardDescription>
              Suas informações básicas no sistema
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="username">Usuário</Label>
                <Input
                  id="username"
                  value={user?.username || ""}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="userId">ID do Usuário</Label>
                <Input
                  id="userId"
                  value={user?.id || ""}
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
            
            {isAdmin && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 text-blue-700 font-medium">
                  <Shield className="h-4 w-4" />
                  Conta Administrativa
                </div>
                <p className="text-sm text-blue-600 mt-1">
                  Você tem acesso completo a todas as funcionalidades do sistema.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Preferências do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Preferências Gerais
            </CardTitle>
            <CardDescription>
              Configurações básicas de uso do sistema
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="theme">Tema da Interface</Label>
                <Input
                  id="theme"
                  value="Padrão (Azul)"
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="language">Idioma</Label>
                <Input
                  id="language"
                  value="Português (Brasil)"
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Informações do Sistema */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Informações do Sistema
            </CardTitle>
            <CardDescription>
              Dados sobre a instalação atual do RS Horse
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <Label>Versão</Label>
                <p className="font-mono bg-muted p-2 rounded mt-1">v1.0.0</p>
              </div>
              <div>
                <Label>Ambiente</Label>
                <p className="font-mono bg-muted p-2 rounded mt-1">Produção</p>
              </div>
              <div>
                <Label>Status</Label>
                <p className="font-mono bg-green-100 text-green-800 p-2 rounded mt-1">Online</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Botões de Ação */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="text-sm text-muted-foreground">
            <p>Para configurações avançadas, entre em contato com o administrador do sistema.</p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Atualizar Página
            </Button>
            <Button 
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? "Salvando..." : "Salvar Configurações"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}