modules = ["nodejs-20", "web", "postgresql-16"]
run = "node replit-start.js"                                               # Alterado para usar o script de inicialização unificado
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
build = ["npm", "run", "build"]
run = ["npm", "run", "start"]

[[ports]]
localPort = 80
externalPort = 80

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 3001

[[ports]]
localPort = 5000
externalPort = 4200

[[ports]]
localPort = 8080
externalPort = 8080

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
waitForPort = 5000

# A tarefa original que chamava "npm run dev" foi removida ou comentada.
# O replit-start.js já contém a lógica de fallback para o modo desenvolvimento
# se o build de produção não for encontrado.
# Se desejar manter uma tarefa explícita para o workflow, pode usar:
# [[workflows.workflow.tasks]]
# task = "shell.exec"
# args = "node replit-start.js"
# waitForPort = 5000

[objectStorage]
defaultBucketID = "replit-objstore-ece15a7c-56ac-4915-bc63-cfd8a255f012"

[rules]

[rules.formatter]

[rules.formatter.fileExtensions]

[rules.formatter.fileExtensions.".tsx"]
id = "module:web/languageServer:typescript-language-server"
