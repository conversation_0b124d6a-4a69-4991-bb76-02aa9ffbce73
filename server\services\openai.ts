import { storage } from "../storage";

// Inicializando a API do OpenAI com a chave fornecida no env
let openai: any = null;

async function getOpenAI() {
  if (!openai) {
    try {
      const OpenAIModule = await import("openai");
      const OpenAI = OpenAIModule.default || OpenAIModule.OpenAI || OpenAIModule;
      openai = new (OpenAI as any)({
        apiKey: process.env.OPENAI_API_KEY || ''
      });
    } catch (error) {
      console.error('[OpenAI] Failed to initialize OpenAI client:', error);
      throw new Error('OpenAI client initialization failed');
    }
  }
  return openai;
}

// Check if API key is available and log warning if missing
if (!process.env.OPENAI_API_KEY) {
  console.warn('[OpenAI] Warning: OPENAI_API_KEY environment variable is not set');
  console.warn('[OpenAI] Some features requiring AI capabilities may not work');
}

// Modelo a ser usado (atualizado para GPT-4o mini conforme solicitado pelo usuário)
const MODEL = "gpt-4o-mini";

// Funções utilitárias para processamento local de consultas
function processLocalQuery(userData: any, message: string): string | null {
  if (!userData) return null;
  
  const lowerMessage = message.toLowerCase();
  
  // Quantos cavalos
  if (lowerMessage.match(/quantos?\s+cavalos?/)) {
    return `Você tem ${userData.estatisticas.total} cavalos cadastrados no sistema.`;
  }
  
  // Listar cavalos
  if (lowerMessage.match(/(liste|listar|quais são)\s+.*cavalos?/)) {
    const nomes = userData.cavalos.map((c: any) => c.nome).slice(0, 10).join(", ");
    return userData.cavalos.length <= 10 ? 
      `Seus cavalos são: ${nomes}.` :
      `Alguns dos seus cavalos: ${nomes}... (${userData.cavalos.length} total)`;
  }
  
  // Estatísticas gerais
  if (lowerMessage.match(/(estatística|resumo|dashboard)/)) {
    const { estatisticas } = userData;
    return `📊 Resumo do seu plantel:
• Total: ${estatisticas.total} cavalos
• Ativos: ${estatisticas.ativos}
• Machos: ${estatisticas.porSexo.machos}
• Fêmeas: ${estatisticas.porSexo.femeas}
• Manejos totais: ${estatisticas.totalManejos}
• Manejos recentes (7 dias): ${estatisticas.manejosRecentes}`;
  }
  
  return null;
}

// Função para verificar se a pergunta é relacionada aos cavalos
function isHorseRelatedQuery(message: string): boolean {
  const lowerMessage = message.toLowerCase();
  
  // Palavras-chave relacionadas a cavalos e gestão equina
  const horseKeywords = [
    'cavalo', 'cavalos', 'égua', 'éguas', 'garanhão', 'garanhões', 'potro', 'potros',
    'plantel', 'haras', 'equino', 'equina', 'equinos', 'equinas',
    'raça', 'raças', 'pelagem', 'pelagens', 'genealogia', 'pedigree',
    'manejo', 'manejos', 'vacinação', 'vermifugação', 'veterinário', 'veterinária',
    'nutrição', 'alimentação', 'ração', 'pasto', 'reprodução', 'cobertura',
    'nascimento', 'desmame', 'peso', 'altura', 'idade', 'registro',
    'morfologia', 'medidas', 'característica', 'características',
    'saúde', 'doença', 'tratamento', 'medicamento', 'exame',
    'ferrageamento', 'casqueamento', 'casco', 'cascos'
  ];
  
  // Verificar se contém palavras-chave relacionadas
  return horseKeywords.some(keyword => lowerMessage.includes(keyword));
}

// Função para detectar solicitações veterinárias
function detectVetRequest(message: string): { detected: boolean, data?: any } {
  const lowerMessage = message.toLowerCase();
  
  // Padrões para detectar solicitações veterinárias
  const vetPatterns = [
    /(?:agendar|marcar|criar|cadastrar)\s+(?:procedimento|consulta|vacinação|vermífugo|exame|cirurgia)/,
    /(?:vacinação|vacina|vacinar)\s+(?:para|do|da|de|no|na)\s+([a-zA-ZÀ-ÿ\s]+)/,
    /(?:vermífugo|verme|vermifugação)\s+(?:para|do|da|de|no|na)\s+([a-zA-ZÀ-ÿ\s]+)/,
    /(?:exame|consulta|check-up)\s+(?:para|do|da|de|no|na)\s+([a-zA-ZÀ-ÿ\s]+)/,
    /(?:cirurgia|operação)\s+(?:para|do|da|de|no|na)\s+([a-zA-ZÀ-ÿ\s]+)/,
    /(?:casqueamento|ferrageamento|casco)\s+(?:para|do|da|de|no|na)\s+([a-zA-ZÀ-ÿ\s]+)/
  ];
  
  for (const pattern of vetPatterns) {
    const match = lowerMessage.match(pattern);
    if (match) {
      // Extrair informações da mensagem
      const horseNameMatch = lowerMessage.match(/(?:para|do|da|de|no|na)\s+(?:cavalo\s+)?([a-zA-ZÀ-ÿ\s]+?)(?:\s+(?:amanhã|hoje|na|em|dia)|\s*$)/);
      const dateMatch = lowerMessage.match(/(?:amanhã|hoje|(?:dia\s+)?(\d{1,2})(?:\/(\d{1,2}))?(?:\/(\d{4}))?|próxima\s+semana|semana\s+que\s+vem)/);
      
      // Determinar tipo de procedimento
      let tipo = 'consulta';
      if (lowerMessage.includes('vacinação') || lowerMessage.includes('vacina')) tipo = 'vacinação';
      else if (lowerMessage.includes('vermífugo') || lowerMessage.includes('verme')) tipo = 'vermifugação';
      else if (lowerMessage.includes('exame')) tipo = 'exame';
      else if (lowerMessage.includes('cirurgia') || lowerMessage.includes('operação')) tipo = 'cirurgia';
      else if (lowerMessage.includes('casqueamento') || lowerMessage.includes('casco')) tipo = 'casqueamento';
      
      // Processar data
      let dataExecucao = new Date();
      if (dateMatch) {
        if (lowerMessage.includes('amanhã')) {
          dataExecucao.setDate(dataExecucao.getDate() + 1);
        } else if (lowerMessage.includes('próxima semana') || lowerMessage.includes('semana que vem')) {
          dataExecucao.setDate(dataExecucao.getDate() + 7);
        } else if (dateMatch[1]) { // Dia específico
          const dia = parseInt(dateMatch[1]);
          const mes = dateMatch[2] ? parseInt(dateMatch[2]) - 1 : dataExecucao.getMonth();
          const ano = dateMatch[3] ? parseInt(dateMatch[3]) : dataExecucao.getFullYear();
          dataExecucao = new Date(ano, mes, dia);
        }
      }
      
      return {
        detected: true,
        data: {
          tipo,
          horseName: horseNameMatch ? horseNameMatch[1].trim() : null,
          dataExecucao: dataExecucao.toISOString().split('T')[0],
          observacoes: `Procedimento criado via assistente IA: ${message}`
        }
      };
    }
  }
  
  return { detected: false };
}

// Sistema inteligente de cache para dados dos cavalos
const smartHorseCache = {
  userCaches: new Map<number, any>(),
  offTopicAttempts: new Map<number, number>(), // Contador de perguntas fora de contexto por usuário
  cacheLifetime: 10 * 60 * 1000, // 10 minutos para dados mais atualizados
  maxOffTopicQuestions: 3, // Limite de perguntas fora de contexto
  
  // Verificar se o cache do usuário precisa ser atualizado
  needsUpdate(user_id: number): boolean {
    const now = Date.now();
    const userCache = this.userCaches.get(user_id);
    return !userCache || (now - userCache.lastUpdated > this.cacheLifetime);
  },
  
  // Obter dados do cache ou buscar no banco
  async getData(user_id: number) {
    if (!this.needsUpdate(user_id)) {
      console.log("[OpenAI] Usando dados do cache para user_id:", user_id);
      return this.userCaches.get(user_id)?.data;
    }
    
    console.log("[OpenAI] Atualizando cache inteligente para user_id:", user_id);
    return await this.updateUserCache(user_id);
  },
  
  // Atualizar cache específico do usuário
  async updateUserCache(user_id: number) {
    try {
      console.log("[OpenAI] Executando updateUserCache para user_id:", user_id);
      // Buscar dados completos dos cavalos do usuário
      const { storage } = await import('../storage');
      const cavalos = await storage.getCavalos(user_id);
      const manejos = await storage.getManejos(user_id);
      
      console.log(`[OpenAI] Cache atualizado: ${cavalos.length} cavalos encontrados para user_id: ${user_id}`);
      
      // Processar dados dos cavalos com informações mais detalhadas
      const processedHorses = cavalos.map((cavalo: any) => {
        const idade = cavalo.birth_date ? 
          Math.floor((Date.now() - new Date(cavalo.birth_date).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : null;
        
        return {
          id: cavalo.id,
          nome: cavalo.name,
          raca: cavalo.breed || "Não informada",
          sexo: cavalo.sexo,
          idade: idade ? `${idade} anos` : "Idade desconhecida",
          status: cavalo.status,
          peso: cavalo.peso ? `${cavalo.peso} kg` : null,
          altura: cavalo.altura ? `${cavalo.altura} cm` : null,
          registro: cavalo.numero_registro || null,
          observacoes: cavalo.notes || null
        };
      });
      
      // Estatísticas inteligentes
      const estatisticas = {
        total: cavalos.length,
        ativos: cavalos.filter((c: any) => c.status === 'ativo').length,
        inativos: cavalos.filter((c: any) => c.status === 'inativo').length,
        porSexo: {
          machos: cavalos.filter((c: any) => c.sexo === 'Macho' || c.sexo === 'Garanhão').length,
          femeas: cavalos.filter((c: any) => c.sexo === 'Fêmea' || c.sexo === 'Égua').length,
          castrados: cavalos.filter((c: any) => c.sexo === 'Macho (Castrado)').length
        },
        racas: [...new Set(cavalos.map((c: any) => c.breed).filter(Boolean))],
        totalManejos: manejos.length,
        manejosRecentes: manejos.filter((m: any) => {
          const dataManejo = new Date(m.data_execucao);
          const seteDiasAtras = new Date();
          seteDiasAtras.setDate(seteDiasAtras.getDate() - 7);
          return dataManejo >= seteDiasAtras;
        }).length
      };
      
      const userData = {
        cavalos: processedHorses,
        estatisticas,
        lastUpdated: Date.now()
      };
      
      this.userCaches.set(user_id, { data: userData, lastUpdated: Date.now() });
      console.log("[OpenAI] Cache inteligente atualizado:", `${cavalos.length} cavalos, ${manejos.length} manejos`);
      
      return userData;
    } catch (error) {
      console.error("[OpenAI] Erro ao atualizar cache inteligente:", error);
      return null;
    }
  }
};

// Context do sistema para o assistente virtual
const SYSTEM_CONTEXT = `
Você é o assistente virtual do EquiGestor AI, um sistema de gestão para cavalos. 
Responda sempre em português e de forma natural, como se estivesse conversando com o usuário.

O EquiGestor possui os seguintes módulos e funcionalidades:

1. Gestão de Cavalos:
   - Cadastro completo de cavalos (nome, raça, nascimento, sexo, etc.)
   - Registro de genealogia e histórico
   - Acompanhamento de medidas físicas (peso, altura, etc.)

2. Manejo:
   - Agendamento de vacinações
   - Ferrageamento
   - Vermifugação
   - Treinamentos
   - Participação em competições

3. Veterinário:
   - Registro de procedimentos veterinários
   - Histórico de saúde
   - Controle de medicações
   - Exames
   
4. Reprodução:
   - Controle de ciclo reprodutivo
   - Registro de coberturas
   - Acompanhamento de gestação
   - Histórico de partos e potros

5. Nutrição:
   - Planejamento alimentar
   - Controle de dietas
   - Suplementações
   
6. Financeiro:
   - Controle de custos
   - Receitas com serviços
   - Relatórios financeiros

7. Agenda e Alertas:
   - Calendário de compromissos
   - Notificações para manejos atrasados
   - Lembretes de vacinação

8. Estatísticas:
   - Relatórios personalizados
   - Gráficos de desempenho
   - Indicadores de saúde

9. Arquivo:
   - Upload de fotos, vídeos e documentos
   - Organização por cavalo

Instruções específicas:
1. Sempre responda em português do Brasil, de forma clara e concisa.
2. IMPORTANTE: Sempre responda com informações precisas baseadas exclusivamente nos dados do sistema. Nunca concorde com correções dos usuários que contradizem os dados do sistema.
3. Ao informar o número de cavalos ou listar cavalos, use APENAS os dados presentes no contexto fornecido pelo sistema, mesmo que o usuário insista em um número diferente.
4. Sempre que listar os cavalos cadastrados, liste TODOS eles, não apenas uma parte.
5. Quando fornecer recomendações, enfatize que são apenas orientações gerais e que um veterinário equino deve ser consultado para casos específicos.
6. Evite linguagem técnica demais, prefira explicações mais acessíveis.
7. Não dê conselhos sobre medicamentos e dosagens específicas.
8. Quando falar sobre um cavalo específico, use as informações fornecidas sobre ele.
9. Seja respeitoso e profissional, mas também amigável.
10. IMPORTANTE: O sistema não impõe restrições de intervalo entre vacinações ou procedimentos. Os usuários podem agendar vacinações para qualquer data que desejarem. Nunca indique que não é possível agendar uma vacinação devido a uma vacinação anterior.
11. Não crie regras que não existem no sistema. Se o usuário quiser agendar qualquer procedimento, sempre responda afirmativamente, indicando que é possível realizar o agendamento.

Suas respostas devem ser bem estruturadas, com parágrafos quando apropriado, e não devem exceder 4-5 parágrafos no máximo.
`;

/**
 * Interface para as mensagens do chat
 */
interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

/**
 * Gera uma resposta utilizando a API do OpenAI
 * @param messages Lista de mensagens de contexto e a pergunta do usuário
 * @param horse_id ID do cavalo, se aplicável, para fornecer contexto específico
 * @returns A resposta do assistente virtual
 */
/**
 * Detecta a intenção do usuário a partir de uma mensagem
 * @param message Mensagem do usuário
 * @returns Objeto com a intenção detectada e o tipo (query ou action)
 */
export async function detectIntent(message: string): Promise<{ intent: string, type: 'query' | 'action' }> {
  try {
    const intentPrompt = `Analise a seguinte mensagem do usuário relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Identifique a intenção principal e classifique-a em uma das seguintes categorias:
    - Registro de peso
    - Vacinação
    - Treinamento
    - Alimentação
    - Ferrageamento
    - Consulta veterinária
    - Cadastro de cavalo
    - Reprodução
    - Consulta de informação
    
    Além disso, classifique se é uma CONSULTA (o usuário só quer informação) ou uma AÇÃO (o usuário quer executar algo no sistema).
    
    Responda em formato JSON:
    {
      "intent": "categoria escolhida",
      "type": "query ou action"
    }`;

    const client = await getOpenAI();
    const response = await client.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: intentPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    const result = JSON.parse(response.choices[0].message.content || '{"intent": "Consulta de informação", "type": "query"}');
    
    return {
      intent: result.intent,
      type: result.type as 'query' | 'action'
    };
  } catch (error) {
    console.error("Erro ao detectar intenção:", error);
    return {
      intent: "Consulta de informação",
      type: "query"
    };
  }
}

/**
 * Extrai entidades relevantes de uma mensagem do usuário
 * @param message Mensagem do usuário
 * @returns Objeto com as entidades extraídas
 */
export async function extractEntities(message: string): Promise<Record<string, any>> {
  try {
    const entityPrompt = `Extraia as informações relevantes da seguinte mensagem relacionada a um sistema de gestão de cavalos:
    "${message}"
    
    Extraia as seguintes entidades, se presentes:
    - nome_cavalo: nome do cavalo mencionado
    - tipo_acao: tipo de ação a ser realizada (vacinar, pesar, treinar, etc.)
    - data: qualquer data mencionada
    - valor_peso: valor numérico de peso, se mencionado (somente o número)
    - valor_altura: valor numérico de altura, se mencionado (somente o número)
    - tipo_vacina: nome da vacina mencionada
    
    Responda em formato JSON, incluindo apenas os campos encontrados:
    {
      "nome_cavalo": "nome encontrado",
      "tipo_acao": "ação encontrada",
      etc.
    }`;

    const client = await getOpenAI();
    const response = await client.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: entityPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  } catch (error) {
    console.error("Erro ao extrair entidades:", error);
    return {};
  }
}

/**
 * Gera uma resposta utilizando a API do OpenAI
 * @param messages Lista de mensagens de contexto e a pergunta do usuário
 * @param horse_id ID do cavalo, se aplicável, para fornecer contexto específico
 * @returns A resposta do assistente virtual
 */
export async function generateChatResponse(
  messages: ChatMessage[],
  horse_id: number | null = null,
  user_id: number
): Promise<string> {
  try {
    // Se um cavalo específico foi selecionado, buscar suas informações
    let horseContext = "";
    
    if (horse_id) {
      try {
        const horse = await storage.getCavalo(parseInt(horse_id.toString()), user_id); // Usar o user_id correto
        
        if (horse) {
          horseContext = `
          Informações sobre o cavalo selecionado:
          Nome: ${horse.name}
          Raça: ${horse.breed}
          Nascimento: ${horse.birth_date}
          Sexo: ${horse.sexo || "Não informado"}
          Altura: ${horse.altura ? `${horse.altura} cm` : "Não informada"}
          Peso: ${horse.peso ? `${horse.peso} kg` : "Não informado"}
          Status: ${horse.status || "Ativo"}
          Observações: ${horse.notes || "Sem observações adicionais"}
          `;

          // Adicionar dados de manejos, se existirem
          try {
            const manejos = await storage.getManejosByHorse(horse.id, user_id);
            if (manejos && manejos.length > 0) {
              horseContext += "\nManejos recentes:";
              
              // Limitar a 5 manejos mais recentes para não sobrecarregar o contexto
              const recentManejos = manejos.slice(0, 5);
              
              recentManejos.forEach(manejo => {
                horseContext += `\n- ${manejo.tipo} (${manejo.data}): ${manejo.observacoes || "Sem observações"}`;
              });
            }
          } catch (error) {
            // Silenciosamente ignorar erros nos manejos
          }
        }
      } catch (error) {
        console.error("Erro ao buscar dados do cavalo:", error);
      }
    }

    // Tentar processar a consulta localmente primeiro (economia de tokens)
    const lastUserMessage = messages[messages.length - 1]?.content || '';
    
    // Verificar se é uma solicitação de procedimento veterinário
    const vetRequest = detectVetRequest(lastUserMessage);
    if (vetRequest.detected) {
      console.log("[OpenAI] Detectada solicitação veterinária - informando sobre desenvolvimento");
      
      const horseName = vetRequest.data.horseName || 'seu cavalo';
      const tipo = vetRequest.data.tipo;
      
      return `🚧 **Funcionalidade em Desenvolvimento**

Detectei que você quer agendar ${tipo} para ${horseName}.

Atualmente, o sistema de agendamento veterinário está em desenvolvimento. Por enquanto, posso apenas fornecer informações sobre seus cavalos cadastrados.

Para agendar procedimentos veterinários, utilize a seção "Procedimentos Veterinários" no menu principal do sistema.

Posso ajudar com:
• Informações sobre seus cavalos
• Estatísticas do plantel  
• Consultas sobre dados cadastrados
• Navegação pelo sistema`;
    }
    
    // Verificar se a pergunta é relacionada aos cavalos
    const isHorseQuery = isHorseRelatedQuery(lastUserMessage);
    
    if (!isHorseQuery) {
      // Buscar dados do usuário para mostrar informações corretas
      const userData = await smartHorseCache.getData(user_id);
      
      // Incrementar contador de perguntas fora de contexto
      const currentAttempts = smartHorseCache.offTopicAttempts.get(user_id) || 0;
      
      if (currentAttempts >= smartHorseCache.maxOffTopicQuestions) {
        return `🚫 **Limite de Perguntas Atingido**

Você já fez ${smartHorseCache.maxOffTopicQuestions} perguntas fora do contexto equino.

Eu sou especializado em fornecer informações sobre:
• Seus cavalos cadastrados
• Dados do plantel
• Manejos e cuidados
• Estatísticas dos animais
• Navegação no sistema

Por favor, faça perguntas relacionadas aos seus cavalos ou ao sistema de gestão equina.`;
      }
      
      smartHorseCache.offTopicAttempts.set(user_id, currentAttempts + 1);
      const remaining = smartHorseCache.maxOffTopicQuestions - currentAttempts - 1;
      
      return `🐎 **Foco no Plantel Equino**

Sou especializado em informações sobre cavalos e gestão equina. 

Posso ajudar com:
• Informações dos seus ${userData?.estatisticas?.total || 0} cavalos
• Estatísticas do plantel
• Dados de manejos e cuidados
• Navegação no sistema

Perguntas fora de contexto restantes: ${remaining}

Como posso ajudar com seus cavalos?`;
    }
    
    // Tentar processar consulta localmente (apenas para perguntas relacionadas a cavalos)
    console.log("[OpenAI] Buscando dados para user_id:", user_id);
    const userData = await smartHorseCache.getData(user_id);
    const localResponse = processLocalQuery(userData, lastUserMessage);
    
    if (localResponse) {
      console.log("[OpenAI] Consulta processada localmente, economizando tokens");
      return localResponse;
    }
    
    // Se não conseguir processar localmente, usar os mesmos dados para contexto da IA
    if (!userData) {
      console.warn("[OpenAI] Não foi possível obter dados do usuário");
    }
    
    // Formatar contexto inteligente dos dados do usuário
    let databaseContext = "";
    if (userData) {
      const { cavalos, estatisticas } = userData;
      
      databaseContext = `
Dados do plantel do usuário (atualização: ${new Date().toLocaleString()}):
📊 ESTATÍSTICAS:
- Total de cavalos: ${estatisticas.total}
- Cavalos ativos: ${estatisticas.ativos}
- Cavalos inativos: ${estatisticas.inativos}
- Distribuição por sexo: ${estatisticas.porSexo.machos} machos, ${estatisticas.porSexo.femeas} fêmeas, ${estatisticas.porSexo.castrados} castrados
- Raças presentes: ${estatisticas.racas.join(', ') || 'Não informadas'}
- Total de manejos: ${estatisticas.totalManejos}
- Manejos recentes (7 dias): ${estatisticas.manejosRecentes}

🐎 CAVALOS (primeiros 10):`;
      
      // Mostrar detalhes dos primeiros 10 cavalos para contexto
      cavalos.slice(0, 10).forEach(cavalo => {
        databaseContext += `\n- ${cavalo.nome} (${cavalo.raca}, ${cavalo.sexo}, ${cavalo.idade}, Status: ${cavalo.status})`;
        if (cavalo.peso || cavalo.altura) {
          databaseContext += ` - ${cavalo.peso || ''} ${cavalo.altura || ''}`.trim();
        }
      });
      
      if (cavalos.length > 10) {
        databaseContext += `\n... e mais ${cavalos.length - 10} cavalos.`;
      }
    }
    
    // Preparar contexto inteligente e otimizado
    const systemMessage: ChatMessage = {
      role: "system",
      content: `${SYSTEM_CONTEXT}

${databaseContext || "Dados do usuário não disponíveis no momento."}

${horseContext || ""}

INSTRUÇÕES ESPECIAIS:
- Use os dados reais fornecidos acima para responder perguntas sobre cavalos
- Para consultas sobre cavalos específicos, referencie as informações detalhadas disponíveis
- Mantenha respostas concisas mas informativas
- Se os dados não estiverem disponíveis, informe que precisa de mais informações

FUNCIONALIDADES ESPECIAIS DA IA:
- Você pode criar procedimentos veterinários automaticamente quando o usuário solicitar
- Exemplos de comandos: "agendar vacinação para Trovão amanhã", "marcar consulta para Estrela dia 15"
- Tipos suportados: vacinação, vermifugação, exame, consulta, cirurgia, casqueamento
- Datas suportadas: hoje, amanhã, dia específico (ex: dia 15), próxima semana`
    };

    // Criar o array completo de mensagens
    const fullMessages: ChatMessage[] = [systemMessage, ...messages];

    // Fazer a chamada para a API do OpenAI
    // Usando o modelo GPT-4o mini conforme solicitação do usuário
    const client = await getOpenAI();
    const response = await client.chat.completions.create({
      model: MODEL, // Usando a constante MODEL que agora é "gpt-4o-mini"
      messages: fullMessages,
      temperature: 0.7, // Valor intermediário para criatividade vs determinismo
      max_tokens: 800, // Limitar o tamanho da resposta
    });

    // Retornar o conteúdo da resposta
    return response.choices[0].message.content || "Desculpe, não consegui processar sua solicitação.";
  } catch (error) {
    console.error("Erro na API do OpenAI:", error);
    
    // Verificar se o erro é relacionado à chave de API
    if (error instanceof Error) {
      if (error.message.includes("API key") || error.message.includes("authentication")) {
        console.error("[OpenAI] Erro de autenticação:", error.message);
        throw new Error("Erro de autenticação com a API do OpenAI. Verifique a chave de API.");
      }
      console.error("[OpenAI] Erro detalhado:", error.message);
      throw error; // Repassar o erro original para melhor diagnóstico
    }
    
    // Erro genérico para casos não-Error
    console.error("[OpenAI] Erro não estruturado:", JSON.stringify(error));
    throw new Error("Não foi possível conectar ao assistente virtual. Por favor, tente novamente mais tarde.");
  }
}