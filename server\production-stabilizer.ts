/**
 * Sistema de Estabilização de Produção - EquiGestor AI
 * Implementa correções críticas identificadas pela auditoria
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import NodeCache from 'node-cache';
import { logger } from './error-logger';

// Cache inteligente para dados frequentemente acessados
const intelligentCache = new NodeCache({ 
  stdTTL: 300, // 5 minutos para dados dinâmicos
  checkperiod: 60,
  maxKeys: 1000,
  useClones: false // Performance otimizada
});

// Cache específico para fotos de cavalos (evitar 404s repetidos)
const photoCache = new NodeCache({
  stdTTL: 3600, // 1 hora para evitar requisições 404 repetidas
  checkperiod: 300,
  maxKeys: 500
});

/**
 * Rate Limiter inteligente para diferentes endpoints
 */
export const intelligentRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minuto
  max: (req) => {
    // Diferentes limits baseados no endpoint
    if (req.path.includes('/photo')) return 30; // 30 fotos por minuto
    if (req.path.includes('/api/cavalos')) return 20; // 20 consultas de cavalos por minuto
    if (req.path.includes('/api/nutricao')) return 15; // 15 consultas nutrição por minuto
    return 60; // Default: 60 requests por minuto
  },
  message: {
    error: 'Rate limit excedido',
    retryAfter: '60 segundos'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Middleware para cache inteligente de requisições
 */
export const intelligentCacheMiddleware = (cacheDuration: number = 300) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Apenas cache para GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Gerar chave de cache baseada na URL e userId
    const userId = (req as any).userId || req.headers['user-id'];
    const cacheKey = `${req.path}_${userId}_${JSON.stringify(req.query)}`;
    
    // Verificar se existe cache válido
    const cachedData = intelligentCache.get(cacheKey);
    if (cachedData) {
      console.log(`[CACHE HIT] ${req.path} - userId: ${userId}`);
      return res.json(cachedData);
    }

    // Interceptar resposta para armazenar no cache
    const originalSend = res.json;
    res.json = function(data: any) {
      // Apenas cachear respostas de sucesso
      if (res.statusCode >= 200 && res.statusCode < 300) {
        intelligentCache.set(cacheKey, data, cacheDuration);
        console.log(`[CACHE SET] ${req.path} - userId: ${userId} - TTL: ${cacheDuration}s`);
      }
      return originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Middleware específico para otimizar requisições de fotos
 */
export const photoOptimizationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const horseId = req.params.horse_id || req.params.id;
  const cacheKey = `photo_${horseId}`;
  
  // Verificar se já sabemos que esta foto não existe
  const photoStatus = photoCache.get(cacheKey);
  if (photoStatus === 'NOT_FOUND') {
    console.log(`[PHOTO CACHE] 404 prevenido para cavalo ${horseId}`);
    return res.status(404).send();
  }

  // Interceptar resposta 404 para cachear
  const originalStatus = res.status;
  res.status = function(code: number) {
    if (code === 404) {
      photoCache.set(cacheKey, 'NOT_FOUND', 1800); // Cache 404s por 30 minutos
      console.log(`[PHOTO CACHE] Cacheando 404 para cavalo ${horseId}`);
    }
    return originalStatus.call(this, code);
  };

  next();
};

/**
 * Middleware para prevenir race conditions
 */
export const requestCoordinationMiddleware = (() => {
  const pendingRequests = new Map<string, Promise<any>>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    // Apenas coordenar requisições GET que podem ser duplicadas
    if (req.method !== 'GET') {
      return next();
    }

    const userId = (req as any).userId || req.headers['user-id'];
    const requestKey = `${req.path}_${userId}_${JSON.stringify(req.query)}`;
    
    // Verificar se já existe uma requisição pendente igual
    const pendingRequest = pendingRequests.get(requestKey);
    if (pendingRequest) {
      console.log(`[COORDINATION] Aguardando requisição existente: ${req.path}`);
      
      // Aguardar a requisição existente e retornar o mesmo resultado
      pendingRequest
        .then((result) => {
          res.json(result);
        })
        .catch((error) => {
          res.status(500).json({ error: 'Erro na requisição coordenada' });
        });
      
      return;
    }

    // Criar promessa para esta requisição
    const requestPromise = new Promise((resolve, reject) => {
      // Interceptar resposta para resolver a promessa
      const originalSend = res.json;
      res.json = function(data: any) {
        pendingRequests.delete(requestKey);
        resolve(data);
        return originalSend.call(this, data);
      };

      // Interceptar erros
      const originalStatus = res.status;
      res.status = function(code: number) {
        if (code >= 400) {
          pendingRequests.delete(requestKey);
          reject(new Error(`HTTP ${code}`));
        }
        return originalStatus.call(this, code);
      };

      next();
    });

    pendingRequests.set(requestKey, requestPromise);
    
    // Limpar requisições orfãs após timeout
    setTimeout(() => {
      pendingRequests.delete(requestKey);
    }, 30000); // 30 segundos timeout
  };
})();

/**
 * Otimizador de queries SQL
 */
export class QueryOptimizer {
  
  /**
   * Otimizar query de cavalos com SELECT específico
   */
  static optimizeCavalosQuery(userId: number, fields?: string[]): string {
    const defaultFields = [
      'id', 'name', 'breed', 'birth_date', 'sexo', 'pelagem_id', 
      'status', 'numero_registro', 'peso', 'altura'
    ];
    
    const selectedFields = fields || defaultFields;
    const fieldList = selectedFields.map(field => `"${field}"`).join(', ');
    
    return `
      SELECT ${fieldList} 
      FROM cavalos 
      WHERE "user_id" = $1 AND status = 'ativo'
      ORDER BY name ASC
      LIMIT 50
    `;
  }

  /**
   * Query batch para dados relacionados
   */
  static getBatchRelatedDataQuery(cavaloIds: number[]): string {
    const placeholders = cavaloIds.map((_, index) => `$${index + 2}`).join(', ');
    
    return `
      SELECT 
        cavalo_id,
        COUNT(*) as total_manejos,
        MAX(data_execucao) as ultimo_manejo
      FROM manejos 
      WHERE cavalo_id IN (${placeholders}) AND "user_id" = $1
      GROUP BY cavalo_id
    `;
  }
}

/**
 * Sistema de invalidação inteligente de cache
 */
export class CacheInvalidator {
  
  /**
   * Invalidar cache relacionado a um cavalo específico
   */
  static invalidateHorseCache(horseId: number, userId: number): void {
    const patterns = [
      `/api/cavalos_${userId}`,
      `/api/cavalos/${horseId}_${userId}`,
      `/api/nutricao/horse/${horseId}_${userId}`,
      `/api/manejos_${userId}`
    ];
    
    patterns.forEach(pattern => {
      const keys = intelligentCache.keys().filter(key => key.includes(pattern));
      keys.forEach(key => intelligentCache.del(key));
    });
    
    logger.info(`[CACHE INVALIDATE] Dados do cavalo ${horseId} invalidados`, `horseId: ${horseId}`);
  }

  /**
   * Invalidar cache por padrão
   */
  static invalidateByPattern(pattern: string): void {
    const keys = intelligentCache.keys().filter(key => key.includes(pattern));
    keys.forEach(key => intelligentCache.del(key));
    logger.info(`[CACHE INVALIDATE] Padrão ${pattern} invalidado (${keys.length} chaves)`, `pattern: ${pattern}, keys: ${keys.length}`);
  }
}

/**
 * Sistema de métricas de performance
 */
export class PerformanceMonitor {
  private static metrics = new Map<string, { count: number, totalTime: number, errors: number }>();

  static startTimer(endpoint: string): () => void {
    const startTime = process.hrtime.bigint();
    
    return () => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // ms
      
      const current = this.metrics.get(endpoint) || { count: 0, totalTime: 0, errors: 0 };
      current.count++;
      current.totalTime += duration;
      
      this.metrics.set(endpoint, current);
      
      if (duration > 1000) { // Log requisições lentas > 1s
        logger.warn(`[SLOW QUERY] ${endpoint} levou ${duration.toFixed(2)}ms`);
      }
    };
  }

  static recordError(endpoint: string): void {
    const current = this.metrics.get(endpoint) || { count: 0, totalTime: 0, errors: 0 };
    current.errors++;
    this.metrics.set(endpoint, current);
  }

  static getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((value, key) => {
      result[key] = {
        requests: value.count,
        avgTime: value.count > 0 ? (value.totalTime / value.count).toFixed(2) + 'ms' : '0ms',
        errorRate: value.count > 0 ? ((value.errors / value.count) * 100).toFixed(2) + '%' : '0%',
        totalErrors: value.errors
      };
    });
    
    return result;
  }
}

export {
  intelligentCache,
  photoCache
};