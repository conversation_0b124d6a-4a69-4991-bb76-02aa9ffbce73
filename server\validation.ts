import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { createError } from './error-handler';
import { validarSenha, calcularForcaSenha } from './senha-validator';

/**
 * Middleware para validação de entrada usando schemas Zod
 * 
 * @param schema Schema Zod para validação
 * @param source Parte da requisição a ser validada ('body', 'query', 'params')
 */
export function validate(schema: z.ZodTypeAny, source: 'body' | 'query' | 'params' = 'body') {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validar os dados da fonte especificada
      const result = await schema.safeParseAsync(req[source]);
      
      if (!result.success) {
        // Em caso de erro de validação, enviar resposta formatada
        return next(createError.validation(
          'Erro de validação nos dados enviados', 
          result.error.format()
        ));
      }
      
      // Substituir os dados originais pelos dados validados e transformados
      req[source] = result.data;
      next();
    } catch (error) {
      // Capturar erros inesperados no processo de validação
      next(createError.validation(
        'Erro ao processar validação de dados',
        { message: error instanceof Error ? error.message : String(error) }
      ));
    }
  };
}

/**
 * Regras de validação de senha
 */
export const passwordRules = {
  minLength: 8,
  maxLength: 100,
  requireUppercase: true,
  requireLowercase: true,
  requireNumber: true,
  requireSpecial: true,
  minEntropy: 50 // Nível de entropia mínimo desejado para senhas
};

/**
 * Calcula a entropia aproximada de uma senha
 * Quanto maior, mais segura é a senha
 * @param password Senha para calcular entropia
 */
function calculatePasswordEntropy(password: string): number {
  // Tamanho do conjunto de caracteres
  let poolSize = 0;
  
  // Verificar cada tipo de caractere
  const hasUpper = /[A-Z]/.test(password);
  const hasLower = /[a-z]/.test(password);
  const hasDigit = /[0-9]/.test(password);
  const hasSpecial = /[^A-Za-z0-9]/.test(password); // Caracteres especiais
  
  if (hasUpper) poolSize += 26; // A-Z
  if (hasLower) poolSize += 26; // a-z
  if (hasDigit) poolSize += 10; // 0-9
  if (hasSpecial) poolSize += 33; // símbolos comuns aproximadamente
  
  // Entropia = comprimento da senha * log2(tamanho do conjunto)
  // log2(x) = ln(x) / ln(2)
  const entropy = password.length * (Math.log(poolSize) / Math.log(2));
  
  return entropy;
}

/**
 * Schemas de validação para autenticação
 */
export const authValidation = {
  login: z.object({
    username: z.string()
      .min(3, { message: "Nome de usuário deve ter pelo menos 3 caracteres" })
      .max(50, { message: "Nome de usuário não pode exceder 50 caracteres" })
      .regex(/^[a-zA-Z0-9_\-\.]+$/, {
        message: "Nome de usuário deve conter apenas caracteres alfanuméricos, underscores, hífens e pontos"
      })
      .trim(),
    password: z.string()
      .min(6, { message: "Senha deve ter pelo menos 6 caracteres" })
      .max(100, { message: "Senha não pode exceder 100 caracteres" })
  }),
  signup: z.object({
    name: z.string().min(1).max(100).trim(),
    username: z.string()
      .min(3, { message: "Nome de usuário deve ter pelo menos 3 caracteres" })
      .max(50, { message: "Nome de usuário não pode exceder 50 caracteres" })
      .regex(/^[a-zA-Z0-9_\-\.]+$/, {
        message: "Nome de usuário deve conter apenas caracteres alfanuméricos, underscores, hífens e pontos"
      })
      .trim(),
    email: z.string().email().max(255).trim(),
    password: z.string()
      .min(6, { message: "Senha deve ter pelo menos 6 caracteres" })
      .max(100, { message: "Senha não pode exceder 100 caracteres" })
  }),
  
  // Validação para criação de senha usando o novo validador
  password: z.string()
    .min(passwordRules.minLength, { 
      message: `Senha deve ter pelo menos ${passwordRules.minLength} caracteres` 
    })
    .max(passwordRules.maxLength, { 
      message: `Senha não pode exceder ${passwordRules.maxLength} caracteres` 
    })
    .refine(
      (password) => {
        // Usar o novo validador de senha
        const resultado = validarSenha(password);
        return resultado.valida;
      },
      {
        message: "Senha não atende aos requisitos de segurança",
        params: { 
          detalhes: "A senha deve conter letras maiúsculas, minúsculas, números e caracteres especiais."
        }
      }
    )
    .refine(
      (password) => calcularForcaSenha(password) >= 60, // Exigir pelo menos força média-alta
      { 
        message: "Senha muito fraca. Use uma combinação mais complexa de caracteres e evite sequências comuns.",
        params: { 
          dica: "Combine letras, números e símbolos de forma não previsível. Evite sequências óbvias como '123456' ou 'password'."
        }
      }
    ),

  token: z.object({
    token: z.string()
      .min(10, { message: "Token deve ter no mínimo 10 caracteres" })
      .max(256, { message: "Token não pode exceder 256 caracteres" })
      .regex(/^[a-zA-Z0-9_\-\.]+$/, { 
        message: "Token deve conter apenas caracteres alfanuméricos, underscores, hífens e pontos" 
      })
  }),

  idParam: z.object({
    id: z.coerce.number().int().positive()
  })
};

/**
 * Schemas de validação para registro ABCCC
 */
export const abcccValidation = {
  token: z.object({
    token: z.string()
      .min(32, { message: "Token ABCCC deve ter no mínimo 32 caracteres" })
      .max(64, { message: "Token ABCCC não pode exceder 64 caracteres" })
      .regex(/^[a-zA-Z0-9_\-\.]+$/, { 
        message: "Token ABCCC deve conter apenas caracteres alfanuméricos, underscores, hífens e pontos" 
      })
  }),

  registroId: z.object({
    registroId: z.string()
      .min(5, { message: "Registro ABCCC deve ter no mínimo 5 caracteres" })
      .regex(/^[A-Z0-9]+$/, { 
        message: "Registro ABCCC deve conter apenas letras maiúsculas e números" 
      })
  })
};

/**
 * Schemas de validação para cavalos
 */
export const cavaloValidation = {
  create: z.object({
    nome: z.string()
      .min(2, { message: "Nome deve ter pelo menos 2 caracteres" })
      .max(100, { message: "Nome não pode exceder 100 caracteres" })
      .trim(),
      
    breed: z.string()
      .min(2, { message: "Raça deve ter pelo menos 2 caracteres" })
      .max(50, { message: "Raça não pode exceder 50 caracteres" })
      .optional(),
      
    sexo: z.enum([
      "macho", "femea", "Castrado", "garanhao", "femea", "nao_informado"
    ], {
      errorMap: () => ({ message: "Sexo do cavalo inválido" })
    }).default("nao_informado"),
    
    status: z.enum([
      "nao_informado", "ativo", "vendido", "falecido", "doado", 
      "emprestado", "reservado", "reprodução", "tratamento"
    ], {
      errorMap: () => ({ message: "Status inválido" })
    }).default("nao_informado"),
    
    birth_date: z.coerce.date().nullable().optional(),
    data_entrada: z.coerce.date().nullable().optional(),
    data_saida: z.coerce.date().nullable().optional(),
    data_compra: z.coerce.date().nullable().optional(),
    
    user_id: z.number().int().positive(),
    
    is_external: z.boolean().optional().default(false),
    
    // Outros campos opcionais
    pelagem: z.string().optional(),
    registroAbccc: z.string().optional(),
    cor: z.string().optional(),
    altura: z.number().positive().optional(),
    peso: z.number().positive().optional(),
    proprietario: z.string().optional(),
    criador: z.string().optional(),
    coatColor: z.string().optional(),
    observacoes: z.string().optional(),
    
    // Campos para genealogia
    pai: z.string().optional(),
    mae: z.string().optional(),
    pai_id: z.number().nullable().optional(),
    mae_id: z.number().nullable().optional(),
  }),
  
  update: z.object({
    nome: z.string()
      .min(2, { message: "Nome deve ter pelo menos 2 caracteres" })
      .max(100, { message: "Nome não pode exceder 100 caracteres" })
      .optional(),
      
    breed: z.string()
      .min(2, { message: "Raça deve ter pelo menos 2 caracteres" })
      .max(50, { message: "Raça não pode exceder 50 caracteres" })
      .optional(),
      
    sexo: z.enum([
      "macho", "femea", "Castrado", "garanhao", "femea", "nao_informado"
    ], {
      errorMap: () => ({ message: "Sexo do cavalo inválido" })
    }).optional(),
    
    status: z.enum([
      "nao_informado", "ativo", "vendido", "falecido", "doado", 
      "emprestado", "reservado", "reprodução", "tratamento"
    ], {
      errorMap: () => ({ message: "Status inválido" })
    }).optional(),
    
    birth_date: z.coerce.date().nullable().optional(),
    data_entrada: z.coerce.date().nullable().optional(),
    data_saida: z.coerce.date().nullable().optional(),
    data_compra: z.coerce.date().nullable().optional(),
    
    user_id: z.number().int().positive().optional(),
    
    is_external: z.boolean().optional(),
    
    // Outros campos opcionais
    pelagem: z.string().optional(),
    registroAbccc: z.string().optional(),
    cor: z.string().optional(),
    altura: z.number().positive().optional(),
    peso: z.number().positive().optional(),
    proprietario: z.string().optional(),
    criador: z.string().optional(),
    coatColor: z.string().optional(),
    observacoes: z.string().optional(),
    
    // Campos para genealogia
    pai: z.string().optional(),
    mae: z.string().optional(),
    pai_id: z.number().nullable().optional(),
    mae_id: z.number().nullable().optional(),
  }),
  
  search: z.object({
    q: z.string().optional(),
    nome: z.string().optional(),
    breed: z.string().optional(),
    status: z.string().optional(),
    sexo: z.string().optional(),
    registroAbccc: z.string().optional(),
    proprietario: z.string().optional(),
    criador: z.string().optional(),
    page: z.coerce.number().int().positive().optional().default(1),
    limit: z.coerce.number().int().positive().max(100).optional().default(20),
    sortBy: z.string().optional().default('nome'),
    sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
  }),

  updateStatus: z.object({
    status: z.string(),
    reason: z.string().optional()
  })
};