/**
 * Configuração MVP - Controle de Visibilidade de Módulos
 * Esta configuração permite ocultar módulos específicos na versão MVP
 * sem deletar as funções - apenas controlando a visibilidade
 */

export interface MVPConfig {
  // Genética - Módulos para ocultar
  genetics: {
    simuladorHeranca: boolean;
    desempenho: boolean;
    sugestoesCruzamento: boolean;
  };
  
  // Relatórios - Tab completa
  relatorios: {
    enabled: boolean;
  };
  
  // Outros módulos que podem ser controlados futuramente
  modules: {
    financeiro: boolean;
    veterinario: boolean;
    nutricao: boolean;
    manejos: boolean;
    morfologia: boolean;
    genealogia: boolean;
  };
}

/**
 * Configuração MVP atual
 * Alterando estes valores, os módulos ficam visíveis/ocultos
 */
export const MVP_CONFIG: MVPConfig = {
  genetics: {
    simuladorHeranca: false,    // Oculto na versão MVP
    desempenho: false,          // Oculto na versão MVP
    sugestoesCruzamento: false, // Oculto na versão MVP
  },
  relatorios: {
    enabled: false,             // Tab completa oculta na versão MVP
  },
  modules: {
    financeiro: true,
    veterinario: true,
    nutricao: true,
    manejos: true,
    morfologia: true,
    genealogia: true,
  },
};

/**
 * Helper functions para verificar visibilidade
 */
export const isModuleVisible = {
  simuladorHeranca: () => MVP_CONFIG.genetics.simuladorHeranca,
  desempenho: () => MVP_CONFIG.genetics.desempenho,
  sugestoesCruzamento: () => MVP_CONFIG.genetics.sugestoesCruzamento,
  relatorios: () => MVP_CONFIG.relatorios.enabled,
  
  // Módulos gerais
  financeiro: () => MVP_CONFIG.modules.financeiro,
  veterinario: () => MVP_CONFIG.modules.veterinario,
  nutricao: () => MVP_CONFIG.modules.nutricao,
  manejos: () => MVP_CONFIG.modules.manejos,
  morfologia: () => MVP_CONFIG.modules.morfologia,
  genealogia: () => MVP_CONFIG.modules.genealogia,
};

/**
 * Função para debug - mostra status atual dos módulos
 */
export const getMVPStatus = () => {
  return {
    version: "MVP v1.0",
    hiddenModules: {
      "Simulador de Herança Genética": !MVP_CONFIG.genetics.simuladorHeranca,
      "Desempenho": !MVP_CONFIG.genetics.desempenho,
      "Sugestões de Cruzamento": !MVP_CONFIG.genetics.sugestoesCruzamento,
      "Relatórios (tab completa)": !MVP_CONFIG.relatorios.enabled,
    },
    visibleModules: Object.entries(MVP_CONFIG.modules)
      .filter(([_, visible]) => visible)
      .map(([module, _]) => module),
  };
};