import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from './use-auth';

// Tipos para as entidades financeiras
export interface CategoriaFinanceira {
  id: number;
  nome: string;
  tipo: 'receita' | 'despesa';
  descricao?: string;
  ativo: boolean;
  user_id: number;
  created_at: string;
}

export interface LancamentoFinanceiro {
  id: number;
  data: string;
  tipo: 'receita' | 'despesa';
  categoria_id: number;
  descricao: string;
  valor: number;
  cavalo_id?: number;
  observacoes?: string;
  user_id: number;
  created_at: string;
  categoria_nome?: string;
  cavalo_nome?: string;
}

export interface RelatorioMensal {
  periodo: { mes: number; ano: number };
  resumo: {
    receitas: number;
    despesas: number;
    saldo: number;
  };
  porCategoria: Array<{
    categoria_id: number;
    categoria_nome: string;
    tipo: 'receita' | 'despesa';
    total: number;
  }>;
  porCavalo: Array<{
    cavalo_id: number;
    cavalo_nome: string;
    tipo: 'receita' | 'despesa';
    total: number;
  }>;
}

// Parâmetros para filtros
export interface FiltrosLancamentos {
  dataInicio?: string;
  dataFim?: string;
  tipo?: 'receita' | 'despesa';
  categoria_id?: number;
  cavalo_id?: number;
}

export function useCategorias() {
  const { user } = useAuth();
  
  return useQuery<CategoriaFinanceira[]>({
    queryKey: ['/api/financeiro/categorias', user?.id],
    queryFn: () => apiRequest('/api/financeiro/categorias', 'GET'),
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });
}

export function useLancamentos(filtros?: FiltrosLancamentos) {
  const { user } = useAuth();
  
  // Construir query string
  const queryParams = new URLSearchParams();
  if (filtros?.dataInicio) queryParams.append('dataInicio', filtros.dataInicio);
  if (filtros?.dataFim) queryParams.append('dataFim', filtros.dataFim);
  if (filtros?.tipo) queryParams.append('tipo', filtros.tipo);
  if (filtros?.categoria_id) queryParams.append('categoria_id', filtros.categoria_id.toString());
  if (filtros?.cavalo_id) queryParams.append('cavalo_id', filtros.cavalo_id.toString());
  
  const url = `/api/financeiro/lancamentos${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  
  return useQuery<LancamentoFinanceiro[]>({
    queryKey: ['/api/financeiro/lancamentos', user?.id, filtros],
    queryFn: () => apiRequest(url, 'GET'),
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 2, // 2 minutos
  });
}

export function useRelatorioMensal(mes: number, ano: number) {
  const { user } = useAuth();
  
  return useQuery<RelatorioMensal>({
    queryKey: ['/api/financeiro/relatorio-mensal', user?.id, mes, ano],
    queryFn: () => apiRequest(`/api/financeiro/relatorio-mensal?mes=${mes}&ano=${ano}`, 'GET'),
    enabled: !!user?.id && mes > 0 && mes <= 12 && ano > 2000,
    staleTime: 1000 * 60 * 10, // 10 minutos
  });
}

export function useCreateCategoria() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: (data: Omit<CategoriaFinanceira, 'id' | 'user_id' | 'created_at'>) =>
      apiRequest('/api/financeiro/categorias', 'POST', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/categorias', user?.id] });
    },
  });
}

export function useUpdateCategoria() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<CategoriaFinanceira> }) =>
      apiRequest(`/api/financeiro/categorias/${id}`, 'PUT', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/categorias', user?.id] });
    },
  });
}

export function useDeleteCategoria() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: (id: number) =>
      apiRequest(`/api/financeiro/categorias/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/categorias', user?.id] });
    },
  });
}

export function useCreateLancamento() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: (data: Omit<LancamentoFinanceiro, 'id' | 'user_id' | 'created_at' | 'categoria_nome' | 'cavalo_nome'>) =>
      apiRequest('/api/financeiro/lancamentos', 'POST', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/lancamentos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/relatorio-mensal', user?.id] });
    },
  });
}

export function useUpdateLancamento() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<LancamentoFinanceiro> }) =>
      apiRequest(`/api/financeiro/lancamentos/${id}`, 'PUT', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/lancamentos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/relatorio-mensal', user?.id] });
    },
  });
}

export function useDeleteLancamento() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: (id: number) =>
      apiRequest(`/api/financeiro/lancamentos/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/lancamentos', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/relatorio-mensal', user?.id] });
    },
  });
}

export function useInitCategoriasPadrao() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: () => apiRequest('/api/financeiro/init-categorias', 'POST'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/financeiro/categorias', user?.id] });
    },
  });
}