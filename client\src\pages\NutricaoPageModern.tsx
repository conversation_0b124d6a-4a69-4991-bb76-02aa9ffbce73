import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2,
  Utensils,
  CalendarDays,
  DollarSign,
  Clock,
  Scale,
  Clipboard,
  Apple,
  ChefHat,
  Banknote
} from 'lucide-react';

// Interface atualizada para o registro de nutrição
interface Nutricao {
  id: number;
  cavalo_id: number;
  tipo_alimento: string;
  quantidade: number;
  unidade: string;
  frequencia: string;
  horario_alimentacao?: string;
  observacoes?: string;
  custo_mensal?: number;
  data_inicio: string;
  data_fim?: string;
  status?: string;
  user_id: number;
  created_at: string;
}

// Schema de validação moderno
const nutricaoFormSchema = z.object({
  cavalo_id: z.number({
    required_error: "Selecione um animal",
  }),
  data_inicio: z.string().min(1, { message: "Selecione uma data" }),
  tipo_alimento: z.string().min(1, { message: "Selecione o tipo de alimento" }),
  quantidade: z.coerce.number().min(0.01, { message: "Quantidade deve ser maior que zero" }),
  unidade: z.string().min(1, { message: "Selecione a unidade" }),
  frequencia: z.string().min(1, { message: "Informe a frequência" }),
  horario_alimentacao: z.string().optional(),
  custo_mensal: z.coerce.number().min(0, { message: "Custo deve ser um valor positivo" }).optional(),
  observacoes: z.string().optional(),
  data_fim: z.string().optional(),
  status: z.string().default("ativo"),
});

type NutricaoFormValues = z.infer<typeof nutricaoFormSchema>;

const NutricaoPageModern = () => {
  const [selectedHorseId, setSelectedHorseId] = useState<number | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedNutricao, setSelectedNutricao] = useState<Nutricao | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);
  const { toast } = useToast();

  // Formulário moderno
  const form = useForm<NutricaoFormValues>({
    resolver: zodResolver(nutricaoFormSchema),
    defaultValues: {
      cavalo_id: 0,
      data_inicio: format(new Date(), 'yyyy-MM-dd'),
      tipo_alimento: "",
      quantidade: 0,
      unidade: "kg",
      frequencia: "2x ao dia",
      horario_alimentacao: "07:00",
      custo_mensal: 0,
      observacoes: "",
      status: "ativo",
    },
  });

  // Buscar cavalos
  const { data: cavalos = [], isLoading: isLoadingCavalos } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      return await apiRequest<Cavalo[]>('/api/cavalos', 'GET');
    }
  });

  // Buscar registros de nutrição
  const { data: nutricoesData = [], isLoading: isLoadingNutricao, refetch: refetchNutricao } = useQuery({
    queryKey: ['/api/nutricao/horse', selectedHorseId],
    enabled: !!selectedHorseId,
    staleTime: 0, // Sempre considerar dados como stale para forçar refetch
    gcTime: 0, // Não manter cache por muito tempo
    queryFn: async () => {
      if (selectedHorseId) {
        console.log("Buscando nutrições para cavalo:", selectedHorseId);
        return await apiRequest<Nutricao[]>(`/api/nutricao/horse/${selectedHorseId}`, 'GET');
      }
      return [];
    }
  });

  // Filtrar registros
  const nutricoes = nutricoesData.filter(nutricao => {
    let matchesSearch = true;
    let matchesFilter = true;

    if (searchTerm) {
      matchesSearch = Boolean(
        nutricao.tipo_alimento?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (nutricao.observacoes && nutricao.observacoes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (filterType) {
      matchesFilter = nutricao.tipo_alimento === filterType;
    }

    return matchesSearch && matchesFilter;
  });

  // Mutação para criar nutrição
  const createNutricaoMutation = useMutation({
    mutationFn: async (data: NutricaoFormValues) => {
      return await apiRequest<Nutricao>('/api/nutricao', 'POST', data);
    },
    onSuccess: () => {
      console.log("Nutrição criada com sucesso, recarregando dados...");
      setIsAddDialogOpen(false);
      form.reset();
      toast({
        title: "Sucesso",
        description: "Registro de nutrição criado com sucesso",
      });
      // Força reload da tela após pequeno delay
      setTimeout(() => {
        refetchNutricao();
        console.log("Dados recarregados após criar nutrição");
      }, 100);
    },
    onError: (error) => {
      console.error("Erro ao criar nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível criar o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Mutação para atualizar nutrição
  const updateNutricaoMutation = useMutation({
    mutationFn: async (data: NutricaoFormValues & { id: number }) => {
      const { id, ...updateData } = data;
      return await apiRequest<Nutricao>(`/api/nutricao/${id}`, 'PUT', updateData);
    },
    onSuccess: () => {
      console.log("Nutrição atualizada com sucesso, recarregando dados...");
      setIsEditDialogOpen(false);
      setSelectedNutricao(null);
      form.reset();
      toast({
        title: "Sucesso",
        description: "Registro de nutrição atualizado com sucesso",
      });
      // Força reload da tela após pequeno delay
      setTimeout(() => {
        refetchNutricao();
        console.log("Dados recarregados após atualizar nutrição");
      }, 100);
    },
    onError: (error) => {
      console.error("Erro ao atualizar nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Mutação para excluir nutrição
  const deleteNutricaoMutation = useMutation({
    mutationFn: async (id: number) => {
      return await apiRequest<void>(`/api/nutricao/${id}`, 'DELETE');
    },
    onSuccess: () => {
      console.log("Nutrição excluída com sucesso, recarregando dados...");
      toast({
        title: "Sucesso",
        description: "Registro de nutrição excluído com sucesso",
      });
      // Força reload da tela após pequeno delay
      setTimeout(() => {
        refetchNutricao();
        console.log("Dados recarregados após excluir nutrição");
      }, 100);
    },
    onError: (error) => {
      console.error("Erro ao excluir nutrição:", error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o registro de nutrição",
        variant: "destructive",
      });
    }
  });

  // Selecionar primeiro cavalo automaticamente
  useEffect(() => {
    if (cavalos.length > 0 && !selectedHorseId && cavalos[0]) {
      setSelectedHorseId(cavalos[0].id);
    }
  }, [cavalos, selectedHorseId]);

  // Resetar form com valores selecionados
  useEffect(() => {
    if (selectedHorseId) {
      form.setValue('cavalo_id', selectedHorseId);
    }
  }, [selectedHorseId, form]);

  // Handlers
  const handleSubmit = (data: NutricaoFormValues) => {
    if (selectedNutricao) {
      updateNutricaoMutation.mutate({ ...data, id: selectedNutricao.id });
    } else {
      createNutricaoMutation.mutate(data);
    }
  };

  const handleEdit = (nutricao: Nutricao) => {
    setSelectedNutricao(nutricao);
    form.reset({
      cavalo_id: nutricao.cavalo_id,
      data_inicio: nutricao.data_inicio,
      tipo_alimento: nutricao.tipo_alimento,
      quantidade: nutricao.quantidade,
      unidade: nutricao.unidade,
      frequencia: nutricao.frequencia,
      horario_alimentacao: nutricao.horario_alimentacao || "",
      custo_mensal: nutricao.custo_mensal || 0,
      observacoes: nutricao.observacoes || "",
      data_fim: nutricao.data_fim || "",
      status: nutricao.status || "ativo",
    });
    setIsEditDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm("Tem certeza que deseja excluir este registro de nutrição?")) {
      deleteNutricaoMutation.mutate(id);
    }
  };

  // Obter tipos únicos de alimento
  const tiposAlimento = Array.from(
    new Set(nutricoesData.map(nutricao => nutricao.tipo_alimento))
  );

  const selectedHorse = cavalos.find(c => c.id === selectedHorseId);

  if (isLoadingCavalos) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header moderno */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Nutrição</h1>
          <p className="text-muted-foreground">
            Gerencie a alimentação e nutrição dos animais
          </p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Novo Registro
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      {/* Cards de estatísticas */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total de Registros
            </CardTitle>
            <Clipboard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{nutricoes.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tipos de Alimento
            </CardTitle>
            <Apple className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tiposAlimento.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Custo Mensal Total
            </CardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              R$ {nutricoes.reduce((total, n) => total + (Number(n.custo_mensal) || 0), 0).toFixed(2)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Animal Selecionado
            </CardTitle>
            <ChefHat className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold truncate">
              {selectedHorse?.name || "Nenhum"}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Seletor de cavalo e filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Animal</label>
              <Select
                value={selectedHorseId?.toString() || ""}
                onValueChange={(value) => setSelectedHorseId(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um animal" />
                </SelectTrigger>
                <SelectContent>
                  {cavalos.map((cavalo) => (
                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                      {cavalo.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Buscar</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por tipo de alimento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Filtrar por tipo</label>
              <Select value={filterType || "all"} onValueChange={(value) => setFilterType(value === "all" ? null : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os tipos</SelectItem>
                  {tiposAlimento.map((tipo) => (
                    <SelectItem key={tipo} value={tipo}>
                      {tipo}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de registros modernizada */}
      {isLoadingNutricao ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : nutricoes.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Utensils className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhum registro encontrado</h3>
            <p className="text-muted-foreground text-center mb-4">
              {selectedHorseId 
                ? "Não há registros de nutrição para este animal."
                : "Selecione um animal para ver os registros de nutrição."
              }
            </p>
            {selectedHorseId && (
              <Button onClick={() => setIsAddDialogOpen(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Criar Primeiro Registro
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {nutricoes.map((nutricao) => (
            <Card key={nutricao.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{nutricao.tipo_alimento}</CardTitle>
                  <Badge variant={nutricao.status === 'ativo' ? 'default' : 'secondary'}>
                    {nutricao.status || 'ativo'}
                  </Badge>
                </div>
                <CardDescription>
                  {format(new Date(nutricao.data_inicio), 'dd/MM/yyyy', { locale: ptBR })}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Scale className="h-4 w-4 text-muted-foreground" />
                  <span>{nutricao.quantidade} {nutricao.unidade}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{nutricao.frequencia}</span>
                </div>

                {nutricao.horario_alimentacao && (
                  <div className="flex items-center gap-2 text-sm">
                    <CalendarDays className="h-4 w-4 text-muted-foreground" />
                    <span>{nutricao.horario_alimentacao}</span>
                  </div>
                )}

                {nutricao.custo_mensal && (
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span>R$ {Number(nutricao.custo_mensal).toFixed(2)}/mês</span>
                  </div>
                )}

                {nutricao.observacoes && (
                  <div className="text-sm text-muted-foreground border-t pt-2">
                    <p className="line-clamp-2">{nutricao.observacoes}</p>
                  </div>
                )}

                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(nutricao)}
                    className="flex-1 gap-1"
                  >
                    <Edit className="h-3 w-3" />
                    Editar
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(nutricao.id)}
                    className="flex-1 gap-1 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-3 w-3" />
                    Excluir
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialog de criação/edição */}
      <Dialog open={isAddDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddDialogOpen(false);
          setIsEditDialogOpen(false);
          setSelectedNutricao(null);
          form.reset();
        }
      }}>
        <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto w-full mx-2 sm:mx-0 p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>
              {selectedNutricao ? 'Editar' : 'Novo'} Registro de Nutrição
            </DialogTitle>
            <DialogDescription>
              Preencha as informações sobre a alimentação do animal.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="cavalo_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Animal</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione um animal" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {cavalos.map((cavalo) => (
                            <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                              {cavalo.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="data_inicio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data de Início</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="tipo_alimento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Alimento</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Ração">Ração</SelectItem>
                          <SelectItem value="Feno">Feno</SelectItem>
                          <SelectItem value="Aveia">Aveia</SelectItem>
                          <SelectItem value="Milho">Milho</SelectItem>
                          <SelectItem value="Capim">Capim</SelectItem>
                          <SelectItem value="Concentrado">Concentrado</SelectItem>
                          <SelectItem value="Suplemento">Suplemento</SelectItem>
                          <SelectItem value="Outro">Outro</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="frequencia"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Frequência</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a frequência" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1x ao dia">1x ao dia</SelectItem>
                          <SelectItem value="2x ao dia">2x ao dia</SelectItem>
                          <SelectItem value="3x ao dia">3x ao dia</SelectItem>
                          <SelectItem value="Ad libitum">Ad libitum</SelectItem>
                          <SelectItem value="Conforme necessário">Conforme necessário</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <FormField
                  control={form.control}
                  name="quantidade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantidade</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unidade"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unidade</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Unidade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="kg">Kg</SelectItem>
                          <SelectItem value="g">Gramas</SelectItem>
                          <SelectItem value="l">Litros</SelectItem>
                          <SelectItem value="ml">Mililitros</SelectItem>
                          <SelectItem value="unidade">Unidade</SelectItem>
                          <SelectItem value="punhado">Punhado</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="horario_alimentacao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Horário</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="custo_mensal"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Custo Mensal (R$)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="data_fim"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data Final (Opcional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="observacoes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Informações adicionais sobre a alimentação..." 
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Separator />

              <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setIsAddDialogOpen(false);
                    setIsEditDialogOpen(false);
                    setSelectedNutricao(null);
                    form.reset();
                  }}
                  className="w-full sm:w-auto"
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={createNutricaoMutation.isPending || updateNutricaoMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  {createNutricaoMutation.isPending || updateNutricaoMutation.isPending 
                    ? "Salvando..." 
                    : selectedNutricao ? "Atualizar" : "Criar"
                  }
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NutricaoPageModern;