import { useState, useCallback, useMemo } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Cavalo, ProcedimentoVet } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { format, addMonths, isBefore } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Hook personalizado para gerenciar procedimentos veterinários
 * 
 * Encapsula a lógica de busca, filtragem e manipulação de
 * procedimentos veterinários (consultas, vacinas, vermifugações)
 * seguindo as boas práticas de React e utilizando React Query.
 */
export function useVeterinario() {
  const queryClient = useQueryClient();
  // Estados para filtragem
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCavalo, setFilterCavalo] = useState('todos');
  const [filterTipo, setFilterTipo] = useState('todos');
  const [filterStatus, setFilterStatus] = useState('todos');
  


  // Consulta para buscar procedimentos veterinários
  const procedimentosQuery = useQuery({
    queryKey: ['/api/procedimentos-vet']
  });
  
  // Consulta para buscar cavalos
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos']
  });
  
  // Mutation para adicionar procedimento
  const addProcedimentoMutation = useMutation({
    mutationFn: (formData: any) => apiRequest('/api/procedimentos-vet', 'POST', formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Mutation para atualizar procedimento
  const updateProcedimentoMutation = useMutation({
    mutationFn: (formData: any) => {
      const { id, ...data } = formData;
      return apiRequest(`/api/procedimentos-vet/${id}`, 'PUT', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Mutation para excluir procedimento
  const deleteProcedimentoMutation = useMutation({
    mutationFn: (id: number) => apiRequest(`/api/procedimentos-vet/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/procedimentos-vet'] });
    },
  });
  
  // Função para filtrar as vacinações
  const vacinacoesFiltradas = useMemo(() => {
    if (!procedimentosQuery.data || !Array.isArray(procedimentosQuery.data)) return [];

    return procedimentosQuery.data
      .filter((proc: any) => 
        proc.tipo_procedimento?.toLowerCase().includes('vacina') || 
        proc.tipo?.toLowerCase().includes('vacina') || 
        proc.diagnostico?.toLowerCase().includes('vacina') ||
        proc.medicamentos?.toLowerCase().includes('vacina')
      )
      .filter((proc: any) => 
        (searchTerm === '' || 
          proc.diagnostico?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.medicamentos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario_responsavel?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || (proc.cavalo_id || proc.horse_id)?.toString() === filterCavalo) &&
        (filterStatus === 'todos' || getStatusVacinacao(proc) === filterStatus) &&
        (filterTipo === 'todos' || getTipoVacina(proc).toLowerCase().includes(filterTipo.toLowerCase()))
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterStatus, filterTipo]);
  
  // Função para filtrar as vermifugações
  const vermifugacoesFiltradas = useMemo(() => {
    if (!procedimentosQuery.data || !Array.isArray(procedimentosQuery.data)) return [];

    return procedimentosQuery.data
      .filter((proc: any) => 
        proc.tipo_procedimento?.toLowerCase().includes('vermifug') || 
        proc.tipo?.toLowerCase().includes('vermifug') || 
        proc.diagnostico?.toLowerCase().includes('vermifug') ||
        proc.medicamentos?.toLowerCase().includes('vermifug')
      )
      .filter((proc: any) => 
        (searchTerm === '' || 
          proc.diagnostico?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.medicamentos?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario_responsavel?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || (proc.cavalo_id || proc.horse_id)?.toString() === filterCavalo) &&
        (filterStatus === 'todos' || getStatusVermifugacao(proc) === filterStatus)
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterStatus]);
  
  // Função para filtrar os registros clínicos
  const registrosClinicosFiltrados = useMemo(() => {
    if (!procedimentosQuery.data || !Array.isArray(procedimentosQuery.data)) return [];

    return procedimentosQuery.data
      .filter((proc: any) => 
        proc.tipo_procedimento?.toLowerCase().includes('exame') || 
        proc.tipo_procedimento?.toLowerCase().includes('consult') ||
        proc.tipo_procedimento?.toLowerCase().includes('check') ||
        proc.tipo?.toLowerCase().includes('exame') || 
        proc.tipo?.toLowerCase().includes('consult') ||
        proc.tipo?.toLowerCase().includes('check')
      )
      .filter((proc: any) => 
        (searchTerm === '' || 
          proc.diagnostico?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario_responsavel?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          proc.veterinario?.toLowerCase().includes(searchTerm.toLowerCase())
        ) &&
        (filterCavalo === 'todos' || (proc.cavalo_id || proc.horse_id)?.toString() === filterCavalo) &&
        (filterTipo === 'todos' || (proc.tipo_procedimento || proc.tipo)?.toLowerCase().includes(filterTipo.toLowerCase()))
      );
  }, [procedimentosQuery.data, searchTerm, filterCavalo, filterTipo]);
  
  // Obter o nome do cavalo pelo ID
  const getCavaloName = useCallback((id: number) => {
    if (!cavalosQuery.data || !Array.isArray(cavalosQuery.data)) return 'N/A';
    const cavalo = cavalosQuery.data.find((c: Cavalo) => c.id === id);
    return cavalo ? cavalo.name : 'N/A';
  }, [cavalosQuery.data]);
  
  // Extrair o tipo de vacina da descrição ou medicamentos
  const getTipoVacina = useCallback((procedimento: any): string => {
    // Primeiro tenta extrair do campo medicamentos
    if (procedimento.medicamentos) {
      const match = procedimento.medicamentos.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.medicamentos.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Depois tenta do diagnóstico
    if (procedimento.diagnostico) {
      const match = procedimento.diagnostico.match(/vacina\s+de\s+([^\s,;.]+)/i) ||
                    procedimento.diagnostico.match(/contra\s+([^\s,;.]+)/i) ||
                    procedimento.diagnostico.match(/([^\s,;.]+)\s+vaccine/i);
      if (match) return match[1];
    }
    
    // Se não conseguir extrair, retorna o tipo genérico
    return 'Não especificada';
  }, []);
  
  // Determinar o status da vacinação
  const getStatusVacinacao = useCallback((procedimento: any): string => {
    // Usar proxima_consulta se disponível, senão calcular baseado na data do procedimento
    const proximaConsulta = procedimento.proxima_consulta;
    if (proximaConsulta) {
      try {
        const dataProximo = new Date(proximaConsulta);
        const hoje = new Date();
        
        // Se a data de próxima vacinação já passou
        if (isBefore(dataProximo, hoje)) return 'vencida';
        
        // Se a data de próxima vacinação está a menos de 30 dias
        const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
        if (diasDiferenca <= 30) return 'próxima';
        
        return 'válida';
      } catch {
        return 'não especificado';
      }
    }
    
    // Se não há próxima consulta, calcular baseado na data do procedimento
    const dataProcedimento = procedimento.data_procedimento || procedimento.data;
    if (dataProcedimento) {
      try {
        // Assume validade de 12 meses para a maioria das vacinas
        const dataVacinacao = new Date(dataProcedimento);
        const dataEstimadaProxima = addMonths(dataVacinacao, 12);
        
        if (isBefore(dataEstimadaProxima, new Date())) {
          return 'vencida';
        }
        
        return 'válida';
      } catch {
        return 'não especificado';
      }
    }
    
    return 'não especificado';
  }, []);
  
  // Determinar o status da vermifugação
  const getStatusVermifugacao = useCallback((procedimento: any): string => {
    // Usar proxima_consulta se disponível
    const proximaConsulta = procedimento.proxima_consulta;
    if (proximaConsulta) {
      try {
        const dataProximo = new Date(proximaConsulta);
        const hoje = new Date();
        
        // Se a data de próxima vermifugação já passou
        if (dataProximo < hoje) return 'atrasado';
        
        // Se a data de próxima vermifugação está a menos de 15 dias
        const diasDiferenca = Math.ceil((dataProximo.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
        if (diasDiferenca <= 15) return 'próximo';
        
        return 'agendado';
      } catch {
        return 'completo';
      }
    }
    
    return 'completo';
  }, []);
  
  // Obter tipos únicos de vacinas
  const tiposVacinaUnicos = useMemo((): string[] => {
    if (!vacinacoesFiltradas.length) return [];
    return Array.from(new Set(vacinacoesFiltradas.map((v: ProcedimentoVet) => getTipoVacina(v))));
  }, [vacinacoesFiltradas, getTipoVacina]);
  
  // Obter tipos únicos de procedimentos
  const tiposProcedimentosUnicos = useMemo((): string[] => {
    if (!registrosClinicosFiltrados.length) return [];
    return Array.from(new Set(registrosClinicosFiltrados.map((proc: ProcedimentoVet) => proc.tipo)));
  }, [registrosClinicosFiltrados]);
  
  // Função para formatar data
  const formatarData = useCallback((dataString: string): string => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dataString;
    }
  }, []);
  
  // Função para truncar texto
  const truncarTexto = useCallback((texto: string, tamanhoMaximo: number): string => {
    if (!texto) return '';
    return texto.length > tamanhoMaximo ? `${texto.substring(0, tamanhoMaximo)}...` : texto;
  }, []);
  
  return {
    // Estados de filtro
    searchTerm,
    setSearchTerm,
    filterCavalo,
    setFilterCavalo,
    filterTipo,
    setFilterTipo,
    filterStatus,
    setFilterStatus,
    
    // Queries
    procedimentosQuery,
    cavalosQuery,
    
    // Dados filtrados
    vacinacoesFiltradas,
    vermifugacoesFiltradas,
    registrosClinicosFiltrados,
    
    // Mutations
    addProcedimentoMutation,
    updateProcedimentoMutation,
    deleteProcedimentoMutation,
    
    // Funções utilitárias
    getCavaloName,
    getTipoVacina,
    getStatusVacinacao,
    getStatusVermifugacao,
    formatarData,
    truncarTexto,
    
    // Dados derivados
    tiposVacinaUnicos,
    tiposProcedimentosUnicos,
  };
}