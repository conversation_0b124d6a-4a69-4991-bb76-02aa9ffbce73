import { useState, useCallback } from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Settings, 
  Palette, 
  Bell, 
  Users, 
  Database,
  Save,
  CloudUpload
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { LayoutWrapper } from '@/components/Layout';

export function ConfiguracoesPage() {
  const { user } = useAuth();
  const isAdmin = user?.id === 1;

  const [fazendaConfig, setFazendaConfig] = useState({
    nome: 'Fazenda Santa Maria',
    razaoSocial: 'Fazenda Santa Maria LTDA',
    cnpj: '12.345.678/0001-90',
    inscricaoEstadual: '123456789',
    logo: null as string | null,
    endereco: {
      rua: 'Estrada Rural, 1234',
      numero: 'Km 15',
      complemento: 'Zona Rural',
      bairro: 'Interior',
      cidade: 'Bagé',
      estado: 'RS',
      cep: '96400-000'
    },
    contato: {
      telefone: '(53) 3242-1234',
      celular: '(53) 99999-1234',
      email: '<EMAIL>',
      site: 'www.fazenda.com.br'
    }
  });

  const [systemConfig, setSystemConfig] = useState({
    theme: 'light' as 'light' | 'dark' | 'system',
    language: 'pt-BR',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: 'pt-BR',
    timezone: 'America/Sao_Paulo',
    notifications: {
      email: true,
      browser: true,
      agenda: true,
      tarefas: true,
      financeiro: true,
      animais: true
    },
    backup: {
      automatic: true,
      frequency: 'daily' as 'daily' | 'weekly' | 'monthly',
      keepBackups: 30
    }
  });

  const [hasChanges, setHasChanges] = useState(false);

  const updateFazendaConfig = useCallback((field: string, value: any) => {
    setFazendaConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  const updateSystemConfig = useCallback((field: string, value: any) => {
    setSystemConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  }, []);

  const saveConfigurations = () => {
    // Implementar salvamento das configurações
    console.log('Salvando configurações:', { fazendaConfig, systemConfig });
    setHasChanges(false);
  };

  return (
    <LayoutWrapper>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
          <p className="text-muted-foreground">
            Gerencie as configurações da fazenda e do sistema
          </p>
        </div>

        <Tabs defaultValue="geral" className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
            <TabsTrigger value="geral" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Geral</span>
            </TabsTrigger>
            <TabsTrigger value="aparencia" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              <span className="hidden sm:inline">Aparência</span>
            </TabsTrigger>
            <TabsTrigger value="notificacoes" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Notificações</span>
            </TabsTrigger>
            <TabsTrigger value="usuarios" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">Perfil</span>
            </TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="sistema" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                <span className="hidden sm:inline">Sistema</span>
              </TabsTrigger>
            )}
          </TabsList>

          {/* Conteúdo da Tab Geral */}
          <TabsContent value="geral">
            <Card>
              <CardHeader>
                <CardTitle>Informações da Fazenda</CardTitle>
                <CardDescription>
                  Configure as informações básicas da fazenda/haras
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="nome-fazenda">Nome da Fazenda</Label>
                      <Input
                        id="nome-fazenda"
                        value={fazendaConfig.nome}
                        onChange={(e) => updateFazendaConfig('nome', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="razao-social">Razão Social</Label>
                      <Input
                        id="razao-social"
                        value={fazendaConfig.razaoSocial}
                        onChange={(e) => updateFazendaConfig('razaoSocial', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="cnpj">CNPJ</Label>
                      <Input
                        id="cnpj"
                        value={fazendaConfig.cnpj}
                        onChange={(e) => updateFazendaConfig('cnpj', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="inscricao">Inscrição Estadual</Label>
                      <Input
                        id="inscricao"
                        value={fazendaConfig.inscricaoEstadual}
                        onChange={(e) => updateFazendaConfig('inscricaoEstadual', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  <Save className="mr-2 h-4 w-4" />
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Conteúdo da Tab Aparência */}
          <TabsContent value="aparencia">
            <Card>
              <CardHeader>
                <CardTitle>Aparência e Interface</CardTitle>
                <CardDescription>
                  Personalize a aparência do sistema conforme sua preferência
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label>Tema do Sistema</Label>
                    <Select value={systemConfig.theme} onValueChange={(value) => updateSystemConfig('theme', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Claro</SelectItem>
                        <SelectItem value="dark">Escuro</SelectItem>
                        <SelectItem value="system">Seguir sistema</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Formato de Data</Label>
                    <Select value={systemConfig.dateFormat} onValueChange={(value) => updateSystemConfig('dateFormat', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DD/MM/YYYY">DD/MM/AAAA</SelectItem>
                        <SelectItem value="MM/DD/YYYY">MM/DD/AAAA</SelectItem>
                        <SelectItem value="YYYY-MM-DD">AAAA-MM-DD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  <Save className="mr-2 h-4 w-4" />
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Conteúdo da Tab Notificações */}
          <TabsContent value="notificacoes">
            <Card>
              <CardHeader>
                <CardTitle>Preferências de Notificação</CardTitle>
                <CardDescription>
                  Configure como e quando você deseja receber notificações
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Notificações por Email</Label>
                      <p className="text-sm text-muted-foreground">
                        Receber alertas importantes por email
                      </p>
                    </div>
                    <Switch
                      checked={systemConfig.notifications.email}
                      onCheckedChange={(checked) => updateSystemConfig('notifications', { ...systemConfig.notifications, email: checked })}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Notificações do Navegador</Label>
                      <p className="text-sm text-muted-foreground">
                        Alertas em tempo real no navegador
                      </p>
                    </div>
                    <Switch
                      checked={systemConfig.notifications.browser}
                      onCheckedChange={(checked) => updateSystemConfig('notifications', { ...systemConfig.notifications, browser: checked })}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={saveConfigurations} disabled={!hasChanges}>
                  <Save className="mr-2 h-4 w-4" />
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Conteúdo da Tab Usuários - Gerenciar Perfil */}
          <TabsContent value="usuarios">
            <Card>
              <CardHeader>
                <CardTitle>Meu Perfil</CardTitle>
                <CardDescription>
                  Gerencie suas informações pessoais e configurações de conta
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Foto do Perfil */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center">
                        <Users className="h-10 w-10 text-muted-foreground" />
                      </div>
                      <div className="space-y-2">
                        <Button variant="outline" size="sm">
                          <CloudUpload className="mr-2 h-4 w-4" />
                          Alterar Foto
                        </Button>
                        <p className="text-xs text-muted-foreground">
                          JPG, PNG ou GIF. Máximo 2MB.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Informações Básicas */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="nome-completo">Nome Completo</Label>
                      <Input 
                        id="nome-completo"
                        placeholder="Seu nome completo"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email-usuario">E-mail</Label>
                      <Input 
                        id="email-usuario"
                        type="email"
                        placeholder="<EMAIL>"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
                
                {/* Alterar Senha */}
                <Separator />
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Alterar Senha</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="senha-atual">Senha Atual</Label>
                      <Input 
                        id="senha-atual"
                        type="password"
                        placeholder="Digite sua senha atual"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="nova-senha">Nova Senha</Label>
                      <Input 
                        id="nova-senha"
                        type="password"
                        placeholder="Digite a nova senha"
                        className="mt-1"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="confirmar-senha">Confirmar Nova Senha</Label>
                      <Input 
                        id="confirmar-senha"
                        type="password"
                        placeholder="Confirme a nova senha"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button>
                  <Save className="mr-2 h-4 w-4" />
                  Salvar Alterações
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Conteúdo da Tab Sistema - somente para administradores */}
          {isAdmin && (
            <TabsContent value="sistema">
              <Card>
                <CardHeader>
                  <CardTitle>Configurações do Sistema</CardTitle>
                  <CardDescription>
                    Configure detalhes técnicos e manutenção do sistema
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Backup Automático</Label>
                        <p className="text-sm text-muted-foreground">
                          Realizar backup automático dos dados
                        </p>
                      </div>
                      <Switch
                        checked={systemConfig.backup.automatic}
                        onCheckedChange={(checked) => updateSystemConfig('backup', { ...systemConfig.backup, automatic: checked })}
                      />
                    </div>
                    <div>
                      <Label>Frequência do Backup</Label>
                      <Select value={systemConfig.backup.frequency} onValueChange={(value) => updateSystemConfig('backup', { ...systemConfig.backup, frequency: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Diário</SelectItem>
                          <SelectItem value="weekly">Semanal</SelectItem>
                          <SelectItem value="monthly">Mensal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={saveConfigurations} disabled={!hasChanges}>
                    <Save className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}