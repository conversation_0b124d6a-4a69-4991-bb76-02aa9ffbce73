import { z } from 'zod';

// Importar schemas para testar ou copiar conteúdo localmente para teste
const loginSchema = z.object({
  username: z.string().min(3, { message: "Nome de usuário deve ter pelo menos 3 caracteres" }),
  password: z.string().min(6, { message: "Senha deve ter pelo menos 6 caracteres" })
});

describe('Auth schemas validation', () => {
  describe('loginSchema', () => {
    it('should accept valid login data', () => {
      const validData = {
        username: 'testuser',
        password: 'password123'
      };
      
      const result = loginSchema.safeParse(validData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });
    
    it('should reject login with short username', () => {
      const invalidData = {
        username: 'te', // Too short
        password: 'password123'
      };
      
      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const formatted = result.error.format();
        expect(formatted.username?._errors).toBeDefined();
        expect(formatted.username?._errors.length).toBeGreaterThan(0);
      }
    });
    
    it('should reject login with short password', () => {
      const invalidData = {
        username: 'testuser',
        password: '12345' // Too short
      };
      
      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const formatted = result.error.format();
        expect(formatted.password?._errors).toBeDefined();
        expect(formatted.password?._errors.length).toBeGreaterThan(0);
      }
    });
    
    it('should reject login with missing fields', () => {
      const invalidData = {
        username: 'testuser'
        // Missing password
      };
      
      const result = loginSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const formatted = result.error.format();
        expect(formatted.password?._errors).toBeDefined();
      }
    });
  });
});