import { useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import { LayoutWrapper } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/DatePicker";
import { useToast } from "@/hooks/use-toast";
import { formatMoney, formatDate } from "@/utils/finance";
import { 
  Plus, Filter, Search, Edit, Trash2, 
  ArrowUpRight, ArrowDownRight, 
  FileDown, ChevronDown, RefreshCw, 
  DollarSign, Calendar
} from 'lucide-react';

/**
 * Interface para lançamentos financeiros
 */
interface Lancamento {
  id: number;
  data: string;
  tipo: 'receita' | 'despesa';
  categoria: string;
  descricao: string;
  valor: number;
  conta: string;
  cavaloId?: number | null;
  cavaloNome?: string | null;
  status: 'confirmado' | 'pendente' | 'cancelado';
}

/**
 * Página de lançamentos financeiros
 */
export default function LancamentosPage() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const [periodoInicio, setPeriodoInicio] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1) // Primeiro dia do mês atual
  );
  const [periodoFim, setPeriodoFim] = useState<Date | undefined>(
    new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0) // Último dia do mês atual
  );
  const [filtroTipo, setFiltroTipo] = useState<string>("todos");
  const [filtroCategoria, setFiltroCategoria] = useState<string>("todas");
  const [filtroConta, setFiltroConta] = useState<string>("todas");
  const [termoBusca, setTermoBusca] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<Lancamento>>({
    tipo: 'receita',
    data: new Date().toISOString().split('T')[0],
    status: 'confirmado',
  });
  
  // Dados simulados para demonstração
  const lancamentos: Lancamento[] = [
    {
      id: 1,
      data: '2025-03-25',
      tipo: 'receita',
      categoria: 'Pensão',
      descricao: 'Pagamento mensal de pensão do cavalo Zeus',
      valor: 2500.00,
      conta: 'Banco do Brasil',
      cavaloId: 5,
      cavaloNome: 'Zeus',
      status: 'confirmado'
    },
    {
      id: 2,
      data: '2025-03-23',
      tipo: 'despesa',
      categoria: 'Alimentação',
      descricao: 'Compra de ração para o mês',
      valor: 850.75,
      conta: 'Dinheiro',
      status: 'confirmado'
    },
    {
      id: 3,
      data: '2025-03-20',
      tipo: 'despesa',
      categoria: 'Medicamentos',
      descricao: 'Antibióticos para tratamento',
      valor: 375.50,
      conta: 'Dinheiro',
      cavaloId: 5,
      cavaloNome: 'Zeus',
      status: 'confirmado'
    },
    {
      id: 4,
      data: '2025-03-30',
      tipo: 'receita',
      categoria: 'Serviços',
      descricao: 'Aula de equitação',
      valor: 150.00,
      conta: 'Banco do Brasil',
      status: 'pendente'
    },
    {
      id: 5,
      data: '2025-03-28',
      tipo: 'despesa',
      categoria: 'Ferrageamento',
      descricao: 'Serviço de ferrageamento',
      valor: 280.00,
      conta: 'Banco do Brasil',
      cavaloId: 5,
      cavaloNome: 'Zeus',
      status: 'confirmado'
    },
  ];
  
  // Categorias simuladas
  const categorias = [
    { id: 1, nome: 'Alimentação', tipo: 'despesa' },
    { id: 2, nome: 'Medicamentos', tipo: 'despesa' },
    { id: 3, nome: 'Ferrageamento', tipo: 'despesa' },
    { id: 4, nome: 'Serviços Veterinários', tipo: 'despesa' },
    { id: 5, nome: 'Manutenção', tipo: 'despesa' },
    { id: 6, nome: 'Transporte', tipo: 'despesa' },
    { id: 7, nome: 'Pensão', tipo: 'receita' },
    { id: 8, nome: 'Serviços', tipo: 'receita' },
    { id: 9, nome: 'Vendas', tipo: 'receita' },
  ];
  
  // Contas simuladas
  const contas = [
    { id: 1, nome: 'Banco do Brasil' },
    { id: 2, nome: 'Dinheiro' },
    { id: 3, nome: 'Banco Itaú' },
  ];
  
  // Cavalos simulados
  const cavalos = [
    { id: 5, nome: 'Zeus' },
    { id: 6, nome: 'Atena' },
    { id: 7, nome: 'Apollo' },
  ];
  
  // Aplicar filtros aos lançamentos
  const lancamentosFiltrados = lancamentos.filter(lancamento => {
    const dataLancamento = new Date(lancamento.data);
    const dataInicioFiltro = periodoInicio ? new Date(periodoInicio) : null;
    const dataFimFiltro = periodoFim ? new Date(periodoFim) : null;
    
    // Filtro de período
    const dentroDePeríodo = (!dataInicioFiltro || dataLancamento >= dataInicioFiltro) && 
                           (!dataFimFiltro || dataLancamento <= dataFimFiltro);
    
    // Filtro de tipo
    const tipoMatch = filtroTipo === 'todos' || lancamento.tipo === filtroTipo;
    
    // Filtro de categoria
    const categoriaMatch = filtroCategoria === 'todas' || lancamento.categoria === filtroCategoria;
    
    // Filtro de conta
    const contaMatch = filtroConta === 'todas' || lancamento.conta === filtroConta;
    
    // Filtro de busca
    const buscaMatch = termoBusca === '' || 
                      lancamento.descricao.toLowerCase().includes(termoBusca.toLowerCase()) ||
                      lancamento.categoria.toLowerCase().includes(termoBusca.toLowerCase()) ||
                      (lancamento.cavaloNome && lancamento.cavaloNome.toLowerCase().includes(termoBusca.toLowerCase()));
    
    return dentroDePeríodo && tipoMatch && categoriaMatch && contaMatch && buscaMatch;
  })
  // Ordenar por data (mais próxima primeiro) e priorizar datas futuras
  .sort((a, b) => {
    // Para priorizar datas futuras mais próximas primeiro, seguidas pelas datas passadas mais recentes
    const now = new Date().getTime();
    const dateA = new Date(a.data).getTime();
    const dateB = new Date(b.data).getTime();
    
    // Dividir em lançamentos futuros e passados
    const aFuturo = dateA >= now;
    const bFuturo = dateB >= now;
    
    // Se um é futuro e o outro é passado, o futuro vem primeiro
    if (aFuturo && !bFuturo) return -1;
    if (!aFuturo && bFuturo) return 1;
    
    // Se ambos são futuros, o mais próximo vem primeiro
    if (aFuturo && bFuturo) {
      return dateA - dateB; // Ordem crescente de data para lançamentos futuros
    }
    
    // Se ambos são passados, o mais recente (mais próximo de hoje) vem primeiro
    return dateB - dateA; // Ordem decrescente de data para lançamentos passados
  });
  
  // Total de receitas filtradas
  const totalReceitas = lancamentosFiltrados
    .filter(l => l.tipo === 'receita')
    .reduce((sum, l) => sum + l.valor, 0);
  
  // Total de despesas filtradas
  const totalDespesas = lancamentosFiltrados
    .filter(l => l.tipo === 'despesa')
    .reduce((sum, l) => sum + l.valor, 0);
  
  // Saldo
  const saldo = totalReceitas - totalDespesas;
  
  
  // Obter classes CSS para o tipo de lançamento
  const getTipoClasses = (tipo: string) => {
    if (tipo === 'receita') {
      return 'bg-green-100 text-green-800 border-green-200';
    } else {
      return 'bg-red-100 text-red-800 border-red-200';
    }
  };
  
  // Obter classes CSS para o status
  const getStatusClasses = (status: string) => {
    switch (status) {
      case 'confirmado':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pendente':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelado':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  // Função para lidar com envio do formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Aqui você implementaria a lógica para salvar o lançamento
    toast({
      title: "Lançamento salvo com sucesso",
      description: `${formData.tipo === 'receita' ? 'Receita' : 'Despesa'} de ${formatMoney(Number(formData.valor || 0))} cadastrada.`,
    });
    
    setDialogOpen(false);
    setFormData({ 
      tipo: 'receita', 
      data: new Date().toISOString().split('T')[0],
      status: 'confirmado'
    });
  };
  
  // Função para abrir o diálogo de edição
  const handleEdit = (lancamento: Lancamento) => {
    setFormData(lancamento);
    setDialogOpen(true);
  };
  
  // Função para lidar com mudanças no formulário
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Função para lidar com mudança de data
  const handleDateChange = (date?: Date) => {
    if (date) {
      setFormData(prev => ({ 
        ...prev, 
        data: date.toISOString().split('T')[0] 
      }));
    }
  };
  
  // Função para lidar com exclusão
  const handleDelete = (id: number) => {
    // Aqui você implementaria a lógica para excluir o lançamento
    toast({
      title: "Lançamento excluído com sucesso",
      description: "O lançamento foi removido permanentemente.",
      variant: "destructive"
    });
  };
  
  // Função para limpar filtros
  const limparFiltros = () => {
    setPeriodoInicio(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
    setPeriodoFim(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0));
    setFiltroTipo("todos");
    setFiltroCategoria("todas");
    setFiltroConta("todas");
    setTermoBusca("");
  };
  
  return (
    <LayoutWrapper pageTitle="Lançamentos Financeiros" showBackButton backUrl="/financeiro">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-blue-700">Lançamentos Financeiros</h1>
            <p className="text-gray-600 mt-1">
              Gerencie suas receitas e despesas
            </p>
          </div>
          
          <div className="flex gap-3 mt-4 md:mt-0">
            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="mr-2 h-4 w-4" /> Novo Lançamento
                </Button>
              </DialogTrigger>
              <DialogContent className="fixed inset-0 z-50 m-2 max-h-[calc(100dvh-1rem)] bg-background shadow-lg duration-200 overflow-y-auto sm:m-0 sm:inset-auto sm:left-[50%] sm:top-[50%] sm:max-h-[85vh] sm:w-full sm:translate-x-[-50%] sm:translate-y-[-50%] sm:rounded-lg sm:border sm:overflow-hidden lg:max-w-2xl xl:max-w-3xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom sm:data-[state=closed]:slide-out-to-left-1/2 sm:data-[state=closed]:slide-out-to-top-[48%] sm:data-[state=open]:slide-in-from-left-1/2 sm:data-[state=open]:slide-in-from-top-[48%] sm:data-[state=closed]:zoom-out-95 sm:data-[state=open]:zoom-in-95 sm:max-w-[600px] pl-[15px] pr-[15px]">
                <DialogHeader>
                  <DialogTitle>
                    {formData.id ? 'Editar Lançamento' : 'Novo Lançamento'}
                  </DialogTitle>
                  <DialogDescription>
                    {formData.id 
                      ? 'Edite os detalhes do lançamento financeiro.'
                      : 'Preencha os dados para registrar um novo lançamento financeiro.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="tipo">Tipo</Label>
                        <Select 
                          name="tipo" 
                          value={formData.tipo} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, tipo: value as 'receita' | 'despesa' }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="receita">Receita</SelectItem>
                            <SelectItem value="despesa">Despesa</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="data">Data</Label>
                        <DatePicker 
                          date={formData.data ? new Date(formData.data) : undefined}
                          onDateChange={handleDateChange}
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="categoria">Categoria</Label>
                        <Select 
                          name="categoria" 
                          value={formData.categoria} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, categoria: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a categoria" />
                          </SelectTrigger>
                          <SelectContent>
                            {categorias
                              .filter(cat => !formData.tipo || cat.tipo === formData.tipo)
                              .map(cat => (
                                <SelectItem key={cat.id} value={cat.nome}>
                                  {cat.nome}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="conta">Conta</Label>
                        <Select 
                          name="conta" 
                          value={formData.conta} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, conta: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a conta" />
                          </SelectTrigger>
                          <SelectContent>
                            {contas.map(conta => (
                              <SelectItem key={conta.id} value={conta.nome}>
                                {conta.nome}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="descricao">Descrição</Label>
                      <Textarea 
                        name="descricao" 
                        placeholder="Descreva o lançamento financeiro"
                        value={formData.descricao || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="valor">Valor</Label>
                        <div className="relative">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                          <Input 
                            name="valor" 
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0,00" 
                            className="pl-10"
                            value={formData.valor || ''}
                            onChange={handleInputChange}
                          />
                        </div>
                      </div>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="status">Status</Label>
                        <Select 
                          name="status" 
                          value={formData.status} 
                          onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as 'confirmado' | 'pendente' | 'cancelado' }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="confirmado">Confirmado</SelectItem>
                            <SelectItem value="pendente">Pendente</SelectItem>
                            <SelectItem value="cancelado">Cancelado</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="cavalo">Cavalo (opcional)</Label>
                      <Select 
                        name="cavaloId" 
                        value={formData.cavaloId?.toString() || ''} 
                        onValueChange={(value) => {
                          const cavaloId = value ? parseInt(value) : null;
                          const cavalo = cavalos.find(c => c.id === cavaloId);
                          setFormData(prev => ({ 
                            ...prev, 
                            cavaloId, 
                            cavaloNome: cavalo?.nome || null
                          }));
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um cavalo (opcional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="nenhum">Nenhum cavalo</SelectItem>
                          {cavalos.map(cavalo => (
                            <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                              {cavalo.nome}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setDialogOpen(false)}
                    >
                      Cancelar
                    </Button>
                    <Button type="submit">
                      {formData.id ? 'Atualizar' : 'Cadastrar'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
            
            <Button variant="outline" className="border-blue-200">
              <FileDown className="mr-2 h-4 w-4" /> Exportar
            </Button>
          </div>
        </div>
        
        {/* Cards de resumo */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="border-green-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-green-700">Total Receitas</p>
                <p className="text-2xl font-bold text-green-600">{formatMoney(totalReceitas)}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <ArrowDownRight className="h-5 w-5 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-red-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-red-700">Total Despesas</p>
                <p className="text-2xl font-bold text-red-600">{formatMoney(totalDespesas)}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <ArrowUpRight className="h-5 w-5 text-red-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-blue-100">
            <CardContent className="p-4 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-blue-700">Saldo</p>
                <p className={`text-2xl font-bold ${saldo >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatMoney(saldo)}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Filtros */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Filter className="h-5 w-5 mr-1.5" />
              Filtros
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Período Início</Label>
                <DatePicker
                  date={periodoInicio}
                  onDateChange={setPeriodoInicio}
                  placeholder="Data inicial"
                />
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Período Fim</Label>
                <DatePicker
                  date={periodoFim}
                  onDateChange={setPeriodoFim}
                  placeholder="Data final"
                />
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Tipo</Label>
                <Select value={filtroTipo} onValueChange={setFiltroTipo}>
                  <SelectTrigger>
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todos">Todos</SelectItem>
                    <SelectItem value="receita">Receitas</SelectItem>
                    <SelectItem value="despesa">Despesas</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Categoria</Label>
                <Select value={filtroCategoria} onValueChange={setFiltroCategoria}>
                  <SelectTrigger>
                    <SelectValue placeholder="Categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todas">Todas</SelectItem>
                    {categorias
                      .filter(cat => filtroTipo === 'todos' || cat.tipo === filtroTipo)
                      .map(cat => (
                        <SelectItem key={cat.id} value={cat.nome}>
                          {cat.nome}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Conta</Label>
                <Select value={filtroConta} onValueChange={setFiltroConta}>
                  <SelectTrigger>
                    <SelectValue placeholder="Conta" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todas">Todas</SelectItem>
                    {contas.map(conta => (
                      <SelectItem key={conta.id} value={conta.nome}>
                        {conta.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col">
                <Label className="mb-2 text-sm">Busca</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Input 
                    placeholder="Buscar..." 
                    className="pl-10"
                    value={termoBusca}
                    onChange={(e) => setTermoBusca(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={limparFiltros}
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Limpar Filtros
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabela de lançamentos */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Lançamentos</CardTitle>
            <CardDescription>
              {lancamentosFiltrados.length} lançamentos encontrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border-b px-4 py-3 text-left font-medium">Data</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Tipo</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Descrição</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Categoria</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Conta</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Valor</th>
                      <th className="border-b px-4 py-3 text-left font-medium">Status</th>
                      <th className="border-b px-4 py-3 text-center font-medium">Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    {lancamentosFiltrados.map((lancamento) => (
                      <tr 
                        key={lancamento.id} 
                        className="border-b transition-colors hover:bg-gray-50"
                      >
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                            {formatDate(lancamento.data)}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span 
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getTipoClasses(lancamento.tipo)}`}
                          >
                            {lancamento.tipo === 'receita' ? 'Receita' : 'Despesa'}
                          </span>
                        </td>
                        <td className="px-4 py-3 max-w-[200px]">
                          <div className="truncate">
                            {lancamento.descricao}
                          </div>
                          {lancamento.cavaloNome && (
                            <div className="text-xs text-gray-500 mt-1">
                              Cavalo: {lancamento.cavaloNome}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-3">{lancamento.categoria}</td>
                        <td className="px-4 py-3">{lancamento.conta}</td>
                        <td className="px-4 py-3 font-medium">
                          <span className={lancamento.tipo === 'receita' ? 'text-green-600' : 'text-red-600'}>
                            {formatMoney(lancamento.valor)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span 
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClasses(lancamento.status)}`}
                          >
                            {lancamento.status.charAt(0).toUpperCase() + lancamento.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex justify-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8" 
                              onClick={() => handleEdit(lancamento)}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Editar</span>
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50" 
                              onClick={() => handleDelete(lancamento.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Remover</span>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    
                    {lancamentosFiltrados.length === 0 && (
                      <tr>
                        <td colSpan={8} className="py-6 text-center text-gray-500">
                          Nenhum lançamento encontrado com os filtros aplicados.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}