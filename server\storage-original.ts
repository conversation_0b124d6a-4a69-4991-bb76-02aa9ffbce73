import { 
  users, type User, type InsertUser,
  cavalos, type Cavalo, type InsertCavalo,
  manejos, type Manejo, type InsertManejo,
  medidas_morfologicas, type MedidaMorfologica, type InsertMedidaMorfologica,
  genealogia, type Genealogia, type InsertGenealogia,
  desempenhoHistorico, type DesempenhoHistorico, type InsertDesempenhoHistorico,
  sugestoesCruzamento, type SugestoesCruzamento, type InsertSugestoesCruzamento,
  procedimentosVet, type ProcedimentoVet, type InsertProcedimentoVet,
  nutricao, type Nutricao, type InsertNutricao,
  reproducao, type Reproducao, type InsertReproducao,
  pelagens, type Pelagem, type InsertPelagem,
  arquivos, type Arquivo, type InsertArquivo,
  eventos, type Evento, type InsertEvento,
  veterinario
} from "@shared/schema";
import { db } from "./db";
import { eq, and, asc, desc, like, sql, ne, or, isNull, notInArray, gte, lte, lt, inArray } from "drizzle-orm";
import { getModuleLogger, logDbError } from './logger';

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Horse methods (updated for modular architecture)
  getCavalos(user_id: number): Promise<Cavalo[]>;
  getCavalo(id: number, user_id: number): Promise<Cavalo | undefined>;
  createCavalo(cavalo: InsertCavalo): Promise<Cavalo>;
  updateCavalo(id: number, user_id: number, cavalo: Partial<InsertCavalo>): Promise<Cavalo | undefined>;
  deleteCavalo(id: number, user_id: number): Promise<boolean>;
  
  // Manejo (Horse care) methods
  getManejos(user_id: number): Promise<Manejo[]>;
  getManejosByHorse(horse_id: number, user_id: number): Promise<Manejo[]>;
  getManejo(id: number, user_id: number): Promise<Manejo | undefined>;
  createManejo(manejo: InsertManejo): Promise<Manejo>;
  updateManejo(id: number, user_id: number, manejo: Partial<InsertManejo>): Promise<Manejo | undefined>;
  deleteManejo(id: number, user_id: number): Promise<boolean>;
  
  // Arquivo (File) methods
  getArquivos(user_id: number): Promise<Arquivo[]>;
  getArquivosByHorse(horse_id: number, user_id: number): Promise<Arquivo[]>;
  getArquivo(id: number, user_id: number): Promise<Arquivo | undefined>;
  createArquivo(arquivo: InsertArquivo): Promise<Arquivo>;
  deleteArquivo(id: number, user_id: number): Promise<boolean>;
  
  // Evento (Schedule) methods
  getEventos(user_id: number): Promise<Evento[]>;
  getEventosByDate(data: string, user_id: number): Promise<Evento[]>;
  getEventosByHorse(horse_id: number, user_id: number): Promise<Evento[]>;
  getEventosByManejo(manejoId: number, user_id: number): Promise<Evento[]>;
  getEvento(id: number, user_id: number): Promise<Evento | undefined>;
  createEvento(evento: InsertEvento): Promise<Evento>;
  updateEvento(id: number, user_id: number, evento: Partial<InsertEvento>): Promise<Evento | undefined>;
  deleteEvento(id: number, user_id: number): Promise<boolean>;
  
  // Procedimentos Veterinários methods
  getProcedimentosVet(user_id: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByHorse(horse_id: number, user_id: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByManejo(manejoId: number, user_id: number): Promise<ProcedimentoVet[]>;
  getProcedimentosVetByEvento(eventoId: number, user_id: number): Promise<ProcedimentoVet[]>;
  getProcedimentoVet(id: number, user_id: number): Promise<ProcedimentoVet | undefined>;
  createProcedimentoVet(procedimento: InsertProcedimentoVet): Promise<ProcedimentoVet>;
  updateProcedimentoVet(id: number, user_id: number, procedimento: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined>;
  deleteProcedimentoVet(id: number, user_id: number): Promise<boolean>;
  
  // Reprodução methods
  getReproducoes(user_id: number): Promise<Reproducao[]>;
  getReproducoesByHorse(horse_id: number, user_id: number): Promise<Reproducao[]>;
  getReproducoesByProcedimento(procedimentoId: number, user_id: number): Promise<Reproducao[]>;
  getReproducoesByEvento(eventoId: number, user_id: number): Promise<Reproducao[]>;
  getReproducao(id: number, user_id: number): Promise<Reproducao | undefined>;
  createReproducao(reproducaoData: InsertReproducao): Promise<Reproducao>;
  updateReproducao(id: number, user_id: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined>;
  deleteReproducao(id: number, user_id: number): Promise<boolean>;
  
  // Medidas Físicas methods
  getMedidasFisicas(user_id: number): Promise<MedidaMorfologica[]>;
  getMedidasFisicasByHorse(horse_id: number, user_id: number): Promise<MedidaMorfologica[]>;
  getMedidaFisica(id: number, user_id: number): Promise<MedidaMorfologica | undefined>;
  createMedidaFisica(medidaData: InsertMedidaMorfologica): Promise<MedidaMorfologica>;
  updateMedidaFisica(id: number, user_id: number, medidaData: Partial<InsertMedidaMorfologica>): Promise<MedidaMorfologica | undefined>;
  deleteMedidaFisica(id: number, user_id: number): Promise<boolean>;
  
  // Nutrição methods
  getNutricoes(user_id: number): Promise<Nutricao[]>;
  getNutricoesByHorse(horse_id: number, user_id: number): Promise<Nutricao[]>;
  getNutricao(id: number, user_id: number): Promise<Nutricao | undefined>;
  createNutricao(nutricaoData: InsertNutricao): Promise<Nutricao>;
  updateNutricao(id: number, user_id: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined>;
  deleteNutricao(id: number, user_id: number): Promise<boolean>;
  
  // Morfologia methods (medidas morfológicas detalhadas)
  getMorfologias(user_id: number): Promise<MedidaMorfologica[]>;
  getMorfologiasByHorse(horse_id: number, user_id: number): Promise<MedidaMorfologica[]>;
  getMorfologia(id: number, user_id: number): Promise<MedidaMorfologica | undefined>;
  createMorfologia(morfologiaData: InsertMedidaMorfologica): Promise<MedidaMorfologica>;
  updateMorfologia(id: number, user_id: number, morfologiaData: Partial<InsertMedidaMorfologica>): Promise<MedidaMorfologica | undefined>;
  deleteMorfologia(id: number, user_id: number): Promise<boolean>;
  
  // Histórico de Desempenho methods (competições, eventos, etc.)
  getDesempenhos(user_id: number): Promise<DesempenhoHistorico[]>;
  getDesempenhosByHorse(horse_id: number, user_id: number): Promise<DesempenhoHistorico[]>;
  getDesempenho(id: number, user_id: number): Promise<DesempenhoHistorico | undefined>;
  createDesempenho(desempenhoData: InsertDesempenhoHistorico): Promise<DesempenhoHistorico>;
  updateDesempenho(id: number, user_id: number, desempenhoData: Partial<InsertDesempenhoHistorico>): Promise<DesempenhoHistorico | undefined>;
  deleteDesempenho(id: number, user_id: number): Promise<boolean>;
  
  // Genealogia methods (registro detalhado de ancestrais)
  getGenealogias(user_id: number): Promise<Genealogia[]>;
  getGenealogiaByHorse(horse_id: number, user_id: number): Promise<Genealogia | undefined>;
  createGenealogia(genealogiaData: InsertGenealogia): Promise<Genealogia>;
  updateGenealogia(id: number, user_id: number, genealogiaData: Partial<InsertGenealogia>): Promise<Genealogia | undefined>;
  deleteGenealogia(id: number, user_id: number): Promise<boolean>;
  calcularConsanguinidade(horse_id: number, user_id: number): Promise<number>;
  
  // Sugestões de Cruzamento methods (IA para sugerir cruzamentos)
  getSugestoesCruzamento(user_id: number): Promise<SugestoesCruzamento[]>;
  getSugestoesCruzamentoByHorse(horseIdBase: number, user_id: number): Promise<SugestoesCruzamento[]>;
  getSugestoesCruzamentoById(id: number, user_id: number): Promise<SugestoesCruzamento | undefined>;
  createSugestaoCruzamento(sugestaoData: InsertSugestoesCruzamento): Promise<SugestoesCruzamento>;
  deleteSugestaoCruzamento(id: number, user_id: number): Promise<boolean>;
  gerarSugestoesCruzamento(horseIdBase: number, objetivo: string, user_id: number): Promise<SugestoesCruzamento[]>;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }
  
  // Cavalo (Horse) methods
  async getCavalos(user_id: number): Promise<Cavalo[]> {
    const logger = getModuleLogger('storage');
    
    try {
      logger.debug(`Buscando cavalos para usuário ${user_id}`);
      
      const horsesResult = await db.select()
        .from(cavalos)
        .where(eq(cavalos.user_id, user_id))
        .orderBy(asc(cavalos.name));
      
      // Mapear campos da tabela horses para o formato esperado pelo frontend
      return horsesResult.map(horse => ({
        ...horse,
        nome: horse.name, // Mapear name -> nome para compatibilidade
        pelagem: horse.cor // Mapear cor -> pelagem para compatibilidade
      })) as Cavalo[];
    } catch (error: any) {
      logger.error(`Erro ao buscar cavalos: ${error.message}`);
      throw error;
    }
  }
  
  async getCavalo(id: number, user_id: number): Promise<Cavalo | undefined> {
    try {
      const result = await db
        .select()
        .from(cavalos)
        .where(eq(cavalos.id, id));

      if (result.length === 0) {
        return undefined;
      }

      const horse = result[0];
      
      // Mapear campos da tabela horses para o formato esperado pelo frontend
      return {
        ...horse,
        nome: horse.name, // Mapear name -> nome para compatibilidade
        pelagem: horse.cor // Mapear cor -> pelagem para compatibilidade
      } as Cavalo;
    } catch (error) {
      console.error(`Erro ao buscar cavalo ${id}:`, error);
      return undefined;
    }
  }

  // Função auxiliar para extrair nomes limpos de campos JSON aninhados
  private extractCleanName(rawValue: any): string | null {
    if (!rawValue) return null;
    
    try {
      let current = rawValue;
      let depth = 0;
      
      // Desaninhar JSON até 5 níveis de profundidade
      while (typeof current === 'string' && current.trim().startsWith('{') && depth < 5) {
        const parsed = JSON.parse(current);
        current = parsed.cavaloNome || parsed.nome || parsed.name || current;
        depth++;
      }
      
      return typeof current === 'string' ? current : null;
    } catch {
      return typeof rawValue === 'string' ? rawValue : null;
    }
  }
  
  async createCavalo(insertCavalo: InsertCavalo): Promise<Cavalo> {
    const logger = getModuleLogger('storage');
    
    try {
      // Mapear campos do frontend para estrutura da tabela horses
      const processedData = { ...insertCavalo } as any;
      
      // Mapear nome -> name
      if ('nome' in processedData) {
        processedData.name = processedData.nome;
        delete processedData.nome;
      }
      
      // Mapear pelagem -> cor
      if ('pelagem' in processedData) {
        processedData.cor = processedData.pelagem;
        delete processedData.pelagem;
      }
      
      // Processar os objetos 'pai' e 'mae' se existirem
      if (processedData.pai) {
        const valorPai = processedData.pai;
        
        if (typeof valorPai === 'object' && valorPai !== null) {
          const entradaPai = valorPai as any;
          
          if (entradaPai.tipo === 'sistema' && entradaPai.cavaloSistemaId) {
            processedData.pai_id = parseInt(entradaPai.cavaloSistemaId);
          } 
          else if (entradaPai.tipo === 'externo' && entradaPai.cavaloNome) {
            // Para cavalos externos, não salvamos ID, apenas mantemos como null
            processedData.pai_id = null;
          }
          else {
            processedData.pai_id = null;
          }
        }
        
        delete processedData.pai;
      }
      
      if (processedData.mae) {
        const valorMae = processedData.mae;
        
        if (typeof valorMae === 'object' && valorMae !== null) {
          const entradaMae = valorMae as any;
          
          if (entradaMae.tipo === 'sistema' && entradaMae.cavaloSistemaId) {
            processedData.mae_id = parseInt(entradaMae.cavaloSistemaId);
          } 
          else if (entradaMae.tipo === 'externo' && entradaMae.cavaloNome) {
            processedData.mae_id = null;
          }
          else {
            processedData.mae_id = null;
          }
        }
        
        delete processedData.mae;
      }

      // Processar campos de avós diretos (novos campos)
      if (processedData.avoPaternoNome && !processedData.avoPaternoId) {
        // Mantem o nome do avô paterno externo
        // processedData.avoPaternoNome já está correto
      }
      
      if (processedData.avoMaternaNome && !processedData.avoMaternaId) {
        // Mantem o nome da avó materna externa
        // processedData.avoMaternaNome já está correto
      }
      
      // Processar campos de avós em formato de objeto (se houver)
      if (processedData.avoPaterno) {
        const valorAvoPaterno = processedData.avoPaterno;
        
        if (typeof valorAvoPaterno === 'object' && valorAvoPaterno !== null) {
          const entradaAvoPaterno = valorAvoPaterno as any;
          
          if (entradaAvoPaterno.tipo === 'sistema' && entradaAvoPaterno.cavaloSistemaId) {
            processedData.avoPaternoId = parseInt(entradaAvoPaterno.cavaloSistemaId);
            processedData.avoPaternoNome = null;
          } 
          else if (entradaAvoPaterno.tipo === 'externo' && entradaAvoPaterno.cavaloNome) {
            processedData.avoPaternoId = null;
            processedData.avoPaternoNome = entradaAvoPaterno.cavaloNome;
          }
          else {
            processedData.avoPaternoId = null;
            processedData.avoPaternoNome = null;
          }
        }
        
        delete processedData.avoPaterno;
      }
      
      if (processedData.avoMaterno) {
        const valorAvoMaterno = processedData.avoMaterno;
        
        if (typeof valorAvoMaterno === 'object' && valorAvoMaterno !== null) {
          const entradaAvoMaterno = valorAvoMaterno as any;
          
          if (entradaAvoMaterno.tipo === 'sistema' && entradaAvoMaterno.cavaloSistemaId) {
            processedData.avoMaternaId = parseInt(entradaAvoMaterno.cavaloSistemaId);
            processedData.avoMaternaNome = null;
          } 
          else if (entradaAvoMaterno.tipo === 'externo' && entradaAvoMaterno.cavaloNome) {
            processedData.avoMaternaId = null;
            processedData.avoMaternaNome = entradaAvoMaterno.cavaloNome;
          }
          else {
            processedData.avoMaternaId = null;
            processedData.avoMaternaNome = null;
          }
        }
        
        delete processedData.avoMaterno;
      }
      
      // Converter datas de string para Date se necessário
      if (processedData.birth_date && typeof processedData.birth_date === 'string') {
        processedData.birth_date = new Date(processedData.birth_date);
      }
      if (processedData.data_compra && typeof processedData.data_compra === 'string') {
        processedData.data_compra = new Date(processedData.data_compra);
      }
      if (processedData.data_entrada && typeof processedData.data_entrada === 'string') {
        processedData.data_entrada = new Date(processedData.data_entrada);
      }
      if (processedData.data_saida && typeof processedData.data_saida === 'string') {
        processedData.data_saida = new Date(processedData.data_saida);
      }
      
      logger.debug('Criando cavalo com dados processados:', {
        dados_originais: JSON.stringify(insertCavalo),
        dados_processados: JSON.stringify(processedData),
        avos_originais: {
          avoPaternoNome: insertCavalo.avoPaternoNome,
          avoMaternaNome: insertCavalo.avoMaternaNome
        },
        avos_processados: {
          avoPaternoNome: processedData.avoPaternoNome,
          avoMaternaNome: processedData.avoMaternaNome
        }
      });
      
      const [cavalo] = await db
        .insert(horses)
        .values(processedData)
        .returning();
        
      return cavalo;
    }
    catch (error) {
      logger.error('Erro ao criar cavalo:', error);
      throw error;
    }
  }
  
  async updateCavalo(id: number, user_id: number, cavaloData: Partial<InsertHorse>): Promise<Horse | undefined> {
    // Usando o logger importado no topo do arquivo
    const storageLogger = getModuleLogger('storage');

    try {
      storageLogger.info(`⚙️ INICIANDO ATUALIZAÇÃO DO CAVALO ${id} PARA USUÁRIO ${user_id}`, { 
        cavaloId: id,
        user_id: user_id,
        operation: 'updateCavalo'
      });
      
      // Log detalhado dos dados recebidos para depuração
      storageLogger.debug(`📋 Dados completos recebidos para atualização:`, { 
        dados_recebidos: JSON.stringify(cavaloData),
        campos_presentes: Object.keys(cavaloData),
        operation: 'updateCavalo'
      });
      
      // Criar uma cópia dos dados para processamento
      const processedData = { ...cavaloData } as any;
      
      // Lista de campos que NUNCA devem ser removidos, mesmo se vazios
      // Isso garante que valores como breed não sejam removidos do banco
      const requiredFields = ['breed', 'name', 'birth_date'];
      
      // SISTEMA DE GENEALOGIA DUAL (ID + NOME)
      // Esta implementação suporta tanto cavalos do sistema (via ID) quanto cavalos externos (via nome)
      // Cada tipo de entrada é armazenado em campos diferentes: pai_id/pai_nome e mae_id/mae_nome

      // Processar campo pai (objeto tipo EntradaGenealogica)
      if ('pai' in processedData) {
        const valorPai = processedData.pai;
        
        // Log debug
        storageLogger.debug(`Processando campo pai:`, { 
          valor_pai: valorPai,
          tipo_pai: typeof valorPai
        });
        
        // Se o valor for null ou undefined, limpar ambos os campos
        if (valorPai === null || valorPai === undefined) {
          processedData.pai_id = null;
          processedData.pai_nome = null;
        }
        // Se for um objeto EntradaGenealogica
        else if (typeof valorPai === 'object' && valorPai !== null) {
          const entradaPai = valorPai as any;
          
          // Log detalhado para diagnóstico
          storageLogger.debug('Objeto EntradaGenealogica para pai:', {
            objeto_completo: entradaPai,
            tipo: entradaPai.tipo,
            cavaloSistemaId: entradaPai.cavaloSistemaId,
            cavaloNome: entradaPai.cavaloNome
          });
          
          if (entradaPai.tipo === 'sistema' && entradaPai.cavaloSistemaId) {
            // Para cavalos do sistema, usar ID e limpar nome
            processedData.pai_id = parseInt(entradaPai.cavaloSistemaId);
            processedData.pai_nome = null;
            
            storageLogger.debug('Pai do sistema selecionado:', { 
              pai_id: processedData.pai_id, 
              tipo: 'sistema' 
            });
          } 
          else if (entradaPai.tipo === 'externo' && entradaPai.cavaloNome) {
            // Para cavalos externos, usar nome e limpar ID
            processedData.pai_id = null;
            processedData.pai_nome = entradaPai.cavaloNome;
            
            storageLogger.debug('Pai externo definido:', { 
              pai_nome: processedData.pai_nome, 
              tipo: 'externo' 
            });
          }
          else if (entradaPai.tipo === 'nenhum') {
            // Para "nenhum", limpar ambos
            processedData.pai_id = null;
            processedData.pai_nome = null;
            
            storageLogger.debug('Pai definido como nenhum');
          }
        }
        
        // Remover o objeto pai que foi processado
        delete processedData.pai;
      }
      
      // Processar campo mae (mesmo padrão do pai)
      if ('mae' in processedData) {
        const valorMae = processedData.mae;
        
        // Log debug
        storageLogger.debug(`Processando campo mae:`, { 
          valor_mae: valorMae,
          tipo_mae: typeof valorMae
        });
        
        // Se o valor for null ou undefined, limpar ambos os campos
        if (valorMae === null || valorMae === undefined) {
          processedData.mae_id = null;
          processedData.mae_nome = null;
        }
        // Se for um objeto EntradaGenealogica
        else if (typeof valorMae === 'object' && valorMae !== null) {
          const entradaMae = valorMae as any;
          
          // Log detalhado para diagnóstico
          storageLogger.debug('Objeto EntradaGenealogica para mãe:', {
            objeto_completo: entradaMae,
            tipo: entradaMae.tipo,
            cavaloSistemaId: entradaMae.cavaloSistemaId,
            cavaloNome: entradaMae.cavaloNome
          });
          
          if (entradaMae.tipo === 'sistema' && entradaMae.cavaloSistemaId) {
            // Para éguas do sistema, usar ID e limpar nome
            processedData.mae_id = parseInt(entradaMae.cavaloSistemaId);
            processedData.mae_nome = null;
            
            storageLogger.debug('Mãe do sistema selecionada:', { 
              mae_id: processedData.mae_id, 
              tipo: 'sistema' 
            });
          } 
          else if (entradaMae.tipo === 'externo' && entradaMae.cavaloNome) {
            // Para éguas externas, usar nome e limpar ID
            processedData.mae_id = null;
            processedData.mae_nome = entradaMae.cavaloNome;
            
            storageLogger.debug('Mãe externa definida:', { 
              mae_nome: processedData.mae_nome, 
              tipo: 'externo' 
            });
          }
          else if (entradaMae.tipo === 'nenhum') {
            // Para "nenhum", limpar ambos
            processedData.mae_id = null;
            processedData.mae_nome = null;
            
            storageLogger.debug('Mãe definida como nenhuma');
          }
        }
        
        // Remover o objeto mae que foi processado
        delete processedData.mae;
      }
      
      // Campos legados para compatibilidade com código antigo
      // Limpar dados antigos para evitar conflitos
      if ('pai' in processedData) delete processedData.pai;
      if ('mae' in processedData) delete processedData.mae;
      
      // Processar os campos numéricos (real) para evitar erro com strings vazias
      ['peso', 'altura', 'valor_compra'].forEach(field => {
        // Verificar se o campo está presente no objeto antes de processá-lo
        if (field in processedData) {
          // Se o campo for uma string vazia, converter para null (permitir limpar o campo)
          if (processedData[field] === '') {
            processedData[field] = null;
            storageLogger.debug(`Campo numérico vazio convertido para null: ${field}`, { field });
          } 
          // Se for uma string contendo número, converta para número
          else if (typeof processedData[field] === 'string') {
            const numberValue = parseFloat(processedData[field]);
            if (!isNaN(numberValue)) {
              storageLogger.debug(`Campo numérico convertido: ${field}`, { 
                field, 
                originalValue: processedData[field],
                convertedValue: numberValue 
              });
              processedData[field] = numberValue;
            } else {
              // Se for string mas não puder ser convertida para número, definir como null
              processedData[field] = null;
              storageLogger.debug(`Campo numérico inválido convertido para null: ${field}`, { field, value: processedData[field] });
            }
          }
        }
      });
      
      // Processar campos de genealogia (texto) para permitir limpeza
      ['pai_nome', 'mae_nome', 'avoPaterno', 'avoMaterno'].forEach(field => {
        if (field in processedData) {
          // Se for string vazia, converter para null (permitir limpar o campo)
          if (processedData[field] === '') {
            processedData[field] = null;
            storageLogger.debug(`Campo de genealogia vazio convertido para null: ${field}`, { field });
          }
        }
      });
      
      // Remover campos undefined para evitar erros na atualização
      // Mas preservar campos obrigatórios mesmo que sejam vazios
      Object.keys(processedData).forEach(key => {
        // Não remover campos obrigatórios mesmo que sejam vazios
        if (processedData[key] === undefined && !requiredFields.includes(key)) {
          storageLogger.debug(`Campo removido por ser undefined: ${key}`);
          delete processedData[key];
        }
      });
      
      // Log detalhado após processamento
      storageLogger.info(`🔄 PROCESSAMENTO CONCLUÍDO - Enviando atualização para o banco:`, { 
        cavaloId: id,
        user_id: user_id, 
        processedData: JSON.stringify(processedData),
        campos_processados: Object.keys(processedData),
        campos_genealogia: {
          pai: processedData.pai || null,
          mae: processedData.mae || null,
          pai_id: processedData.pai_id || null,
          mae_id: processedData.mae_id || null
        }
      });
      
      // Executar a atualização no banco de dados
      const [cavalo] = await db
        .update(horses)
        .set(processedData)
        .where(and(eq(horses.id, id), eq(horses.user_id, user_id)))
        .returning();
      
      if (cavalo) {
        storageLogger.info(`✅ ATUALIZAÇÃO CONCLUÍDA COM SUCESSO`, { 
          cavaloId: cavalo.id,
          nome: cavalo.name,
          // Inclui informações específicas sobre genealogia para depuração
          genealogia: {
            fatherId: cavalo.fatherId,
            motherId: cavalo.motherId,
            // Mostra quais campos de fato foram salvos no banco
            pai_salvo: typeof cavalo.fatherId !== 'undefined' && cavalo.fatherId !== null,
            mae_salvo: typeof cavalo.motherId !== 'undefined' && cavalo.motherId !== null,
          },
          // Inclui informações sobre todos os campos atualizados
          campos_atualizados: Object.keys(processedData)
        });
      } else {
        storageLogger.warn(`⚠️ FALHA NA ATUALIZAÇÃO - Cavalo não encontrado`, { 
          cavaloId: id, 
          user_id 
        });
      }
      
      return cavalo || undefined;
    } catch (error) {
      // Log detalhado do erro usando nosso sistema de logging
      logDbError(
        error instanceof Error ? error : new Error(String(error)), 
        `UPDATE cavalos SET ... WHERE id=${id} AND user_id=${user_id}`,
        { cavaloId: id, user_id, error: String(error) }
      );
      
      // Re-throw para tratamento posterior
      throw error;
    }
  }
  
  async deleteCavalo(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(cavalos)
      .where(and(eq(cavalos.id, id), eq(cavalos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Manejo (Horse care) methods
  async getManejos(user_id: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(eq(manejos.user_id, user_id))
      .orderBy(asc(manejos.data)); // Ordenação crescente por data (mais antigo primeiro)
  }
  
  async getManejosByHorse(horse_id: number, user_id: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(and(eq(manejos.horse_id, horse_id), eq(manejos.user_id, user_id)))
      .orderBy(asc(manejos.data)); // Ordenação cronológica por data
  }
  
  async getManejo(id: number, user_id: number): Promise<Manejo | undefined> {
    const [manejo] = await db.select()
      .from(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)));
    return manejo || undefined;
  }
  
  async createManejo(insertManejo: InsertManejo): Promise<Manejo> {
    const [manejo] = await db
      .insert(manejos)
      .values(insertManejo)
      .returning();
    return manejo;
  }
  
  async updateManejo(id: number, user_id: number, manejoData: Partial<InsertManejo>): Promise<Manejo | undefined> {
    const [manejo] = await db
      .update(manejos)
      .set(manejoData)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)))
      .returning();
    return manejo || undefined;
  }
  
  async deleteManejo(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Arquivo (File) methods
  async getArquivos(user_id: number): Promise<Arquivo[]> {
    return db.select().from(arquivos).where(eq(arquivos.user_id, user_id));
  }
  
  async getArquivosByHorse(horse_id: number, user_id: number): Promise<Arquivo[]> {
    return db.select()
      .from(arquivos)
      .where(and(eq(arquivos.cavalo_id, horse_id), eq(arquivos.user_id, user_id)));
  }
  
  async getArquivo(id: number, user_id: number): Promise<Arquivo | undefined> {
    const [arquivo] = await db.select()
      .from(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.user_id, user_id)));
    return arquivo || undefined;
  }
  
  async createArquivo(insertArquivo: InsertArquivo): Promise<Arquivo> {
    const [arquivo] = await db
      .insert(arquivos)
      .values(insertArquivo)
      .returning();
    return arquivo;
  }
  
  async deleteArquivo(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(arquivos)
      .where(and(eq(arquivos.id, id), eq(arquivos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Evento (Schedule) methods
  async getEventos(user_id: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(eq(eventos.user_id, user_id))
      .orderBy(asc(eventos.data)); // Ordenação cronológica por data (asc = ascendente)
  }
  
  async getEventosByDate(data: string, user_id: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.data, data), eq(eventos.user_id, user_id)))
      .orderBy(asc(eventos.hora_inicio)); // Ordenação pelos horários
  }
  
  async getEventosByHorse(horse_id: number, user_id: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.horse_id, horse_id), eq(eventos.user_id, user_id)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEventosByManejo(manejoId: number, user_id: number): Promise<Evento[]> {
    return db.select()
      .from(eventos)
      .where(and(eq(eventos.manejoId, manejoId), eq(eventos.user_id, user_id)))
      .orderBy(asc(eventos.data)); // Ordenação por data ascendente
  }
  
  async getEvento(id: number, user_id: number): Promise<Evento | undefined> {
    const [evento] = await db.select()
      .from(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.user_id, user_id)));
    return evento || undefined;
  }
  
  async createEvento(insertEvento: InsertEvento): Promise<Evento> {
    // Verificar se os campos obrigatórios estão presentes
    if (!insertEvento.horaInicio) {
      insertEvento.horaInicio = "09:00"; // Valor padrão para hora de início
    }
    if (!insertEvento.horaFim) {
      insertEvento.horaFim = "10:00"; // Valor padrão para hora de fim
    }
    if (!insertEvento.tipo) {
      insertEvento.tipo = "Outro"; // Valor padrão para tipo
    }
    
    console.log("Criando evento com dados:", JSON.stringify(insertEvento, null, 2));
    
    const [evento] = await db
      .insert(eventos)
      .values(insertEvento)
      .returning();
    return evento;
  }
  
  async updateEvento(id: number, user_id: number, eventoData: Partial<InsertEvento>): Promise<Evento | undefined> {
    const [evento] = await db
      .update(eventos)
      .set(eventoData)
      .where(and(eq(eventos.id, id), eq(eventos.user_id, user_id)))
      .returning();
    return evento || undefined;
  }
  
  async deleteEvento(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(eventos)
      .where(and(eq(eventos.id, id), eq(eventos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  // Procedimentos Veterinários methods
  async getProcedimentosVet(user_id: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(eq(procedimentosVet.user_id, user_id))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByHorse(horse_id: number, user_id: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.horse_id, horse_id), eq(procedimentosVet.user_id, user_id)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByManejo(manejoId: number, user_id: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.manejoId, manejoId), eq(procedimentosVet.user_id, user_id)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentosVetByEvento(eventoId: number, user_id: number): Promise<ProcedimentoVet[]> {
    return db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.eventoId, eventoId), eq(procedimentosVet.user_id, user_id)))
      .orderBy(asc(procedimentosVet.data)); // Ordenação cronológica por data
  }
  
  async getProcedimentoVet(id: number, user_id: number): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db.select()
      .from(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.user_id, user_id)));
    return procedimento || undefined;
  }
  
  async createProcedimentoVet(insertProcedimento: InsertProcedimentoVet): Promise<ProcedimentoVet> {
    const [procedimento] = await db
      .insert(procedimentosVet)
      .values(insertProcedimento)
      .returning();
    return procedimento;
  }
  
  async updateProcedimentoVet(id: number, user_id: number, procedimentoData: Partial<InsertProcedimentoVet>): Promise<ProcedimentoVet | undefined> {
    const [procedimento] = await db
      .update(procedimentosVet)
      .set(procedimentoData)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.user_id, user_id)))
      .returning();
    return procedimento || undefined;
  }
  
  async deleteProcedimentoVet(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(procedimentosVet)
      .where(and(eq(procedimentosVet.id, id), eq(procedimentosVet.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Reprodução methods
  async getReproducoes(user_id: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(eq(reproducao.user_id, user_id))
      .orderBy(asc(reproducao.created_at)); // Ordenação por data de criação
  }
  
  async getReproducoesByHorse(horse_id: number, user_id: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.horse_id, horse_id), eq(reproducao.user_id, user_id)))
      .orderBy(asc(reproducao.created_at)); // Ordenação por data de criação
  }
  
  async getReproducoesByProcedimento(procedimentoId: number, user_id: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.procedimentoVetId, procedimentoId), eq(reproducao.user_id, user_id)))
      .orderBy(asc(reproducao.created_at)); // Ordenação por data de criação
  }
  
  async getReproducoesByEvento(eventoId: number, user_id: number): Promise<Reproducao[]> {
    return db.select()
      .from(reproducao)
      .where(and(eq(reproducao.eventoId, eventoId), eq(reproducao.user_id, user_id)))
      .orderBy(asc(reproducao.created_at)); // Ordenação por data de criação
  }
  
  async getReproducao(id: number, user_id: number): Promise<Reproducao | undefined> {
    const [item] = await db.select()
      .from(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.user_id, user_id)));
    return item || undefined;
  }
  
  async createReproducao(insertReproducao: InsertReproducao): Promise<Reproducao> {
    const [item] = await db
      .insert(reproducao)
      .values(insertReproducao)
      .returning();
    return item;
  }
  
  async updateReproducao(id: number, user_id: number, reproducaoData: Partial<InsertReproducao>): Promise<Reproducao | undefined> {
    const [item] = await db
      .update(reproducao)
      .set(reproducaoData)
      .where(and(eq(reproducao.id, id), eq(reproducao.user_id, user_id)))
      .returning();
    return item || undefined;
  }
  
  async deleteReproducao(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(reproducao)
      .where(and(eq(reproducao.id, id), eq(reproducao.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Medidas Físicas methods
  async getMedidasFisicas(user_id: number): Promise<MedidaMorfologica[]> {
    return db.select()
      .from(medidas_morfologicas)
      .where(eq(medidas_morfologicas.user_id, user_id))
      .orderBy(asc(medidas_morfologicas.data_medicao)); // Ordenação cronológica por data
  }
  
  async getMedidasFisicasByHorse(horse_id: number, user_id: number): Promise<MedidaMorfologica[]> {
    return db.select()
      .from(medidas_morfologicas)
      .where(and(eq(medidas_morfologicas.cavalo_id, horse_id), eq(medidas_morfologicas.user_id, user_id)))
      .orderBy(asc(medidas_morfologicas.data_medicao)); // Ordenação cronológica por data
  }
  
  async getMedidaFisica(id: number, user_id: number): Promise<MedidaMorfologica | undefined> {
    const [medida] = await db.select()
      .from(medidas_morfologicas)
      .where(and(eq(medidas_morfologicas.id, id), eq(medidas_morfologicas.user_id, user_id)));
    return medida || undefined;
  }
  
  async createMedidaFisica(insertMedida: InsertMedidaMorfologica): Promise<MedidaMorfologica> {
    const [medida] = await db
      .insert(medidas_morfologicas)
      .values(insertMedida)
      .returning();
    return medida;
  }
  
  async updateMedidaFisica(id: number, user_id: number, medidaData: Partial<InsertMedidaMorfologica>): Promise<MedidaMorfologica | undefined> {
    const [medida] = await db
      .update(medidas_morfologicas)
      .set(medidaData)
      .where(and(eq(medidas_morfologicas.id, id), eq(medidas_morfologicas.user_id, user_id)))
      .returning();
    return medida || undefined;
  }
  
  async deleteMedidaFisica(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(medidas_morfologicas)
      .where(and(eq(medidas_morfologicas.id, id), eq(medidas_morfologicas.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Nutrição methods
  async getNutricoes(user_id: number): Promise<Nutricao[]> {
    return db.select()
      .from(nutricao)
      .where(eq(nutricao.user_id, user_id))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricoesByHorse(horse_id: number, user_id: number): Promise<Nutricao[]> {
    // Validação para evitar problemas de tipos
    if (isNaN(horse_id) || isNaN(user_id)) {
      console.error(`Valores inválidos para consulta: horse_id=${horse_id}, user_id=${user_id}`);
      return [];
    }
    
    return db.select()
      .from(nutricao)
      .where(and(eq(nutricao.horse_id, horse_id), eq(nutricao.user_id, user_id)))
      .orderBy(asc(nutricao.data)); // Ordenação cronológica por data
  }
  
  async getNutricao(id: number, user_id: number): Promise<Nutricao | undefined> {
    const [item] = await db.select()
      .from(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.user_id, user_id)));
    return item || undefined;
  }
  
  async createNutricao(insertNutricao: InsertNutricao): Promise<Nutricao> {
    const [item] = await db
      .insert(nutricao)
      .values(insertNutricao)
      .returning();
    return item;
  }
  
  async updateNutricao(id: number, user_id: number, nutricaoData: Partial<InsertNutricao>): Promise<Nutricao | undefined> {
    const [item] = await db
      .update(nutricao)
      .set(nutricaoData)
      .where(and(eq(nutricao.id, id), eq(nutricao.user_id, user_id)))
      .returning();
    return item || undefined;
  }
  
  async deleteNutricao(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(nutricao)
      .where(and(eq(nutricao.id, id), eq(nutricao.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  // Métodos Morfologia
  async getMorfologias(user_id: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologia)
      .where(eq(morfologia.user_id, user_id))
      .orderBy(asc(morfologia.dataMedicao));
  }

  async getMorfologiasByHorse(horse_id: number, user_id: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologia)
      .where(and(eq(morfologia.horse_id, horse_id), eq(morfologia.user_id, user_id)))
      .orderBy(asc(morfologia.dataMedicao));
  }

  async getMorfologia(id: number, user_id: number): Promise<Morfologia | undefined> {
    const [item] = await db.select()
      .from(morfologia)
      .where(and(eq(morfologia.id, id), eq(morfologia.user_id, user_id)));
    return item || undefined;
  }

  async createMorfologia(morfologiaData: InsertMorfologia): Promise<Morfologia> {
    // Calcular pontuação total caso não esteja definida
    if (!morfologiaData.pontuacaoTotal) {
      const pontuacoes = [
        morfologiaData.pontuacaoCabeca,
        morfologiaData.pontuacaoPescoco,
        morfologiaData.pontuacaoEspalda,
        morfologiaData.pontuacaoDorso,
        morfologiaData.pontuacaoGarupa,
        morfologiaData.pontuacaoMembros,
        morfologiaData.pontuacaoAprumos,
        morfologiaData.pontuacaoAndamento,
        morfologiaData.pontuacaoHarmonia
      ];

      // Filtrar pontuações definidas e calcular média
      const pontuacoesFiltradas = pontuacoes.filter(p => p !== undefined && p !== null) as number[];
      if (pontuacoesFiltradas.length > 0) {
        const media = pontuacoesFiltradas.reduce((a, b) => a + b, 0) / pontuacoesFiltradas.length;
        morfologiaData.pontuacaoTotal = parseFloat(media.toFixed(2));
      }
    }

    const [item] = await db
      .insert(morfologia)
      .values(morfologiaData)
      .returning();
    return item;
  }

  async updateMorfologia(id: number, user_id: number, morfologiaData: Partial<InsertMorfologia>): Promise<Morfologia | undefined> {
    // Se houver atualizações de pontuações, recalcular pontuação total
    if (
      morfologiaData.pontuacaoCabeca !== undefined ||
      morfologiaData.pontuacaoPescoco !== undefined ||
      morfologiaData.pontuacaoEspalda !== undefined ||
      morfologiaData.pontuacaoDorso !== undefined ||
      morfologiaData.pontuacaoGarupa !== undefined ||
      morfologiaData.pontuacaoMembros !== undefined ||
      morfologiaData.pontuacaoAprumos !== undefined ||
      morfologiaData.pontuacaoAndamento !== undefined ||
      morfologiaData.pontuacaoHarmonia !== undefined
    ) {
      // Buscar item atual para ter todos os dados
      const itemAtual = await this.getMorfologia(id, user_id);
      if (itemAtual) {
        // Combinar dados atuais com atualizações
        const pontuacoes = [
          morfologiaData.pontuacaoCabeca ?? itemAtual.pontuacaoCabeca,
          morfologiaData.pontuacaoPescoco ?? itemAtual.pontuacaoPescoco,
          morfologiaData.pontuacaoEspalda ?? itemAtual.pontuacaoEspalda,
          morfologiaData.pontuacaoDorso ?? itemAtual.pontuacaoDorso,
          morfologiaData.pontuacaoGarupa ?? itemAtual.pontuacaoGarupa,
          morfologiaData.pontuacaoMembros ?? itemAtual.pontuacaoMembros,
          morfologiaData.pontuacaoAprumos ?? itemAtual.pontuacaoAprumos,
          morfologiaData.pontuacaoAndamento ?? itemAtual.pontuacaoAndamento,
          morfologiaData.pontuacaoHarmonia ?? itemAtual.pontuacaoHarmonia
        ];

        // Filtrar pontuações definidas e calcular média
        const pontuacoesFiltradas = pontuacoes.filter(p => p !== undefined && p !== null) as number[];
        if (pontuacoesFiltradas.length > 0) {
          const media = pontuacoesFiltradas.reduce((a, b) => a + b, 0) / pontuacoesFiltradas.length;
          morfologiaData.pontuacaoTotal = parseFloat(media.toFixed(2));
        }
      }
    }

    const [item] = await db
      .update(morfologia)
      .set(morfologiaData)
      .where(and(eq(morfologia.id, id), eq(morfologia.user_id, user_id)))
      .returning();
    return item || undefined;
  }

  async deleteMorfologia(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(morfologia)
      .where(and(eq(morfologia.id, id), eq(morfologia.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  // Métodos Histórico de Desempenho
  async getDesempenhos(user_id: number): Promise<DesempenhoHistorico[]> {
    return db.select()
      .from(desempenhoHistorico)
      .where(eq(desempenhoHistorico.user_id, user_id))
      .orderBy(desc(desempenhoHistorico.data)); // Mais recentes primeiro
  }

  async getDesempenhosByHorse(horse_id: number, user_id: number): Promise<DesempenhoHistorico[]> {
    return db.select()
      .from(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.horse_id, horse_id), eq(desempenhoHistorico.user_id, user_id)))
      .orderBy(desc(desempenhoHistorico.data)); // Mais recentes primeiro
  }

  async getDesempenho(id: number, user_id: number): Promise<DesempenhoHistorico | undefined> {
    const [item] = await db.select()
      .from(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.user_id, user_id)));
    return item || undefined;
  }

  async createDesempenho(desempenhoData: InsertDesempenhoHistorico): Promise<DesempenhoHistorico> {
    const [item] = await db
      .insert(desempenhoHistorico)
      .values(desempenhoData)
      .returning();
    return item;
  }

  async updateDesempenho(id: number, user_id: number, desempenhoData: Partial<InsertDesempenhoHistorico>): Promise<DesempenhoHistorico | undefined> {
    const [item] = await db
      .update(desempenhoHistorico)
      .set(desempenhoData)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.user_id, user_id)))
      .returning();
    return item || undefined;
  }

  async deleteDesempenho(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(desempenhoHistorico)
      .where(and(eq(desempenhoHistorico.id, id), eq(desempenhoHistorico.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  // Métodos Genealogia
  async getGenealogias(user_id: number): Promise<Genealogia[]> {
    return db.select()
      .from(genealogia)
      .where(eq(genealogia.user_id, user_id));
  }

  async getGenealogiaByHorse(horse_id: number, user_id: number): Promise<Genealogia | undefined> {
    const [item] = await db.select()
      .from(genealogia)
      .where(and(eq(genealogia.horse_id, horse_id), eq(genealogia.user_id, user_id)));
    return item || undefined;
  }

  async createGenealogia(genealogiaData: InsertGenealogia): Promise<Genealogia> {
    // Calcular coeficiente de consanguinidade se não estiver definido
    if (genealogiaData.coeficienteConsanguinidade === undefined) {
      genealogiaData.coeficienteConsanguinidade = 0; // Valor padrão, será calculado depois
    }

    const [item] = await db
      .insert(genealogia)
      .values(genealogiaData)
      .returning();
    
    // Após criar, recalcular o coeficiente de consanguinidade
    await this.calcularConsanguinidade(genealogiaData.horse_id, genealogiaData.user_id);
    
    return item;
  }

  async updateGenealogia(id: number, user_id: number, genealogiaData: Partial<InsertGenealogia>): Promise<Genealogia | undefined> {
    const [item] = await db
      .update(genealogia)
      .set(genealogiaData)
      .where(and(eq(genealogia.id, id), eq(genealogia.user_id, user_id)))
      .returning();
    
    // Se houver atualização de ancestrais, recalcular consanguinidade
    if (item && (
      genealogiaData.pai !== undefined || 
      genealogiaData.mae !== undefined ||
      genealogiaData.avoPaternoId !== undefined ||
      genealogiaData.avoMaternoId !== undefined
    )) {
      await this.calcularConsanguinidade(item.horse_id, user_id);
    }
    
    return item || undefined;
  }

  async deleteGenealogia(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(genealogia)
      .where(and(eq(genealogia.id, id), eq(genealogia.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  async calcularConsanguinidade(horse_id: number, user_id: number): Promise<number> {
    // Obter a genealogia do cavalo
    const gen = await this.getGenealogiaByHorse(horse_id, user_id);
    if (!gen) return 0;

    // Algoritmo simplificado para cálculo de consanguinidade até 3 gerações
    let coeficiente = 0;
    
    // Verificar ancestrais comuns e calcular coeficiente
    // Este é um algoritmo simplificado que verifica apenas coincidências diretas
    
    // Exemplo: verificar se há algum avô que também seja pai
    if (gen.pai && (gen.pai === gen.avoMaterno || gen.pai === gen.avoPaterno)) {
      coeficiente += 0.125; // Adiciona 12.5% por cada ancestral comum
    }
    
    if (gen.mae && (gen.mae === gen.avoPaterno || gen.mae === gen.avoMaterno)) {
      coeficiente += 0.125;
    }
    
    // Verificar bisavós comuns com avós
    if (gen.avoPaterno && (
      gen.avoPaterno === gen.bisavoPaternoPaterno || 
      gen.avoPaterno === gen.bisavoMaternoPaterno || 
      gen.avoPaterno === gen.bisavoPaternoMaterno || 
      gen.avoPaterno === gen.bisavoMaternoMaterno
    )) {
      coeficiente += 0.0625; // 6.25% para cada bisavô comum
    }
    
    // E assim por diante para outros casos...
    
    // Limitar o coeficiente a 1 (100%)
    coeficiente = Math.min(coeficiente, 1);
    
    // Atualizar o coeficiente na tabela
    await db
      .update(genealogia)
      .set({ coeficienteConsanguinidade: coeficiente })
      .where(and(eq(genealogia.horse_id, horse_id), eq(genealogia.user_id, user_id)));
    
    return coeficiente;
  }

  // Métodos Sugestões de Cruzamento
  async getSugestoesCruzamento(user_id: number): Promise<SugestoesCruzamento[]> {
    return db.select()
      .from(sugestoesCruzamento)
      .where(eq(sugestoesCruzamento.user_id, user_id))
      .orderBy(desc(sugestoesCruzamento.dataSugestao)); // Mais recentes primeiro
  }

  async getSugestoesCruzamentoByHorse(horseIdBase: number, user_id: number): Promise<SugestoesCruzamento[]> {
    return db.select()
      .from(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.horseIdBase, horseIdBase), eq(sugestoesCruzamento.user_id, user_id)))
      .orderBy(desc(sugestoesCruzamento.pontuacaoCompatibilidade)); // Melhores pontuações primeiro
  }

  async getSugestoesCruzamentoById(id: number, user_id: number): Promise<SugestoesCruzamento | undefined> {
    const [item] = await db.select()
      .from(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.id, id), eq(sugestoesCruzamento.user_id, user_id)));
    return item || undefined;
  }

  async createSugestaoCruzamento(sugestaoData: InsertSugestoesCruzamento): Promise<SugestoesCruzamento> {
    const [item] = await db
      .insert(sugestoesCruzamento)
      .values(sugestaoData)
      .returning();
    return item;
  }

  async deleteSugestaoCruzamento(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(sugestoesCruzamento)
      .where(and(eq(sugestoesCruzamento.id, id), eq(sugestoesCruzamento.user_id, user_id)))
      .returning();
    return result.length > 0;
  }

  async gerarSugestoesCruzamento(horseIdBase: number, objetivo: string, user_id: number): Promise<SugestoesCruzamento[]> {
    // Obter informações do cavalo base
    const cavaloBase = await this.getCavalo(horseIdBase, user_id);
    if (!cavaloBase) {
      throw new Error("Cavalo base não encontrado");
    }
    
    // Obter todos os cavalos possíveis para cruzamento (sexo oposto)
    const sexoOposto = cavaloBase.sexo === 'macho' ? 'femea' : 'macho';
    const cavalosCompativeis = await db.select()
      .from(cavalos)
      .where(and(
        eq(cavalos.user_id, user_id),
        eq(cavalos.sexo, sexoOposto),
        // Cavalo não pode estar inativo
        eq(cavalos.status, 'ativo')
      ));
    
    if (cavalosCompativeis.length === 0) {
      return []; // Não há cavalos compatíveis para cruzamento
    }
    
    // Obter informações de morfologia e desempenho do cavalo base
    const morfologiaBase = await this.getMorfologiasByHorse(horseIdBase, user_id);
    const desempenhoBase = await this.getDesempenhosByHorse(horseIdBase, user_id);
    const genealogiaBase = await this.getGenealogiaByHorse(horseIdBase, user_id);
    
    // Lista para armazenar as sugestões
    const sugestoes: SugestoesCruzamento[] = [];
    
    // Avaliar cada cavalo compatível
    for (const candidato of cavalosCompativeis) {
      if (candidato.id === horseIdBase) continue; // Pular o próprio cavalo
      
      // Obter informações de morfologia e desempenho do candidato
      const morfologiaCandidato = await this.getMorfologiasByHorse(candidato.id, user_id);
      const desempenhoCandidato = await this.getDesempenhosByHorse(candidato.id, user_id);
      const genealogiaCandidato = await this.getGenealogiaByHorse(candidato.id, user_id);
      
      // Calcular pontuação de compatibilidade (algoritmo simplificado)
      let pontuacaoCompatibilidade = 70; // Pontuação base
      
      // Análises para diferentes objetivos
      let analiseMorfologia = "Compatibilidade morfológica padrão.";
      let analiseDesempenho = "Compatibilidade de desempenho padrão.";
      let analiseConsanguinidade = "Sem informações de consanguinidade detalhadas.";
      
      // Pontuação morfológica
      let prevPotencialMorfologico = 7; // Valor padrão
      if (morfologiaBase.length > 0 && morfologiaCandidato.length > 0) {
        const ultimaMorfologiaBase = morfologiaBase[0];
        const ultimaMorfologiaCandidato = morfologiaCandidato[0];
        
        // Análise morfológica baseada no objetivo
        if (objetivo === 'morfologia') {
          // Para objetivo morfológico, valorizar candidatos com boa pontuação total
          if (ultimaMorfologiaCandidato.pontuacaoTotal && ultimaMorfologiaCandidato.pontuacaoTotal > 8) {
            pontuacaoCompatibilidade += 15;
            analiseMorfologia = "Excelente morfologia do candidato complementa o cavalo base.";
            prevPotencialMorfologico = 9;
          } else if (ultimaMorfologiaCandidato.pontuacaoTotal && ultimaMorfologiaCandidato.pontuacaoTotal > 7) {
            pontuacaoCompatibilidade += 10;
            analiseMorfologia = "Boa morfologia do candidato complementa o cavalo base.";
            prevPotencialMorfologico = 8;
          }
        }
      }
      
      // Pontuação de desempenho
      let prevPotencialDesempenho = 7; // Valor padrão
      if (desempenhoBase.length > 0 && desempenhoCandidato.length > 0) {
        // Análise de desempenho baseada no objetivo
        if (objetivo === 'desempenho') {
          // Para objetivo de desempenho, valorizar candidatos com bons resultados
          if (desempenhoCandidato.some(d => d.resultado?.toLowerCase().includes('primeiro') || d.resultado?.toLowerCase().includes('1º'))) {
            pontuacaoCompatibilidade += 15;
            analiseDesempenho = "Excelente histórico de desempenho em competições.";
            prevPotencialDesempenho = 9;
          } else if (desempenhoCandidato.some(d => d.resultado?.toLowerCase().includes('segundo') || d.resultado?.toLowerCase().includes('2º'))) {
            pontuacaoCompatibilidade += 10;
            analiseDesempenho = "Bom histórico de desempenho em competições.";
            prevPotencialDesempenho = 8;
          }
        }
      }
      
      // Análise de consanguinidade
      let prevPotencialTemperamento = 7; // Valor padrão
      if (genealogiaBase && genealogiaCandidato) {
        // Verificar ancestrais comuns (simplificado)
        const ancestraisComuns = [];
        if (genealogiaBase.pai && (genealogiaBase.pai === genealogiaCandidato.pai || 
                                    genealogiaBase.pai === genealogiaCandidato.mae)) {
          ancestraisComuns.push(genealogiaBase.pai);
        }
        if (genealogiaBase.mae && (genealogiaBase.mae === genealogiaCandidato.pai || 
                                    genealogiaBase.mae === genealogiaCandidato.mae)) {
          ancestraisComuns.push(genealogiaBase.mae);
        }
        
        if (ancestraisComuns.length > 0) {
          // Penalizar consanguinidade próxima
          pontuacaoCompatibilidade -= 20;
          analiseConsanguinidade = `Atenção: Consanguinidade detectada com ${ancestraisComuns.length} ancestrais comuns.`;
          prevPotencialTemperamento = 5;
        } else {
          analiseConsanguinidade = "Não foram detectados ancestrais comuns próximos.";
          prevPotencialTemperamento = 7;
        }
      }
      
      // Análise de docilidade (objetivo temperamento)
      if (objetivo === 'docilidade') {
        if (candidato.notes?.toLowerCase().includes('dócil') || candidato.notes?.toLowerCase().includes('calmo')) {
          pontuacaoCompatibilidade += 15;
          prevPotencialTemperamento = 9;
        }
      }
      
      // Análise de força (objetivo força)
      if (objetivo === 'força') {
        // Verificar se o candidato tem bom porte físico
        const ultimaMedidaCandidato = await this.getMedidasFisicasByHorse(candidato.id, user_id);
        if (ultimaMedidaCandidato.length > 0 && ultimaMedidaCandidato[0].peso && ultimaMedidaCandidato[0].peso > 500) {
          pontuacaoCompatibilidade += 15;
          prevPotencialMorfologico = 9;
        }
      }
      
      // Limitar pontuação entre 0 e 100
      pontuacaoCompatibilidade = Math.max(0, Math.min(100, pontuacaoCompatibilidade));
      
      // Criar recomendação com justificativa
      const justificativa = `Baseado na análise de compatibilidade para o objetivo "${objetivo}", este cruzamento apresenta 
                            pontuação de ${pontuacaoCompatibilidade.toFixed(1)}/100.`;
      
      // Recomendações baseadas na pontuação
      let recomendacoes = "";
      if (pontuacaoCompatibilidade >= 85) {
        recomendacoes = "Cruzamento altamente recomendado. Alta probabilidade de resultados excelentes.";
      } else if (pontuacaoCompatibilidade >= 70) {
        recomendacoes = "Cruzamento recomendado. Boa probabilidade de resultados positivos.";
      } else if (pontuacaoCompatibilidade >= 50) {
        recomendacoes = "Cruzamento aceitável. Resultados medianos esperados.";
      } else {
        recomendacoes = "Cruzamento não recomendado. Considere outras opções.";
      }
      
      // Criar entrada de sugestão
      const hoje = new Date().toISOString().split('T')[0];
      const sugestao: InsertSugestoesCruzamento = {
        horseIdBase,
        horseIdSugerido: candidato.id,
        dataSugestao: hoje,
        objetivo,
        pontuacaoCompatibilidade,
        analiseMorfologia,
        analiseDesempenho,
        analiseConsanguinidade,
        prevPotencialMorfologico,
        prevPotencialDesempenho,
        prevPotencialTemperamento,
        justificativa,
        recomendacoes,
        user_id
      };
      
      // Salvar no banco de dados
      const novaSugestao = await this.createSugestaoCruzamento(sugestao);
      sugestoes.push(novaSugestao);
    }
    
    // Retornar sugestões ordenadas por compatibilidade (melhor primeiro)
    return sugestoes.sort((a, b) => b.pontuacaoCompatibilidade - a.pontuacaoCompatibilidade);
  }
}

export const storage = new DatabaseStorage();
