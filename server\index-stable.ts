import express from "express";
import cors from "cors";
import path from "path";
import { createServer } from "http";

const app = express();

// Configuração básica e robusta
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Health check sempre funcional
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'EquiGestor AI',
    version: '1.0.0'
  });
});

// Rota básica para testar
app.get('/api/test', (_req, res) => {
  res.json({ message: 'EquiGestor AI funcionando!' });
});

// Servir arquivos estáticos como fallback
app.use(express.static(path.join(process.cwd(), 'client', 'public')));

// Catch-all para SPA
app.get('*', (_req, res) => {
  res.sendFile(path.join(process.cwd(), 'client', 'index.html'));
});

// Tratamento de erros
app.use((err: any, req: any, res: any, next: any) => {
  console.error('Erro:', err);
  if (!res.headersSent) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

const server = createServer(app);
const port = 5000;

server.listen(port, "0.0.0.0", () => {
  console.log(`✅ EquiGestor AI estável iniciado na porta ${port}`);
  console.log(`🌐 Acesse: http://localhost:${port}`);
});

server.on('error', (error) => {
  console.error('Erro no servidor:', error);
});

// Tratamento de sinais
process.on('SIGTERM', () => {
  console.log('Encerrando servidor...');
  server.close();
});

process.on('SIGINT', () => {
  console.log('Encerrando servidor...');
  server.close();
});