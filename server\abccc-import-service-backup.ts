/**
 * Serviço de Importação de Registros Genealógicos da ABCCC
 *
 * Este módulo fornece funções para processar registros da ABCCC e
 * importá-los para o banco de dados EquiGestor, atualizando ou
 * criando cavalos e suas relações genealógicas.
 *
 * O serviço atua como uma camada entre o parser de PDF e o banco de dados,
 * garantindo que as operações sejam realizadas com integridade e sem impacto
 * nos módulos existentes.
 */

import path from "path";
import * as fs from "fs";
import { db } from "./db";
import { cavalos, genealogia, pelagens } from "../shared/schema";
import { eq, sql, ilike, or, and } from "drizzle-orm";
import {
  CavaloInfo,
  GenealogiaInfo,
  processarPdfABCCC,
} from "./abccc-pdf-parser";
import { processarPdfComOpenAI } from "./openai-pdf-service";
import {
  ABCCCScrapedData,
  getCriouloDataByRegistro,
} from "./abccc-scraper-service";
import { Cavalo, InsertCavalo } from "../shared/schema";
import { getModuleLogger } from "./logger";
import { updateGenealogySync, updateParentRelationshipSync } from "./genealogy-sync-service";

const logger = getModuleLogger("abccc-import");

/**
 * Interface para o resultado do processamento de cavalos
 * Elimina a necessidade de type casting com 'as any'
 */
interface ResultadoProcessamento {
  cavalo: Cavalo;
  novo: boolean;
}

/**
 * Salva log de debug para importação de registros
 */
async function salvarLogDebugImportacao(logId: string, conteudo: string): Promise<void> {
  try {
    // Garantir que o diretório de logs existe
    if (!fs.existsSync('./logs')) {
      fs.mkdirSync('./logs', { recursive: true });
    }
    
    const nomeArquivo = `importacao_${logId}.json`;
    const caminhoArquivo = `./logs/${nomeArquivo}`;
    
    fs.writeFileSync(caminhoArquivo, conteudo, 'utf8');
    logger.debug(`Log de debug salvo: ${nomeArquivo}`);
  } catch (error) {
    logger.error(`Erro ao salvar log de debug: ${error}`);
  }
}

/**
 * Normaliza valores de sexo do PDF para o formato do enum do banco
 */
function normalizarSexo(sexo: string | null): string | null {
  if (!sexo) return null;
  
  const sexoUpper = sexo.toUpperCase().trim();
  
  switch (sexoUpper) {
    case 'FÊMEA':
    case 'FEMEA':
    case 'F':
      return 'Fêmea';
    case 'MACHO':
    case 'M':
      return 'Macho';
    case 'CASTRADO':
    case 'MACHO CASTRADO':
    case 'MACHO (CASTRADO)':
      return 'Macho (Castrado)';
    case 'GARANHÃO':
    case 'GARANHAO':
      return 'Garanhão';
    case 'ÉGUA':
    case 'EGUA':
      return 'Égua';
    default:
      // Se não conseguir mapear, retorna o valor original
      return sexo;
  }
}

// Interface estendida para CavaloInfo (familiares podem conter ID)
interface CavaloInfoCompleto extends CavaloInfo {
  id?: number;
}

export interface ResumoImportacao {
  cavaloPrincipal: {
    nome: string;
    registro: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  };
  familiares: {
    tipo: string;
    nome: string;
    registro?: string;
    novo: boolean;
    id?: number;
    sexo?: string;
    nascimento?: string;
    pelagem?: string;
    criador?: string;
    proprietario?: string;
    inspetor?: string;
  }[];
  detalhes: {
    nomeArquivo: string;
    dataImportacao: string;
    totalAnimais: number;
    novosAnimais: number;
    atualizados: number;
    erros: string[];
    dadosComplementares?: boolean;
  };
  logDebug: string;
  dadosOriginaisPdf?: any;
  dadosComplementares?: any;
}

/**
 * Pré-visualiza dados de um arquivo PDF da ABCCC sem salvá-los no banco de dados
 *
 * @param filePath Caminho do arquivo PDF
 * @param user_id ID do usuário realizando a pré-visualização
 * @returns Resumo dos dados extraídos para pré-visualização
 */
export async function previsualizarRegistroABCCC(
  filePath: string,
  user_id: number,
): Promise<ResumoImportacao> {
  try {
    // Gerar um nome de arquivo para o log usando apenas o nome do arquivo sem o caminho
    const nomeArquivo = path.basename(filePath);
    const dataAtual = new Date();

    // Log de início do processo
    logger.info(
      `Iniciando pré-visualização do arquivo ${nomeArquivo} para o usuário ${user_id}`,
    );

    // Processar o PDF utilizando OpenAI para melhor extração
    logger.info(`Utilizando OpenAI para extrair dados do PDF ${nomeArquivo}`);

    // Obter resultado do processamento com OpenAI
    const resultadoOpenAI = await processarPdfComOpenAI(filePath);

    // Verificar se temos dados válidos
    if (
      !resultadoOpenAI.dados ||
      !resultadoOpenAI.dados.cavalo ||
      !resultadoOpenAI.dados.cavalo.nome
    ) {
      throw new Error(
        "Não foi possível extrair os dados do cavalo principal do PDF",
      );
    }

    // Salvar o log de debug da extração
    const logId = `preview_${Date.now()}`;
    await salvarLogDebugImportacao(logId, JSON.stringify(resultadoOpenAI, null, 2));

    // Inicializar o resumo da importação
    const resumo: ResumoImportacao = {
      cavaloPrincipal: {
        nome: resultadoOpenAI.dados.cavalo.nome,
        registro: resultadoOpenAI.dados.cavalo.registro || "",
        novo: true, // Assumimos como novo para a pré-visualização, será verificado na importação real
        sexo: resultadoOpenAI.dados.cavalo.sexo,
        nascimento: resultadoOpenAI.dados.cavalo.nascimento,
        pelagem: resultadoOpenAI.dados.cavalo.pelagem,
        criador: resultadoOpenAI.dados.cavalo.criador,
        proprietario: resultadoOpenAI.dados.cavalo.proprietario,
        inspetor: resultadoOpenAI.dados.cavalo.inspetor,
      },
      familiares: [],
      detalhes: {
        nomeArquivo: nomeArquivo,
        dataImportacao: dataAtual.toLocaleString(),
        totalAnimais: 1, // Inicializa com 1 (o cavalo principal)
        novosAnimais: 0,
        atualizados: 0,
        erros: [],
      },
      logDebug: logId,
      dadosOriginaisPdf: resultadoOpenAI.dados,
    };

    // Enriquecer com dados complementares se possível
    try {
      if (resultadoOpenAI.dados.cavalo.registro) {
        logger.info(
          `Buscando dados complementares no StudBook para ${resultadoOpenAI.dados.cavalo.registro}`,
        );
        const dadosComplementares = await getCriouloDataByRegistro(
          resultadoOpenAI.dados.cavalo.registro,
        );

        // Vamos apenas armazenar dados complementares sem sobrescrever os dados do PDF na pré-visualização
        // Isso garante que os valores extraídos do PDF sempre serão exibidos, mesmo que sejam "Não informado"
        if (dadosComplementares) {
          // Armazenar dados complementares apenas para referência, sem sobrescrever os dados do PDF
          resumo.detalhes.dadosComplementares = true;
          resumo.dadosComplementares = dadosComplementares;
          
          // Comentamos a substituição para garantir que os valores do PDF sejam preservados
          // Object.assign(resumo.cavaloPrincipal, {
          //   sexo: dadosComplementares.sexo || resumo.cavaloPrincipal.sexo,
          //   nascimento: dadosComplementares.nascimento || resumo.cavaloPrincipal.nascimento,
          //   pelagem: dadosComplementares.pelagem || resumo.cavaloPrincipal.pelagem,
          //   criador: dadosComplementares.criador || resumo.cavaloPrincipal.criador,
          //   proprietario: dadosComplementares.proprietario || resumo.cavaloPrincipal.proprietario,
          // });
        }
      }
    } catch (error: any) {
      logger.warn(
        `Não foi possível obter dados complementares do StudBook: ${error.message}`,
      );
      resumo.detalhes.erros.push(
        `Aviso: Não foi possível obter dados complementares do StudBook: ${error.message}`,
      );
    }

    // Verificar existência do cavalo principal no sistema (apenas para pré-visualização)
    try {
      if (resultadoOpenAI.dados.cavalo.registro) {
        const cavaloPrincipalExistente = await buscarCavaloPorRegistro(
          resultadoOpenAI.dados.cavalo.registro,
        );

        if (cavaloPrincipalExistente) {
          resumo.cavaloPrincipal.novo = false;
          resumo.cavaloPrincipal.id = cavaloPrincipalExistente.id;
        }
      }
    } catch (error: any) {
      logger.warn(
        `Erro ao verificar existência prévia do cavalo principal: ${error.message}`,
      );
    }

    // Processar familiares para pré-visualização
    if (resultadoOpenAI.dados.pai) {
      const paiInfo = await prepararInfoFamiliar(
        resultadoOpenAI.dados.pai,
        "Pai",
        resumo,
      );
      resumo.familiares.push(paiInfo);
      resumo.detalhes.totalAnimais++;
    }

    if (resultadoOpenAI.dados.mae) {
      const maeInfo = await prepararInfoFamiliar(
        resultadoOpenAI.dados.mae,
        "Mãe",
        resumo,
      );
      resumo.familiares.push(maeInfo);
      resumo.detalhes.totalAnimais++;
    }

    // Processar avós se disponíveis
    if (resultadoOpenAI.dados.avoPai) {
      const avoPaiInfo = await prepararInfoFamiliar(
        resultadoOpenAI.dados.avoPai,
        "Avô Paterno",
        resumo,
      );
      resumo.familiares.push(avoPaiInfo);
      resumo.detalhes.totalAnimais++;
    }

    if (resultadoOpenAI.dados.avaMae) {
      const avaMaeInfo = await prepararInfoFamiliar(
        resultadoOpenAI.dados.avaMae,
        "Avó Paterna",
        resumo,
      );
      resumo.familiares.push(avaMaeInfo);
      resumo.detalhes.totalAnimais++;
    }

    if (resultadoOpenAI.dados.avoPai2) {
      const avoPai2Info = await prepararInfoFamiliar(
        resultadoOpenAI.dados.avoPai2,
        "Avô Materno",
        resumo,
      );
      resumo.familiares.push(avoPai2Info);
      resumo.detalhes.totalAnimais++;
    }

    if (resultadoOpenAI.dados.avaMae2) {
      const avaMae2Info = await prepararInfoFamiliar(
        resultadoOpenAI.dados.avaMae2,
        "Avó Materna",
        resumo,
      );
      resumo.familiares.push(avaMae2Info);
      resumo.detalhes.totalAnimais++;
    }

    // Calcular quantidade de animais novos vs. existentes
    resumo.detalhes.novosAnimais =
      resumo.familiares.filter((f) => f.novo).length +
      (resumo.cavaloPrincipal.novo ? 1 : 0);
    resumo.detalhes.atualizados =
      resumo.detalhes.totalAnimais - resumo.detalhes.novosAnimais;

    logger.info(
      `Pré-visualização concluída: ${resumo.detalhes.totalAnimais} animais identificados, ${resumo.detalhes.novosAnimais} novos`,
    );

    return resumo;
  } catch (error: any) {
    logger.error(`Erro durante a pré-visualização: ${error.message}`);
    throw error;
  }
}

/**
 * Função auxiliar para preparar informações de familiares na pré-visualização
 * Utiliza apenas os dados extraídos do PDF sem consultar site externo
 * 
 * Esta função preserva valores "Não informado" para todos os campos
 */
async function prepararInfoFamiliar(
  info: CavaloInfo,
  tipo: string,
  resumo: ResumoImportacao,
) {
  // Garantimos que todos os valores serão preservados exatamente como vieram do PDF
  // incluindo strings "Não informado" e semelhantes
  const familiar: any = {
    tipo,
    nome: info.nome,
    registro: info.registro,
    novo: true,
    // Preservamos todos os campos como estão no PDF, sem converter para null
    sexo: info.sexo,
    nascimento: info.nascimento,
    pelagem: info.pelagem,
    criador: info.criador,
    proprietario: info.proprietario,
    inspetor: info.inspetor,
  };

  // Verificar se o familiar já existe no sistema
  try {
    if (info.registro) {
      const familiarExistente = await buscarCavaloPorRegistro(info.registro);
      if (familiarExistente) {
        familiar.novo = false;
        familiar.id = familiarExistente.id;
      }
      
      // Não consultar site externo, usar apenas dados do PDF
      logger.info(
        `Utilizando apenas dados do PDF para ${info.nome} (${info.registro})`
      );
      
    } else if (info.nome) {
      // Tentar buscar pelo nome apenas se não tiver registro
      const familiarExistente = await buscarCavaloPorNome(info.nome);
      if (familiarExistente) {
        familiar.novo = false;
        familiar.id = familiarExistente.id;
      }
    }
  } catch (error: any) {
    logger.warn(
      `Não foi possível verificar familiar "${info.nome}": ${error.message}`,
    );
    resumo.detalhes.erros.push(
      `Aviso: Não foi possível verificar familiar "${info.nome}": ${error.message}`,
    );
  }

  return familiar;
}

/**
 * Importa dados de um arquivo PDF da ABCCC para o banco de dados
 *
 * @param filePath Caminho do arquivo PDF
 * @param user_id ID do usuário realizando a importação
 * @returns Resumo da importação
 */
export async function importarRegistroABCCC(
  filePath: string,
  user_id: number,
): Promise<ResumoImportacao> {
  // Iniciar dados do resumo
  const resumo: ResumoImportacao = {
    cavaloPrincipal: {
      nome: "",
      registro: "",
      novo: false,
    },
    familiares: [],
    detalhes: {
      nomeArquivo: path.basename(filePath),
      dataImportacao: new Date().toISOString(),
      totalAnimais: 0,
      novosAnimais: 0,
      atualizados: 0,
      erros: [],
    },
    logDebug: "",
  };

  try {
    // Processar o PDF com OpenAI
    logger.info(
      `Iniciando processamento do PDF: ${filePath} para usuário ${user_id}`,
    );

    // Verificar se a API key da OpenAI está configurada
    if (!process.env.OPENAI_API_KEY) {
      logger.error("OPENAI_API_KEY não está configurada no ambiente");
      throw new Error(
        "API key da OpenAI não configurada. Contacte o administrador do sistema.",
      );
    }

    // Processar o PDF e obter dados da genealogia
    logger.info(`Tentando processamento com OpenAI`);
    let resultado;

    try {
      // Tentar usar OpenAI primeiro
      resultado = await processarPdfComOpenAI(filePath);

      // Adicionar logs para debug
      resumo.logDebug = resultado.log;

      logger.info("Processamento com OpenAI concluído com sucesso");
    } catch (error: any) {
      logger.error(`Erro no processamento com OpenAI: ${error.message}`);

      // Fallback para o processador original em caso de falha com a OpenAI
      logger.info("Tentando processamento com o parser original como fallback");
      resultado = await processarPdfABCCC(filePath);

      // Salvar log para debug
      const logId = `import_${Date.now()}`;
      await salvarLogDebugImportacao(logId, JSON.stringify(resultado, null, 2));
      resumo.logDebug = logId;
    }

    // A partir daqui, use resultado.dados para todas as operações
    const dadosGenealogia = resultado.dados;

    // Contagens para estatísticas
    let totalAnimais = 0;
    let novosAnimais = 0;
    let atualizados = 0;

    // 1. Processar cavalo principal
    const resultadoCavaloPrincipal = await processarCavaloPrincipal(
      dadosGenealogia.cavalo,
      user_id,
      resumo,
    );
    const cavaloPrincipal = resultadoCavaloPrincipal.cavalo;
    if (resultadoCavaloPrincipal.novo) {
      novosAnimais++;
    } else {
      atualizados++;
    }
    totalAnimais++;

    // 2. Processar pai
    let pai = null;
    if (dadosGenealogia.pai) {
      const resultadoPai = await processarCavaloFamiliar(
        dadosGenealogia.pai,
        user_id,
        "Pai",
        resumo,
      );
      pai = resultadoPai.cavalo;
      totalAnimais++;

      if (resultadoPai.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação com o pai usando o serviço sincronizado
      await updateParentRelationshipSync(
        cavaloPrincipal.id,
        pai.id,
        pai.name,
        undefined, // mae_id - será preservado se já existir
        undefined  // mae_nome - será preservado se já existir
      );
    }

    // 3. Processar mãe
    let mae = null;
    if (dadosGenealogia.mae) {
      const resultadoMae = await processarCavaloFamiliar(
        dadosGenealogia.mae,
        user_id,
        "Mãe",
        resumo,
      );
      mae = resultadoMae.cavalo;
      totalAnimais++;

      if (resultadoMae.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação com a mãe usando o serviço sincronizado
      await updateParentRelationshipSync(
        cavaloPrincipal.id,
        undefined, // pai_id - será preservado se já existir
        undefined, // pai_nome - será preservado se já existir
        mae.id,
        mae.name
      );
    }

    // 4. Processar avós
    if (dadosGenealogia.avoPai && pai) {
      const resultadoAvoPai = await processarCavaloFamiliar(
        dadosGenealogia.avoPai,
        user_id,
        "Avô Paterno",
        resumo,
      );
      const avoPaiProcessado = resultadoAvoPai.cavalo;
      totalAnimais++;

      if (resultadoAvoPai.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação entre pai e avô paterno usando o serviço sincronizado
      await updateParentRelationshipSync(
        pai.id,
        avoPaiProcessado.id,
        avoPaiProcessado.name,
        null, // preserve existing mae data
        null
      );
    }

    if (dadosGenealogia.avaMae && pai) {
      const resultadoAvaMae = await processarCavaloFamiliar(
        dadosGenealogia.avaMae,
        user_id,
        "Avó Paterna",
        resumo,
      );
      const avaMaeProcessada = resultadoAvaMae.cavalo;
      totalAnimais++;

      if (resultadoAvaMae.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação entre pai e avó paterna usando o serviço sincronizado
      await updateParentRelationshipSync(
        pai.id,
        null, // preserve existing pai data
        null,
        avaMaeProcessada.id,
        avaMaeProcessada.name
      );
    }

    if (dadosGenealogia.avoPai2 && mae) {
      const resultadoAvoPai2 = await processarCavaloFamiliar(
        dadosGenealogia.avoPai2,
        user_id,
        "Avô Materno",
        resumo,
      );
      const avoPai2Processado = resultadoAvoPai2.cavalo;
      totalAnimais++;

      if (resultadoAvoPai2.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação entre mãe e avô materno usando o serviço sincronizado
      await updateParentRelationshipSync(
        mae.id,
        avoPai2Processado.id,
        avoPai2Processado.name,
        null, // preserve existing mae data
        null
      );
    }

    if (dadosGenealogia.avaMae2 && mae) {
      const resultadoAvaMae2 = await processarCavaloFamiliar(
        dadosGenealogia.avaMae2,
        user_id,
        "Avó Materna",
        resumo,
      );
      const avaMae2Processada = resultadoAvaMae2.cavalo;
      totalAnimais++;

      if (resultadoAvaMae2.novo) {
        novosAnimais++;
      } else {
        atualizados++;
      }

      // Atualizar relação entre mãe e avó materna usando o serviço sincronizado
      await updateParentRelationshipSync(
        mae.id,
        null, // preserve existing pai data
        null,
        avaMae2Processada.id,
        avaMae2Processada.name
      );
    }

    // Atualizar estatísticas
    resumo.detalhes.totalAnimais = totalAnimais;
    resumo.detalhes.novosAnimais = novosAnimais;
    resumo.detalhes.atualizados = atualizados;

    logger.info(
      `Importação concluída com sucesso: ${totalAnimais} animais processados, ${novosAnimais} novos`,
    );
    return resumo;
  } catch (error: any) {
    const mensagem = error.message || String(error);
    logger.error(`Erro durante a importação: ${mensagem}`);

    // Atualizar resumo com o erro
    resumo.detalhes.erros.push(`Erro fatal: ${mensagem}`);

    return resumo;
  }
}

/**
 * Processa o cavalo principal, criando ou atualizando no banco
 * Inclui enriquecimento de dados via scraper da ABCCC quando possível
 */
async function processarCavaloPrincipal(
  info: CavaloInfo,
  user_id: number,
  resumo: ResumoImportacao,
): Promise<ResultadoProcessamento> {
  try {
    logger.info(
      `Processando cavalo principal: ${info.nome} (${info.registro || "sem registro"})`,
    );

    // Tentar enriquecer os dados com o scraper da ABCCC se tiver registro
    if (info.registro) {
      // Não buscar dados complementares no site da ABCCC
      logger.info(
        `Utilizando apenas dados do PDF para ${info.nome} (${info.registro})`,
      );
      
      // Adicionar ao resumo sobre usar apenas dados do PDF
      resumo.detalhes.dadosComplementares = false;
    }

    // Verificar se o cavalo já existe no banco
    let cavalo: Cavalo | undefined = undefined;
    let novo = true;

    if (info.registro) {
      // Procurar primeiro pelo registro se disponível
      cavalo = await buscarCavaloPorRegistro(info.registro);
    }

    if (!cavalo && info.nome) {
      // Se não encontrar por registro, tenta pelo nome
      cavalo = await buscarCavaloPorNome(info.nome);
    }

    // Verificar se encontrou o cavalo
    if (cavalo) {
      logger.info(
        `Cavalo principal já existe no banco: ${info.nome} (ID: ${cavalo.id}). Atualizando...`,
      );

      // Atualizar os dados do cavalo existente
      cavalo = await atualizarCavaloExistente(cavalo.id, info, user_id);
      novo = false;

      // Atualizar resumo
      resumo.cavaloPrincipal = {
        nome: cavalo.name,
        registro: info.registro || "",
        novo: false,
        id: cavalo.id,
        sexo: info.sexo,
        nascimento: info.nascimento,
        pelagem: info.pelagem,
        criador: info.criador,
        proprietario: info.proprietario,
      };
    } else {
      // Criar um novo cavalo
      logger.info(`Criando novo registro para cavalo principal: ${info.nome}`);
      cavalo = await criarNovoCavalo(info, user_id);
      novo = true;

      // Atualizar resumo
      resumo.cavaloPrincipal = {
        nome: cavalo.name,
        registro: info.registro || "",
        novo: true,
        id: cavalo.id,
        sexo: info.sexo,
        nascimento: info.nascimento,
        pelagem: info.pelagem,
        criador: info.criador,
        proprietario: info.proprietario,
      };
    }

    return { cavalo, novo };
  } catch (error: any) {
    logger.error(`Erro ao processar cavalo principal: ${error.message}`);
    resumo.detalhes.erros.push(
      `Erro ao processar cavalo principal: ${error.message}`,
    );
    throw error;
  }
}

/**
 * Processa cavalo familiar (pai, mãe, avós, etc.)
 * Os cavalos familiares são sempre cadastrados como "externos" (isExternal = true)
 * Inclui enriquecimento de dados via scraper da ABCCC quando possível
 */
async function processarCavaloFamiliar(
  info: CavaloInfo,
  user_id: number,
  tipo: string,
  resumo: ResumoImportacao,
): Promise<ResultadoProcessamento> {
  try {
    logger.info(
      `Processando familiar (${tipo}): ${info.nome} (${info.registro || "sem registro"})`,
    );

    // Não buscar dados complementares no site da ABCCC, usar apenas os dados do PDF
    if (info.registro) {
      logger.info(
        `Utilizando apenas dados do PDF para familiar ${tipo}: ${info.nome} (${info.registro})`,
      );
    }

    let familiar: Cavalo;
    let novo = true;

    // Verificar se o cavalo já existe no banco
    let cavalo: Cavalo | undefined = undefined;

    if (info.registro) {
      // Procurar primeiro pelo registro se disponível
      cavalo = await buscarCavaloPorRegistro(info.registro);
    }

    if (!cavalo && info.nome) {
      // Se não encontrar por registro, tenta pelo nome
      cavalo = await buscarCavaloPorNome(info.nome);
    }

    // Verificar se encontrou o cavalo
    if (cavalo) {
      logger.info(
        `Familiar (${tipo}) já existe no banco: ${info.nome} (ID: ${cavalo.id}). Atualizando...`,
      );

      // Se o cavalo já existe, atualizamos o valor de isExternal para garantir que seja externo
      // assim corrigimos cadastros anteriores que possam ter sido feitos incorretamente
      const atualizacoes: Partial<InsertCavalo> = {
        is_external: true,
      };

      // Atualizar flag isExternal para garantir que seja marcado como externo
      await db
        .update(cavalos)
        .set(atualizacoes)
        .where(eq(cavalos.id, cavalo.id));

      // Atualizar os dados do cavalo existente
      familiar = await atualizarCavaloExistente(cavalo.id, info, user_id);
      novo = false;

      // Adicionar ao resumo
      resumo.familiares.push({
        tipo,
        nome: familiar.name,
        registro: info.registro,
        novo: false,
        id: familiar.id,
        sexo: info.sexo,
        nascimento: info.nascimento,
        pelagem: info.pelagem,
        criador: info.criador,
        proprietario: info.proprietario,
      });
    } else {
      // Criar um novo cavalo (como externo)
      logger.info(
        `Criando novo registro para familiar (${tipo}): ${info.nome} (como cavalo externo)`,
      );

      // Modificar as informações para incluir o flag isExternal
      const infoExternal = {
        ...info,
        is_external: true, // Marcar como cavalo externo
      };

      familiar = await criarNovoCavalo(infoExternal, user_id);
      novo = true;

      // Adicionar ao resumo
      resumo.familiares.push({
        tipo,
        nome: familiar.name,
        registro: info.registro,
        novo: true,
        id: familiar.id,
        sexo: info.sexo,
        nascimento: info.nascimento,
        pelagem: info.pelagem,
        criador: info.criador,
        proprietario: info.proprietario,
      });
    }

    return { cavalo: familiar, novo };
  } catch (error: any) {
    logger.error(
      `Erro ao processar familiar ${tipo} (${info.nome}): ${error.message}`,
    );
    resumo.detalhes.erros.push(
      `Erro ao processar familiar ${tipo} (${info.nome}): ${error.message}`,
    );
    throw error;
  }
}

/**
 * Busca um cavalo pelo número de registro
 */
async function buscarCavaloPorRegistro(
  registro: string,
): Promise<Cavalo | undefined> {
  try {
    logger.debug(
      `[DEBUG] Buscando cavalo por registro "${registro}" usando campo numero_registro`,
    );
    // Buscar pelo campo numero_registro (nome correto conforme schema.ts)
    const resultado = await db
      .select()
      .from(cavalos)
      .where(eq(cavalos.numero_registro, registro))
      .limit(1);

    if (resultado && resultado.length > 0) {
      logger.debug(
        `[DEBUG] Cavalo encontrado com registro "${registro}": ID=${resultado[0].id}, Nome=${resultado[0].name}`,
      );
      return resultado[0] as any;
    }

    logger.debug(`[DEBUG] Nenhum cavalo encontrado com registro "${registro}"`);
    return undefined;
  } catch (error: any) {
    logger.error(
      `Erro ao buscar cavalo por registro ${registro}: ${error.message}`,
    );
    throw error;
  }
}

/**
 * Busca um cavalo pelo nome
 */
async function buscarCavaloPorNome(nome: string): Promise<Cavalo | undefined> {
  try {
    // Buscar pelo nome exato
    const resultado = await db
      .select()
      .from(cavalos)
      .where(eq(cavalos.name, nome))
      .limit(1);

    if (resultado && resultado.length > 0) {
      return resultado[0] as any;
    }

    return undefined;
  } catch (error: any) {
    logger.error(`Erro ao buscar cavalo por nome ${nome}: ${error.message}`);
    throw error;
  }
}

/**
 * Atualiza um cavalo existente com novas informações
 */
async function atualizarCavaloExistente(
  id: number,
  info: CavaloInfo,
  user_id: number,
): Promise<Cavalo> {
  try {
    // Verificar se a pelagem corresponde a uma entrada na tabela de pelagens
    let pelagemId = null;

    if (info.pelagem) {
      try {
        // Tentar encontrar a pelagem na tabela
        logger.debug(
          `Buscando pelagem correspondente a "${info.pelagem}" na tabela de pelagens`,
        );
        const pelagemEncontrada = await db
          .select()
          .from(pelagens)
          .where(eq(pelagens.nome, info.pelagem))
          .limit(1);

        if (pelagemEncontrada && pelagemEncontrada.length > 0) {
          logger.info(
            `Pelagem encontrada no banco: ${info.pelagem} (ID=${pelagemEncontrada[0].id})`,
          );
          pelagemId = pelagemEncontrada[0].id;
        } else {
          // Tentar buscar com case insensitive para caso a capitalização seja diferente
          const pelagemSimilar = await db
            .select()
            .from(pelagens)
            .where(eq(sql`LOWER(${pelagens.nome})`, info.pelagem.toLowerCase()))
            .limit(1);

          if (pelagemSimilar && pelagemSimilar.length > 0) {
            logger.info(
              `Pelagem encontrada no banco (case insensitive): ${info.pelagem} (ID=${pelagemSimilar[0].id})`,
            );
            pelagemId = pelagemSimilar[0].id;
          } else {
            // Se a pelagem não for encontrada, registrá-la automaticamente na tabela de pelagens
            try {
              logger.info(
                `Pelagem "${info.pelagem}" não encontrada na tabela. Criando novo registro de pelagem.`,
              );
              const novaPelagem = await db
                .insert(pelagens)
                .values({
                  nome: info.pelagem,
                  fonte: 'ABCCC Import',
                } as any)
                .returning();

              if (novaPelagem && novaPelagem.length > 0) {
                logger.info(
                  `Nova pelagem criada com sucesso: "${info.pelagem}" (ID=${novaPelagem[0].id})`,
                );
                pelagemId = novaPelagem[0].id;
              } else {
                logger.warn(
                  `Falha ao criar pelagem: "${info.pelagem}". Será usada apenas como texto.`,
                );
              }
            } catch (createErr: any) {
              logger.warn(
                `Erro ao criar pelagem: ${createErr.message}. Será usada apenas como texto no campo cor`,
              );
            }
          }
        }
      } catch (err: any) {
        logger.warn(
          `Erro ao buscar pelagem na tabela: ${err.message}. Usando apenas o texto da pelagem.`,
        );
      }
    }

    // Montar objeto com as atualizações - garantir que sempre tenha pelo menos um campo
    const atualizacoes: Partial<InsertCavalo> = {};
    
    // Sempre incluir o nome como campo obrigatório se fornecido
    if (info.nome && info.nome.trim() !== '') {
      atualizacoes.name = info.nome.trim();
    }

    // Adicionar campos opcionais se presentes, usando os nomes corretos do schema
    if (info.registro && info.registro.trim()) atualizacoes.numero_registro = info.registro.trim();
    if (info.sexo && info.sexo.trim()) atualizacoes.sexo = normalizarSexo(info.sexo) as any;
    if (info.nascimento && info.nascimento.trim()) {
      atualizacoes.birth_date = info.nascimento.includes('/') ? 
        info.nascimento.split('/').reverse().join('-') : // Convert DD/MM/YYYY to YYYY-MM-DD
        info.nascimento;
    }
    if (info.pelagem && info.pelagem.trim()) atualizacoes.cor = info.pelagem.trim();
    if (info.criador && info.criador.trim()) atualizacoes.criador = info.criador.trim();
    if (info.proprietario && info.proprietario.trim()) atualizacoes.proprietario = info.proprietario.trim();
    if (info.inspetor && info.inspetor.trim()) atualizacoes.inspetor = info.inspetor.trim();
    if (info.origem && info.origem.trim()) atualizacoes.origem = info.origem.trim();
    if (pelagemId !== null && pelagemId !== undefined) atualizacoes.pelagem_id = pelagemId;

    // Armazenar títulos e observações no campo notes
    if (info.titulos || info.observacoes) {
      const notesArray = [];
      if (info.titulos) notesArray.push(`Títulos: ${info.titulos.join(", ")}`);
      if (info.observacoes) notesArray.push(info.observacoes);
      atualizacoes.notes = notesArray.join("\n");
    }

    // Validar se há atualizações para aplicar
    if (Object.keys(atualizacoes).length === 0) {
      logger.warn(`Nenhuma atualização fornecida para cavalo ${id}, retornando cavalo existente`);
      // Buscar o cavalo existente para retornar
      const cavaloExistente = await db
        .select()
        .from(cavalos)
        .where(eq(cavalos.id, id))
        .limit(1);
      
      if (cavaloExistente.length === 0) {
        throw new Error(`Cavalo com ID ${id} não encontrado no banco de dados`);
      }
      
      return cavaloExistente[0] as any;
    }

    // Garantir que pelo menos um campo será atualizado (usar timestamp como fallback)
    if (Object.keys(atualizacoes).length === 0) {
      atualizacoes.created_at = new Date();
      logger.debug(`Nenhum campo para atualizar no cavalo ${id}, atualizando timestamp`);
    }

    // Atualizar no banco de dados
    logger.debug(`Atualizando cavalo ${id} com campos:`, Object.keys(atualizacoes));
    const resultado = await db
      .update(cavalos)
      .set(atualizacoes)
      .where(eq(cavalos.id, id))
      .returning();

    return resultado[0] as any;
  } catch (error: any) {
    logger.error(`Erro ao atualizar cavalo ${id}: ${error.message}`);
    throw error;
  }
}

/**
 * Cria um novo cavalo a partir das informações extraídas
 */
async function criarNovoCavalo(
  info: CavaloInfo & { is_external?: boolean },
  user_id: number,
): Promise<Cavalo> {
  try {
    // Verificar se a pelagem corresponde a uma entrada na tabela de pelagens
    let pelagemId = null;

    if (info.pelagem) {
      try {
        // Tentar encontrar a pelagem na tabela
        logger.debug(
          `Buscando pelagem correspondente a "${info.pelagem}" na tabela de pelagens`,
        );
        const pelagemEncontrada = await db
          .select()
          .from(pelagens)
          .where(eq(pelagens.nome, info.pelagem))
          .limit(1);

        if (pelagemEncontrada && pelagemEncontrada.length > 0) {
          logger.info(
            `Pelagem encontrada no banco: ${info.pelagem} (ID=${pelagemEncontrada[0].id})`,
          );
          pelagemId = pelagemEncontrada[0].id;
        } else {
          // Tentar buscar com case insensitive para caso a capitalização seja diferente
          const pelagemSimilar = await db
            .select()
            .from(pelagens)
            .where(eq(sql`LOWER(${pelagens.nome})`, info.pelagem.toLowerCase()))
            .limit(1);

          if (pelagemSimilar && pelagemSimilar.length > 0) {
            logger.info(
              `Pelagem encontrada no banco (case insensitive): ${info.pelagem} (ID=${pelagemSimilar[0].id})`,
            );
            pelagemId = pelagemSimilar[0].id;
          } else {
            // Se a pelagem não for encontrada, registrá-la automaticamente na tabela de pelagens
            try {
              logger.info(
                `Pelagem "${info.pelagem}" não encontrada na tabela. Criando novo registro de pelagem.`,
              );
              const novaPelagem = await db
                .insert(pelagens)
                .values({
                  nome: info.pelagem,
                  descricao: `Pelagem extraída automaticamente via scraper ABCCC. Data: ${new Date().toISOString().split("T")[0]}`,
                } as any)
                .returning();

              if (novaPelagem && novaPelagem.length > 0) {
                logger.info(
                  `Nova pelagem criada com sucesso: "${info.pelagem}" (ID=${novaPelagem[0].id})`,
                );
                pelagemId = novaPelagem[0].id;
              } else {
                logger.warn(
                  `Falha ao criar pelagem: "${info.pelagem}". Será usada apenas como texto.`,
                );
              }
            } catch (createErr: any) {
              logger.warn(
                `Erro ao criar pelagem: ${createErr.message}. Será usada apenas como texto no campo cor`,
              );
            }
          }
        }
      } catch (err: any) {
        logger.warn(
          `Erro ao buscar pelagem na tabela: ${err.message}. Usando apenas o texto da pelagem.`,
        );
      }
    }

    // Preparar dados para inserção
    const dadosInsercao: InsertCavalo = {
      user_id,
      name: info.nome || 'Nome não informado',
      breed: "Crioulo", // Assumindo que todos os cavalos da ABCCC são da raça Crioula
      birth_date: info.nascimento ? 
        (info.nascimento.includes('/') ? 
          info.nascimento.split('/').reverse().join('-') : // Convert DD/MM/YYYY to YYYY-MM-DD
          info.nascimento
        ) : new Date().toISOString().split("T")[0],
      status: "ativo", // Usar "ativo" em minúsculas para corresponder ao valor padrão no schema
      altura: null,
      peso: null,
      cor: info.pelagem || null,
      pelagem_id: pelagemId, // Adicionar o ID da pelagem se encontrado ou criado
      sexo: normalizarSexo(info.sexo) as any,
      numero_registro: info.registro || null,
      origem: info.origem || null,
      criador: info.criador || null,
      proprietario: info.proprietario || null,
      inspetor: info.inspetor || null,
      notes:
        info.observacoes ||
        (info.titulos ? "Títulos: " + info.titulos.join(", ") : null),
      is_external: info.is_external || false, // Verificar se é um cavalo externo (da genealogia)
    };

    // Inserir no banco de dados
    const resultado = await db
      .insert(cavalos)
      .values(dadosInsercao)
      .returning();

    return resultado[0] as any;
  } catch (error: any) {
    logger.error(`Erro ao criar novo cavalo: ${error.message}`);
    throw error;
  }
}

// Removed: atualizarRelacaoPai function - replaced with synchronized genealogy service

// Removed: atualizarRelacaoMae function - replaced with synchronized genealogy service

// Removed: atualizarRelacaoAvo function - replaced with synchronized genealogy service

// Removed: atualizarGenealogiaCompleta function - replaced with synchronized genealogy service
