/**
 * Página Principal do Módulo de Logística de Nutrição - EquiGestor AI
 * Integra dashboard de alimentação e alertas de estoque
 */

import React from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import FeedingDashboard from '@/components/nutrition/FeedingDashboard';
import StockAlerts from '@/components/nutrition/StockAlerts';
import { Utensils, Package, BarChart3 } from 'lucide-react';

export default function NutricaoLogistica() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-6">
        {/* Header Principal */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Logística de Nutrição
          </h1>
          <p className="text-lg text-gray-600">
            Sistema completo de gestão alimentar com planejamento automático e controle de estoque
          </p>
        </div>

        {/* Abas Principais */}
        <Tabs defaultValue="feeding" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="feeding" className="flex items-center gap-2">
              <Utensils className="w-4 h-4" />
              Planos de Alimentação
            </TabsTrigger>
            <TabsTrigger value="stock" className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Controle de Estoque
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Relatórios
            </TabsTrigger>
          </TabsList>

          {/* Dashboard de Alimentação */}
          <TabsContent value="feeding">
            <FeedingDashboard />
          </TabsContent>

          {/* Alertas de Estoque */}
          <TabsContent value="stock">
            <StockAlerts />
          </TabsContent>

          {/* Relatórios (Para implementação futura) */}
          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle>Relatórios Nutricionais</CardTitle>
                <CardDescription>
                  Análises detalhadas de consumo, custos e eficiência alimentar
                </CardDescription>
              </CardHeader>
              <CardContent className="p-12 text-center">
                <BarChart3 className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Relatórios Avançados
                </h3>
                <p className="text-gray-600 mb-4">
                  Esta seção incluirá relatórios detalhados de:
                </p>
                <ul className="text-left max-w-md mx-auto space-y-2 text-gray-600">
                  <li>• Análise de custos por período</li>
                  <li>• Eficiência alimentar por animal</li>
                  <li>• Tendências de desperdício</li>
                  <li>• Previsões de estoque</li>
                  <li>• Comparativos de fornecedores</li>
                </ul>
                <p className="text-sm text-gray-500 mt-6">
                  Em desenvolvimento - Disponível em breve!
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}