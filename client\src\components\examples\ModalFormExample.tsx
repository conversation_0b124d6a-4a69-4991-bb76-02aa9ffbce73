import React, { useState } from 'react';
import { ModalForm, ModalFormRow, ModalFormSection } from '@/components/ui/modal-form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  DialogResponsive,
  DialogResponsiveContent,
  DialogResponsiveDescription,
  DialogResponsiveHeader,
  DialogResponsiveTitle,
  DialogResponsiveTrigger,
} from '@/components/ui/dialog-responsive';
import { Button } from '@/components/ui/button';

/**
 * Exemplo de uso do ModalForm
 * 
 * Este componente demonstra como usar o ModalForm para criar
 * formulários responsivos em modais.
 */
export function ModalFormExample() {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isMobile = useIsMobile();
  
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulação de envio de formulário
    setTimeout(() => {
      setIsSubmitting(false);
      setOpen(false);
      alert('Formulário enviado com sucesso!');
    }, 1500);
  };
  
  const handleCancel = () => {
    setOpen(false);
  };
  
  return (
    <DialogResponsive open={open} onOpenChange={setOpen}>
      <DialogResponsiveTrigger asChild>
        <Button>Abrir Formulário Modal</Button>
      </DialogResponsiveTrigger>
      <DialogResponsiveContent className="sm:max-w-[600px]">
        <DialogResponsiveHeader>
          <DialogResponsiveTitle>Formulário Responsivo</DialogResponsiveTitle>
          <DialogResponsiveDescription>
            Exemplo de formulário responsivo usando o componente ModalForm.
          </DialogResponsiveDescription>
        </DialogResponsiveHeader>
        
        <ModalForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
          submitLabel="Enviar"
          cancelLabel="Cancelar"
        >
          <ModalFormSection title="Informações Pessoais">
            <ModalFormRow columns={2}>
              <div className="space-y-2">
                <Label htmlFor="nome">Nome</Label>
                <Input id="nome" placeholder="Digite seu nome" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" />
              </div>
            </ModalFormRow>
            
            <ModalFormRow>
              <div className="space-y-2">
                <Label htmlFor="telefone">Telefone</Label>
                <Input id="telefone" placeholder="(00) 00000-0000" />
              </div>
            </ModalFormRow>
          </ModalFormSection>
          
          <ModalFormSection title="Detalhes do Pedido">
            <ModalFormRow columns={3}>
              <div className="space-y-2">
                <Label htmlFor="produto">Produto</Label>
                <Select>
                  <SelectTrigger id="produto">
                    <SelectValue placeholder="Selecione" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="produto1">Produto 1</SelectItem>
                    <SelectItem value="produto2">Produto 2</SelectItem>
                    <SelectItem value="produto3">Produto 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="quantidade">Quantidade</Label>
                <Input id="quantidade" type="number" min="1" defaultValue="1" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="prioridade">Prioridade</Label>
                <Select>
                  <SelectTrigger id="prioridade">
                    <SelectValue placeholder="Selecione" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="baixa">Baixa</SelectItem>
                    <SelectItem value="media">Média</SelectItem>
                    <SelectItem value="alta">Alta</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </ModalFormRow>
            
            <ModalFormRow>
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea 
                  id="observacoes" 
                  placeholder="Informações adicionais sobre o pedido..." 
                  className={isMobile ? "min-h-[100px]" : "min-h-[80px]"}
                />
              </div>
            </ModalFormRow>
          </ModalFormSection>
        </ModalForm>
      </DialogResponsiveContent>
    </DialogResponsive>
  );
}

export default ModalFormExample;
