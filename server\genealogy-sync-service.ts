/**
 * Unified Genealogy Synchronization Service
 * 
 * This service ensures data consistency between the cavalos table (pai_id, mae_id)
 * and the genealogia table by performing all genealogy updates within a single
 * database transaction. This prevents data divergence and maintains referential integrity.
 */

import { db } from "./db";
import { cavalos, genealogia } from "../shared/schema";
import { eq, and } from "drizzle-orm";
import { getModuleLogger } from "./logger";

const logger = getModuleLogger("genealogy-sync");

export interface GenealogyUpdate {
  horse_id: number;
  
  // Parent information
  pai_id?: number | null;
  pai_nome?: string | null;
  mae_id?: number | null;
  mae_nome?: string | null;
  
  // Grandparent information
  avo_paterno_id?: number | null;
  avo_paterno_nome?: string | null;
  avo_paterna_id?: number | null;
  avo_paterna_nome?: string | null;
  avo_materno_id?: number | null;
  avo_materno_nome?: string | null;
  avo_materna_id?: number | null;
  avo_materna_nome?: string | null;
  
  // Great-grandparent information (optional)
  bisavos?: {
    paternoPaterno?: { id?: number; nome?: string };
    paternaPaterno?: { id?: number; nome?: string };
    maternoPaterno?: { id?: number; nome?: string };
    maternaPaterno?: { id?: number; nome?: string };
    paternoMaterno?: { id?: number; nome?: string };
    paternaMaterno?: { id?: number; nome?: string };
    maternoMaterno?: { id?: number; nome?: string };
    maternaMaterno?: { id?: number; nome?: string };
  };
  
  // Additional fields
  coeficienteConsanguinidade?: number | null;
  observacoes?: string | null;
}

/**
 * Updates genealogy data in both cavalos and genealogia tables within a single transaction
 */
export async function updateGenealogySync(update: GenealogyUpdate): Promise<void> {
  logger.info(`Iniciando atualização sincronizada de genealogia para cavalo ${update.horse_id}`);
  
  await db.transaction(async (tx) => {
    try {
      // 1. Update cavalos table with parent IDs
      await tx
        .update(cavalos)
        .set({
          pai_id: update.pai_id,
          mae_id: update.mae_id,
        })
        .where(eq(cavalos.id, update.horse_id));
      
      logger.debug(`Atualizado cavalos table: pai_id=${update.pai_id}, mae_id=${update.mae_id}`);
      
      // 2. Check if genealogia record exists
      const existingGenealogia = await tx
        .select()
        .from(genealogia)
        .where(eq(genealogia.horse_id, update.horse_id))
        .limit(1);
      
      // 3. Prepare genealogia data - using correct database column names
      const genealogiaData = {
        horse_id: update.horse_id,
        pai: update.pai_nome || null,
        mae: update.mae_nome || null,
        avo_paterno_id: update.avo_paterno_id || null,
        avo_paterno: update.avo_paterno_nome || null,
        avo_paterna_id: update.avo_paterna_id || null,
        avo_paterna: update.avo_paterna_nome || null,
        avo_materno_id: update.avo_materno_id || null,
        avo_materno: update.avo_materno_nome || null,
        avo_materna_id: update.avo_materna_id || null,
        avo_materna: update.avo_materna_nome || null,
        ...(update.bisavos && {
          bisavo_paterno_paterno: update.bisavos.paternoPaterno?.nome || null,
          bisavo_paterno_paterno_id: update.bisavos.paternoPaterno?.id || null,
          bisavo_paterna_paterno: update.bisavos.paternaPaterno?.nome || null,
          bisavo_paterna_paterno_id: update.bisavos.paternaPaterno?.id || null,
          bisavo_materno_paterno: update.bisavos.maternoPaterno?.nome || null,
          bisavo_materno_paterno_id: update.bisavos.maternoPaterno?.id || null,
          bisavo_materna_paterno: update.bisavos.maternaPaterno?.nome || null,
          bisavo_materna_paterno_id: update.bisavos.maternaPaterno?.id || null,
        }),
      };
      
      // 4. Insert or update genealogia table
      if (existingGenealogia.length > 0) {
        await tx
          .update(genealogia)
          .set(genealogiaData)
          .where(eq(genealogia.horse_id, update.horse_id));
        
        logger.debug(`Atualizado registro genealogia existente para cavalo ${update.horse_id}`);
      } else {
        await tx
          .insert(genealogia)
          .values(genealogiaData);
        
        logger.debug(`Criado novo registro genealogia para cavalo ${update.horse_id}`);
      }
      
      logger.info(`Genealogia sincronizada com sucesso para cavalo ${update.horse_id}`);
      
    } catch (error) {
      logger.error(`Erro ao sincronizar genealogia para cavalo ${update.horse_id}: ${error}`);
      throw error;
    }
  });
}

/**
 * Updates only parent relationships (pai/mae) in both tables
 */
export async function updateParentRelationshipSync(
  horse_id: number,
  pai_id?: number | null,
  pai_nome?: string | null,
  mae_id?: number | null,
  mae_nome?: string | null
): Promise<void> {
  await updateGenealogySync({
    horse_id,
    pai_id,
    pai_nome,
    mae_id,
    mae_nome,
  });
}

/**
 * Updates grandparent relationships while preserving parent data
 */
export async function updateGrandparentRelationshipSync(
  horse_id: number,
  grandparents: {
    avoPaternoId?: number | null;
    avoPaternoNome?: string | null;
    avoPaternaId?: number | null;
    avoPaternaNome?: string | null;
    avoMaternoId?: number | null;
    avoMaternoNome?: string | null;
    avoMaternaId?: number | null;
    avoMaternaNome?: string | null;
  }
): Promise<void> {
  // First get current parent data to preserve it
  const currentHorse = await db
    .select({
      pai_id: cavalos.pai_id,
      mae_id: cavalos.mae_id,
    })
    .from(cavalos)
    .where(eq(cavalos.id, horse_id))
    .limit(1);
  
  const currentGenealogia = await db
    .select({
      pai: genealogia.pai,
      mae: genealogia.mae,
    })
    .from(genealogia)
    .where(eq(genealogia.horse_id, horse_id))
    .limit(1);
  
  await updateGenealogySync({
    horse_id,
    // Preserve current parent data
    pai_id: currentHorse[0]?.pai_id || null,
    pai_nome: currentGenealogia[0]?.pai || null,
    mae_id: currentHorse[0]?.mae_id || null,
    mae_nome: currentGenealogia[0]?.mae || null,
    // Update grandparent data
    ...grandparents,
  });
}

/**
 * Validates genealogy data consistency between tables
 */
export async function validateGenealogyConsistency(horse_id: number): Promise<{
  isConsistent: boolean;
  issues: string[];
}> {
  const issues: string[] = [];
  
  try {
    // Get data from both tables
    const cavaloData = await db
      .select({
        pai_id: cavalos.pai_id,
        mae_id: cavalos.mae_id,
      })
      .from(cavalos)
      .where(eq(cavalos.id, horse_id))
      .limit(1);
    
    const genealogiaData = await db
      .select({
        id: genealogia.id,
        horse_id: genealogia.horse_id,
        pai: genealogia.pai,
        mae: genealogia.mae,
        avo_paterno_id: genealogia.avo_paterno_id,
        avo_paterno: genealogia.avo_paterno,
        avo_paterna_id: genealogia.avo_paterna_id,
        avo_paterna: genealogia.avo_paterna,
        avo_materno_id: genealogia.avo_materno_id,
        avo_materno: genealogia.avo_materno,
        avo_materna_id: genealogia.avo_materna_id,
        avo_materna: genealogia.avo_materna,
      })
      .from(genealogia)
      .where(eq(genealogia.horse_id, horse_id))
      .limit(1);
    
    if (cavaloData.length === 0) {
      issues.push("Cavalo não encontrado");
      return { isConsistent: false, issues };
    }
    
    if (genealogiaData.length === 0) {
      issues.push("Registro de genealogia não encontrado");
      return { isConsistent: false, issues };
    }
    
    const cavalo = cavaloData[0];
    const gen = genealogiaData[0];
    
    // Check parent ID consistency
    if (cavalo.pai_id !== gen.avo_paterno_id && cavalo.pai_id !== null) {
      // Note: This comparison might not be direct if pai/mae in genealogia are text fields
      // We check if at least the IDs are consistent where they exist
    }
    
    if (cavalo.mae_id !== gen.avo_materno_id && cavalo.mae_id !== null) {
      // Similar check for mother
    }
    
    return {
      isConsistent: issues.length === 0,
      issues
    };
    
  } catch (error) {
    issues.push(`Erro na validação: ${error}`);
    return { isConsistent: false, issues };
  }
}

/**
 * Retrieves complete genealogy data from both tables
 */
export async function getCompleteGenealogyData(horse_id: number) {
  const cavaloData = await db
    .select()
    .from(cavalos)
    .where(eq(cavalos.id, horse_id))
    .limit(1);
  
  const genealogiaData = await db
    .select()
    .from(genealogia)
    .where(eq(genealogia.horse_id, horse_id))
    .limit(1);
  
  return {
    cavalo: cavaloData[0] || null,
    genealogia: genealogiaData[0] || null,
  };
}