import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Squirrel, // Usamos este ícone como substituto para cavalo
  CircleDot, // Usaremos este para representar gênero feminino
  Circle, // Usaremos este para representar gênero masculino
  AlertTriangle,
  ChevronRight,
  ChevronDown,
} from 'lucide-react';
import ConsanguinityIndicator from './ConsanguinityIndicator';

// Tipos para representar a estrutura genealógica
export interface GenealogyNode {
  id: number | null;
  name: string;
  gender: 'M' | 'F';
  breed?: string;
  color?: string;
  consanguinity?: number;
  isUnknown?: boolean;
  isHighlighted?: boolean;
  father?: GenealogyNode;
  mother?: GenealogyNode;
}

interface GenealogyTreeProps {
  node: GenealogyNode;
  maxGenerations?: number;
  currentGeneration?: number;
  collapsed?: boolean;
  onNodeClick?: (id: number | null) => void;
  className?: string;
}

/**
 * Componente para visualizar a árvore genealógica de um cavalo
 * Suporta exibição recursiva de ancestrais com opções de colapsar/expandir
 */
const GenealogyTree: React.FC<GenealogyTreeProps> = ({
  node,
  maxGenerations = 3,
  currentGeneration = 1,
  collapsed = false,
  onNodeClick,
  className = '',
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(collapsed);
  
  // Determinar se esse é o nível mais profundo baseado na configuração
  const isMaxDepth = currentGeneration >= maxGenerations;
  
  // Verificar se o nó tem pai ou mãe (com verificação de segurança)
  const hasParents = node && (node.father || node.mother) && !isMaxDepth;
  
  // Verificação de segurança para evitar erros quando node é undefined
  if (!node) {
    return (
      <div className={`genealogy-node relative ${className}`}>
        <Card className="bg-muted opacity-70">
          <CardContent className="p-3 text-center">
            <AlertTriangle className="h-4 w-4 text-amber-500 mx-auto mb-2" />
            <span className="text-xs text-muted-foreground">Dados não disponíveis</span>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Ícone baseado no gênero
  const GenderIcon = node.gender === 'M' ? Circle : CircleDot;
  const genderColor = node.gender === 'M' ? 'text-blue-500' : 'text-pink-500';
  
  // Classes para cartão destacado
  const highlightClass = node.isHighlighted ? 'ring-2 ring-primary ring-offset-2' : '';
  
  // Função para alternar o colapso da árvore
  const toggleCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasParents) {
      setIsCollapsed(!isCollapsed);
    }
  };
  
  // Função para lidar com cliques no cartão
  const handleCardClick = () => {
    if (onNodeClick && node.id !== null && !node.isUnknown) {
      onNodeClick(node.id);
    }
  };
  
  // Renderizar um cartão para um cavalo desconhecido
  const renderUnknownHorse = () => (
    <Card 
      className="bg-muted opacity-70 cursor-not-allowed"
    >
      <CardContent className="p-3 text-center flex items-center justify-center">
        <AlertTriangle className="h-3 w-3 text-muted-foreground mr-2" />
        <span className="text-xs text-muted-foreground">Desconhecido</span>
      </CardContent>
    </Card>
  );
  
  return (
    <div className={`genealogy-node relative ${className}`}>
      <div className="flex items-center">
        {/* Botão para expandir/colapsar, só aparece se há pais */}
        {hasParents && (
          <button 
            onClick={toggleCollapse}
            className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-muted-foreground/10 mr-2"
            title={isCollapsed ? "Expandir" : "Colapsar"}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            )}
          </button>
        )}
        
        {/* Cartão do cavalo atual */}
        {node.isUnknown ? (
          renderUnknownHorse()
        ) : (
          <Card 
            className={`min-w-[180px] max-w-[300px] ${highlightClass} ${hasParents ? "cursor-pointer" : ""} hover:shadow-md transition-shadow`}
            onClick={handleCardClick}
          >
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Squirrel className={`h-4 w-4 ${genderColor}`} />
                  <GenderIcon className={`h-4 w-4 ${genderColor}`} />
                </div>
                {node.breed && (
                  <Badge variant="outline" className="text-xs">
                    {node.breed}
                  </Badge>
                )}
              </div>
              
              <h4 className="font-medium text-sm mt-2 truncate">{node.name}</h4>
              
              {node.color && (
                <div className="text-xs text-muted-foreground mt-1">
                  Cor: {node.color}
                </div>
              )}
              
              {node.consanguinity !== undefined && node.consanguinity > 0 && (
                <div className="mt-2">
                  <ConsanguinityIndicator 
                    coefficient={node.consanguinity} 
                    size="sm"
                    showInfo={false}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* Árvore genealógica colapsável */}
      {hasParents && !isCollapsed && (
        <div className="mt-4 ml-8 space-y-4">
          {/* Pai */}
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">Pai:</div>
            {node.father ? (
              <GenealogyTree 
                node={node.father}
                maxGenerations={maxGenerations}
                currentGeneration={currentGeneration + 1}
                collapsed={isCollapsed}
                onNodeClick={onNodeClick}
              />
            ) : (
              renderUnknownHorse()
            )}
          </div>
          
          {/* Mãe */}
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">Mãe:</div>
            {node.mother ? (
              <GenealogyTree 
                node={node.mother}
                maxGenerations={maxGenerations}
                currentGeneration={currentGeneration + 1}
                collapsed={isCollapsed}
                onNodeClick={onNodeClick}
              />
            ) : (
              renderUnknownHorse()
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GenealogyTree;