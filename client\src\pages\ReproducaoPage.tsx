import React, { useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Reproducao, InsertReproducao, Cavalo } from '@shared/schema';
import { format, parseISO, isValid } from 'date-fns';
import { pt } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import * as z from 'zod';
import { CavalosSelector } from '@/components/CavalosSelector';

import {
  Loader2,
  Calendar,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  Heart,
  BarChart
} from 'lucide-react';

// Definição de tipos específicos para reprodução
interface TipoCobertura {
  value: string;
  label: string;
}

interface EstadoReproducao {
  value: string;
  label: string;
}

// Opções para os selects
const tiposCoberturas: TipoCobertura[] = [
  { value: 'monta_natural', label: 'Monta Natural' },
  { value: 'inseminacao_artificial', label: 'Inseminação Artificial' },
  { value: 'transferencia_embriao', label: 'Transferência de Embrião' }
];

const estadosReproducao: EstadoReproducao[] = [
  { value: 'pre_cobertura', label: 'Pré-Cobertura' },
  { value: 'em_cobertura', label: 'Em Cobertura' },
  { value: 'gestante', label: 'Gestante' },
  { value: 'parto_realizado', label: 'Parto Realizado' },
  { value: 'falha_embriao', label: 'Falha de Embrião' },
  { value: 'aborto', label: 'Aborto' },
  { value: 'nao_prenhe', label: 'Não Prenhe' }
];

// Form schema - corrigido para corresponder ao schema da tabela
const reproducaoFormSchema = z.object({
  egua_id: z.number({
    required_error: "Selecione uma égua",
  }),
  garanhao_id: z.number({
    required_error: "Selecione um garanhão",
  }).optional().nullable(),
  data_cobertura: z.string().min(1, "Data da cobertura é obrigatória"),
  tipo_cobertura: z.string().min(1, "Tipo de cobertura é obrigatório"),
  resultado: z.string().min(1, "Resultado da reprodução é obrigatório"),
  observacoes: z.string().optional().nullable(),
  user_id: z.number().optional()
});

const ReproducaoPage = () => {
  const [location, setLocation] = useLocation();
  const queryParams = new URLSearchParams(location.split('?')[1]);
  const horseIdParam = queryParams.get('horse_id');
  
  // Estados locais
  const [user, setUser] = useState<any>(null);
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [currentReproducao, setCurrentReproducao] = useState<Reproducao | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [filterHorseId, setFilterHorseId] = useState<number | null>(horseIdParam ? parseInt(horseIdParam) : null);
  
  const toast = useToast();
  const queryClient = useQueryClient();

  // Carregar usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);

  // Formulário
  const form = useForm<z.infer<typeof reproducaoFormSchema>>({
    resolver: zodResolver(reproducaoFormSchema),
    defaultValues: {
      egua_id: horseIdParam ? parseInt(horseIdParam) : 0,
      garanhao_id: null,
      data_cobertura: '',
      tipo_cobertura: '',
      resultado: '',
      observacoes: ''
    },
  });

  // Consulta para buscar reproduções
  const { 
    data: reproducoes = [], 
    isLoading: isLoadingReproducoes,
    refetch: refetchReproducoes
  } = useQuery({
    queryKey: [filterHorseId ? `/api/cavalos/${filterHorseId}/reproducao` : '/api/reproducao'],
    queryFn: async () => {
      try {
        // Adicionar cabeçalho user_id para autenticação
        let user = null;
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            user = JSON.parse(userStr);
          }
        } catch (e) {
          console.error("Erro ao obter usuário do localStorage:", e);
        }
        
        const user_id = user?.id;
        
        if (!user_id) {
          throw new Error("Usuário não autenticado. Faça login novamente.");
        }

        let url = '/api/reproducao';
        if (filterHorseId) {
          url = `/api/cavalos/${filterHorseId}/reproducao`;
        }

        const response = await fetch(url, {
          headers: {
            'user-id': user_id.toString()
          }
        });

        if (!response.ok) {
          throw new Error('Erro ao buscar registros de reprodução');
        }

        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar reproduções:', error);
        toast.toast({
          title: "Erro",
          description: error instanceof Error ? error.message : "Falha ao carregar registros de reprodução",
          variant: "destructive"
        });
        return [];
      }
    },
    enabled: !!user
  });

  // Consulta para buscar cavalos (para selects)
  const { 
    data: cavalos = [],
    isLoading: isLoadingCavalos
  } = useQuery({
    queryKey: ['/api/cavalos'],
    queryFn: async () => {
      try {
        // Adicionar cabeçalho user_id para autenticação
        let user = null;
        try {
          const userStr = localStorage.getItem('user');
          if (userStr) {
            user = JSON.parse(userStr);
          }
        } catch (e) {
          console.error("Erro ao obter usuário do localStorage:", e);
        }
        
        const user_id = user?.id;
        
        if (!user_id) {
          throw new Error("Usuário não autenticado. Faça login novamente.");
        }

        const response = await fetch('/api/cavalos', {
          headers: {
            'user-id': user_id.toString()
          }
        });

        if (!response.ok) {
          throw new Error('Erro ao buscar cavalos');
        }

        return await response.json();
      } catch (error) {
        console.error('Erro ao buscar cavalos:', error);
        toast.toast({
          title: "Erro",
          description: error instanceof Error ? error.message : "Falha ao carregar cavalos",
          variant: "destructive"
        });
        return [];
      }
    },
    enabled: !!user
  });

  // Mutations
  const createReproducaoMutation = useMutation({
    mutationFn: async (data: InsertReproducao) => {
      // Adicionar cabeçalho user_id para autenticação
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const user_id = user?.id;
      
      if (!user_id) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }

      const response = await fetch('/api/reproducao', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user-id': user_id.toString()
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Falha ao criar registro de reprodução');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.toast({
        title: "Sucesso",
        description: "Registro de reprodução criado com sucesso",
      });
      setIsFormDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/reproducao'] });
      if (filterHorseId) {
        queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${filterHorseId}/reproducao`] });
      }
      form.reset();
    },
    onError: (error) => {
      toast.toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao criar registro de reprodução",
        variant: "destructive"
      });
    }
  });

  const updateReproducaoMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<InsertReproducao> }) => {
      // Adicionar cabeçalho user_id para autenticação
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const user_id = user?.id;
      
      if (!user_id) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }

      const response = await fetch(`/api/reproducao/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'user-id': user_id.toString()
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Falha ao atualizar registro de reprodução');
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.toast({
        title: "Sucesso",
        description: "Registro de reprodução atualizado com sucesso",
      });
      setIsFormDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/reproducao'] });
      if (filterHorseId) {
        queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${filterHorseId}/reproducao`] });
      }
      setCurrentReproducao(null);
      form.reset();
    },
    onError: (error) => {
      toast.toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao atualizar registro de reprodução",
        variant: "destructive"
      });
    }
  });

  const deleteReproducaoMutation = useMutation({
    mutationFn: async (id: number) => {
      // Adicionar cabeçalho user_id para autenticação
      let user = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          user = JSON.parse(userStr);
        }
      } catch (e) {
        console.error("Erro ao obter usuário do localStorage:", e);
      }
      
      const user_id = user?.id;
      
      if (!user_id) {
        throw new Error("Usuário não autenticado. Faça login novamente.");
      }

      const response = await fetch(`/api/reproducao/${id}`, {
        method: 'DELETE',
        headers: {
          'user-id': user_id.toString()
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Falha ao excluir registro de reprodução');
      }

      return true;
    },
    onSuccess: () => {
      toast.toast({
        title: "Sucesso",
        description: "Registro de reprodução excluído com sucesso",
      });
      setIsDeleteDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/reproducao'] });
      if (filterHorseId) {
        queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${filterHorseId}/reproducao`] });
      }
      setCurrentReproducao(null);
    },
    onError: (error) => {
      toast.toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao excluir registro de reprodução",
        variant: "destructive"
      });
    }
  });

  // Funções auxiliares
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '';
    
    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return 'Data inválida';
      return format(date, 'dd/MM/yyyy', { locale: pt });
    } catch (error) {
      return 'Data inválida';
    }
  };

  const getHorseName = (id: number | null) => {
    if (!id) return 'Não especificado';
    const horse = cavalos.find((h: any) => h.id === id);
    return horse ? horse.name : 'Cavalo não encontrado';
  };

  const getTipoCoberturaLabel = (value: string) => {
    const tipo = tiposCoberturas.find(t => t.value === value);
    return tipo ? tipo.label : value;
  };

  const getEstadoReproducaoLabel = (value: string) => {
    const estado = estadosReproducao.find(e => e.value === value);
    return estado ? estado.label : value;
  };

  const handleEditReproducao = (reproducao: any) => {
    setCurrentReproducao(reproducao);
    
    form.reset({
      egua_id: reproducao.egua_id || reproducao.horse_id, // Support both field names
      garanhao_id: reproducao.garanhao_id || reproducao.padreiro_id, // Support both field names
      data_cobertura: reproducao.data_cobertura ? reproducao.data_cobertura.split('T')[0] : '',
      tipo_cobertura: reproducao.tipo_cobertura || '',
      resultado: reproducao.resultado || reproducao.estado || '', // Support both field names
      observacoes: reproducao.observacoes || ''
    });
    
    setIsFormDialogOpen(true);
  };

  const handleDeleteReproducao = (reproducao: Reproducao) => {
    setCurrentReproducao(reproducao);
    setIsDeleteDialogOpen(true);
  };

  const handleFilterChange = (horse_id: number | null) => {
    setFilterHorseId(horse_id);
    
    // Atualizar URL com o filtro
    if (horse_id) {
      setLocation(`/reproducao?horse_id=${horse_id}`);
    } else {
      setLocation('/reproducao');
    }
  };

  const onSubmit = (data: z.infer<typeof reproducaoFormSchema>) => {
    // Dados já estão no formato correto do schema
    const formData = {
      user_id: user?.id,
      egua_id: data.egua_id,
      garanhao_id: data.garanhao_id,
      data_cobertura: data.data_cobertura,
      tipo_cobertura: data.tipo_cobertura,
      resultado: data.resultado,
      observacoes: data.observacoes || null
    };
    
    if (currentReproducao) {
      updateReproducaoMutation.mutate({ id: currentReproducao.id, data: formData });
    } else {
      createReproducaoMutation.mutate(formData as unknown as InsertReproducao);
    }
  };

  const handleOpenForm = () => {
    setCurrentReproducao(null);
    form.reset({
      horse_id: filterHorseId || 0,
      padreiro_id: null,
      data_cobertura: '',
      tipo_cobertura: '',
      estado: '',
      observacoes: ''
    });
    setIsFormDialogOpen(true);
  };

  // Renderização
  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-10 w-10 text-yellow-500 mx-auto mb-2" />
          <h3 className="text-lg font-medium">Autenticação necessária</h3>
          <p className="text-gray-600 mt-1">Faça login para acessar esta página</p>
          <Link href="/login">
            <Button className="mt-4">Ir para Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Heart className="mr-2 h-6 w-6 text-pink-500" />
            Controle Reprodutivo
          </h1>
          <p className="text-gray-600 mt-1">
            Gerencie reproduções, inseminações e gestações dos seus cavalos
          </p>
        </div>
        
        <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
          <div className="w-full sm:w-64">
            <CavalosSelector
              value={filterHorseId}
              onChange={handleFilterChange}
              includeAllOption={true}
              placeholder="Filtrar por égua"
              label="Filtrar por égua"
            />
          </div>
          
          <div className="flex space-x-2">
            <Link href="/reproducao/estatisticas">
              <Button variant="outline" className="whitespace-nowrap">
                <BarChart className="mr-2 h-4 w-4" />
                Ver Estatísticas
              </Button>
            </Link>
            
            <Button onClick={handleOpenForm} className="whitespace-nowrap">
              <Plus className="mr-2 h-4 w-4" />
              Novo Registro
            </Button>
          </div>
        </div>
      </div>

      {isLoadingReproducoes || isLoadingCavalos ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Carregando...</span>
        </div>
      ) : reproducoes.length === 0 ? (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Nenhum registro reprodutivo encontrado</CardTitle>
            <CardDescription>
              {filterHorseId 
                ? `Não há registros de reprodução para este cavalo.` 
                : `Comece adicionando seu primeiro registro de reprodução.`}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Button onClick={handleOpenForm}>
              <Plus className="mr-2 h-4 w-4" />
              Adicionar Registro Reprodutivo
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableCaption>Lista de registros reprodutivos</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Égua</TableHead>
                <TableHead>Garanhão</TableHead>
                <TableHead>Data Cobertura</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Prev. Parto</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reproducoes.map((reproducao: any) => (
                <TableRow key={reproducao.id}>
                  <TableCell className="font-medium">{getHorseName(reproducao.horse_id)}</TableCell>
                  <TableCell>{getHorseName(reproducao.padreiroId)}</TableCell>
                  <TableCell>{formatDate(reproducao.dataCobertura)}</TableCell>
                  <TableCell>{getTipoCoberturaLabel(reproducao.tipoCobertura || '')}</TableCell>
                  <TableCell>
                    <span 
                      className={`px-2 py-1 rounded-full text-xs ${
                        reproducao.estado === 'gestante' || reproducao.estado === 'parto_realizado' 
                          ? 'bg-green-100 text-green-800' 
                          : reproducao.estado === 'aborto' || reproducao.estado === 'falha_embriao' || reproducao.estado === 'nao_prenhe'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {getEstadoReproducaoLabel(reproducao.estado || '')}
                    </span>
                  </TableCell>
                  <TableCell>{formatDate(reproducao.dataPrevistaParto)}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEditReproducao(reproducao)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleDeleteReproducao(reproducao)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Diálogo de Formulário */}
      <Dialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {currentReproducao ? 'Editar Registro Reprodutivo' : 'Novo Registro Reprodutivo'}
            </DialogTitle>
            <DialogDescription>
              {currentReproducao
                ? 'Atualize as informações do registro reprodutivo'
                : 'Preencha as informações para adicionar um novo registro reprodutivo'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Égua */}
                <FormField
                  control={form.control}
                  name="egua_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Égua *</FormLabel>
                      <CavalosSelector 
                        value={field.value} 
                        onChange={field.onChange}
                        placeholder="Selecione a égua"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Garanhão */}
                <FormField
                  control={form.control}
                  name="garanhao_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Garanhão</FormLabel>
                      <CavalosSelector 
                        value={field.value || null} 
                        onChange={(val) => field.onChange(val)}
                        placeholder="Selecione o garanhão"
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Data da Cobertura */}
                <FormField
                  control={form.control}
                  name="data_cobertura"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data da Cobertura *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Tipo de Cobertura */}
                <FormField
                  control={form.control}
                  name="tipo_cobertura"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>Tipo de Cobertura *</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de cobertura" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tiposCoberturas.map(tipo => (
                            <SelectItem key={tipo.value} value={tipo.value}>
                              {tipo.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Resultado da Reprodução */}
                <FormField
                  control={form.control}
                  name="resultado"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Resultado da Reprodução *</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o resultado atual" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {estadosReproducao.map(estado => (
                            <SelectItem key={estado.value} value={estado.value}>
                              {estado.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Observações adicionais */}
                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observações</FormLabel>
                      <FormControl>
                        <textarea 
                          {...field} 
                          value={field.value ?? ''}
                          className="w-full min-h-[80px] p-2 border rounded-md resize-vertical"
                          placeholder="Observações sobre a reprodução..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>Data Prevista do Parto</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Data do Diagnóstico de Gestação */}
                <FormField
                  control={form.control}
                  name="data_cobertura"
                  render={({ field }: { field: any }) => (
                    <FormItem>
                      <FormLabel>Data do Diagnóstico de Gestação</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Resultado do Diagnóstico */}
                <FormField
                  control={form.control}
                  name="resultado"
                  render={({ field }: { field: any }) => (
                    <FormItem className="col-span-1 md:col-span-2">
                      <FormLabel>Resultado do Diagnóstico</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Observações */}
                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem className="col-span-1 md:col-span-2">
                      <FormLabel>Observações</FormLabel>
                      <FormControl>
                        <Textarea 
                          {...field} 
                          value={field.value ?? ''} 
                          placeholder="Adicione observações e detalhes relevantes"
                          className="min-h-[100px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsFormDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  disabled={createReproducaoMutation.isPending || updateReproducaoMutation.isPending}
                >
                  {(createReproducaoMutation.isPending || updateReproducaoMutation.isPending) && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {currentReproducao ? 'Atualizar' : 'Cadastrar'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo de Confirmação de Exclusão */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este registro reprodutivo?<br />
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (currentReproducao) {
                  deleteReproducaoMutation.mutate(currentReproducao.id);
                }
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              {deleteReproducaoMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Excluindo...
                </>
              ) : (
                'Excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ReproducaoPage;