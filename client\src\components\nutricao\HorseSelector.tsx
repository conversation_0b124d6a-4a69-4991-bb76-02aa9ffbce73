import React, { memo } from 'react';
import { 
  Card, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { Skeleton } from '@/components/ui/skeleton';

interface HorseSelectorProps {
  cavalos: Cavalo[];
  selectedHorseId: number | null;
  onSelectHorse: (id: number) => void;
  isLoading: boolean;
}

/**
 * Componente para seleção de cavalos
 * 
 * Componente reutilizável para selecionar um cavalo dentre uma lista,
 * com visual consistente e estados de carregamento.
 * Implementa memoização para evitar re-renderizações desnecessárias.
 */
const HorseSelector: React.FC<HorseSelectorProps> = ({
  cavalos,
  selectedHorseId,
  onSelectHorse,
  isLoading
}) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-24" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-40" /></CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Animais</CardTitle>
        <CardDescription>Selecione um animal para gerenciar a nutrição</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {cavalos.map((cavalo) => (
            <Button
              key={cavalo.id}
              variant={cavalo.id === selectedHorseId ? "default" : "outline"}
              className="w-full justify-start"
              onClick={() => onSelectHorse(cavalo.id)}
            >
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  {cavalo.name.charAt(0).toUpperCase()}
                </div>
                <div className="text-left">
                  <div className="font-medium">{cavalo.name}</div>
                  <div className="text-xs text-gray-500">{cavalo.breed}</div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Aplicando memoização para evitar re-renderizações desnecessárias
export default memo(HorseSelector);