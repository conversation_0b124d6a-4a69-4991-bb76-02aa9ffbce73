import { db } from './db';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';
import {
  cavalos,
  manejos,
  eventos,
  procedimentosVet,
  reproducao,
  medidasFisicas,
  morfologia,
  desempenhoHistorico,
  genealogia
} from '@shared/schema';
import { medidasMorfologicas } from '@shared/schema_medidas_morfologicas';

// Diretório para armazenar os arquivos de exportação
const EXPORT_DIR = path.join(process.cwd(), 'exports');

// Garantir que o diretório de exportação exista
if (!fs.existsSync(EXPORT_DIR)) {
  fs.mkdirSync(EXPORT_DIR, { recursive: true });
}

// Função para exportar dados para um arquivo JSON
export async function exportData(user_id: number): Promise<string> {
  try {
    // Criar objeto para armazenar todos os dados
    const exportData = {
      cavalos: [],
      medidasMorfologicas: [],
      manejos: [],
      eventos: [],
      procedimentosVet: [],
      reproducao: [],
      medidasFisicas: [],
      morfologia: [],
      desempenhoHistorico: [],
      genealogia: [],
      exportDate: new Date().toISOString()
    };

    // Buscar dados do banco de dados
    exportData.cavalos = await db.select().from(cavalos).where(eq(cavalos.user_id, user_id));

    // Buscar medidas morfológicas para os cavalos do usuário
    exportData.medidasMorfologicas = await db.select()
      .from(medidasMorfologicas)
      .where(eq(medidasMorfologicas.user_id, user_id));

    // Buscar manejos para os cavalos do usuário
    exportData.manejos = await db.select()
      .from(manejos)
      .where(eq(manejos.user_id, user_id));

    // Buscar eventos para os cavalos do usuário
    exportData.eventos = await db.select()
      .from(eventos)
      .where(eq(eventos.user_id, user_id));

    // Buscar procedimentos veterinários para os cavalos do usuário
    exportData.procedimentosVet = await db.select()
      .from(procedimentosVet)
      .where(eq(procedimentosVet.user_id, user_id));

    // Buscar dados de reprodução para os cavalos do usuário
    exportData.reproducao = await db.select()
      .from(reproducao)
      .where(eq(reproducao.user_id, user_id));

    // Buscar medidas físicas para os cavalos do usuário
    exportData.medidasFisicas = await db.select()
      .from(medidasFisicas)
      .where(eq(medidasFisicas.user_id, user_id));

    // Buscar dados de morfologia para os cavalos do usuário
    exportData.morfologia = await db.select()
      .from(morfologia)
      .where(eq(morfologia.user_id, user_id));

    // Buscar histórico de desempenho para os cavalos do usuário
    exportData.desempenhoHistorico = await db.select()
      .from(desempenhoHistorico)
      .where(eq(desempenhoHistorico.user_id, user_id));

    // Buscar dados de genealogia para os cavalos do usuário
    exportData.genealogia = await db.select()
      .from(genealogia)
      .where(eq(genealogia.user_id, user_id));

    // Criar nome de arquivo com timestamp
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const fileName = `user_${user_id}_export_${timestamp}.json`;
    const filePath = path.join(EXPORT_DIR, fileName);

    // Salvar dados em arquivo JSON
    fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2));

    console.log(`Dados exportados com sucesso para ${filePath}`);
    return filePath;
  } catch (error) {
    console.error('Erro ao exportar dados:', error);
    throw new Error('Falha ao exportar dados');
  }
}

// Função para importar dados de um arquivo JSON
export async function importData(filePath: string): Promise<void> {
  try {
    // Verificar se o arquivo existe
    if (!fs.existsSync(filePath)) {
      throw new Error(`Arquivo não encontrado: ${filePath}`);
    }

    // Ler arquivo JSON
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const importData = JSON.parse(fileContent);

    // Verificar se o arquivo contém dados válidos
    if (!importData || !importData.cavalos) {
      throw new Error('Arquivo de importação inválido');
    }

    // Importar cavalos
    for (const cavalo of importData.cavalos) {
      // Verificar se o cavalo já existe
      const existingCavalo = await db.select()
        .from(cavalos)
        .where(eq(cavalos.id, cavalo.id));

      if (existingCavalo.length > 0) {
        // Atualizar cavalo existente
        await db.update(cavalos)
          .set({
            ...cavalo,
            updatedAt: new Date()
          })
          .where(eq(cavalos.id, cavalo.id));
      } else {
        // Inserir novo cavalo
        await db.insert(cavalos).values(cavalo);
      }
    }

    // Importar medidas morfológicas
    for (const medida of importData.medidasMorfologicas) {
      // Verificar se a medida já existe
      const existingMedida = await db.select()
        .from(medidasMorfologicas)
        .where(eq(medidasMorfologicas.id, medida.id));

      if (existingMedida.length > 0) {
        // Atualizar medida existente
        await db.update(medidasMorfologicas)
          .set({
            ...medida,
            updatedAt: new Date()
          })
          .where(eq(medidasMorfologicas.id, medida.id));
      } else {
        // Inserir nova medida
        await db.insert(medidasMorfologicas).values(medida);
      }
    }

    // Importar manejos
    for (const manejo of importData.manejos) {
      // Verificar se o manejo já existe
      const existingManejo = await db.select()
        .from(manejos)
        .where(eq(manejos.id, manejo.id));

      if (existingManejo.length > 0) {
        // Atualizar manejo existente
        await db.update(manejos)
          .set({
            ...manejo,
            updatedAt: new Date()
          })
          .where(eq(manejos.id, manejo.id));
      } else {
        // Inserir novo manejo
        await db.insert(manejos).values(manejo);
      }
    }

    // Importar eventos
    for (const evento of importData.eventos) {
      // Verificar se o evento já existe
      const existingEvento = await db.select()
        .from(eventos)
        .where(eq(eventos.id, evento.id));

      if (existingEvento.length > 0) {
        // Atualizar evento existente
        await db.update(eventos)
          .set({
            ...evento,
            updatedAt: new Date()
          })
          .where(eq(eventos.id, evento.id));
      } else {
        // Inserir novo evento
        await db.insert(eventos).values(evento);
      }
    }

    // Importar procedimentos veterinários
    for (const procedimento of importData.procedimentosVet) {
      // Verificar se o procedimento já existe
      const existingProcedimento = await db.select()
        .from(procedimentosVet)
        .where(eq(procedimentosVet.id, procedimento.id));

      if (existingProcedimento.length > 0) {
        // Atualizar procedimento existente
        await db.update(procedimentosVet)
          .set({
            ...procedimento,
            updatedAt: new Date()
          })
          .where(eq(procedimentosVet.id, procedimento.id));
      } else {
        // Inserir novo procedimento
        await db.insert(procedimentosVet).values(procedimento);
      }
    }

    // Importar dados de reprodução
    for (const repro of importData.reproducao) {
      // Verificar se o registro já existe
      const existingRepro = await db.select()
        .from(reproducao)
        .where(eq(reproducao.id, repro.id));

      if (existingRepro.length > 0) {
        // Atualizar registro existente
        await db.update(reproducao)
          .set({
            ...repro,
            updatedAt: new Date()
          })
          .where(eq(reproducao.id, repro.id));
      } else {
        // Inserir novo registro
        await db.insert(reproducao).values(repro);
      }
    }

    // Importar medidas físicas
    for (const medida of importData.medidasFisicas) {
      // Verificar se a medida já existe
      const existingMedida = await db.select()
        .from(medidasFisicas)
        .where(eq(medidasFisicas.id, medida.id));

      if (existingMedida.length > 0) {
        // Atualizar medida existente
        await db.update(medidasFisicas)
          .set({
            ...medida,
            updatedAt: new Date()
          })
          .where(eq(medidasFisicas.id, medida.id));
      } else {
        // Inserir nova medida
        await db.insert(medidasFisicas).values(medida);
      }
    }

    // Importar dados de morfologia
    for (const morfo of importData.morfologia) {
      // Verificar se o registro já existe
      const existingMorfo = await db.select()
        .from(morfologia)
        .where(eq(morfologia.id, morfo.id));

      if (existingMorfo.length > 0) {
        // Atualizar registro existente
        await db.update(morfologia)
          .set({
            ...morfo,
            updatedAt: new Date()
          })
          .where(eq(morfologia.id, morfo.id));
      } else {
        // Inserir novo registro
        await db.insert(morfologia).values(morfo);
      }
    }

    // Importar histórico de desempenho
    for (const desempenho of importData.desempenhoHistorico) {
      // Verificar se o registro já existe
      const existingDesempenho = await db.select()
        .from(desempenhoHistorico)
        .where(eq(desempenhoHistorico.id, desempenho.id));

      if (existingDesempenho.length > 0) {
        // Atualizar registro existente
        await db.update(desempenhoHistorico)
          .set({
            ...desempenho,
            updatedAt: new Date()
          })
          .where(eq(desempenhoHistorico.id, desempenho.id));
      } else {
        // Inserir novo registro
        await db.insert(desempenhoHistorico).values(desempenho);
      }
    }

    // Importar dados de genealogia
    for (const gene of importData.genealogia) {
      // Verificar se o registro já existe
      const existingGene = await db.select()
        .from(genealogia)
        .where(eq(genealogia.id, gene.id));

      if (existingGene.length > 0) {
        // Atualizar registro existente
        await db.update(genealogia)
          .set({
            ...gene,
            updatedAt: new Date()
          })
          .where(eq(genealogia.id, gene.id));
      } else {
        // Inserir novo registro
        await db.insert(genealogia).values(gene);
      }
    }

    console.log(`Dados importados com sucesso de ${filePath}`);
  } catch (error) {
    console.error('Erro ao importar dados:', error);
    throw new Error('Falha ao importar dados');
  }
}

// Função para listar arquivos de exportação disponíveis
export function listExportFiles(): string[] {
  try {
    // Verificar se o diretório de exportação existe
    if (!fs.existsSync(EXPORT_DIR)) {
      return [];
    }

    // Listar arquivos no diretório de exportação
    const files = fs.readdirSync(EXPORT_DIR)
      .filter(file => file.endsWith('.json'))
      .map(file => path.join(EXPORT_DIR, file));

    return files;
  } catch (error) {
    console.error('Erro ao listar arquivos de exportação:', error);
    return [];
  }
}
