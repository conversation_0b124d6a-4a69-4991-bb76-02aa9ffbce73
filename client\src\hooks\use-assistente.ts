import { useState, useEffect, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { openDB, DBSchema } from "idb";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

// Definindo tipos
type MessageType = "user" | "assistant";

interface Message {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
}

interface StoredMessage {
  id: string;
  type: MessageType;
  content: string;
  timestamp: string; // Armazenado como string no IndexedDB
}

interface ChatMessage {
  content: string;
  role: string;
}

// Interface para o banco de dados IndexedDB
interface ChatDBSchema extends DBSchema {
  messages: {
    key: string;
    value: StoredMessage; // Usando o tipo com timestamp como string
    indexes: {
      "by-timestamp": string;
    };
  };
  pendingMessages: {
    key: string;
    value: {
      message: string;
      horse_id: string | null;
      timestamp: string; // Também armazenado como string
    };
  };
}

// Nome do banco de dados
const DB_NAME = "equigestor-chat";
const DB_VERSION = 1;

/**
 * Hook para gerenciar a comunicação com o assistente virtual
 * @param horse_id ID opcional do cavalo para contextualização
 * @returns Funções e estados para interagir com o assistente
 */
export function useAssistente(horse_id: string | null = null) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Inicializar o banco de dados IndexedDB
  const initDB = useCallback(async () => {
    return openDB<ChatDBSchema>(DB_NAME, DB_VERSION, {
      upgrade(db) {
        // Criar ou atualizar o armazenamento de mensagens
        const messagesStore = db.createObjectStore("messages", {
          keyPath: "id",
        });
        messagesStore.createIndex("by-timestamp", "timestamp");

        // Criar armazenamento para mensagens pendentes (para quando estiver offline)
        db.createObjectStore("pendingMessages", {
          keyPath: "timestamp",
          autoIncrement: true,
        });
      },
    });
  }, []);

  // Carregar mensagens do IndexedDB quando o componente montar
  useEffect(() => {
    const loadMessages = async () => {
      try {
        const db = await initDB();
        // Carregar apenas as últimas 50 mensagens (evitar sobrecarga)
        const allMessages = await db.getAllFromIndex(
          "messages",
          "by-timestamp",
          null
        );
        
        // Mapear as mensagens de volta, convertendo o timestamp de string para Date
        const mappedMessages = allMessages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        
        // Ordenar por timestamp e limitar a 50
        const sortedMessages = mappedMessages
          .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
          .slice(-50);
        
        console.log("[Assistente] Mensagens carregadas:", sortedMessages);
        setMessages(sortedMessages);
        
        // Verificar se há mensagens pendentes para enviar
        const pendingMessages = await db.getAll("pendingMessages");
        if (pendingMessages.length > 0) {
          console.log(`${pendingMessages.length} mensagens pendentes encontradas`);
          // Tentar enviar as mensagens pendentes se estiver online
          if (navigator.onLine) {
            processPendingMessages();
          }
        }
      } catch (error) {
        console.error("Erro ao carregar mensagens:", error);
        toast({
          title: "Erro ao carregar histórico",
          description: "Não foi possível carregar o histórico de mensagens.",
          variant: "destructive",
        });
      }
    };

    loadMessages();
    
    // Adicionar listeners para eventos de conectividade
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Handler para quando a conexão for restabelecida
  const handleOnline = useCallback(() => {
    console.log("Conexão online detectada");
    toast({
      title: "Você está online",
      description: "Sincronizando mensagens pendentes...",
    });
    processPendingMessages();
  }, []);

  // Handler para quando a conexão cair
  const handleOffline = useCallback(() => {
    console.log("Conexão offline detectada");
    toast({
      title: "Você está offline",
      description: "As mensagens serão enviadas quando a conexão for restabelecida.",
      variant: "destructive",
    });
  }, []);

  // Processar mensagens pendentes (quando voltar online)
  const processPendingMessages = useCallback(async () => {
    try {
      const db = await initDB();
      const pendingMessages = await db.getAll("pendingMessages");
      
      if (pendingMessages.length === 0) return;
      
      console.log(`[Assistente] Tentando enviar ${pendingMessages.length} mensagens pendentes:`, pendingMessages);
      
      for (const pendingMsg of pendingMessages) {
        try {
          // Tentar enviar a mensagem
          console.log("[Assistente] Enviando mensagem pendente:", pendingMsg.message);
          const response = await sendMessageToAPI(pendingMsg.message, pendingMsg.horse_id);
          console.log("[Assistente] Resposta recebida para mensagem pendente:", response);
          
          // Se enviar com sucesso, remover da fila
          console.log("[Assistente] Removendo mensagem pendente com timestamp:", pendingMsg.timestamp);
          await db.delete("pendingMessages", pendingMsg.timestamp);
        } catch (error) {
          console.error("[Assistente] Erro ao enviar mensagem pendente:", error);
          // Se falhar, mantém na fila para tentar novamente depois
          break; // Para de tentar se uma falhar
        }
      }
      
      // Verificar se há mais mensagens pendentes
      const remainingMessages = await db.getAll("pendingMessages");
      if (remainingMessages.length === 0) {
        toast({
          title: "Sincronização completa",
          description: "Todas as mensagens pendentes foram enviadas.",
        });
      } else {
        toast({
          title: "Sincronização parcial",
          description: `${pendingMessages.length - remainingMessages.length} de ${pendingMessages.length} mensagens enviadas.`,
        });
      }
    } catch (error) {
      console.error("[Assistente] Erro ao processar mensagens pendentes:", error);
    }
  }, []);

  // Adicionar uma mensagem ao IndexedDB
  const addMessageToIndexedDB = async (message: Message) => {
    try {
      const db = await initDB();
      // Converter a data para string para evitar problemas de serialização
      const messageToStore = {
        ...message,
        timestamp: message.timestamp.toISOString()
      };
      await db.add("messages", messageToStore as any);
    } catch (error) {
      console.error("Erro ao salvar mensagem no IndexedDB:", error);
    }
  };

  // Adicionar uma mensagem pendente quando estiver offline
  const addPendingMessage = async (message: string, horse_id: string | null) => {
    try {
      const db = await initDB();
      await db.add("pendingMessages", {
        message,
        horse_id,
        timestamp: new Date().toISOString(),
      });
      return true;
    } catch (error) {
      console.error("Erro ao salvar mensagem pendente:", error);
      return false;
    }
  };

  // Limpar todas as mensagens
  const clearMessages = useCallback(async () => {
    try {
      const db = await initDB();
      await db.clear("messages");
      setMessages([]);
    } catch (error) {
      console.error("Erro ao limpar mensagens:", error);
      toast({
        title: "Erro",
        description: "Não foi possível limpar o histórico de mensagens.",
        variant: "destructive",
      });
    }
  }, []);

  // Enviar mensagem para a API
  const sendMessageToAPI = async (message: string, currentHorseId: string | null) => {
    // Preparar as mensagens para envio à API
    const apiMessages: ChatMessage[] = [];
    
    // Adicionar as últimas 5 mensagens do histórico para contexto
    // (limitado para reduzir carga no modelo GPT-3.5-turbo)
    const contextMessages = messages.slice(-5);
    
    contextMessages.forEach((msg) => {
      apiMessages.push({
        role: msg.type === "user" ? "user" : "assistant",
        content: msg.content,
      });
    });
    
    // Adicionar a mensagem atual
    apiMessages.push({
      role: "user",
      content: message,
    });
    
    console.log("[Assistente] Enviando mensagens para API:", JSON.stringify(apiMessages));
    
    try {
      // Fazer a requisição para a API com timeout aumentado
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 segundos de timeout
      
      const response = await apiRequest("/api/chat", "POST", {
        messages: apiMessages,
        horse_id: currentHorseId,
      }, { signal: controller.signal });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Erro desconhecido" }));
        throw new Error(errorData.message || "Erro ao comunicar com o assistente");
      }
      
      const data = await response.json();
      console.log("[Assistente] Resposta da API recebida:", data);
      return data.content;
    } catch (error) {
      console.error("[Assistente] Erro na comunicação com API:", error);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error("A requisição demorou muito tempo. Tente novamente.");
        }
        
        // Preservar a mensagem de erro original se for um objeto Error
        throw error;
      } else {
        // Se não for um Error, criar um novo objeto de erro com melhor mensagem
        throw new Error("Falha na comunicação com o servidor. Tente novamente.");
      }
    }
  };

  // Função principal para enviar mensagem
  const sendMessage = async (message: string) => {
    if (message.trim() === "") return;
    
    setIsLoading(true);
    
    try {
      // Criar e adicionar a mensagem do usuário imediatamente
      const userMessage: Message = {
        id: uuidv4(),
        type: "user",
        content: message,
        timestamp: new Date(),
      };
      
      console.log("[Assistente] Adicionando mensagem do usuário:", userMessage);
      
      // Adicionar à lista de mensagens e ao IndexedDB
      setMessages((prev) => [...prev, userMessage]);
      await addMessageToIndexedDB(userMessage);
      
      let assistantResponse;
      
      // Verificar se está online
      if (navigator.onLine) {
        try {
          // Enviar mensagem diretamente usando fetch em vez de usar a função auxiliar
          console.log("[Assistente] Enviando para API com horse_id:", horse_id);
          
          // Preparar as mensagens para envio à API
          const apiMessages = [];
          
          // Adicionar as últimas 5 mensagens do histórico para contexto
          const contextMessages = messages.slice(-5);
          
          contextMessages.forEach((msg) => {
            apiMessages.push({
              role: msg.type === "user" ? "user" : "assistant",
              content: msg.content,
            });
          });
          
          // Adicionar a mensagem atual
          apiMessages.push({
            role: "user",
            content: message,
          });
          
          console.log("[Assistente] Enviando diretamente:", JSON.stringify({
            messages: apiMessages,
            horse_id: horse_id
          }));
          
          // Usar fetch diretamente com um timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000);
          
          // Obter token de autenticação - simplificado
          const userData = localStorage.getItem('user');
          const authHeaders = { 'Content-Type': 'application/json' };
          
          if (userData) {
            try {
              const user = JSON.parse(userData);
              if (user.token) authHeaders['Authorization'] = `Bearer ${user.token}`;
              if (user.id) authHeaders['user-id'] = user.id.toString();
            } catch (e) {
              console.warn('[Assistente] Erro ao parsear dados do usuário:', e);
            }
          }

          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: authHeaders,
            body: JSON.stringify({
              messages: apiMessages,
              horse_id: horse_id
            }),
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`Erro do servidor: ${response.status}`);
          }
          
          const data = await response.json();
          console.log("[Assistente] Resposta direta recebida:", data);
          
          assistantResponse = data.content;
          
        } catch (error) {
          console.error("[Assistente] Erro na comunicação direta com a API:", error);
          
          // Se falhar, adicionar à lista de mensagens pendentes
          const added = await addPendingMessage(message, horse_id);
          
          if (added) {
            // Informar ao usuário que a mensagem será enviada posteriormente
            assistantResponse = "Parece que estou com dificuldades para me conectar ao servidor. Sua mensagem foi salva e será processada assim que a conexão for restabelecida.";
          } else {
            // Falha total - não conseguiu nem salvar localmente
            assistantResponse = "Não foi possível processar sua mensagem no momento. Por favor, tente novamente mais tarde.";
          }
        }
      } else {
        // Estamos offline, salvar para envio posterior
        await addPendingMessage(message, horse_id);
        assistantResponse = "Você parece estar offline. Sua mensagem foi salva e será enviada quando a conexão for restabelecida.";
      }
      
      // Criar e adicionar a resposta do assistente
      const assistantMessage: Message = {
        id: uuidv4(),
        type: "assistant",
        content: assistantResponse || "Desculpe, não consegui processar sua mensagem. Tente novamente mais tarde.",
        timestamp: new Date(),
      };
      
      console.log("[Assistente] Adicionando resposta:", assistantMessage);
      
      // Adicionar à lista de mensagens e ao IndexedDB
      setMessages((prev) => [...prev, assistantMessage]);
      await addMessageToIndexedDB(assistantMessage);
      
    } catch (error) {
      console.error("[Assistente] Erro geral ao enviar mensagem:", error);
      toast({
        title: "Erro ao enviar mensagem",
        description: error instanceof Error ? error.message : "Houve um problema ao processar sua mensagem. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { 
    messages, 
    sendMessage, 
    isLoading,
    clearMessages
  };
}