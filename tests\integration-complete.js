/**
 * Testes de Integração Completos - EquiGestor AI
 * Script para validar todos os módulos após atualizações
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_URL = 'http://localhost:5000';

// Configurar autenticação
let authHeaders = {};

class IntegrationTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      errors: [],
      modules: {}
    };
  }

  async authenticate() {
    try {
      console.log('🔐 Autenticando usuário...');
      const response = await axios.post(`${BASE_URL}/api/auth/login`, {
        username: 'samuel',
        password: 'samu69'
      }, {
        timeout: 10000
      });

      if (response.data.token) {
        authHeaders = {
          'Authorization': `Bearer ${response.data.token}`,
          'Content-Type': 'application/json'
        };
        console.log('✅ Autenticação bem-sucedida');
        this.results.passed++;
        return true;
      }
    } catch (error) {
      console.error('❌ Falha na autenticação:', error.message);
      this.results.failed++;
      this.results.errors.push('Authentication failed');
      return false;
    }
  }

  async testEndpoint(method, endpoint, data = null, description = '') {
    try {
      const config = {
        method,
        url: `${BASE_URL}${endpoint}`,
        headers: authHeaders,
        timeout: 15000
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        config.data = data;
      }

      const response = await axios(config);
      console.log(`✅ ${description}: ${response.status}`);
      this.results.passed++;
      return response.data;
    } catch (error) {
      const status = error.response?.status || 'NO_RESPONSE';
      const message = error.response?.data?.message || error.message;
      console.error(`❌ ${description}: ${status} - ${message}`);
      this.results.failed++;
      this.results.errors.push(`${endpoint}: ${message}`);
      return null;
    }
  }

  async testCavalosModule() {
    console.log('\n📋 Testando Módulo de Cavalos...');
    this.results.modules.cavalos = {};

    // Buscar cavalos
    const cavalos = await this.testEndpoint('GET', '/api/cavalos', null, 'Buscar cavalos');
    this.results.modules.cavalos.list = cavalos !== null;

    // Testar busca de pelagens
    const pelagens = await this.testEndpoint('GET', '/api/pelagens', null, 'Buscar pelagens');
    this.results.modules.cavalos.pelagens = pelagens !== null;

    // Testar criação de cavalo (dados mínimos válidos)
    const novoCavalo = {
      name: 'Teste Integração',
      sexo: 'Macho',
      status: 'ativo',
      birth_date: '2020-01-01'
    };

    const cavaloCreated = await this.testEndpoint('POST', '/api/cavalos', novoCavalo, 'Criar cavalo');
    this.results.modules.cavalos.create = cavaloCreated !== null;

    if (cavaloCreated && cavaloCreated.id) {
      // Testar busca individual
      await this.testEndpoint('GET', `/api/cavalos/${cavaloCreated.id}`, null, 'Buscar cavalo específico');
      
      // Testar atualização
      const updateData = { notes: 'Atualizado via teste de integração' };
      await this.testEndpoint('PUT', `/api/cavalos/${cavaloCreated.id}`, updateData, 'Atualizar cavalo');
      
      // Testar exclusão
      await this.testEndpoint('DELETE', `/api/cavalos/${cavaloCreated.id}`, null, 'Excluir cavalo');
    }
  }

  async testManejosModule() {
    console.log('\n🔧 Testando Módulo de Manejos...');
    this.results.modules.manejos = {};

    // Buscar manejos
    const manejos = await this.testEndpoint('GET', '/api/manejos', null, 'Buscar manejos');
    this.results.modules.manejos.list = manejos !== null;

    // Testar tipos de manejo
    const tiposManejos = await this.testEndpoint('GET', '/api/manejos/tipos', null, 'Buscar tipos de manejos');
    this.results.modules.manejos.tipos = tiposManejos !== null;
  }

  async testNutricaoModule() {
    console.log('\n🌾 Testando Módulo de Nutrição...');
    this.results.modules.nutricao = {};

    // Buscar registros de nutrição
    const nutricao = await this.testEndpoint('GET', '/api/nutricao', null, 'Buscar registros nutrição');
    this.results.modules.nutricao.list = nutricao !== null;

    // Testar estatísticas
    const stats = await this.testEndpoint('GET', '/api/nutricao/estatisticas', null, 'Estatísticas nutrição');
    this.results.modules.nutricao.stats = stats !== null;
  }

  async testGenealogyModule() {
    console.log('\n🧬 Testando Módulo de Genealogia...');
    this.results.modules.genealogia = {};

    // Buscar genealogias
    const genealogias = await this.testEndpoint('GET', '/api/genealogia', null, 'Buscar genealogias');
    this.results.modules.genealogia.list = genealogias !== null;
  }

  async testVeterinarioModule() {
    console.log('\n🏥 Testando Módulo Veterinário...');
    this.results.modules.veterinario = {};

    // Buscar procedimentos veterinários
    const procedimentos = await this.testEndpoint('GET', '/api/veterinario', null, 'Buscar procedimentos vet');
    this.results.modules.veterinario.list = procedimentos !== null;

    // Testar tipos de procedimentos
    const tipos = await this.testEndpoint('GET', '/api/veterinario/tipos', null, 'Tipos procedimentos vet');
    this.results.modules.veterinario.tipos = tipos !== null;
  }

  async testReproducaoModule() {
    console.log('\n🐎 Testando Módulo de Reprodução...');
    this.results.modules.reproducao = {};

    // Buscar registros de reprodução
    const reproducao = await this.testEndpoint('GET', '/api/reproducao', null, 'Buscar reprodução');
    this.results.modules.reproducao.list = reproducao !== null;

    // Testar estatísticas reprodutivas
    const stats = await this.testEndpoint('GET', '/api/reproducao/estatisticas', null, 'Stats reprodução');
    this.results.modules.reproducao.stats = stats !== null;
  }

  async testABCCCModule() {
    console.log('\n📄 Testando Módulo ABCCC...');
    this.results.modules.abccc = {};

    // Testar tokens (sem autenticação específica necessária)
    const tokens = await this.testEndpoint('GET', '/api/abccc/tokens', null, 'Buscar tokens ABCCC');
    this.results.modules.abccc.tokens = tokens !== null;
  }

  async testAssistenteModule() {
    console.log('\n🤖 Testando Assistente Virtual...');
    this.results.modules.assistente = {};

    // Testar endpoint do assistente
    const mensagemTeste = {
      message: 'Quantos cavalos tenho cadastrados?',
      context: 'dashboard'
    };

    const response = await this.testEndpoint('POST', '/api/assistente/mensagem', mensagemTeste, 'Mensagem assistente');
    this.results.modules.assistente.chat = response !== null;
  }

  async testHealthChecks() {
    console.log('\n🏥 Testando Health Checks...');
    
    // Health check geral
    await this.testEndpoint('GET', '/health', null, 'Health check geral');
    
    // Status do banco
    await this.testEndpoint('GET', '/api/status/database', null, 'Status database');
    
    // Métricas do sistema
    await this.testEndpoint('GET', '/api/status/metrics', null, 'Métricas sistema');
  }

  async runCompleteTests() {
    console.log('🚀 Iniciando Testes de Integração Completos - EquiGestor AI\n');
    
    const startTime = Date.now();

    // Autenticar primeiro
    const authSuccess = await this.authenticate();
    if (!authSuccess) {
      console.log('❌ Testes abortados - falha na autenticação');
      return this.generateReport();
    }

    // Executar todos os testes de módulos
    await this.testCavalosModule();
    await this.testManejosModule();
    await this.testNutricaoModule();
    await this.testGenealogyModule();
    await this.testVeterinarioModule();
    await this.testReproducaoModule();
    await this.testABCCCModule();
    await this.testAssistenteModule();
    await this.testHealthChecks();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`\n⏱️  Testes concluídos em ${duration.toFixed(2)}s`);
    
    return this.generateReport();
  }

  generateReport() {
    const total = this.results.passed + this.results.failed;
    const successRate = total > 0 ? ((this.results.passed / total) * 100).toFixed(1) : 0;

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total_tests: total,
        passed: this.results.passed,
        failed: this.results.failed,
        success_rate: `${successRate}%`
      },
      modules: this.results.modules,
      errors: this.results.errors
    };

    // Salvar relatório
    const reportPath = path.join(__dirname, '..', 'reports', `integration-test-${Date.now()}.json`);
    try {
      if (!fs.existsSync(path.dirname(reportPath))) {
        fs.mkdirSync(path.dirname(reportPath), { recursive: true });
      }
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`📊 Relatório salvo em: ${reportPath}`);
    } catch (error) {
      console.error('❌ Erro ao salvar relatório:', error.message);
    }

    // Exibir resumo
    console.log('\n📊 RESUMO DOS TESTES:');
    console.log(`Total: ${total} | Passou: ${this.results.passed} | Falhou: ${this.results.failed}`);
    console.log(`Taxa de Sucesso: ${successRate}%`);

    if (this.results.errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:');
      this.results.errors.forEach(error => console.log(`  - ${error}`));
    }

    // Status dos módulos
    console.log('\n📋 STATUS DOS MÓDULOS:');
    Object.entries(this.results.modules).forEach(([module, tests]) => {
      const moduleTests = Object.values(tests);
      const moduleSuccess = moduleTests.filter(Boolean).length;
      const moduleTotal = moduleTests.length;
      const moduleRate = moduleTotal > 0 ? ((moduleSuccess / moduleTotal) * 100).toFixed(0) : 0;
      console.log(`  ${module}: ${moduleSuccess}/${moduleTotal} (${moduleRate}%)`);
    });

    return report;
  }
}

// Executar testes se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new IntegrationTester();
  tester.runCompleteTests()
    .then(report => {
      const exitCode = report.summary.failed > 0 ? 1 : 0;
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('❌ Erro fatal nos testes:', error);
      process.exit(1);
    });
}

export default IntegrationTester;