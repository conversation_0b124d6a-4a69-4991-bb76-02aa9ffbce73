import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  PieChart,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Download,
  RefreshCw
} from 'lucide-react';
import { formatMoney } from '@/utils/finance';
import { useRelatorioMensal } from '@/hooks/use-financeiro';

/**
 * Página de relatórios financeiros refatorada
 * 
 * Funcionalidades MVP:
 * - Relatório mensal com resumo de receitas/despesas
 * - Distribuição por categorias
 * - Distribuição por cavalos
 * - Seleção de período
 * - Exportação básica (futuro)
 */
export default function RelatoriosPageRefactored() {
  const currentDate = new Date();
  const [selectedMonth, setSelectedMonth] = useState(currentDate.getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear());

  // Hook para dados do relatório
  const { data: relatorio, isLoading, error, refetch } = useRelatorioMensal(selectedMonth, selectedYear);

  // Gerar lista de meses e anos
  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' },
  ];

  const years = Array.from({ length: 5 }, (_, i) => currentDate.getFullYear() - i);

  // Cálculos para visualizações
  const totalMovimentacao = (relatorio?.resumo.receitas || 0) + (relatorio?.resumo.despesas || 0);
  const percentualReceitas = totalMovimentacao > 0 ? (relatorio?.resumo.receitas || 0) / totalMovimentacao * 100 : 0;
  const percentualDespesas = totalMovimentacao > 0 ? (relatorio?.resumo.despesas || 0) / totalMovimentacao * 100 : 0;

  const renderResumoCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <Card className="border-green-200 bg-green-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-green-700 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Receitas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {formatMoney(relatorio?.resumo.receitas || 0)}
          </div>
          <Progress value={percentualReceitas} className="mt-2" />
          <p className="text-sm text-green-600 mt-1">
            {percentualReceitas.toFixed(1)}% do total
          </p>
        </CardContent>
      </Card>
      
      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-red-700 flex items-center">
            <TrendingDown className="h-5 w-5 mr-2" />
            Despesas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {formatMoney(relatorio?.resumo.despesas || 0)}
          </div>
          <Progress value={percentualDespesas} className="mt-2" />
          <p className="text-sm text-red-600 mt-1">
            {percentualDespesas.toFixed(1)}% do total
          </p>
        </CardContent>
      </Card>
      
      <Card className={`border-2 ${(relatorio?.resumo.saldo || 0) >= 0 ? 'border-blue-200 bg-blue-50' : 'border-orange-200 bg-orange-50'}`}>
        <CardHeader className="pb-3">
          <CardTitle className={`text-lg flex items-center ${(relatorio?.resumo.saldo || 0) >= 0 ? 'text-blue-700' : 'text-orange-700'}`}>
            <DollarSign className="h-5 w-5 mr-2" />
            Saldo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${(relatorio?.resumo.saldo || 0) >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
            {formatMoney(relatorio?.resumo.saldo || 0)}
          </div>
          <Badge variant={(relatorio?.resumo.saldo || 0) >= 0 ? 'default' : 'destructive'} className="mt-2">
            {(relatorio?.resumo.saldo || 0) >= 0 ? 'Positivo' : 'Negativo'}
          </Badge>
        </CardContent>
      </Card>
      
      <Card className="border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg text-gray-700 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Movimentação
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-600">
            {formatMoney(totalMovimentacao)}
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Total movimentado
          </p>
        </CardContent>
      </Card>
    </div>
  );

  const renderCategoriaTable = (titulo: string, categorias: any[], tipo: 'receita' | 'despesa') => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {tipo === 'receita' ? 
            <TrendingUp className="h-5 w-5 text-green-600" /> : 
            <TrendingDown className="h-5 w-5 text-red-600" />
          }
          {titulo}
        </CardTitle>
        <CardDescription>
          {categorias.length} categoria(s) com movimentação
        </CardDescription>
      </CardHeader>
      <CardContent>
        {categorias.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nenhuma movimentação de {tipo} neste período
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Categoria</TableHead>
                  <TableHead className="text-right">Valor</TableHead>
                  <TableHead className="text-right">% do Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categorias.map((categoria) => {
                  const totalTipo = tipo === 'receita' ? relatorio?.resumo.receitas || 0 : relatorio?.resumo.despesas || 0;
                  const percentual = totalTipo > 0 ? (categoria.total / totalTipo) * 100 : 0;
                  
                  return (
                    <TableRow key={categoria.categoria_id}>
                      <TableCell className="font-medium">
                        {categoria.categoria_nome}
                      </TableCell>
                      <TableCell className={`text-right font-bold ${tipo === 'receita' ? 'text-green-600' : 'text-red-600'}`}>
                        {formatMoney(categoria.total)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Badge variant="outline">
                          {percentual.toFixed(1)}%
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderCavalosTable = () => {
    if (!relatorio?.porCavalo.length) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Movimentação por Cavalo
          </CardTitle>
          <CardDescription>
            Lançamentos associados a cavalos específicos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cavalo</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead className="text-right">Valor</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {relatorio.porCavalo.map((cavalo, index) => (
                  <TableRow key={`${cavalo.cavalo_id}-${cavalo.tipo}-${index}`}>
                    <TableCell className="font-medium">
                      {cavalo.cavalo_nome}
                    </TableCell>
                    <TableCell>
                      <Badge variant={cavalo.tipo === 'receita' ? 'default' : 'destructive'}>
                        {cavalo.tipo === 'receita' ? 'Receita' : 'Despesa'}
                      </Badge>
                    </TableCell>
                    <TableCell className={`text-right font-bold ${cavalo.tipo === 'receita' ? 'text-green-600' : 'text-red-600'}`}>
                      {formatMoney(cavalo.total)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho com seletores */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Relatórios Financeiros</h2>
          <p className="text-gray-600">Análise detalhada das movimentações financeiras</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex gap-2">
            <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value.toString()}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <Button onClick={() => refetch()} disabled={isLoading} variant="outline">
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          
          <Button variant="outline" disabled>
            <Download className="mr-2 h-4 w-4" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Período selecionado */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-lg text-blue-700 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Período: {months.find(m => m.value === selectedMonth)?.label} de {selectedYear}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Estados de loading e erro */}
      {isLoading && (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          Carregando relatório...
        </div>
      )}

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">Erro ao carregar relatório. Tente novamente.</p>
          </CardContent>
        </Card>
      )}

      {/* Conteúdo do relatório */}
      {!isLoading && !error && relatorio && (
        <>
          {renderResumoCards()}
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderCategoriaTable(
              'Receitas por Categoria',
              (relatorio.porCategoria || []).filter(c => c.tipo === 'receita'),
              'receita'
            )}
            {renderCategoriaTable(
              'Despesas por Categoria',
              (relatorio.porCategoria || []).filter(c => c.tipo === 'despesa'),
              'despesa'
            )}
          </div>
          
          {relatorio.porCavalo && relatorio.porCavalo.length > 0 && (
            <div className="mt-6">
              {renderCavalosTable()}
            </div>
          )}
          
          {/* Mensagem quando não há dados */}
          {(!relatorio.porCategoria || relatorio.porCategoria.length === 0) && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Nenhuma movimentação financeira neste período</p>
                  <p className="text-sm mt-2">Adicione lançamentos para visualizar relatórios</p>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}