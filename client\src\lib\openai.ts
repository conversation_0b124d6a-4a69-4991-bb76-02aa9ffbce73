import OpenAI from "openai";

// o modelo mais recente da OpenAI é "gpt-4o" que foi lançado em 13 de maio de 2024. não alterar a menos que solicitado explicitamente pelo usuário
const MODEL = "gpt-4.1-nano"; // Atualizado para GPT-4o mini conforme solicitado pelo usuário

// Tipos para as mensagens
type MessageRole = "user" | "assistant" | "system";
type ChatMessage = {
  role: MessageRole;
  content: string;
};

// Criação da instância do cliente OpenAI
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Necessário para uso no frontend
});

// Contexto do sistema para o assistente do EquiGestor
const systemPrompt = `Você é o assistente virtual do EquiGestor, um sistema de gestão para cavalos. 
Responda sempre em português e de forma natural, como se estivesse conversando com o usuário.

O EquiGestor possui os seguintes módulos e funcionalidades:

1. Gestão de Cavalos:
   - Cadastro completo de cavalos (nome, raça, nascimento, sexo, etc.)
   - Registro de genealogia e histórico
   - Acompanhamento de medidas físicas (peso, altura, etc.)

2. Manejo:
   - Agendamento de vacinações
   - Ferrageamento
   - Vermifugação
   - Treinamentos
   - Participação em competições

3. Veterinário:
   - Registro de procedimentos veterinários
   - Histórico de saúde
   - Controle de medicações
   - Exames
   
4. Reprodução:
   - Controle de ciclo reprodutivo
   - Registro de coberturas
   - Acompanhamento de gestação
   - Histórico de partos e potros

5. Nutrição:
   - Planejamento alimentar
   - Controle de dietas
   - Suplementações
   
6. Financeiro:
   - Controle de custos
   - Receitas com serviços
   - Relatórios financeiros

7. Agenda e Alertas:
   - Calendário de compromissos
   - Notificações para manejos atrasados
   - Lembretes de vacinação

8. Estatísticas:
   - Relatórios personalizados
   - Gráficos de desempenho
   - Indicadores de saúde

9. Arquivo:
   - Upload de fotos, vídeos e documentos
   - Organização por cavalo

Responda perguntas no contexto dessas funcionalidades e ajude o usuário a utilizar o sistema da melhor forma possível.
Seja específico quando um usuário perguntar sobre como realizar uma tarefa.

Sobre os cavalos cadastrados no sistema, temos:
- Thor: garanhão, 8 anos, 520kg, Quarto de Milha, especialista em provas de velocidade
- Pegasus: macho castrado, 8 anos, 450kg, Árabe, usado principalmente para enduro
- Trovão: macho, 5 anos, 480kg, Mangalarga Marchador, em treinamento para marcha
- Zeus: garanhão, 12 anos, 500kg, Brasileiro de Hipismo, usado para saltos

Ao responder, seja diretamente útil e objetivo. Se a pergunta não estiver relacionada ao sistema EquiGestor, informe educadamente que você é um assistente especializado em gestão equina.`;

/**
 * Função que envia uma mensagem para a API da OpenAI e retorna a resposta
 * 
 * @param userMessage Mensagem do usuário
 * @param chatHistory Histórico de mensagens anteriores
 * @returns Resposta da OpenAI
 */
export async function sendMessageToOpenAI(userMessage: string, chatHistory: Array<{role: string, content: string}> = []) {
  try {
    // Preparar as mensagens para envio
    const messages = [
      { 
        role: "system", 
        content: systemPrompt 
      },
      ...chatHistory.map(msg => ({
        role: msg.role as "user" | "assistant" | "system",
        content: msg.content
      })),
      { 
        role: "user", 
        content: userMessage 
      }
    ];

    // Enviar para a API
    const response = await openai.chat.completions.create({
      model: MODEL,
      messages,
      temperature: 0.7,
      max_tokens: 800,
    });

    return response.choices[0].message.content || "Desculpe, não consegui processar sua solicitação.";
  } catch (error) {
    console.error("Erro ao chamar a API da OpenAI:", error);
    return "Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente mais tarde.";
  }
}

/**
 * Detecta a intenção do usuário a partir de uma mensagem, usando a OpenAI
 * 
 * @param userMessage Mensagem do usuário
 * @returns Objeto com a intenção e o tipo (consulta ou ação)
 */
export async function detectIntentWithAI(userMessage: string): Promise<{intent: string, type: 'action' | 'query'}> {
  try {
    const intentPrompt = `Analise a seguinte mensagem do usuário relacionada a um sistema de gestão de cavalos:
    "${userMessage}"
    
    Identifique a intenção principal e classifique-a em uma das seguintes categorias:
    - Registro de peso
    - Vacinação
    - Treinamento
    - Alimentação
    - Ferrageamento
    - Consulta veterinária
    - Cadastro de cavalo
    - Reprodução
    - Consulta de informação
    
    Além disso, classifique se é uma CONSULTA (o usuário só quer informação) ou uma AÇÃO (o usuário quer executar algo no sistema).
    
    Responda em formato JSON:
    {
      "intent": "categoria escolhida",
      "type": "query ou action"
    }`;

    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: intentPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    const result = JSON.parse(response.choices[0].message.content || '{"intent": "Consulta de informação", "type": "query"}');
    return {
      intent: result.intent,
      type: result.type as 'action' | 'query'
    };
  } catch (error) {
    console.error("Erro ao detectar intenção com IA:", error);
    return {
      intent: "Consulta de informação",
      type: "query"
    };
  }
}

/**
 * Extrai entidades relevantes de uma mensagem do usuário
 * 
 * @param userMessage Mensagem do usuário
 * @returns Objeto com as entidades extraídas
 */
export async function extractEntities(userMessage: string): Promise<Record<string, any>> {
  try {
    const entityPrompt = `Extraia as informações relevantes da seguinte mensagem relacionada a um sistema de gestão de cavalos:
    "${userMessage}"
    
    Extraia as seguintes entidades, se presentes:
    - nome_cavalo: nome do cavalo mencionado
    - tipo_acao: tipo de ação a ser realizada (vacinar, pesar, treinar, etc.)
    - data: qualquer data mencionada
    - valor_peso: valor numérico de peso, se mencionado (somente o número)
    - valor_altura: valor numérico de altura, se mencionado (somente o número)
    - tipo_vacina: nome da vacina mencionada
    
    Responda em formato JSON, incluindo apenas os campos encontrados:
    {
      "nome_cavalo": "nome encontrado",
      "tipo_acao": "ação encontrada",
      etc.
    }`;

    const response = await openai.chat.completions.create({
      model: MODEL,
      messages: [
        { role: "user", content: entityPrompt }
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  } catch (error) {
    console.error("Erro ao extrair entidades com IA:", error);
    return {};
  }
}

export default openai;