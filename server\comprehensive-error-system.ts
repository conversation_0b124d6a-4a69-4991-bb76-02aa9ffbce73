/**
 * Sistema Abrangente de Monitoramento de Erros
 * Monitora e registra erros sem interferir na lógica existente
 */

import { Request, Response, NextFunction } from 'express';

interface ErrorLog {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: {
    userId?: number;
    endpoint?: string;
    method?: string;
    userAgent?: string;
    ip?: string;
  };
  resolved: boolean;
}

class ComprehensiveErrorSystem {
  private errors: Map<string, ErrorLog> = new Map();
  private maxErrors = 1000; // Limite de erros mantidos em memória

  /**
   * Registra um erro no sistema
   */
  logError(error: Error, context?: Partial<ErrorLog['context']>): string {
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const errorLog: ErrorLog = {
      id,
      timestamp: new Date(),
      level: 'error',
      message: error.message,
      stack: error.stack,
      context,
      resolved: false
    };

    this.errors.set(id, errorLog);
    
    // Limitar tamanho da memória
    if (this.errors.size > this.maxErrors) {
      const firstKey = this.errors.keys().next().value;
      if (firstKey) {
        this.errors.delete(firstKey);
      }
    }

    // Log no console para desenvolvimento
    console.error(`[ERROR_SYSTEM] ${error.message}`, {
      id,
      context,
      stack: error.stack
    });

    return id;
  }

  /**
   * Registra um warning
   */
  logWarning(message: string, context?: Partial<ErrorLog['context']>): string {
    const id = `warning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const warningLog: ErrorLog = {
      id,
      timestamp: new Date(),
      level: 'warning',
      message,
      context,
      resolved: false
    };

    this.errors.set(id, warningLog);
    
    console.warn(`[WARNING_SYSTEM] ${message}`, { id, context });

    return id;
  }

  /**
   * Middleware para capturar erros automaticamente
   */
  errorMiddleware = (error: Error, req: Request, res: Response, next: NextFunction) => {
    const context = {
      userId: req.user?.id,
      endpoint: req.path,
      method: req.method,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };

    const errorId = this.logError(error, context);

    // Resposta padronizada sem expor detalhes internos
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Um erro interno ocorreu. Nossa equipe foi notificada.',
        errorId: errorId,
        timestamp: new Date().toISOString()
      });
    }

    next(error);
  };

  /**
   * Middleware para logging de requisições suspeitas
   */
  requestMonitor = (req: Request, res: Response, next: NextFunction) => {
    // Monitorar requisições com falha de autenticação
    const originalSend = res.send;
    res.send = function(data) {
      if (res.statusCode === 401 || res.statusCode === 403) {
        errorSystem.logWarning(`Authentication/Authorization failure: ${res.statusCode}`, {
          endpoint: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
      }
      return originalSend.call(this, data);
    };

    next();
  };

  /**
   * Obter estatísticas de erros
   */
  getErrorStats() {
    const errors = Array.from(this.errors.values());
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      total: errors.length,
      lastHour: errors.filter(e => e.timestamp > oneHourAgo).length,
      lastDay: errors.filter(e => e.timestamp > oneDayAgo).length,
      unresolved: errors.filter(e => !e.resolved).length,
      byLevel: {
        error: errors.filter(e => e.level === 'error').length,
        warning: errors.filter(e => e.level === 'warning').length,
        info: errors.filter(e => e.level === 'info').length
      }
    };
  }

  /**
   * Obter erros recentes
   */
  getRecentErrors(limit = 10) {
    const errors = Array.from(this.errors.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);

    return errors.map(error => ({
      id: error.id,
      timestamp: error.timestamp,
      level: error.level,
      message: error.message,
      context: error.context,
      resolved: error.resolved
    }));
  }

  /**
   * Marcar erro como resolvido
   */
  resolveError(errorId: string): boolean {
    const error = this.errors.get(errorId);
    if (error) {
      error.resolved = true;
      return true;
    }
    return false;
  }

  /**
   * Obter saúde geral do sistema
   */
  getSystemHealth() {
    const stats = this.getErrorStats();
    const recentErrors = stats.lastHour;
    const criticalErrors = stats.byLevel.error;

    let healthScore = 100;
    let status = 'healthy';

    // Penalizar por erros recentes
    if (recentErrors > 10) {
      healthScore -= 30;
      status = 'degraded';
    } else if (recentErrors > 5) {
      healthScore -= 15;
    }

    // Penalizar por erros críticos não resolvidos
    if (criticalErrors > 5) {
      healthScore -= 25;
      status = 'critical';
    }

    return {
      status,
      score: Math.max(0, healthScore),
      stats,
      timestamp: new Date()
    };
  }
}

// Instância singleton
export const comprehensiveErrorSystem = new ComprehensiveErrorSystem();
export const errorSystem = comprehensiveErrorSystem;

// Capturar erros não tratados
process.on('uncaughtException', (error) => {
  comprehensiveErrorSystem.logError(error, { endpoint: 'uncaughtException' });
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  comprehensiveErrorSystem.logError(error, { endpoint: 'unhandledRejection' });
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

export default comprehensiveErrorSystem;