import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { CavaloFormValues } from '@/pages/CavaloFormPage';
import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/lib/queryClient';
import { useValidation } from '@/lib/validation-engine';
// Removed performance optimizer import

/**
 * Hook unificado para buscar cavalos para dropdowns
 * Inclui cavalos do plantel e externos com cache inteligente
 */
export function useCavalosDropdown() {
  const { user } = useAuth();
  
  const query = useQuery({
    queryKey: ['/api/cavalos-genealogia', user?.id],
    queryFn: () => apiRequest('/api/cavalos-genealogia', 'GET'),
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false
  });
  
  return {
    cavalos: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error
  };
}

/**
 * Hook para buscar detalhes de um cavalo pelo ID
 */
export function useCavaloDetalhes(id: string | number | null) {
  const query = useQuery({
    queryKey: [`/api/cavalos/${id}`],
    queryFn: async () => {
      if (!id) return null;
      
      try {
        const response = await fetch(`/api/cavalos/${id}`, {
          headers: {
            'Content-Type': 'application/json',
            'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
          }
        });
        
        if (!response.ok) {
          throw new Error('Erro ao buscar detalhes do cavalo');
        }
        
        return await response.json();
      } catch (error) {
        console.error(`Erro ao buscar cavalo ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 60 * 1000, // 1 minuto
    refetchOnWindowFocus: false
  });
  
  return {
    cavalo: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error
  };
}

/**
 * Função para transformar dados do formulário para o formato do banco
 */
function transformarDadosParaBanco(dados: CavaloFormValues) {
  return {
    ...dados,
    birth_date: dados.birth_date || null,
    numero_registro: dados.numero_registro || null,
    data_entrada: dados.data_entrada || null,
    data_saida: dados.data_saida || null,
    data_compra: dados.data_compra || null,
    motivo_saida: dados.motivo_saida || null,
    valor_compra: dados.valor_compra || null,
    pai_id: dados.pai_id || null,
    mae_id: dados.mae_id || null,
    is_external: dados.is_external || false,
    pelagem_id: dados.pelagem_id || null
  };
}

/**
 * Hook para criar um novo cavalo
 */
export function useCriarCavalo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (dados: CavaloFormValues) => {
      const dadosTransformados = transformarDadosParaBanco(dados);
      console.log('Dados transformados para criação:', dadosTransformados);
      
      const response = await fetch('/api/cavalos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
        },
        body: JSON.stringify(dadosTransformados)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao criar cavalo');
      }
      
      return await response.json();
    },
    onSuccess: () => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      
      toast({
        title: 'Sucesso!',
        description: 'Cavalo cadastrado com sucesso.',
      });
    },
    onError: (error: Error) => {
      console.error('Erro ao criar cavalo:', error);
      
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível cadastrar o cavalo. Tente novamente.',
        variant: 'destructive'
      });
    }
  });
}

/**
 * Hook para atualizar um cavalo existente
 */
export function useAtualizarCavalo() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, dados }: { id: number, dados: CavaloFormValues }) => {
      const dadosTransformados = transformarDadosParaBanco(dados);
      console.log('Dados transformados para atualização:', dadosTransformados);
      
      const response = await fetch(`/api/cavalos/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'User-Id': localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')!).id : '1'
        },
        body: JSON.stringify(dadosTransformados)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao atualizar cavalo');
      }
      
      return await response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: [`/api/cavalos/${variables.id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      
      toast({
        title: 'Sucesso!',
        description: 'Cavalo atualizado com sucesso.',
      });
    },
    onError: (error: Error) => {
      console.error('Erro ao atualizar cavalo:', error);
      
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível atualizar o cavalo. Tente novamente.',
        variant: 'destructive'
      });
    }
  });
}