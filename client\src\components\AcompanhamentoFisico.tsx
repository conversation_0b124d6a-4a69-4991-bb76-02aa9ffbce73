import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { <PERSON>avalo } from "../../../shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { CalendarIcon, ChevronDown, Plus, PlusCircle, Save } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface AcompanhamentoFisicoProps {
  horse_id: number;
  horseName?: string;
}

interface MedidaFisica {
  id?: number;
  horse_id: number;
  data: string;
  peso?: number | null;
  altura?: number | null;
  condicaoCorporal?: number | null;
  observacoes?: string | null;
  user_id: number;
}

/**
 * Componente de acompanhamento físico do cavalo
 * 
 * Exibe gráficos de evolução de peso, altura e condição corporal
 * e permite registrar novas medidas
 */
export function AcompanhamentoFisico({ horse_id, horseName }: AcompanhamentoFisicoProps) {
  const [user] = useState(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch {
        return null;
      }
    }
    return null;
  });

  const [medidasFisicas, setMedidasFisicas] = useState<MedidaFisica[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<MedidaFisica>({
    horse_id,
    data: format(new Date(), 'yyyy-MM-dd'),
    peso: null,
    altura: null,
    condicaoCorporal: null,
    observacoes: "",
    user_id: user?.id || 0
  });

  // Carregar as medidas físicas do cavalo
  useEffect(() => {
    const fetchMedidasFisicas = async () => {
      try {
        setLoading(true);
        const response = await apiRequest<MedidaFisica[]>(`/api/cavalos/${horse_id}/medidas-fisicas`, 'GET');
        
        if (response && Array.isArray(response)) {
          // Ordenar por data
          const sorted = [...response].sort((a, b) => 
            new Date(a.data).getTime() - new Date(b.data).getTime()
          );
          setMedidasFisicas(sorted);
        } else {
          setMedidasFisicas([]);
        }
      } catch (error) {
        console.error("Erro ao buscar medidas físicas:", error);
        toast({
          title: "Erro",
          description: "Não foi possível carregar as medidas físicas.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (horse_id && user) {
      fetchMedidasFisicas();
    }
  }, [horse_id, user]);

  // Dados formatados para os gráficos
  const chartData = medidasFisicas.map(medida => ({
    name: format(new Date(medida.data), 'dd/MM/yyyy'),
    peso: medida.peso || 0,
    altura: medida.altura || 0,
    condicaoCorporal: medida.condicaoCorporal || 0,
    dataCompleta: medida.data
  }));

  // Calcular médias e valores máximos/mínimos
  const estatisticas = {
    peso: {
      media: medidasFisicas.length > 0 
        ? medidasFisicas.reduce((acc, curr) => acc + (curr.peso || 0), 0) / medidasFisicas.filter(m => m.peso).length 
        : 0,
      max: medidasFisicas.length > 0 
        ? Math.max(...medidasFisicas.map(m => m.peso || 0)) 
        : 0,
      min: medidasFisicas.length > 0 
        ? Math.min(...medidasFisicas.filter(m => m.peso).map(m => m.peso || Infinity)) 
        : 0,
      ultimaMedicao: medidasFisicas.length > 0 
        ? medidasFisicas[medidasFisicas.length - 1]?.peso || 0
        : null
    },
    altura: {
      media: medidasFisicas.length > 0 
        ? medidasFisicas.reduce((acc, curr) => acc + (curr.altura || 0), 0) / medidasFisicas.filter(m => m.altura).length 
        : 0,
      max: medidasFisicas.length > 0 
        ? Math.max(...medidasFisicas.map(m => m.altura || 0)) 
        : 0,
      min: medidasFisicas.length > 0 
        ? Math.min(...medidasFisicas.filter(m => m.altura).map(m => m.altura || Infinity)) 
        : 0,
      ultimaMedicao: medidasFisicas.length > 0 
        ? medidasFisicas[medidasFisicas.length - 1]?.altura || 0
        : null
    }
  };

  // Funções para o formulário
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'observacoes' ? value : value === '' ? null : Number(value)
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value === 'null' ? null : Number(value)
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await apiRequest('/api/medidas-fisicas', 'POST', formData);
      
      if (response) {
        toast({
          title: "Sucesso",
          description: "Medida física registrada com sucesso!"
        });
        
        // Adicionar nova medida à lista e ordenar
        const novasMedidas = [...medidasFisicas, response].sort((a, b) => 
          new Date(a.data).getTime() - new Date(b.data).getTime()
        );
        
        setMedidasFisicas(novasMedidas);
        setShowForm(false);
        
        // Resetar o formulário
        setFormData({
          horse_id,
          data: format(new Date(), 'yyyy-MM-dd'),
          peso: null,
          altura: null,
          condicaoCorporal: null,
          observacoes: "",
          user_id: user?.id || 0
        });
      }
    } catch (error) {
      console.error("Erro ao salvar medida física:", error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a medida física.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-700">
            Acompanhamento Físico
            {horseName && <span className="text-blue-600"> - {horseName}</span>}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Acompanhe a evolução física do animal ao longo do tempo
          </p>
        </div>
        
        <Button 
          onClick={() => setShowForm(!showForm)}
          size="sm"
          className="flex items-center gap-1"
        >
          {showForm ? (
            <>
              <ChevronDown className="h-4 w-4" />
              <span>Fechar Formulário</span>
            </>
          ) : (
            <>
              <PlusCircle className="h-4 w-4" />
              <span>Nova Medição</span>
            </>
          )}
        </Button>
      </div>
      
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>Registrar Nova Medição</CardTitle>
            <CardDescription>
              Registre um novo conjunto de medidas físicas para o animal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="data">Data da Medição</Label>
                  <div className="relative">
                    <CalendarIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="data"
                      name="data"
                      type="date"
                      value={formData.data}
                      onChange={handleInputChange}
                      className="pl-9"
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="peso">Peso (kg)</Label>
                  <Input
                    id="peso"
                    name="peso"
                    type="number"
                    step="0.1"
                    min="0"
                    placeholder="Ex: 450.5"
                    value={formData.peso === null ? '' : formData.peso}
                    onChange={handleInputChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="altura">Altura (m)</Label>
                  <Input
                    id="altura"
                    name="altura"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="Ex: 1.65"
                    value={formData.altura === null ? '' : formData.altura}
                    onChange={handleInputChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="condicaoCorporal">Condição Corporal (1-9)</Label>
                  <Select 
                    value={formData.condicaoCorporal?.toString() || ''} 
                    onValueChange={(val) => handleSelectChange('condicaoCorporal', val)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">Não informar</SelectItem>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(valor => (
                        <SelectItem key={valor} value={valor.toString()}>
                          {valor} - {
                            valor <= 3 ? 'Abaixo do peso' : 
                            valor <= 6 ? 'Peso ideal' : 
                            'Acima do peso'
                          }
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <textarea
                  id="observacoes"
                  name="observacoes"
                  rows={3}
                  className="w-full p-2 border rounded-md"
                  placeholder="Observações adicionais sobre a medição..."
                  value={formData.observacoes || ''}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="flex justify-end">
                <Button type="submit" className="flex items-center gap-1">
                  <Save className="h-4 w-4" />
                  Salvar Medição
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
      
      {loading ? (
        <div className="h-64 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : medidasFisicas.length === 0 ? (
        <Card className="bg-gray-50">
          <CardContent className="pt-6">
            <div className="text-center p-6">
              <div className="mx-auto h-12 w-12 text-gray-400 rounded-full bg-gray-100 flex items-center justify-center">
                <Plus className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Nenhuma medida registrada</h3>
              <p className="mt-1 text-sm text-gray-500">
                Adicione medidas físicas para visualizar os gráficos de evolução.
              </p>
              <Button 
                onClick={() => setShowForm(true)}
                className="mt-4"
              >
                Adicionar Primeira Medição
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Estatísticas rápidas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-gray-500">Última Medição</h3>
                  <p className="mt-1 text-3xl font-semibold text-blue-600">
                    {medidasFisicas.length > 0 
                      ? format(new Date(medidasFisicas[medidasFisicas.length - 1]?.data || new Date()), 'dd/MM/yyyy') 
                      : '-'}
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-gray-500">Peso Atual</h3>
                  <p className="mt-1 text-3xl font-semibold text-blue-600">
                    {estatisticas.peso.ultimaMedicao 
                      ? `${estatisticas.peso.ultimaMedicao} kg` 
                      : 'N/D'}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Média: {estatisticas.peso.media.toFixed(1)} kg
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-gray-500">Altura Atual</h3>
                  <p className="mt-1 text-3xl font-semibold text-blue-600">
                    {estatisticas.altura.ultimaMedicao 
                      ? `${estatisticas.altura.ultimaMedicao} m` 
                      : 'N/D'}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Média: {estatisticas.altura.media.toFixed(2)} m
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <h3 className="text-sm font-medium text-gray-500">Total de Medições</h3>
                  <p className="mt-1 text-3xl font-semibold text-blue-600">
                    {medidasFisicas.length}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {medidasFisicas.length > 0 
                      ? `Primeira: ${format(new Date(medidasFisicas[0]?.data || new Date()), 'dd/MM/yyyy')}` 
                      : ''}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Gráficos */}
          <Tabs defaultValue="peso" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="peso">Evolução do Peso</TabsTrigger>
              <TabsTrigger value="altura">Evolução da Altura</TabsTrigger>
              <TabsTrigger value="condicao">Condição Corporal</TabsTrigger>
            </TabsList>
            
            <TabsContent value="peso" className="p-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-center">Evolução do Peso (kg)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip 
                          formatter={(value, name) => [
                            `${Number(value).toFixed(1)} kg`, 
                            'Peso'
                          ]}
                          labelFormatter={(label) => `Data: ${label}`}
                        />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="peso" 
                          name="Peso (kg)" 
                          stroke="#2563eb" 
                          activeDot={{ r: 8 }} 
                          strokeWidth={2}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="altura" className="p-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-center">Evolução da Altura (m)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis domain={['dataMin', 'dataMax']} />
                        <Tooltip 
                          formatter={(value, name) => [
                            `${Number(value).toFixed(2)} m`, 
                            'Altura'
                          ]}
                          labelFormatter={(label) => `Data: ${label}`}
                        />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="altura" 
                          name="Altura (m)" 
                          stroke="#16a34a" 
                          activeDot={{ r: 8 }} 
                          strokeWidth={2}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="condicao" className="p-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-center">Evolução da Condição Corporal (1-9)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={chartData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis domain={[0, 9]} ticks={[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]} />
                        <Tooltip 
                          formatter={(value, name) => [
                            `${Number(value)}`, 
                            'Escore'
                          ]}
                          labelFormatter={(label) => `Data: ${label}`}
                        />
                        <Legend />
                        <Bar 
                          dataKey="condicaoCorporal" 
                          name="Condição Corporal (1-9)" 
                          fill="#f59e0b"
                          barSize={30}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          {/* Tabela de medições */}
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Medições</CardTitle>
              <CardDescription>
                Lista de todas as medições físicas registradas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-700">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      <th className="px-6 py-3">Data</th>
                      <th className="px-6 py-3">Peso (kg)</th>
                      <th className="px-6 py-3">Altura (m)</th>
                      <th className="px-6 py-3">Condição Corporal</th>
                      <th className="px-6 py-3">Observações</th>
                    </tr>
                  </thead>
                  <tbody>
                    {medidasFisicas.slice().reverse().map((medida, index) => (
                      <tr key={index} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-6 py-3">
                          {format(new Date(medida.data), 'dd/MM/yyyy')}
                        </td>
                        <td className="px-6 py-3">
                          {medida.peso ? `${medida.peso} kg` : '-'}
                        </td>
                        <td className="px-6 py-3">
                          {medida.altura ? `${medida.altura} m` : '-'}
                        </td>
                        <td className="px-6 py-3">
                          {medida.condicaoCorporal ? (
                            <div className="flex items-center">
                              <span>{medida.condicaoCorporal}</span>
                              <span className="ml-2 text-xs text-gray-500">
                                ({medida.condicaoCorporal <= 3 ? 'Abaixo do peso' : 
                                 medida.condicaoCorporal <= 6 ? 'Peso ideal' : 
                                 'Acima do peso'})
                              </span>
                            </div>
                          ) : '-'}
                        </td>
                        <td className="px-6 py-3">
                          {medida.observacoes || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}