/**
 * Página de Importação de Certificados de Registro Genealógico da ABCCC
 * 
 * Esta página permite aos usuários importar PDFs de certificados
 * da ABCCC para extrair automaticamente a genealogia de cavalos Crioulos.
 */

import React, { useState } from 'react';
import { ImportadorPdfABCCC } from '@/components/ImportadorPdfABCCC';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { FileText, HelpCircle, History } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export default function ImportacaoPdfABCCCPage() {
  const [activeTab, setActiveTab] = useState('importar');

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Importação de Certificados ABCCC</h1>
        <p className="text-gray-600">
          Importe automaticamente dados de cavalos e sua genealogia a partir dos certificados oficiais da ABCCC.
        </p>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="importar" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Importar PDF</span>
          </TabsTrigger>
          <TabsTrigger value="ajuda" className="flex items-center gap-2">
            <HelpCircle className="h-4 w-4" />
            <span>Como Funciona</span>
          </TabsTrigger>
          <TabsTrigger value="historico" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            <span>Histórico</span>
          </TabsTrigger>
        </TabsList>
        
        {/* Aba de importação */}
        <TabsContent value="importar" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Upload de Certificado</CardTitle>
              <CardDescription>
                Carregue certificados de registro genealógico em formato PDF emitidos pela ABCCC.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImportadorPdfABCCC />
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Aba de informação/ajuda */}
        <TabsContent value="ajuda" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Como Funciona a Importação</CardTitle>
              <CardDescription>
                Explicação passo a passo do processo de importação automática.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">1. Seleção do Arquivo</h3>
                <p className="text-gray-600">
                  Selecione um arquivo PDF do certificado de registro genealógico oficial emitido pela ABCCC.
                  O documento deve estar legível e ser um certificado válido.
                </p>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">2. Processamento Automático</h3>
                <p className="text-gray-600">
                  O sistema processará o PDF e extrairá automaticamente:
                </p>
                <ul className="list-disc pl-5 text-gray-600 space-y-1">
                  <li>Dados do cavalo principal (nome, registro, sexo, nascimento, pelagem)</li>
                  <li>Informações de pais, avós e outros ancestrais presentes no documento</li>
                  <li>Dados de criador, proprietário e origem</li>
                  <li>Títulos e outras anotações relevantes</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">3. Validação e Confirmação</h3>
                <p className="text-gray-600">
                  Após o processamento, você receberá um resumo dos dados extraídos para confirmação.
                  Poderá visualizar quais animais serão:
                </p>
                <ul className="list-disc pl-5 text-gray-600 space-y-1">
                  <li>Adicionados como novos registros no sistema</li>
                  <li>Atualizados, caso já existam no sistema</li>
                  <li>Conectados através de relações genealógicas</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">4. Finalização</h3>
                <p className="text-gray-600">
                  Após confirmar, o sistema registrará todos os dados no banco e criará 
                  automaticamente as relações genealógicas. Você poderá acessar os 
                  registros dos cavalos e visualizar a árvore genealógica completa.
                </p>
              </div>
              
              <Alert className="mt-6">
                <HelpCircle className="h-4 w-4" />
                <AlertTitle>Dica</AlertTitle>
                <AlertDescription>
                  Para uma extração mais precisa, certifique-se que o PDF é uma versão digital oficial,
                  e não uma digitalização de baixa qualidade do documento físico.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Aba de histórico */}
        <TabsContent value="historico" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Importações</CardTitle>
              <CardDescription>
                Certificados importados recentemente no sistema.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 italic text-center py-8">
                O histórico de importações estará disponível após a primeira importação.
              </p>
              {/* O histórico seria carregado através de uma API que listaría as importações recentes */}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}