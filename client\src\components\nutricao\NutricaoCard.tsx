import React from 'react';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2 } from 'lucide-react';
import { Nutricao } from '@/hooks/use-nutricao';

interface NutricaoCardProps {
  nutricao: Nutricao;
  onEdit: (nutricao: Nutricao) => void;
  onDelete: (id: number) => void;
  calculateMonthlyCost: (quantidade: number, frequencia: number, custoUnitario: number) => string;
}

/**
 * Componente de cartão para exibir um registro de nutrição
 * 
 * Este componente pode ser reutilizado em diferentes partes do sistema
 * onde for necessário exibir informações de um registro de nutrição.
 */
export const NutricaoCard: React.FC<NutricaoCardProps> = ({
  nutricao,
  onEdit,
  onDelete,
  calculateMonthlyCost,
}) => {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{nutricao.nomeAlimento}</CardTitle>
            <CardDescription>
              <Badge variant="outline" className="mt-1">
                {nutricao.tipoAlimentacao}
              </Badge>
            </CardDescription>
          </div>
          <div className="flex space-x-1">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => onEdit(nutricao)}
              title="Editar"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => {
                if (window.confirm("Tem certeza que deseja excluir este registro de nutrição?")) {
                  onDelete(nutricao.id);
                }
              }}
              title="Excluir"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Quantidade:</span>
            <span className="font-medium">
              {nutricao.quantidade} {nutricao.unidadeMedida}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Frequência:</span>
            <span className="font-medium">
              {nutricao.frequenciaDiaria}x por dia
            </span>
          </div>
          
          {nutricao.horarios && (
            <div className="flex justify-between">
              <span className="text-gray-500">Horários:</span>
              <span className="font-medium">{nutricao.horarios}</span>
            </div>
          )}
          
          <div className="flex justify-between">
            <span className="text-gray-500">Custo por unidade:</span>
            <span className="font-medium">
              R$ {nutricao.custoUnitario.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Custo mensal:</span>
            <span className="font-medium text-green-600">
              R$ {calculateMonthlyCost(
                nutricao.quantidade, 
                nutricao.frequenciaDiaria, 
                nutricao.custoUnitario
              )}
            </span>
          </div>
          
          {nutricao.fornecedor && (
            <div className="flex justify-between">
              <span className="text-gray-500">Fornecedor:</span>
              <span className="font-medium">{nutricao.fornecedor}</span>
            </div>
          )}
        </div>
      </CardContent>
      
      {(nutricao.observacoes || nutricao.recomendacao) && (
        <CardFooter className="flex flex-col items-start border-t pt-4 pb-2">
          {nutricao.observacoes && (
            <div className="w-full mb-2">
              <h5 className="text-xs font-medium text-gray-500">Observações:</h5>
              <p className="text-xs mt-1">{nutricao.observacoes}</p>
            </div>
          )}
          
          {nutricao.recomendacao && (
            <div className="w-full">
              <h5 className="text-xs font-medium text-gray-500">Recomendações:</h5>
              <p className="text-xs mt-1">{nutricao.recomendacao}</p>
            </div>
          )}
        </CardFooter>
      )}
    </Card>
  );
};