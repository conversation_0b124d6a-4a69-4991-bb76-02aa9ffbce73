import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';
import { Cavalo } from '@shared/schema';

interface FilterControlsProps {
  title: string;
  description: string;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filterCavalo: string;
  setFilterCavalo: (value: string) => void;
  cavalos: Cavalo[];
  filterTipo?: string;
  setFilterTipo?: (value: string) => void;
  tiposOptions?: Array<{ value: string; label: string }>;
  filterStatus?: string;
  setFilterStatus?: (value: string) => void;
  statusOptions?: Array<{ value: string; label: string }>;
  additionalFilters?: React.ReactNode;
}

/**
 * Componente de controles de filtro para procedimentos veterinários
 * 
 * Este componente fornece uma interface unificada para filtrar procedimentos
 * com opções de busca, filtro por cavalo, tipo e status, além de suportar
 * filtros personalizados adicionais.
 */
export const FilterControls: React.FC<FilterControlsProps> = ({
  title,
  description,
  searchTerm,
  setSearchTerm,
  filterCavalo,
  setFilterCavalo,
  cavalos,
  filterTipo,
  setFilterTipo,
  tiposOptions,
  filterStatus,
  setFilterStatus,
  statusOptions,
  additionalFilters
}) => {
  // Determinar número de colunas com base nos filtros disponíveis
  const getGridCols = () => {
    let numCols = 1; // Busca sempre presente
    if (filterCavalo !== undefined) numCols++;
    if (filterTipo !== undefined) numCols++;
    if (filterStatus !== undefined) numCols++;
    if (additionalFilters) numCols++;
    
    // Limitar a 4 colunas máximo para evitar elementos muito pequenos
    return Math.min(numCols, 4);
  };
  
  const gridClass = `grid-cols-1 md:grid-cols-${getGridCols()}`;
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className={`grid ${gridClass} gap-4`}>
          <div className="space-y-2">
            <label className="text-sm font-medium">Buscar</label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          {filterCavalo !== undefined && setFilterCavalo && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Animal</label>
              <Select value={filterCavalo} onValueChange={setFilterCavalo}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um animal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {(Array.isArray(cavalos) ? cavalos : []).map((cavalo) => (
                    <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                      {cavalo.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {filterTipo !== undefined && setFilterTipo && tiposOptions && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Tipo</label>
              <Select value={filterTipo} onValueChange={setFilterTipo}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {(Array.isArray(tiposOptions) ? tiposOptions : []).map((tipo) => (
                    <SelectItem key={tipo.value} value={tipo.value || `tipo_${Date.now()}`}>
                      {tipo.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {filterStatus !== undefined && setFilterStatus && statusOptions && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  {(Array.isArray(statusOptions) ? statusOptions : []).map((status) => (
                    <SelectItem key={status.value} value={status.value || `status_${Date.now()}`}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {additionalFilters && additionalFilters}
        </div>
      </CardContent>
    </Card>
  );
};