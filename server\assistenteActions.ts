import { storage } from './storage';
import { format, parseISO, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Função auxiliar para converter nomes de meses em números
const obterNumeroMes = (nomeMes: string): number => {
  const meses: {[key: string]: number} = {
    'janeiro': 0,
    'fevereiro': 1,
    'março': 2, 'marco': 2,
    'abril': 3,
    'maio': 4,
    'junho': 5,
    'julho': 6,
    'agosto': 7,
    'setembro': 8, 'setembros': 8,
    'outubro': 9,
    'novembro': 10,
    'dezembro': 11,
    // Abreviações comuns
    'jan': 0,
    'fev': 1,
    'mar': 2,
    'abr': 3,
    'mai': 4,
    'jun': 5,
    'jul': 6,
    'ago': 7,
    'set': 8,
    'out': 9,
    'nov': 10,
    'dez': 11
  };
  
  return meses[nomeMes] !== undefined ? meses[nomeMes] : -1;
};

// Funções auxiliares para processamento de datas
const processarData = (texto: string): string => {
  console.log("Processando data:", texto);

  // Verificar se a string é vazia ou undefined
  if (!texto) {
    console.warn("Data vazia recebida");
    return format(new Date(), 'yyyy-MM-dd'); // Data padrão para hoje
  }
  
  // Se já for uma data no formato ISO (yyyy-MM-dd), retornar diretamente
  if (/^\d{4}-\d{2}-\d{2}$/.test(texto)) {
    console.log("Data já está no formato ISO:", texto);
    return texto;
  }
  
  // Processar termos relativos como "hoje" e "amanhã"
  const hoje = new Date();
  
  if (texto.toLowerCase() === 'hoje') {
    return format(hoje, 'yyyy-MM-dd');
  }
  
  if (texto.toLowerCase() === 'amanhã' || texto.toLowerCase() === 'amanha') {
    return format(addDays(hoje, 1), 'yyyy-MM-dd');
  }
  
  // Tentar formatos comuns de data (dia/mês/ano, dia-mês-ano)
  const formatosData = [
    // dd/mm/yyyy ou dd/mm/yy
    {
      regex: /^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/,
      parse: (matches: RegExpMatchArray) => {
        const dia = parseInt(matches[1], 10);
        const mes = parseInt(matches[2], 10) - 1; // Meses em JS são 0-indexed
        let ano = parseInt(matches[3], 10);
        
        // Corrigir anos abreviados (22 -> 2022)
        if (ano < 100) {
          ano += 2000;
        }
        
        return new Date(ano, mes, dia);
      }
    },
    // dd-mm-yyyy ou dd-mm-yy
    {
      regex: /^(\d{1,2})-(\d{1,2})-(\d{2,4})$/,
      parse: (matches: RegExpMatchArray) => {
        const dia = parseInt(matches[1], 10);
        const mes = parseInt(matches[2], 10) - 1;
        let ano = parseInt(matches[3], 10);
        
        if (ano < 100) {
          ano += 2000;
        }
        
        return new Date(ano, mes, dia);
      }
    },
    // yyyy/mm/dd
    {
      regex: /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
      parse: (matches: RegExpMatchArray) => {
        const ano = parseInt(matches[1], 10);
        const mes = parseInt(matches[2], 10) - 1;
        const dia = parseInt(matches[3], 10);
        return new Date(ano, mes, dia);
      }
    },
    // yyyy-mm-dd (já tratado antes, mas por segurança)
    {
      regex: /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
      parse: (matches: RegExpMatchArray) => {
        const ano = parseInt(matches[1], 10);
        const mes = parseInt(matches[2], 10) - 1;
        const dia = parseInt(matches[3], 10);
        return new Date(ano, mes, dia);
      }
    },
    // dd.mm.yyyy
    {
      regex: /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
      parse: (matches: RegExpMatchArray) => {
        const dia = parseInt(matches[1], 10);
        const mes = parseInt(matches[2], 10) - 1;
        const ano = parseInt(matches[3], 10);
        return new Date(ano, mes, dia);
      }
    }
  ];
  
  // Tentar cada formato de data
  for (const formato of formatosData) {
    const matches = texto.match(formato.regex);
    if (matches) {
      try {
        const data = formato.parse(matches);
        // Verificar se a data é válida
        if (!isNaN(data.getTime())) {
          console.log("Data interpretada como:", data);
          return format(data, 'yyyy-MM-dd');
        }
      } catch (e) {
        console.error("Erro ao parsear data:", e);
      }
    }
  }
  
  // Processar nomes dos dias da semana
  const diasSemana = [
    { nome: 'segunda', dias: 1 },
    { nome: 'terça', dias: 2 },
    { nome: 'quarta', dias: 3 },
    { nome: 'quinta', dias: 4 },
    { nome: 'sexta', dias: 5 },
    { nome: 'sábado', dias: 6 },
    { nome: 'domingo', dias: 0 }
  ];
  
  for (const dia of diasSemana) {
    if (texto.toLowerCase().includes(dia.nome)) {
      // Calcular a próxima ocorrência desse dia da semana
      const diaSemanaAtual = hoje.getDay(); // 0 = domingo, 1 = segunda...
      let diasParaAdicionar = dia.dias - diaSemanaAtual;
      
      if (diasParaAdicionar <= 0) {
        diasParaAdicionar += 7; // Adicionar uma semana para pegar o próximo
      }
      
      return format(addDays(hoje, diasParaAdicionar), 'yyyy-MM-dd');
    }
  }
  
  // Processar datas por extenso (ex: "10 de abril")
  const padroesData = [
    // 10 de abril, 10 de abril de 2025
    /(\d{1,2})\s+de\s+(\w+)(?:\s+de\s+(\d{4}))?/i,
    // abril de 2025
    /(\w+)\s+de\s+(\d{4})/i,
    // 24 dezembro, 24 dezembro 2025
    /(\d{1,2})\s+(\w+)(?:\s+(\d{4}))?/i
  ];
  
  for (const padrao of padroesData) {
    const match = texto.match(padrao);
    if (match) {
      let dia, mes, ano;
      
      // Primeira forma: dia de mês (de ano)
      if (match[1] && match[2]) {
        if (/^\d+$/.test(match[1])) {
          // O primeiro grupo é um número (dia)
          dia = parseInt(match[1], 10);
          const nomeMes = match[2].toLowerCase();
          mes = obterNumeroMes(nomeMes);
          ano = match[3] ? parseInt(match[3], 10) : hoje.getFullYear();
        } else {
          // O primeiro grupo é o nome do mês
          const nomeMes = match[1].toLowerCase();
          mes = obterNumeroMes(nomeMes);
          dia = 1; // Primeiro dia do mês
          ano = parseInt(match[2], 10);
        }
        
        // Verificar se temos um mês válido
        if (mes >= 0) {
          // Criar a data
          const data = new Date(ano, mes, dia);
          return format(data, 'yyyy-MM-dd');
        }
      }
    }
  }
  
  // Se chegou aqui, a data não pôde ser interpretada  
  // Tenta interpretar como data no formato brasileiro (DD/MM/YYYY) - mantido para compatibilidade
  if (/\d{1,2}\/\d{1,2}(\/\d{2,4})?/.test(texto)) {
    const partes = texto.split('/');
    
    let dia = parseInt(partes[0], 10);
    let mes = parseInt(partes[1], 10) - 1; // Meses em JS são 0-indexados
    
    let ano = hoje.getFullYear();
    if (partes.length > 2) {
      ano = parseInt(partes[2], 10);
      // Se for um ano de 2 dígitos, converter para 4 dígitos
      if (ano < 100) {
        ano += 2000;
      }
    }
    
    const data = new Date(ano, mes, dia);
    if (!isNaN(data.getTime())) {
      return format(data, 'yyyy-MM-dd');
    }
  }
  
  console.warn("Não foi possível interpretar a data:", texto);
  
  // Retornar data padrão para evitar erros (hoje)
  return format(hoje, 'yyyy-MM-dd');
};

// Definir as ações para cada intenção
const executarCadastrarCavalo = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    const novoCavalo = {
      name: args.nome,
      breed: args.raca || "Não especificada",
      user_id
    };
    
    const resultado = await storage.createCavalo(novoCavalo);
    return {
      success: true,
      message: `Cavalo ${args.nome} cadastrado com sucesso!`,
      data: resultado
    };
  } catch (error) {
    console.error("Erro ao cadastrar cavalo:", error);
    return {
      success: false,
      message: "Não foi possível cadastrar o cavalo.",
      error: error.message
    };
  }
};

const executarRegistrarPeso = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome} não encontrado.`
      };
    }
    
    // Atualizar o peso do cavalo
    const peso = parseInt(args.peso, 10);
    
    if (isNaN(peso)) {
      return {
        success: false,
        message: `Peso inválido: ${args.peso}`
      };
    }
    
    const resultado = await storage.updateCavalo(cavalo.id, user_id, { peso });
    
    // Registrar também como medida física para histórico
    await storage.createMedidaFisica({
      tipo: "peso",
      valor: peso,
      data: format(new Date(), 'yyyy-MM-dd'),
      horse_id: cavalo.id,
      user_id
    });
    
    return {
      success: true,
      message: `Peso do cavalo ${cavalo.name} atualizado para ${peso}kg!`,
      data: resultado
    };
  } catch (error) {
    console.error("Erro ao registrar peso:", error);
    return {
      success: false,
      message: "Não foi possível registrar o peso.",
      error: error.message
    };
  }
};

/**
 * Executa o agendamento de uma vacinação
 * @param args Argumentos para o agendamento (nome do cavalo, data, tipo de vacina)
 * @returns Objeto com sucesso, mensagem e dados
 */
const executarRegistrarVacina = async (args: any) => {
  try {
    console.log("Executando registro de vacina com args:", JSON.stringify(args));
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o nome do cavalo
    if (!args.nome) {
      return {
        success: false,
        message: "Por favor, informe o nome do cavalo para agendar a vacinação."
      };
    }
    
    // Verificar se temos a data
    if (!args.data) {
      return {
        success: false,
        message: "Por favor, informe a data para o agendamento da vacinação (ex: 10/04/2025)."
      };
    }
    
    // Verificar o tipo de vacina
    if (!args.tipo_vacina) {
      return {
        success: false,
        message: "Por favor, informe o tipo de vacina (ex: tétano, influenza, raiva)."
      };
    }
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data);
    console.log("Data processada:", data);
    
    // Definir horários padrão se não fornecidos
    const horaInicio = args.hora || "09:00";
    const horaFim = args.hora_fim || "10:00";
    
    console.log("Hora início:", horaInicio);
    console.log("Hora fim:", horaFim);
    
    // Criar o manejo de vacinação
    const novoManejo = {
      tipo: "Vacinação",
      data,
      status: "Pendente", // Alterado de "Agendado" para "Pendente" para consistência
      horse_id: cavalo.id,
      user_id,
      detalhes: `Vacina contra ${args.tipo_vacina}`
    };
    
    const manejo = await storage.createManejo(novoManejo);
    console.log("Manejo criado:", manejo);
    
    // Criar um evento na agenda
    const novoEvento = {
      titulo: `Vacinação: ${cavalo.name}`,
      descricao: `Vacina contra ${args.tipo_vacina}`,
      data,
      horaInicio, // Nome correto do campo conforme schema
      horaFim, // Campo obrigatório conforme schema
      tipo: "Vacinação", // Campo obrigatório conforme schema
      prioridade: "Alta",
      status: "Pendente",
      horse_id: cavalo.id,
      user_id,
      manejoId: manejo.id
    };
    
    console.log("Criando evento:", novoEvento);
    const evento = await storage.createEvento(novoEvento);
    console.log("Evento criado:", evento);
    
    // Formato de data mais legível
    const dataFormatada = format(parseISO(data), 'dd/MM/yyyy', { locale: ptBR });
    
    return {
      success: true,
      message: `✅ Vacinação contra ${args.tipo_vacina} para ${cavalo.name} agendada com sucesso para ${dataFormatada}!
      
O manejo foi registrado no sistema e aparecerá na lista de manejos pendentes. Um lembrete também foi adicionado à sua agenda.`,
      data: { manejo, evento }
    };
  } catch (error: any) {
    console.error("Erro ao registrar vacina:", error);
    return {
      success: false,
      message: "Não foi possível registrar a vacinação. Por favor, verifique os dados e tente novamente.",
      error: error.message
    };
  }
};

const executarAgendarEvento = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data || 'hoje');
    
    // Criar um evento na agenda
    const novoEvento = {
      titulo: args.tipo_evento || "Evento",
      descricao: args.detalhes || `Evento para ${cavalo.name}`,
      data,
      horaInicio: args.hora || "10:00", // Nome correto do campo conforme schema
      horaFim: args.hora_fim || "11:00", // Campo obrigatório conforme schema
      tipo: "Evento", // Campo obrigatório conforme schema
      prioridade: "Média",
      status: "Pendente",
      horse_id: cavalo.id,
      user_id
    };
    
    const evento = await storage.createEvento(novoEvento);
    
    return {
      success: true,
      message: `Evento ${novoEvento.titulo} para ${cavalo.name} agendado para ${format(parseISO(data), 'dd/MM/yyyy', { locale: ptBR })}!`,
      data: evento
    };
  } catch (error) {
    console.error("Erro ao agendar evento:", error);
    return {
      success: false,
      message: "Não foi possível agendar o evento.",
      error: error.message
    };
  }
};

const executarConsultarAgenda = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Processar período solicitado
    let dataFiltro: string | null = null;
    let mensagem = "Agenda completa";
    
    if (args.periodo) {
      if (args.periodo.toLowerCase().includes('hoje')) {
        dataFiltro = format(new Date(), 'yyyy-MM-dd');
        mensagem = "Agenda para hoje";
      } else if (args.periodo.toLowerCase().includes('amanhã')) {
        dataFiltro = format(addDays(new Date(), 1), 'yyyy-MM-dd');
        mensagem = "Agenda para amanhã";
      } else if (args.periodo.toLowerCase().includes('semana')) {
        // Para simplificar, vamos retornar todos (sem filtro)
        mensagem = "Agenda da semana";
      }
    }
    
    // Buscar eventos
    let eventos;
    if (dataFiltro) {
      eventos = await storage.getEventosByDate(dataFiltro, user_id);
    } else {
      eventos = await storage.getEventos(user_id);
    }
    
    return {
      success: true,
      message: mensagem,
      data: eventos
    };
  } catch (error) {
    console.error("Erro ao consultar agenda:", error);
    return {
      success: false,
      message: "Não foi possível consultar a agenda.",
      error: error.message
    };
  }
};

// Função para registrar treinamento
const executarRegistrarTreino = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo?.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome_cavalo || 'informado'} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data || 'hoje');
    
    // Criar o manejo de treinamento
    const novoManejo = {
      tipo: "Treinamento",
      data,
      status: "Agendado",
      horse_id: cavalo.id,
      user_id,
      detalhes: args.tipo_acao || args.detalhes || "Treinamento regular",
      observacoes: args.observacoes || ""
    };
    
    const manejo = await storage.createManejo(novoManejo);
    
    return {
      success: true,
      message: `Treinamento para ${cavalo.name} registrado para ${format(parseISO(data), 'dd/MM/yyyy', { locale: ptBR })}!`,
      data: manejo
    };
  } catch (error) {
    console.error("Erro ao registrar treinamento:", error);
    return {
      success: false,
      message: "Não foi possível registrar o treinamento.",
      error: error.message
    };
  }
};

// Função para registrar alimentação
const executarRegistrarAlimentacao = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo?.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome_cavalo || 'informado'} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data || 'hoje');
    
    // Criar o manejo de alimentação
    const novoManejo = {
      tipo: "Alimentação",
      data,
      status: "Realizado",
      horse_id: cavalo.id,
      user_id,
      detalhes: args.tipo_alimento || args.detalhes || "Alimentação regular",
      observacoes: args.observacoes || ""
    };
    
    const manejo = await storage.createManejo(novoManejo);
    
    return {
      success: true,
      message: `Alimentação para ${cavalo.name} registrada com sucesso!`,
      data: manejo
    };
  } catch (error) {
    console.error("Erro ao registrar alimentação:", error);
    return {
      success: false,
      message: "Não foi possível registrar a alimentação.",
      error: error.message
    };
  }
};

// Função para registrar ferrageamento
const executarRegistrarFerrageamento = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo?.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome_cavalo || 'informado'} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data || 'hoje');
    
    // Criar o manejo de ferrageamento
    const novoManejo = {
      tipo: "Ferrageamento",
      data,
      status: args.data ? "Agendado" : "Realizado",
      horse_id: cavalo.id,
      user_id,
      detalhes: args.tipo_acao || "Ferrageamento completo",
      observacoes: args.observacoes || ""
    };
    
    const manejo = await storage.createManejo(novoManejo);
    
    return {
      success: true,
      message: args.data
        ? `Ferrageamento para ${cavalo.name} agendado para ${format(parseISO(data), 'dd/MM/yyyy', { locale: ptBR })}!`
        : `Ferrageamento para ${cavalo.name} registrado com sucesso!`,
      data: manejo
    };
  } catch (error) {
    console.error("Erro ao registrar ferrageamento:", error);
    return {
      success: false,
      message: "Não foi possível registrar o ferrageamento.",
      error: error.message
    };
  }
};

// Função para agendar consulta veterinária
const executarAgendarVeterinario = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo?.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome_cavalo || 'informado'} não encontrado.`
      };
    }
    
    // Processar a data fornecida
    const data = processarData(args.data || 'hoje');
    
    // Criar procedimento veterinário
    const novoProcedimento = {
      tipo: args.tipo_acao || "Consulta de rotina",
      data,
      status: "Agendado",
      horse_id: cavalo.id,
      user_id,
      veterinario: args.veterinario || "",
      observacoes: args.observacoes || "",
      descricao: args.observacoes || "Consulta veterinária" // Campo obrigatório
    };
    
    const procedimento = await storage.createProcedimentoVet(novoProcedimento);
    
    // Criar um evento na agenda
    const novoEvento = {
      titulo: `Veterinário: ${cavalo.name}`,
      descricao: args.tipo_acao || "Consulta veterinária",
      data,
      horaInicio: args.hora || "14:00", // Nome correto do campo conforme schema
      horaFim: args.hora_fim || "15:00", // Campo obrigatório conforme schema
      tipo: "Veterinário", // Campo obrigatório conforme schema
      prioridade: "Alta",
      status: "Pendente",
      horse_id: cavalo.id,
      user_id
    };
    
    const evento = await storage.createEvento(novoEvento);
    
    return {
      success: true,
      message: `Consulta veterinária para ${cavalo.name} agendada para ${format(parseISO(data), 'dd/MM/yyyy', { locale: ptBR })}!`,
      data: { procedimento, evento }
    };
  } catch (error) {
    console.error("Erro ao agendar veterinário:", error);
    return {
      success: false,
      message: "Não foi possível agendar a consulta veterinária.",
      error: error.message
    };
  }
};

// Função para consultar dados de um cavalo
const executarConsultarDados = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Se não foi especificado um cavalo, retorna erro
    if (!args.nome_cavalo) {
      return {
        success: false,
        message: "É necessário informar o nome do cavalo para consultar seus dados."
      };
    }
    
    // Buscar o cavalo pelo nome
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
    
    if (!cavalo) {
      return {
        success: false,
        message: `Cavalo ${args.nome_cavalo} não encontrado.`
      };
    }
    
    // Buscar manejos do cavalo
    const manejos = await storage.getManejosByHorse(cavalo.id, user_id);
    
    // Buscar medidas físicas
    const medidasFisicas = await storage.getMedidasFisicasByHorse(cavalo.id, user_id);
    
    // Estruturar resposta com dados principais
    return {
      success: true,
      message: `Dados do cavalo ${cavalo.name} recuperados com sucesso.`,
      data: {
        cavalo,
        manejos: manejos.slice(0, 5), // Limitar a 5 manejos mais recentes
        medidasFisicas: medidasFisicas.slice(0, 5) // Limitar a 5 medidas mais recentes
      }
    };
  } catch (error) {
    console.error("Erro ao consultar dados do cavalo:", error);
    return {
      success: false,
      message: "Não foi possível consultar os dados do cavalo.",
      error: error.message
    };
  }
};

// Mapear as intenções para as funções executoras
// Funções para genética e morfologia
const executarConsultarMorfologia = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o ID do cavalo ou o nome
    if (!args.nome_cavalo && !args.horse_id) {
      return {
        success: false,
        message: "Por favor, informe o nome ou ID do cavalo para consultar a morfologia."
      };
    }
    
    // Obter ID do cavalo se temos apenas o nome
    let horse_id = args.horse_id;
    if (!horse_id && args.nome_cavalo) {
      const cavalos = await storage.getCavalos(user_id);
      const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
      
      if (!cavalo) {
        return {
          success: false,
          message: `Cavalo ${args.nome_cavalo} não encontrado.`
        };
      }
      
      horse_id = cavalo.id;
    }
    
    // Buscar avaliações morfológicas do cavalo
    const morfologias = await storage.getMorfologiasByHorse(horse_id, user_id);
    
    if (morfologias.length === 0) {
      return {
        success: true,
        message: "Não há avaliações morfológicas registradas para este cavalo.",
        data: []
      };
    }
    
    return {
      success: true,
      message: `Foram encontradas ${morfologias.length} avaliações morfológicas para este cavalo.`,
      data: morfologias
    };
  } catch (error: any) {
    console.error("Erro ao consultar morfologia:", error);
    return {
      success: false,
      message: "Não foi possível consultar as avaliações morfológicas.",
      error: error.message
    };
  }
};

const executarConsultarGenealogia = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o ID do cavalo ou o nome
    if (!args.nome_cavalo && !args.horse_id) {
      return {
        success: false,
        message: "Por favor, informe o nome ou ID do cavalo para consultar a genealogia."
      };
    }
    
    // Obter ID do cavalo se temos apenas o nome
    let horse_id = args.horse_id;
    if (!horse_id && args.nome_cavalo) {
      const cavalos = await storage.getCavalos(user_id);
      const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
      
      if (!cavalo) {
        return {
          success: false,
          message: `Cavalo ${args.nome_cavalo} não encontrado.`
        };
      }
      
      horse_id = cavalo.id;
    }
    
    // Buscar genealogia do cavalo
    const genealogia = await storage.getGenealogiaByHorse(horse_id, user_id);
    
    if (!genealogia.cavalo) {
      return {
        success: false,
        message: "Não foi possível encontrar a genealogia deste cavalo.",
        data: null
      };
    }
    
    // Montar resposta detalhada
    let resposta = `Genealogia de ${genealogia.cavalo.name}:\n`;
    
    if (genealogia.pai) {
      resposta += `\nPai: ${genealogia.pai.name}`;
      
      if (genealogia.avo_paterno) {
        resposta += `\n- Avô paterno: ${genealogia.avo_paterno.name}`;
      }
      
      if (genealogia.avo_paterna) {
        resposta += `\n- Avó paterna: ${genealogia.avo_paterna.name}`;
      }
    } else {
      resposta += "\nPai: Desconhecido";
    }
    
    if (genealogia.mae) {
      resposta += `\n\nMãe: ${genealogia.mae.name}`;
      
      if (genealogia.avo_materno) {
        resposta += `\n- Avô materno: ${genealogia.avo_materno.name}`;
      }
      
      if (genealogia.avo_materna) {
        resposta += `\n- Avó materna: ${genealogia.avo_materna.name}`;
      }
    } else {
      resposta += "\n\nMãe: Desconhecida";
    }
    
    if (genealogia.consanguinidade !== null) {
      resposta += `\n\nConsanguinidade: ${genealogia.consanguinidade.toFixed(2)}%`;
    }
    
    resposta += `\nBisavós conhecidos: ${genealogia.bisavosConhecidos} de 8 possíveis`;
    
    return {
      success: true,
      message: resposta,
      data: genealogia
    };
  } catch (error: any) {
    console.error("Erro ao consultar genealogia:", error);
    return {
      success: false,
      message: "Não foi possível consultar a genealogia.",
      error: error.message
    };
  }
};

const executarConsultarDesempenho = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o ID do cavalo ou o nome
    if (!args.nome_cavalo && !args.horse_id) {
      return {
        success: false,
        message: "Por favor, informe o nome ou ID do cavalo para consultar o desempenho."
      };
    }
    
    // Obter ID do cavalo se temos apenas o nome
    let horse_id = args.horse_id;
    if (!horse_id && args.nome_cavalo) {
      const cavalos = await storage.getCavalos(user_id);
      const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
      
      if (!cavalo) {
        return {
          success: false,
          message: `Cavalo ${args.nome_cavalo} não encontrado.`
        };
      }
      
      horse_id = cavalo.id;
    }
    
    // Buscar registros de desempenho do cavalo
    const desempenhos = await storage.getDesempenhosByHorse(horse_id, user_id);
    
    if (desempenhos.length === 0) {
      return {
        success: true,
        message: "Não há registros de desempenho para este cavalo.",
        data: []
      };
    }
    
    // Ordenar por data (mais recentes primeiro)
    desempenhos.sort((a, b) => new Date(b.dataEvento).getTime() - new Date(a.dataEvento).getTime());
    
    // Montar resposta detalhada
    let resposta = `Histórico de desempenho (${desempenhos.length} eventos):\n`;
    
    desempenhos.forEach((d, index) => {
      if (index < 5) { // Limitar para os 5 mais recentes
        const data = new Date(d.dataEvento);
        const dataFormatada = `${data.getDate().toString().padStart(2, '0')}/${(data.getMonth() + 1).toString().padStart(2, '0')}/${data.getFullYear()}`;
        
        resposta += `\n• ${dataFormatada} - ${d.nomeEvento} (${d.tipoEvento})`;
        
        if (d.categoria) {
          resposta += `, categoria: ${d.categoria}`;
        }
        
        if (d.posicao) {
          resposta += `, posição: ${d.posicao}º lugar`;
        }
        
        if (d.tempoOuPontuacao) {
          resposta += `, resultado: ${d.tempoOuPontuacao}`;
        }
      }
    });
    
    if (desempenhos.length > 5) {
      resposta += `\n\n... e mais ${desempenhos.length - 5} eventos anteriores`;
    }
    
    return {
      success: true,
      message: resposta,
      data: desempenhos
    };
  } catch (error: any) {
    console.error("Erro ao consultar desempenho:", error);
    return {
      success: false,
      message: "Não foi possível consultar o histórico de desempenho.",
      error: error.message
    };
  }
};

const executarSugerirCruzamento = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o ID do cavalo ou o nome
    if (!args.nome_cavalo && !args.horse_id) {
      return {
        success: false,
        message: "Por favor, informe o nome ou ID do cavalo para sugestões de cruzamento."
      };
    }
    
    // Obter ID do cavalo se temos apenas o nome
    let horse_id = args.horse_id;
    if (!horse_id && args.nome_cavalo) {
      const cavalos = await storage.getCavalos(user_id);
      const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
      
      if (!cavalo) {
        return {
          success: false,
          message: `Cavalo ${args.nome_cavalo} não encontrado.`
        };
      }
      
      horse_id = cavalo.id;
    }
    
    // Verificar o objetivo do cruzamento (opcional)
    const objetivo = args.objetivo_cruzamento || "geral";
    
    // Buscar sugestões de cruzamento
    const sugestoes = await storage.getSugestoesCruzamento(horse_id, user_id, objetivo);
    
    if (sugestoes.length === 0) {
      return {
        success: true,
        message: "Não foi possível encontrar sugestões de cruzamento compatíveis para este cavalo.",
        data: []
      };
    }
    
    // Ordenar por compatibilidade (maior primeiro)
    sugestoes.sort((a, b) => b.compatibilidade - a.compatibilidade);
    
    // Montar resposta detalhada
    let resposta = `Sugestões de cruzamento para ${sugestoes[0].cavalo.name}:\n`;
    
    sugestoes.forEach((s, index) => {
      if (index < 3) { // Limitar para as 3 melhores sugestões
        resposta += `\n🏆 ${index + 1}. ${s.cavalo.name}`;
        resposta += `\n   Compatibilidade: ${s.compatibilidade}%`;
        
        if (s.consanguinidade !== null) {
          resposta += `\n   Consanguinidade: ${s.consanguinidade.toFixed(2)}%`;
        }
        
        if (s.pontosFortesCompativeis && s.pontosFortesCompativeis.length > 0) {
          resposta += `\n   Pontos fortes compatíveis: ${s.pontosFortesCompativeis.join(', ')}`;
        }
        
        resposta += `\n   ${s.descricaoCompatibilidade}\n`;
      }
    });
    
    if (sugestoes.length > 3) {
      resposta += `\nHá mais ${sugestoes.length - 3} sugestões disponíveis no sistema.`;
    }
    
    return {
      success: true,
      message: resposta,
      data: sugestoes
    };
  } catch (error: any) {
    console.error("Erro ao sugerir cruzamentos:", error);
    return {
      success: false,
      message: "Não foi possível gerar sugestões de cruzamento.",
      error: error.message
    };
  }
};

const executarConsultarGenetica = async (args: any) => {
  try {
    const user_id = args.user_id || 1; // Valor padrão temporário
    
    // Verificar se temos o ID do cavalo ou o nome
    if (!args.nome_cavalo && !args.horse_id) {
      // Se não temos um cavalo específico, retornar informações gerais sobre o módulo
      return {
        success: true,
        message: `O módulo de Genética do EquiGestor oferece:

1. Avaliação morfológica completa dos animais
2. Genealogia com cálculo de consanguinidade até 3 gerações
3. Histórico de desempenho em competições e provas
4. Sugestões de cruzamento com análise de compatibilidade

Para obter informações específicas, por favor mencione o nome do cavalo.`,
        data: null
      };
    }
    
    // Obter ID do cavalo se temos apenas o nome
    let horse_id = args.horse_id;
    if (!horse_id && args.nome_cavalo) {
      const cavalos = await storage.getCavalos(user_id);
      const cavalo = cavalos.find(c => c.name.toLowerCase() === args.nome_cavalo.toLowerCase());
      
      if (!cavalo) {
        return {
          success: false,
          message: `Cavalo ${args.nome_cavalo} não encontrado.`
        };
      }
      
      horse_id = cavalo.id;
    }
    
    // Buscar informações genéticas completas
    const morfologias = await storage.getMorfologiasByHorse(horse_id, user_id);
    const genealogia = await storage.getGenealogiaByHorse(horse_id, user_id);
    const desempenhos = await storage.getDesempenhosByHorse(horse_id, user_id);
    
    // Obter cavalo
    const cavalos = await storage.getCavalos(user_id);
    const cavalo = cavalos.find(c => c.id === horse_id);
    
    if (!cavalo) {
      return {
        success: false,
        message: "Cavalo não encontrado."
      };
    }
    
    // Montar resposta detalhada
    let resposta = `📊 Perfil Genético de ${cavalo.name}:\n\n`;
    
    // Informações morfológicas
    resposta += "🔍 **Morfologia**:\n";
    if (morfologias.length > 0) {
      // Pegar a mais recente
      const recente = morfologias.sort((a, b) => new Date(b.dataMedicao).getTime() - new Date(a.dataMedicao).getTime())[0];
      resposta += `- Última avaliação: ${new Date(recente.dataMedicao).toLocaleDateString('pt-BR')}\n`;
      resposta += `- Pontuação total: ${recente.pontuacaoTotal}\n`;
      resposta += `- Destaques: ${recente.pontuacaoCabeca >= 8 ? 'Cabeça,' : ''} ${recente.pontuacaoPescoco >= 8 ? 'Pescoço,' : ''} ${recente.pontuacaoGarupa >= 8 ? 'Garupa,' : ''} ${recente.pontuacaoAndamento >= 8 ? 'Andamento' : ''}\n`;
    } else {
      resposta += "- Sem avaliações morfológicas registradas\n";
    }
    
    // Informações genealógicas
    resposta += "\n🔍 **Genealogia**:\n";
    if (genealogia.cavalo) {
      if (genealogia.pai) {
        resposta += `- Pai: ${genealogia.pai.name}\n`;
      } else {
        resposta += "- Pai: Desconhecido\n";
      }
      
      if (genealogia.mae) {
        resposta += `- Mãe: ${genealogia.mae.name}\n`;
      } else {
        resposta += "- Mãe: Desconhecida\n";
      }
      
      if (genealogia.consanguinidade !== null) {
        resposta += `- Consanguinidade: ${genealogia.consanguinidade.toFixed(2)}%\n`;
      }
    } else {
      resposta += "- Sem registros genealógicos\n";
    }
    
    // Informações de desempenho
    resposta += "\n🔍 **Desempenho**:\n";
    if (desempenhos.length > 0) {
      resposta += `- Total de eventos: ${desempenhos.length}\n`;
      
      // Contar por tipo
      const tiposEvento = desempenhos.reduce((acc: {[key: string]: number}, curr) => {
        acc[curr.tipoEvento] = (acc[curr.tipoEvento] || 0) + 1;
        return acc;
      }, {});
      
      // Montar string com tipos
      const tiposString = Object.entries(tiposEvento)
        .map(([tipo, count]) => `${tipo} (${count})`)
        .join(', ');
      
      resposta += `- Tipos de eventos: ${tiposString}\n`;
      
      // Melhores resultados
      const melhoresPosicoes = desempenhos
        .filter(d => d.posicao !== null && d.posicao <= 3)
        .slice(0, 3);
      
      if (melhoresPosicoes.length > 0) {
        resposta += "- Melhores resultados:\n";
        melhoresPosicoes.forEach(d => {
          resposta += `  • ${d.nomeEvento}: ${d.posicao}º lugar\n`;
        });
      }
    } else {
      resposta += "- Sem registros de desempenho\n";
    }
    
    return {
      success: true,
      message: resposta,
      data: {
        cavalo,
        morfologias,
        genealogia,
        desempenhos
      }
    };
  } catch (error: any) {
    console.error("Erro ao consultar informações genéticas:", error);
    return {
      success: false,
      message: "Não foi possível consultar as informações genéticas.",
      error: error.message
    };
  }
};

const acoes = {
  // Funções existentes
  cadastrarCavalo: executarCadastrarCavalo,
  registrarPeso: executarRegistrarPeso,
  registrarVacina: executarRegistrarVacina,
  
  // Novas funções
  registrarTreino: executarRegistrarTreino,
  registrarAlimentacao: executarRegistrarAlimentacao,
  registrarFerrageamento: executarRegistrarFerrageamento,
  agendarVeterinario: executarAgendarVeterinario,
  consultarDados: executarConsultarDados,
  
  // Aliases para compatibilidade
  agendar_evento: executarAgendarEvento,
  consultar_agenda: executarConsultarAgenda,
  
  // Funções para genética
  consultarMorfologia: executarConsultarMorfologia,
  consultarGenealogia: executarConsultarGenealogia,
  consultarDesempenho: executarConsultarDesempenho,
  sugerirCruzamento: executarSugerirCruzamento,
  consultarGenetica: executarConsultarGenetica
};

// Exportar função que processa a chamada de função
export const processarAcaoAssistente = async (
  functionCall: { name: string; arguments: any }
) => {
  const { name, arguments: args } = functionCall;
  
  console.log(`Processando ação ${name} com argumentos:`, JSON.stringify(args));
  
  // Verificar se a ação existe
  if (!acoes[name as keyof typeof acoes]) {
    return {
      success: false,
      message: `Ação "${name}" não implementada.`
    };
  }
  
  // Executar a ação
  try {
    const resultado = await acoes[name as keyof typeof acoes](args);
    console.log(`Resultado da ação ${name}:`, JSON.stringify(resultado));
    return resultado;
  } catch (error: any) {
    console.error(`Erro ao executar ação ${name}:`, error);
    return {
      success: false,
      message: `Erro ao executar ação: ${error.message || 'Erro desconhecido'}`,
      error: error.message
    };
  }
};