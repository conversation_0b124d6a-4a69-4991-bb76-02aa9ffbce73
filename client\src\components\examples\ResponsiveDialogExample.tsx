import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  DialogResponsive,
  DialogResponsiveContent,
  DialogResponsiveDescription,
  DialogResponsiveFooter,
  DialogResponsiveHeader,
  DialogResponsiveTitle,
  DialogResponsiveTrigger,
} from '@/components/ui/dialog-responsive';

/**
 * Exemplo de uso do DialogResponsive
 * 
 * Este componente demonstra como usar o DialogResponsive para criar
 * modais que se adaptam automaticamente a dispositivos móveis.
 */
export function ResponsiveDialogExample() {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();
  
  return (
    <DialogResponsive open={open} onOpenChange={setOpen}>
      <DialogResponsiveTrigger asChild>
        <Button>Abrir Modal Responsivo</Button>
      </DialogResponsiveTrigger>
      <DialogResponsiveContent className="sm:max-w-[500px]">
        <DialogResponsiveHeader>
          <DialogResponsiveTitle>Formulário Responsivo</DialogResponsiveTitle>
          <DialogResponsiveDescription>
            Este modal se adapta automaticamente a dispositivos móveis.
            Experimente redimensionar a janela do navegador.
          </DialogResponsiveDescription>
        </DialogResponsiveHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Nome</Label>
            <Input id="name" placeholder="Digite seu nome" />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="<EMAIL>" />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="message">Mensagem</Label>
            <Textarea 
              id="message" 
              placeholder="Digite sua mensagem aqui..." 
              className={isMobile ? "min-h-[100px]" : "min-h-[80px]"}
            />
          </div>
        </div>
        
        <DialogResponsiveFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancelar
          </Button>
          <Button type="submit">Salvar</Button>
        </DialogResponsiveFooter>
      </DialogResponsiveContent>
    </DialogResponsive>
  );
}

export default ResponsiveDialogExample;
