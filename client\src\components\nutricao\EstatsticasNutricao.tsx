import React from 'react';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import { Nutricao } from '@/hooks/use-nutricao';
import { DollarSign, Scale, Clock } from 'lucide-react';

interface EstatisticasNutricaoProps {
  nutricoes: Nutricao[];
  calculateMonthlyCost: (quantidade: number, frequencia: number, custoUnitario: number) => string;
}

/**
 * Componente para exibir estatísticas de nutrição
 * 
 * Este componente é responsável por exibir um resumo
 * das estatísticas de nutrição como custo total mensal,
 * consumo diário e frequência de alimentação.
 */
export const EstatisticasNutricao: React.FC<EstatisticasNutricaoProps> = ({
  nutricoes,
  calculateMonthlyCost,
}) => {
  // Calcular custo total mensal
  const custoTotalMensal = nutricoes.reduce((total, n) => {
    const monthlyCost = n.quantidade * n.frequenciaDiaria * 30 * n.custoUnitario;
    return total + monthlyCost;
  }, 0).toFixed(2);

  // Calcular consumo diário
  const consumoDiario = nutricoes.reduce((total, n) => {
    if (n.unidadeMedida === 'kg') {
      return total + (n.quantidade * n.frequenciaDiaria);
    }
    if (n.unidadeMedida === 'g') {
      return total + ((n.quantidade / 1000) * n.frequenciaDiaria);
    }
    return total;
  }, 0).toFixed(2);

  // Calcular frequência total
  const frequenciaTotal = nutricoes.reduce((total, n) => total + n.frequenciaDiaria, 0);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card className="bg-gradient-to-br from-blue-50 to-green-50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Custo Total Mensal</p>
              <h4 className="text-2xl font-bold mt-1">
                R$ {custoTotalMensal}
              </h4>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Consumo Diário</p>
              <h4 className="text-2xl font-bold mt-1">
                {consumoDiario} kg
              </h4>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Scale className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Frequência Total</p>
              <h4 className="text-2xl font-bold mt-1">
                {frequenciaTotal} vezes/dia
              </h4>
            </div>
            <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};