# ETAPA 1 - Auditoria de Schemas EquiGestor

## Aná<PERSON>e Completa do Estado Atual

### Fonte de Verdade Identificada
**Arquivo Principal:** `/shared/schema.ts` (snake_case consistente)

### Enums Conflitantes Identificados

| Enum | Arquivo | Valores | Status |
|------|---------|---------|---------|
| `horseSexoEnum` | `/shared/schema.ts` | ['macho', 'femea', 'macho_castrado', 'garanhao', 'femea'] | **FONTE DE VERDADE** |
| `horseSexoEnumV2` | `/shared/schema.ts` | ['macho', 'femea', 'macho_castrado', 'garanhao', 'egua'] | CONFLITANTE - REMOVER |

### Tabelas Analisadas

#### 1. USERS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| username | varchar(120) | snake_case | ✅ OK |
| password | varchar(60) | snake_case | ✅ OK |
| email | varchar(255) | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 2. CAVALOS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| name | varchar(255) | snake_case | ✅ OK |
| breed | varchar(100) | snake_case | ✅ OK |
| birth_date | date | snake_case | ✅ OK |
| sexo | horseSexoEnum | snake_case | ✅ OK |
| cor | varchar(100) | snake_case | ✅ OK |
| pelagem_id | integer | snake_case | ✅ OK |
| status | horseStatusEnum | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |
| notes | text | snake_case | ✅ OK |
| peso | doublePrecision | snake_case | ✅ OK |
| altura | doublePrecision | snake_case | ✅ OK |
| data_entrada | date | snake_case | ✅ OK |
| data_saida | date | snake_case | ✅ OK |
| motivo_saida | text | snake_case | ✅ OK |
| numero_registro | varchar(50) | snake_case | ✅ OK |
| origem | varchar(255) | snake_case | ✅ OK |
| criador | varchar(255) | snake_case | ✅ OK |
| proprietario | varchar(255) | snake_case | ✅ OK |
| valor_compra | numeric | snake_case | ✅ OK |
| data_compra | date | snake_case | ✅ OK |
| inspetor | varchar(255) | snake_case | ✅ OK |
| is_external | boolean | snake_case | ✅ OK |
| pai_id | integer | snake_case | ✅ OK |
| mae_id | integer | snake_case | ✅ OK |

#### 3. GENEALOGIA
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| pai | text | snake_case | ✅ OK |
| mae | text | snake_case | ✅ OK |
| avoPaternoId | integer | **camelCase** | ⚠️ MIGRAR PARA avo_paterno_id |
| avoPaterno | text | **camelCase** | ⚠️ MIGRAR PARA avo_paterno |
| avoPaternaId | integer | **camelCase** | ⚠️ MIGRAR PARA avo_paterna_id |
| avoPaterna | text | **camelCase** | ⚠️ MIGRAR PARA avo_paterna |
| avoMaternoId | integer | **camelCase** | ⚠️ MIGRAR PARA avo_materno_id |
| avoMaterno | text | **camelCase** | ⚠️ MIGRAR PARA avo_materno |
| avoMaternaId | integer | **camelCase** | ⚠️ MIGRAR PARA avo_materna_id |
| avoMaterna | text | **camelCase** | ⚠️ MIGRAR PARA avo_materna |
| coeficienteConsanguinidade | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA coeficiente_consanguinidade |
| observacoes | text | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 4. MANEJOS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| cavalo_id | integer | snake_case | ✅ OK |
| tipo | varchar(100) | snake_case | ✅ OK |
| data_execucao | date | snake_case | ✅ OK |
| descricao | text | snake_case | ✅ OK |
| observacoes | text | snake_case | ✅ OK |
| responsavel | varchar(255) | snake_case | ✅ OK |
| custo | numeric | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |
| status | varchar(50) | snake_case | ✅ OK |

#### 5. NUTRICAO
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| data | date | snake_case | ✅ OK |
| tipoAlimentacao | varchar(100) | **camelCase** | ⚠️ MIGRAR PARA tipo_alimentacao |
| nomeAlimento | varchar(255) | **camelCase** | ⚠️ MIGRAR PARA nome_alimento |
| quantidade | doublePrecision | snake_case | ✅ OK |
| unidadeMedida | varchar(50) | **camelCase** | ⚠️ MIGRAR PARA unidade_medida |
| frequenciaDiaria | integer | **camelCase** | ⚠️ MIGRAR PARA frequencia_diaria |
| horarios | text | snake_case | ✅ OK |
| observacoes | text | snake_case | ✅ OK |
| custoUnitario | numeric | **camelCase** | ⚠️ MIGRAR PARA custo_unitario |
| custoMensal | numeric | **camelCase** | ⚠️ MIGRAR PARA custo_mensal |
| fornecedor | varchar(255) | snake_case | ✅ OK |
| recomendacao | text | snake_case | ✅ OK |
| status | varchar(50) | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 6. ARQUIVOS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| fileName | text | **camelCase** | ⚠️ MIGRAR PARA file_name |
| filePath | text | **camelCase** | ⚠️ MIGRAR PARA file_path |
| fileType | text | **camelCase** | ⚠️ MIGRAR PARA file_type |
| description | text | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 7. EVENTOS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| titulo | text | snake_case | ✅ OK |
| descricao | text | snake_case | ✅ OK |
| data | date | snake_case | ✅ OK |
| horaInicio | text | **camelCase** | ⚠️ MIGRAR PARA hora_inicio |
| horaFim | text | **camelCase** | ⚠️ MIGRAR PARA hora_fim |
| tipo | text | snake_case | ✅ OK |
| status | text | snake_case | ✅ OK |
| prioridade | text | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 8. REPRODUCAO
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| padreiroId | integer | **camelCase** | ⚠️ MIGRAR PARA padreiro_id |
| dataCobertura | date | **camelCase** | ⚠️ MIGRAR PARA data_cobertura |
| tipoCobertura | text | **camelCase** | ⚠️ MIGRAR PARA tipo_cobertura |
| estado | text | snake_case | ✅ OK |
| observacoes | text | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 9. MORFOLOGIA
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| horse_id | integer | snake_case | ✅ OK |
| dataMedicao | date | **camelCase** | ⚠️ MIGRAR PARA data_medicao |
| alturaCernelha | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA altura_cernelha |
| alturaDorso | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA altura_dorso |
| alturaGarupa | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA altura_garupa |
| comprimentoCorpo | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA comprimento_corpo |
| comprimentoPescoco | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA comprimento_pescoco |
| larguraPeito | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA largura_peito |
| perimetroToracico | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA perimetro_toracico |
| perimetroPescoco | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA perimetro_pescoco |
| perimetroCanela | doublePrecision | **camelCase** | ⚠️ MIGRAR PARA perimetro_canela |
| responsavelMedicao | varchar(255) | **camelCase** | ⚠️ MIGRAR PARA responsavel_medicao |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 10. MORFOLOGIA_ARQUIVOS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| morfologiaId | integer | **camelCase** | ⚠️ MIGRAR PARA morfologia_id |
| arquivoId | integer | **camelCase** | ⚠️ MIGRAR PARA arquivo_id |
| created_at | timestamp | snake_case | ✅ OK |

#### 11. FEED_TEMPLATES
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| nome | text | snake_case | ✅ OK |
| descricao | text | snake_case | ✅ OK |
| categoria | text | snake_case | ✅ OK |
| ingredientes | text | snake_case | ✅ OK |
| valorNutricional | text | **camelCase** | ⚠️ MIGRAR PARA valor_nutricional |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 12. FEED_PLAN_ITEMS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| templateId | integer | **camelCase** | ⚠️ MIGRAR PARA template_id |
| horse_id | integer | snake_case | ✅ OK |
| quantidade | real | snake_case | ✅ OK |
| frequencia | text | snake_case | ✅ OK |
| observacoes | text | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 13. STOCK_BATCHES
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| produto | text | snake_case | ✅ OK |
| lote | text | snake_case | ✅ OK |
| quantidade | real | snake_case | ✅ OK |
| dataValidade | date | **camelCase** | ⚠️ MIGRAR PARA data_validade |
| fornecedor | text | snake_case | ✅ OK |
| custo | real | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

#### 14. STOCK_ALERTS
| Campo | Tipo | Nomenclatura | Status |
|-------|------|--------------|--------|
| id | serial | snake_case | ✅ OK |
| produto | text | snake_case | ✅ OK |
| estoqueMinimo | real | **camelCase** | ⚠️ MIGRAR PARA estoque_minimo |
| estoqueAtual | real | **camelCase** | ⚠️ MIGRAR PARA estoque_atual |
| ativo | boolean | snake_case | ✅ OK |
| user_id | integer | snake_case | ✅ OK |
| created_at | timestamp | snake_case | ✅ OK |

## Resumo das Migrações Necessárias

### Total de Campos para Migração: 38 campos

**Prioridade Alta (tabelas principais):**
- GENEALOGIA: 8 campos camelCase → snake_case
- NUTRICAO: 7 campos camelCase → snake_case
- MORFOLOGIA: 13 campos camelCase → snake_case
- REPRODUCAO: 3 campos camelCase → snake_case

**Prioridade Média:**
- ARQUIVOS: 3 campos camelCase → snake_case
- EVENTOS: 2 campos camelCase → snake_case
- STOCK_ALERTS: 2 campos camelCase → snake_case

### Enum para Remover:
- `horseSexoEnumV2` (conflita com `horseSexoEnum`)

## Status da Auditoria
✅ **ETAPA 1 CONCLUÍDA**

**Flag de Aprovação:** `WAIT_APPROVAL_STAGE_1`

**Próximos Passos:** Aguardar aprovação humana para prosseguir com ETAPA 2.