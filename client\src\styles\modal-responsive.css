/* Estilos específicos para modais responsivos */

/* Ajustes gerais para modais em dispositivos móveis */
@media screen and (max-width: 768px) {
  /* Melhorar a experiência de scroll em modais */
  [data-radix-dialog-content] {
    max-height: 100dvh;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Aumentar área de toque para botões em modais */
  [data-radix-dialog-content] button {
    min-height: 2.75rem;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-bottom: 0.25rem;
  }
  
  /* Melhorar espaçamento de formulários em modais */
  [data-radix-dialog-content] form {
    padding-bottom: 1rem;
  }
  
  /* Ajustar tamanho de inputs para melhor usabilidade em touch */
  [data-radix-dialog-content] input,
  [data-radix-dialog-content] select,
  [data-radix-dialog-content] textarea {
    font-size: 16px; /* Prevenir zoom automático em iOS */
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  /* Melhorar espaçamento de labels */
  [data-radix-dialog-content] label {
    margin-bottom: 0.25rem;
    display: block;
  }
  
  /* Ajustar tamanho do botão de fechar */
  [data-radix-dialog-content] [data-radix-dialog-close] {
    transform: scale(1.2);
    margin: 0.5rem;
  }
  
  /* Melhorar contraste do overlay */
  [data-radix-dialog-overlay] {
    backdrop-filter: blur(3px);
  }
  
  /* Ajustar footer para melhor usabilidade em mobile */
  .dialog-footer-mobile {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
    width: 100%;
  }
  
  .dialog-footer-mobile button {
    width: 100%;
  }
  
  /* Melhorar animações para performance em dispositivos móveis */
  @keyframes mobileDialogIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes mobileDialogOut {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(20px);
    }
  }
  
  .mobile-dialog-animation-in {
    animation: mobileDialogIn 0.3s ease forwards;
  }
  
  .mobile-dialog-animation-out {
    animation: mobileDialogOut 0.2s ease forwards;
  }
  
  /* Ajustar tamanho de fonte para títulos em modais */
  [data-radix-dialog-title] {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  /* Melhorar espaçamento de conteúdo */
  [data-radix-dialog-content] > div {
    padding: 0.5rem 0;
  }
  
  /* Ajustar espaçamento para formulários longos */
  [data-radix-dialog-content] .form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
