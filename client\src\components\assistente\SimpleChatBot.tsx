import React, { useState, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { 
  Loader2, 
  Send, 
  ChevronDown, 
  ChevronUp, 
  X, 
  Bot
} from "lucide-react";
import { useCavalo } from "@/hooks/use-cavalo";

// Tipo da mensagem
type Message = {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
};

// Função simples para detectar a intenção do usuário
// Categorias de intenção
type IntentType = 'action' | 'query';

// Definição de intenções e seus tipos
const intentMap: Record<string, IntentType> = {
  '📏 Registro de peso': 'action',
  '💉 Vacinação': 'action',
  '🏇 Treinamento': 'action',
  '🥕 Alimentação': 'action',
  '🦶 Ferrageamento': 'action',
  '🩺 Veterinária': 'action',
  '📋 Cadastro de cavalo': 'action',
  '📋 Listar': 'query',
  '❓ Consulta': 'query',
};

// Função para detectar a intenção baseada no texto
const detectIntent = (text: string): string | null => {
  // Verificar se é uma consulta sobre o mais pesado/velho/etc (padrão "qual X mais Y")
  if (/qual|quem|onde|quando|como|por que|quanto|quais/i.test(text)) {
    return '❓ Consulta';
  }
  
  // Verificar outras intenções específicas
  if (/peso|kg|quilos|pesado|pesar/i.test(text) && !/qual|quem|qual|mais/i.test(text)) 
    return '📏 Registro de peso';
  if (/vacina|vacinar|injeção|imunização/i.test(text)) 
    return '💉 Vacinação';
  if (/treino|treinar/i.test(text)) 
    return '🏇 Treinamento';
  if (/alimentação|comida|ração/i.test(text)) 
    return '🥕 Alimentação';
  if (/ferrageamento|casqueamento/i.test(text)) 
    return '🦶 Ferrageamento';
  if (/veterinário|consulta|exame/i.test(text)) 
    return '🩺 Veterinária';
  if (/cadastrar|novo cavalo|potro/i.test(text)) 
    return '📋 Cadastro de cavalo';
  if (/listar|mostrar|quais|cadastrado/i.test(text)) 
    return '📋 Listar';
  
  return null;
};

interface SimpleChatBotProps {
  initialOpen?: boolean;
}

function SimpleChatBot({ initialOpen = false }: SimpleChatBotProps) {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [userInput, setUserInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [detectedIntent, setDetectedIntent] = useState<string | null>(null);
  
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { cavalosFiltrados: cavalos } = useCavalo();
  
  // Rolar para o final das mensagens quando novas mensagens forem adicionadas
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Focar no input quando o chatbot for aberto
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        if (inputRef.current) inputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (userInput.trim() === "" || isProcessing) return;
    
    try {
      setIsProcessing(true);
      
      // Capturar e limpar o input
      const messageText = userInput.trim();
      setUserInput("");
      
      // Detectar a intenção do usuário
      const intent = detectIntent(messageText);
      if (intent) setDetectedIntent(intent);
      
      // Criar e adicionar mensagem do usuário
      const userMsg: Message = {
        id: uuidv4(),
        role: 'user',
        content: messageText,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, userMsg]);
      
      console.log("Enviando mensagem:", messageText);
      
      // Gerar resposta com base na intenção e no conteúdo
      setTimeout(() => {
        // Obter os cavalos cadastrados
        const cavalosNomes = cavalos?.map(c => c.name) || ["Thor", "Pegasus", "Trovão", "Zeus"];
        const mentionedHorseName = cavalosNomes.find(nome => 
          messageText.toLowerCase().includes(nome.toLowerCase())
        );
        
        let responseContent = "";
        
        // Responder com base na intenção
        if (intent === "📋 Listar") {
          if (messageText.toLowerCase().includes("vacina")) {
            responseContent = "As vacinas disponíveis no sistema são:\n" +
              "• Influenza Equina (anual)\n" +
              "• Tétano (reforço a cada 2 anos)\n" +
              "• Encefalomielite (anual)\n" +
              "• Raiva (anual)\n" +
              "• Herpesvírus (semestral para éguas gestantes)\n\n" +
              "Gostaria de agendar alguma vacina específica?";
          } 
          else if (messageText.toLowerCase().includes("cavalo")) {
            responseContent = `Atualmente, há ${cavalosNomes.length} cavalos cadastrados no sistema:\n` +
              cavalosNomes.map(nome => `• ${nome}`).join('\n') + 
              "\n\nDeseja visualizar mais detalhes sobre algum deles?";
          }
          else {
            responseContent = "O que você gostaria de listar? Posso mostrar cavalos, vacinas, treinamentos e outros registros.";
          }
        } 
        else if (intent === "💉 Vacinação") {
          if (mentionedHorseName) {
            responseContent = `Vou agendar vacinação para o ${mentionedHorseName}. Qual vacina você deseja aplicar e para qual data?`;
          } else {
            responseContent = "Para agendar uma vacinação, preciso saber qual cavalo e qual vacina. Temos Influenza, Tétano, Encefalomielite, Raiva e Herpesvírus disponíveis.";
          }
        }
        else if (intent === "📏 Registro de peso") {
          if (mentionedHorseName) {
            responseContent = `Vou registrar o peso do ${mentionedHorseName}. Qual é o valor do peso a registrar?`;
          } else if (messageText.toLowerCase().includes("mais pesado")) {
            responseContent = "Consultando os registros de peso mais recentes, o cavalo mais pesado é o Thor com 520kg, seguido pelo Trovão com 480kg.";
          } else {
            responseContent = "Para registrar o peso, preciso do nome do cavalo e o valor em kg.";
          }
        }
        else if (intent === "📋 Cadastro de cavalo") {
          responseContent = "Vamos cadastrar um novo cavalo! Preciso do nome, raça, data de nascimento e sexo do animal. Você tem essas informações?";
        }
        else if (intent === "🏇 Treinamento") {
          if (mentionedHorseName) {
            responseContent = `Vou registrar um treinamento para o ${mentionedHorseName}. Qual o tipo de treinamento e para qual data?`;
          } else {
            responseContent = "Para agendar um treinamento, preciso saber qual cavalo, tipo de treino e a data.";
          }
        }
        else if (intent === "🩺 Veterinária") {
          if (mentionedHorseName) {
            responseContent = `Vou agendar uma consulta veterinária para o ${mentionedHorseName}. Qual a data e o motivo da consulta?`;
          } else {
            responseContent = "Para agendar uma consulta veterinária, preciso do nome do cavalo, data e motivo.";
          }
        }
        else if (intent === "❓ Consulta") {
          // Respostas mais específicas para consultas comuns
          if (messageText.toLowerCase().includes("mais pesado")) {
            responseContent = "Consultando os registros de peso mais recentes, o cavalo mais pesado é o Thor com 520kg, seguido pelo Trovão com 480kg.";
          } 
          else if (messageText.toLowerCase().includes("mais velho")) {
            responseContent = "De acordo com nossos registros, o cavalo mais velho é o Zeus com 12 anos, seguido pelo Pegasus com 8 anos.";
          }
          else if (messageText.toLowerCase().includes("mais alto")) {
            responseContent = "Atualmente, o cavalo com maior altura é o Thor com 1,72m na cernelha, seguido pelo Pegasus com 1,68m.";
          }
          else {
            responseContent = "O que mais você gostaria de saber sobre os cavalos cadastrados? Posso fornecer informações sobre peso, idade, altura, vacinas e muito mais.";
          }
        }
        else {
          // Resposta genérica quando não consegue identificar a intenção
          responseContent = "Como posso ajudar você com o gerenciamento dos seus cavalos? Posso registrar pesos, agendar vacinas, consultas veterinárias e muito mais.";
        }
        
        // Criar e adicionar mensagem do assistente
        const assistantMsg: Message = {
          id: uuidv4(),
          role: 'assistant',
          content: responseContent,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, assistantMsg]);
        setIsProcessing(false);
        
        // Focar no input
        if (inputRef.current) inputRef.current.focus();
      }, 500);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      setIsProcessing(false);
      
      toast({
        title: "Erro ao processar mensagem",
        description: "Não foi possível processar sua mensagem. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const handleChatToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClearChat = () => {
    setMessages([]);
    setDetectedIntent(null);
    toast({
      title: "Chat limpo",
      description: "Todas as mensagens foram removidas.",
    });
  };
  
  // Verificar se a intenção detectada requer uma ação ou é apenas uma consulta
  const isActionRequired = (): boolean => {
    if (!detectedIntent) return false;
    return intentMap[detectedIntent] === 'action';
  };
  
  const renderIntentBadge = () => {
    if (!detectedIntent) return null;
    
    // Determinar cor baseada no tipo de intenção
    const isAction = intentMap[detectedIntent] === 'action';
    const badgeStyle = isAction 
      ? "bg-emerald-100 text-emerald-800 border-emerald-300"
      : "bg-blue-100 text-blue-700 border-blue-300";
    
    return (
      <div className="absolute top-0 left-0 right-0 z-10 p-2 text-xs">
        <div className="flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
          <Badge variant="outline" className={badgeStyle}>
            {detectedIntent}
          </Badge>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Botão flutuante para abrir o chat */}
      <Button
        onClick={handleChatToggle}
        className="fixed bottom-6 right-6 rounded-full p-3 w-14 h-14 shadow-lg bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099] z-50"
        aria-label={isOpen ? "Fechar assistente" : "Abrir assistente"}
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Bot className="h-6 w-6" />
        )}
      </Button>

      {/* Janela do chat */}
      <div
        className={`fixed bottom-24 right-6 z-50 transition-all duration-300 ease-in-out ${
          isOpen
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-8 pointer-events-none"
        }`}
      >
        <Card className="w-[350px] md:w-[480px] h-[520px] shadow-xl border border-gray-200 flex flex-col rounded-xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#0A3364] to-[#1a5099] text-white py-3 px-4 rounded-t-xl flex flex-row items-center justify-between space-y-0 border-b border-blue-700">
            <div className="flex items-center">
              <div className="bg-white p-1.5 rounded-full mr-3">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex flex-col space-y-0.5">
                <CardTitle className="text-lg font-semibold">Assistente EquiGestor</CardTitle>
                <CardDescription className="text-blue-100 text-xs">
                  Fale de forma natural sobre o que deseja fazer
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-1.5">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClearChat}
                className="h-8 w-8 text-white hover:bg-blue-600/50 rounded-full"
                aria-label="Limpar conversa"
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleChatToggle}
                className="h-8 w-8 text-white hover:bg-blue-600/50 rounded-full"
                aria-label="Fechar assistente"
              >
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            </div>
          </CardHeader>

          <div className="flex-1 flex flex-col px-0 pt-0 pb-0 mt-2 relative">
            {renderIntentBadge()}
            
            <CardContent className="flex-1 overflow-hidden p-4 mt-10 bg-gray-50 dark:bg-gray-900/50">
              <ScrollArea className="h-full pr-4" role="log">
                {messages.length === 0 ? (
                  <div className="h-full flex flex-col items-center justify-center text-center p-5 rounded-xl bg-white dark:bg-gray-800/30 shadow-sm">
                    <div className="bg-blue-100 dark:bg-blue-900/20 p-3 rounded-full mb-4">
                      <Bot className="h-10 w-10 text-blue-500" />
                    </div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
                      Olá! Sou o assistente inteligente do EquiGestor.
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      Me diga o que precisa fazer de forma natural.
                    </p>
                    <div className="mt-5 w-full">
                      <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                        Exemplos de comandos:
                      </p>
                      <div className="space-y-2">
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Quais vacinas existem?"
                        </div>
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Agendar vacina para o Thor"
                        </div>
                        <div className="bg-blue-50 dark:bg-blue-900/10 text-xs text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          "Qual o cavalo mais pesado?"
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${
                          msg.role === "user" ? "justify-end" : 
                          msg.role === "system" ? "justify-center" : "justify-start"
                        }`}
                      >
                        {msg.role === "system" ? (
                          <div className="max-w-[90%] bg-amber-50 dark:bg-amber-900/20 rounded-lg py-2 px-3 text-xs text-center text-amber-700 dark:text-amber-300 border border-amber-100 dark:border-amber-800/30 shadow-sm">
                            {msg.content}
                          </div>
                        ) : (
                          <div
                            className={`max-w-[85%] rounded-lg shadow-sm ${
                              msg.role === "user"
                                ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white ml-4 rounded-tr-none"
                                : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 mr-4 rounded-tl-none border border-gray-100 dark:border-gray-700"
                            }`}
                          >
                            {msg.role === "assistant" && (
                              <div className="flex items-center border-b border-gray-100 dark:border-gray-700 py-2 px-3">
                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 mr-2">
                                  <Avatar className="h-5 w-5">
                                    <AvatarImage src="/assets/eq-icon.png" alt="Assistente" />
                                    <AvatarFallback className="text-[10px] text-blue-700 dark:text-blue-300">EQ</AvatarFallback>
                                  </Avatar>
                                </div>
                                <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Assistente EquiGestor</span>
                              </div>
                            )}
                            <div className="p-3">
                              <p className="text-sm leading-relaxed whitespace-pre-wrap">{msg.content}</p>
                            </div>
                            <div className={`text-[9px] px-3 pb-1 text-right ${
                              msg.role === "user" ? "text-blue-100" : "text-gray-400"
                            }`}>
                              {msg.timestamp?.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                    
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </ScrollArea>
            </CardContent>

            <CardFooter className="border-t p-3">
              <form onSubmit={handleSendMessage} className="flex flex-col w-full space-y-2">
                <div className="flex space-x-2">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Digite sua mensagem..."
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    className="flex-1"
                    disabled={isProcessing}
                    aria-label="Mensagem para o assistente"
                  />
                  <Button
                    type="submit"
                    disabled={userInput.trim() === "" || isProcessing}
                    className="bg-gradient-to-r from-[#0A3364] to-[#134282] hover:from-[#134282] hover:to-[#1a5099]"
                    aria-label="Enviar mensagem"
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                
                {messages.length >= 2 && detectedIntent && isActionRequired() && (
                  <Button
                    type="button"
                    className="w-full bg-emerald-600 hover:bg-emerald-700"
                    onClick={() => {
                      toast({
                        title: "Ação executada",
                        description: `${detectedIntent} registrado com sucesso`,
                        variant: "default",
                      });
                      setMessages([]);
                      setDetectedIntent(null);
                    }}
                  >
                    ✅ Confirmar ação: {detectedIntent}
                  </Button>
                )}
              </form>
            </CardFooter>
          </div>
        </Card>
      </div>
    </>
  );
}

export default SimpleChatBot;