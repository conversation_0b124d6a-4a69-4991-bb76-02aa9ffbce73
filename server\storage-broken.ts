import { 
  // Core domain
  horses, type <PERSON>,
  
  // Only import what actually exists
  morfologia,
  manejos, type Manejo
} from "@shared/schemas";
import { db } from "./db";
import { eq, and, asc, desc, like, sql, ne, or, isNull, notInArray, gte, lte, lt, inArray } from "drizzle-orm";
import { getModuleLogger, logDbError } from './logger';

// Interface simplificada apenas com métodos funcionais
export interface IStorage {
  // Horses
  getCavalos(user_id: number): Promise<Horse[]>;
  getHorse(id: number, user_id: number): Promise<Horse | undefined>;
  createHorse(horse: any): Promise<Horse>;
  updateHorse(id: number, user_id: number, horseData: Partial<Horse>): Promise<Horse | undefined>;
  deleteCavalo(id: number, user_id: number): Promise<boolean>;
  
  // Manejos
  getManejos(user_id: number): Promise<Manejo[]>;
  getManejosByHorse(horse_id: number, user_id: number): Promise<Manejo[]>;
  getManejo(id: number, user_id: number): Promise<Manejo | undefined>;
  createManejo(insertManejo: any): Promise<Manejo>;
  updateManejo(id: number, user_id: number, manejoData: Partial<any>): Promise<Manejo | undefined>;
  deleteManejo(id: number, user_id: number): Promise<boolean>;
  
  // Genetics
  getMorfologias(user_id: number): Promise<Morfologia[]>;
  getMorfologiasByHorse(horse_id: number, user_id: number): Promise<Morfologia[]>;
  getMorfologia(id: number, user_id: number): Promise<Morfologia | undefined>;
  createMorfologia(insertMorfologia: NewMorfologia): Promise<Morfologia>;
  updateMorfologia(id: number, user_id: number, morfologiaData: Partial<NewMorfologia>): Promise<Morfologia | undefined>;
  deleteMorfologia(id: number, user_id: number): Promise<boolean>;
  
  getGenealogias(user_id: number): Promise<Genealogia[]>;
  getGenealogiasByHorse(horse_id: number, user_id: number): Promise<Genealogia[]>;
  getGenealogia(id: number, user_id: number): Promise<Genealogia | undefined>;
  createGenealogia(insertGenealogia: NewGenealogia): Promise<Genealogia>;
  updateGenealogia(id: number, user_id: number, genealogiaData: Partial<NewGenealogia>): Promise<Genealogia | undefined>;
  deleteGenealogia(id: number, user_id: number): Promise<boolean>;
  
  getCruzamentos(user_id: number): Promise<Cruzamentos[]>;
  getCruzamentosByHorse(horse_id: number, user_id: number): Promise<Cruzamentos[]>;
  getCruzamento(id: number, user_id: number): Promise<Cruzamentos | undefined>;
  createCruzamento(insertCruzamento: NewCruzamentos): Promise<Cruzamentos>;
  updateCruzamento(id: number, user_id: number, cruzamentoData: Partial<NewCruzamentos>): Promise<Cruzamentos | undefined>;
  deleteCruzamento(id: number, user_id: number): Promise<boolean>;
}

export class Storage implements IStorage {
  private logger = getModuleLogger('Storage');

  // Horse methods
  async getCavalos(user_id: number): Promise<Horse[]> {
    return db.select()
      .from(horses)
      .where(eq(horses.user_id, user_id))
      .orderBy(asc(horses.name));
  }
  
  async getHorse(id: number, user_id: number): Promise<Horse | undefined> {
    const [horse] = await db.select()
      .from(horses)
      .where(and(eq(horses.id, id), eq(horses.user_id, user_id)));
    return horse || undefined;
  }
  
  async createHorse(horse: any): Promise<Horse> {
    const [newHorse] = await db
      .insert(horses)
      .values(horse)
      .returning();
    return newHorse;
  }
  
  async updateHorse(id: number, user_id: number, horseData: Partial<Horse>): Promise<Horse | undefined> {
    const [horse] = await db
      .update(horses)
      .set(horseData)
      .where(and(eq(horses.id, id), eq(horses.user_id, user_id)))
      .returning();
    return horse || undefined;
  }
  
  async deleteCavalo(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(horses)
      .where(and(eq(horses.id, id), eq(horses.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Manejo methods
  async getManejos(user_id: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      .where(eq(manejos.user_id, user_id))
      // Sort by execution date (data_execucao)
      .orderBy(asc(manejos.data_execucao));
  }
  
  async getManejosByHorse(horse_id: number, user_id: number): Promise<Manejo[]> {
    return db.select()
      .from(manejos)
      // Filter by cavalo_id using horse_id param
      .where(and(eq(manejos.horse_id, horse_id), eq(manejos.user_id, user_id)))
      // Sort by execution date (data_execucao)
      .orderBy(asc(manejos.data_execucao));
  }
  
  async getManejo(id: number, user_id: number): Promise<Manejo | undefined> {
    const [manejo] = await db.select()
      .from(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)));
    return manejo || undefined;
  }
  
  async createManejo(insertManejo: any): Promise<Manejo> {
    const [manejo] = await db
      .insert(manejos)
      .values(insertManejo)
      .returning();
    return manejo;
  }
  
  async updateManejo(id: number, user_id: number, manejoData: Partial<any>): Promise<Manejo | undefined> {
    const [manejo] = await db
      .update(manejos)
      .set(manejoData)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)))
      .returning();
    return manejo || undefined;
  }
  
  async deleteManejo(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(manejos)
      .where(and(eq(manejos.id, id), eq(manejos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Morfologia methods
  async getMorfologias(user_id: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologias)
      .where(eq(morfologias.user_id, user_id))
      .orderBy(asc(morfologias.dataAvaliacao));
  }
  
  async getMorfologiasByHorse(horse_id: number, user_id: number): Promise<Morfologia[]> {
    return db.select()
      .from(morfologias)
      .where(and(eq(morfologias.horse_id, horse_id), eq(morfologias.user_id, user_id)))
      .orderBy(asc(morfologias.dataAvaliacao));
  }
  
  async getMorfologia(id: number, user_id: number): Promise<Morfologia | undefined> {
    const [morfologia] = await db.select()
      .from(morfologias)
      .where(and(eq(morfologias.id, id), eq(morfologias.user_id, user_id)));
    return morfologia || undefined;
  }
  
  async createMorfologia(insertMorfologia: NewMorfologia): Promise<Morfologia> {
    const [morfologia] = await db
      .insert(morfologias)
      .values(insertMorfologia)
      .returning();
    return morfologia;
  }
  
  async updateMorfologia(id: number, user_id: number, morfologiaData: Partial<NewMorfologia>): Promise<Morfologia | undefined> {
    const [morfologia] = await db
      .update(morfologias)
      .set(morfologiaData)
      .where(and(eq(morfologias.id, id), eq(morfologias.user_id, user_id)))
      .returning();
    return morfologia || undefined;
  }
  
  async deleteMorfologia(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(morfologias)
      .where(and(eq(morfologias.id, id), eq(morfologias.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Genealogia methods
  async getGenealogias(user_id: number): Promise<Genealogia[]> {
    return db.select()
      .from(genealogias)
      .where(eq(genealogias.user_id, user_id))
      .orderBy(asc(genealogias.id));
  }
  
  async getGenealogiasByHorse(horse_id: number, user_id: number): Promise<Genealogia[]> {
    return db.select()
      .from(genealogias)
      .where(and(eq(genealogias.horse_id, horse_id), eq(genealogias.user_id, user_id)))
      .orderBy(asc(genealogias.id));
  }
  
  async getGenealogia(id: number, user_id: number): Promise<Genealogia | undefined> {
    const [genealogia] = await db.select()
      .from(genealogias)
      .where(and(eq(genealogias.id, id), eq(genealogias.user_id, user_id)));
    return genealogia || undefined;
  }
  
  async createGenealogia(insertGenealogia: NewGenealogia): Promise<Genealogia> {
    const [genealogia] = await db
      .insert(genealogias)
      .values(insertGenealogia)
      .returning();
    return genealogia;
  }
  
  async updateGenealogia(id: number, user_id: number, genealogiaData: Partial<NewGenealogia>): Promise<Genealogia | undefined> {
    const [genealogia] = await db
      .update(genealogias)
      .set(genealogiaData)
      .where(and(eq(genealogias.id, id), eq(genealogias.user_id, user_id)))
      .returning();
    return genealogia || undefined;
  }
  
  async deleteGenealogia(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(genealogias)
      .where(and(eq(genealogias.id, id), eq(genealogias.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
  
  // Cruzamentos methods
  async getCruzamentos(user_id: number): Promise<Cruzamentos[]> {
    return db.select()
      .from(cruzamentos)
      .where(eq(cruzamentos.user_id, user_id))
      .orderBy(asc(cruzamentos.dataRecomendacao));
  }
  
  async getCruzamentosByHorse(horse_id: number, user_id: number): Promise<Cruzamentos[]> {
    return db.select()
      .from(cruzamentos)
      .where(and(eq(cruzamentos.femaleId, horse_id), eq(cruzamentos.user_id, user_id)))
      .orderBy(asc(cruzamentos.dataRecomendacao));
  }
  
  async getCruzamento(id: number, user_id: number): Promise<Cruzamentos | undefined> {
    const [cruzamento] = await db.select()
      .from(cruzamentos)
      .where(and(eq(cruzamentos.id, id), eq(cruzamentos.user_id, user_id)));
    return cruzamento || undefined;
  }
  
  async createCruzamento(insertCruzamento: NewCruzamentos): Promise<Cruzamentos> {
    const [cruzamento] = await db
      .insert(cruzamentos)
      .values(insertCruzamento)
      .returning();
    return cruzamento;
  }
  
  async updateCruzamento(id: number, user_id: number, cruzamentoData: Partial<NewCruzamentos>): Promise<Cruzamentos | undefined> {
    const [cruzamento] = await db
      .update(cruzamentos)
      .set(cruzamentoData)
      .where(and(eq(cruzamentos.id, id), eq(cruzamentos.user_id, user_id)))
      .returning();
    return cruzamento || undefined;
  }
  
  async deleteCruzamento(id: number, user_id: number): Promise<boolean> {
    const result = await db
      .delete(cruzamentos)
      .where(and(eq(cruzamentos.id, id), eq(cruzamentos.user_id, user_id)))
      .returning();
    return result.length > 0;
  }
}

export const storage = new Storage();