import * as React from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

interface MobileOptimizedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /** Adiciona padding extra para facilitar toque em dispositivos móveis */
  touchFriendly?: boolean
  /** Otimiza para entrada de números */
  numeric?: boolean
  /** Otimiza para entrada de email */
  email?: boolean
  /** Otimiza para entrada de telefone */
  phone?: boolean
}

const MobileOptimizedInput = React.forwardRef<HTMLInputElement, MobileOptimizedInputProps>(
  ({ className, touchFriendly = true, numeric, email, phone, type, ...props }, ref) => {
    // Determina o tipo de input baseado nas props
    let inputType = type
    let inputMode: React.InputHTMLAttributes<HTMLInputElement>['inputMode'] = undefined
    let pattern: string | undefined = undefined

    if (numeric) {
      inputType = "number"
      inputMode = "numeric"
    } else if (email) {
      inputType = "email"
      inputMode = "email"
    } else if (phone) {
      inputType = "tel"
      inputMode = "tel"
      pattern = "[0-9]*"
    }

    return (
      <Input
        type={inputType}
        inputMode={inputMode}
        pattern={pattern}
        className={cn(
          // Base styles
          "w-full",
          // Mobile-friendly touch targets
          touchFriendly && "min-h-[44px] text-base",
          // Prevent zoom on iOS
          "text-[16px] sm:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
MobileOptimizedInput.displayName = "MobileOptimizedInput"

interface MobileOptimizedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** Adiciona padding extra para facilitar toque em dispositivos móveis */
  touchFriendly?: boolean
}

const MobileOptimizedTextarea = React.forwardRef<HTMLTextAreaElement, MobileOptimizedTextareaProps>(
  ({ className, touchFriendly = true, ...props }, ref) => {
    return (
      <Textarea
        className={cn(
          // Base styles
          "w-full",
          // Mobile-friendly touch targets
          touchFriendly && "min-h-[88px] text-base",
          // Prevent zoom on iOS
          "text-[16px] sm:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
MobileOptimizedTextarea.displayName = "MobileOptimizedTextarea"

export { MobileOptimizedInput, MobileOptimizedTextarea }