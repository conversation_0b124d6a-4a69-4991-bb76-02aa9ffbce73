/* Estilos específicos para dispositivos móveis */

/* Ajustes para o layout principal */
.has-mobile-menu {
  padding-top: 3.5rem;
}

/* Ajustes para o menu lateral */
.sidebar-mobile-padded {
  padding-top: 1.5rem;
}

.sidebar-mobile-open {
  padding-top: 0;
  overflow: hidden;
}

/* Ajustes gerais para mobile */
@media screen and (max-width: 768px) {
  /* Ajuste de tamanho de fonte para mobile */
  html {
    font-size: 14px;
  }
  
  /* Ajustes no padding para conteúdo principal */
  .layout-content {
    padding: 1rem 0.75rem;
  }
  
  /* Ajustes em títulos */
  h1 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  /* Cards mais compactos em mobile */
  .card-mobile {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }
  
  /* Botões mais fáceis de clicar */
  .button-mobile {
    min-height: 2.75rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Menu mobile */
  .mobile-menu-button {
    position: fixed;
    top: 0.5rem;
    left: 0.5rem;
    z-index: 50;
    width: 3.5rem;
    height: 3.5rem;
    background-color: rgb(220, 38, 38);
    border-radius: 50%;
    border: 2px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  /* Espaço para o botão do menu */
  .mobile-menu-offset {
    padding-left: 3.5rem;
  }
  
  /* Ajuste nos formulários */
  .form-mobile-spacing .form-item {
    margin-bottom: 0.75rem;
  }
  
  /* Grid de uma coluna em mobile */
  .grid-cols-1-mobile {
    grid-template-columns: 1fr;
  }
  
  /* Espaço para o botão de menu fixo */
  .has-fixed-menu {
    padding-top: 4rem;
  }
  
  /* Ajuste para tabelas em telas pequenas */
  .mobile-table {
    display: block;
    overflow-x: auto;
    width: 100%;
    white-space: nowrap;
  }
  
  /* Aumento dos botões para toque */
  button, 
  [role="button"],
  .clickable {
    min-height: 2.5rem;
    min-width: 2.5rem;
  }
  
  /* Espaço extra para conteúdo abaixo do último item para evitar que 
     botões fixos no fundo da tela escondam conteúdo */
  .pb-safe {
    padding-bottom: 5rem;
  }
  
  /* Ajustes para o layout */
  .sidebar-open .layout-main {
    margin-left: 0;
  }
  
  /* Ajustes para cabeçalhos de página */
  .page-header {
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
  }
  
  .page-header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  /* Cavalos lateral compactado */
  .cavalos-lateral-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: auto;
    max-height: 40vh;
    z-index: 30;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  /* Overlay para quando o menu mobile está aberto */
  .mobile-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
  }
  
  /* Menu mobile styles */
  .sidebar-mobile-menu {
    height: 100vh;
    overflow-y: auto;
    width: 100%;
    max-width: 280px;
    background-color: #0A3364;
    padding: 1rem 0;
  }
  
  .sidebar-mobile-menu a, 
  .sidebar-mobile-menu button {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    color: white;
    font-size: 1rem;
    text-align: left;
    border-radius: 0;
    border-left: 3px solid transparent;
  }
  
  .sidebar-mobile-menu a.active,
  .sidebar-mobile-menu button.active {
    background-color: #134282;
    border-left-color: white;
  }
  
  /* Submenu styling */
  .sidebar-mobile-submenu {
    padding-left: 1rem;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    margin-left: 1rem;
  }
  
  /* Improve button clickability in mobile menu */
  .menu-button-large {
    min-height: 3rem;
    padding: 0.75rem 1rem;
  }
  
  /* Ajustes específicos para a seção de genética */
  .genetics-mobile {
    overflow-x: auto;
    min-height: 400px;
  }
  
  /* Árvore genealógica em mobile */
  .genealogy-tree-mobile {
    transform: scale(0.8);
    transform-origin: top center;
  }
  
  /* Nós da árvore em mobile */
  .genealogy-node-mobile {
    min-width: 120px !important;
    padding: 8px !important;
  }
  
  /* Cards de pai/mãe em mobile */
  .parent-cards-mobile {
    gap: 8px !important;
    padding: 12px !important;
  }
  
  /* Controles de zoom em mobile */
  .zoom-controls-mobile {
    flex-direction: column !important;
    gap: 4px !important;
    right: 8px !important;
    top: 8px !important;
  }
  
  /* Texto responsivo na genética */
  .genetics-text {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  
  /* Melhorar visibilidade de botões pequenos em mobile */
  .genetics-button-mobile {
    min-height: 40px !important;
    min-width: 40px !important;
    padding: 8px !important;
  }
  
  /* Ajustes para cards de genealogia em mobile */
  .genealogy-card-mobile {
    padding: 12px !important;
    margin: 8px 0 !important;
  }
  
  /* Melhorar quebra de texto em nomes longos */
  .horse-name-mobile {
    word-break: break-word;
    hyphens: auto;
    line-height: 1.2;
  }
}