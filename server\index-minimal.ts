import express from "express";
import { addApiRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { createServer } from "http";

const app = express();

// Basic middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Add API routes
addApiRoutes(app);

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

const server = createServer(app);

(async () => {
  // Setup Vite for development
  const nodeEnv = process.env.NODE_ENV || "development";
  if (nodeEnv === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  const port = 5000;
  
  // Start server immediately
  server.listen(port, "0.0.0.0", () => {
    log(`serving on port ${port}`);
    
    // Load and run full initialization after server is running
    setTimeout(async () => {
      try {
        const { default: initializeApp } = await import('./init-after-start');
        await initializeApp();
      } catch (error) {
        console.error('Error during post-startup initialization:', error);
      }
    }, 100);
  });

  server.on('error', (error) => {
    console.error('Server error:', error);
  });
})();