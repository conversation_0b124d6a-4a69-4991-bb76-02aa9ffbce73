import express from "express";
import cors from "cors";
import { createServer } from "http";
import path from "path";
import { fileURLToPath } from 'url';
import { addApiRoutes } from "./routes.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = Number(process.env.PORT) || 3000;

console.log('Iniciando servidor de produção...');
console.log('Porta:', port);
console.log('NODE_ENV:', process.env.NODE_ENV);

// Middlewares básicos
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: false, limit: '2mb' }));

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'EquiGestor AI Online!',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime()),
    port: port,
    env: process.env.NODE_ENV
  });
});

// Configurar rotas da API
const setupRoutes = async () => {
  try {
    await addApiRoutes(app);
    console.log('Rotas da API configuradas');
  } catch (error) {
    console.error('Erro ao configurar rotas:', error);
  }
};

setupRoutes();

// Servir arquivos estáticos do frontend
const publicPath = path.join(__dirname, '../dist/public');
console.log('Servindo arquivos estáticos de:', publicPath);
app.use(express.static(publicPath));

// Rota catch-all para SPA
app.get('*', (_req, res) => {
  const indexPath = path.join(publicPath, 'index.html');
  console.log('Servindo index.html para SPA:', indexPath);
  res.sendFile(indexPath);
});

const server = createServer(app);

server.listen(port, () => {
  console.log(`🎉 EquiGestor AI funcionando na porta ${port}`);
  console.log(`Ambiente: ${process.env.NODE_ENV || 'production'}`);
  console.log(`URL: http://localhost:${port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM recebido, fechando servidor...');
  server.close(() => {
    console.log('Servidor fechado.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT recebido, fechando servidor...');
  server.close(() => {
    console.log('Servidor fechado.');
    process.exit(0);
  });
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});