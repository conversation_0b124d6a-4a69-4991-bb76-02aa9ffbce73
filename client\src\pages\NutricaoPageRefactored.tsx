import React, { useState } from 'react';
import { useNutricao } from '@/hooks/use-nutricao';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { 
  Plus, 
  Search, 
  Filter, 
  ChevronDown, 
  Utensils,
  GraduationCap,
  Clipboard
} from 'lucide-react';
import HorseSelector from '@/components/nutricao/HorseSelector';
import { NutricaoCard } from '@/components/nutricao/NutricaoCard';
import { NutricaoForm, NutricaoFormValues } from '@/components/nutricao/NutricaoForm';
import { EstatisticasNutricao } from '@/components/nutricao/EstatsticasNutricao';
import { TipoAlimentacaoDistribuicao } from '@/components/nutricao/TipoAlimentacaoDistribuicao';

/**
 * Página de Nutrição refatorada com aplicação das boas práticas
 * 
 * Implementa:
 * - Componentização
 * - Hooks personalizados para gerenciar estado
 * - Memoização para evitar re-renderizações desnecessárias
 * - Código limpo e bem organizado
 */
const NutricaoPageRefactored: React.FC = () => {
  // Usar o hook personalizado para toda a lógica de nutrição
  const nutricao = useNutricao();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedNutricaoId, setSelectedNutricaoId] = useState<number | null>(null);

  // Encontrar o item selecionado para edição
  const selectedNutricao = selectedNutricaoId 
    ? nutricao.nutricoesFiltradas.find(item => item.id === selectedNutricaoId) 
    : null;

  // Handler para abrir o diálogo de edição
  const handleEdit = (id: number) => {
    setSelectedNutricaoId(id);
    setIsEditDialogOpen(true);
  };

  // Handler para o envio do formulário de adição
  const handleAddSubmit = (values: NutricaoFormValues) => {
    nutricao.addNutricaoMutation.mutate(values);
    setIsAddDialogOpen(false);
  };

  // Handler para o envio do formulário de edição
  const handleEditSubmit = (values: NutricaoFormValues) => {
    if (selectedNutricaoId) {
      nutricao.updateNutricaoMutation.mutate({ 
        ...values, 
        id: selectedNutricaoId 
      });
      setIsEditDialogOpen(false);
      setSelectedNutricaoId(null);
    }
  };

  // Handler para excluir um registro
  const handleDelete = (id: number) => {
    nutricao.deleteNutricaoMutation.mutate(id);
  };

  // Valores padrão para o formulário de edição
  const getEditDefaultValues = () => {
    if (!selectedNutricao) return {};
    
    return {
      horse_id: selectedNutricao.horse_id,
      data: selectedNutricao.data,
      tipoAlimentacao: selectedNutricao.tipoAlimentacao,
      nomeAlimento: selectedNutricao.nomeAlimento,
      quantidade: selectedNutricao.quantidade,
      unidadeMedida: selectedNutricao.unidadeMedida,
      frequenciaDiaria: selectedNutricao.frequenciaDiaria,
      horarios: selectedNutricao.horarios || "",
      custoUnitario: selectedNutricao.custoUnitario,
      observacoes: selectedNutricao.observacoes || "",
      recomendacao: selectedNutricao.recomendacao || "",
      status: selectedNutricao.status,
      fornecedor: selectedNutricao.fornecedor || "",
    };
  };

  return (
    <div className="container py-6">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Seletor de cavalos */}
        <div className="w-full lg:w-1/4">
          <HorseSelector 
            cavalos={nutricao.cavalosQuery.data || []}
            selectedHorseId={nutricao.selectedHorseId}
            onSelectHorse={nutricao.setSelectedHorseId}
            isLoading={nutricao.cavalosQuery.isLoading}
          />
        </div>

        {/* Painel principal */}
        <div className="w-full lg:w-3/4">
          {nutricao.selectedHorse ? (
            <Tabs defaultValue="historico" className="space-y-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <TabsList>
                  <TabsTrigger value="historico" className="flex items-center gap-2">
                    <Clipboard className="h-4 w-4" />
                    <span>Histórico de Alimentação</span>
                  </TabsTrigger>
                  <TabsTrigger value="resumo" className="flex items-center gap-2">
                    <GraduationCap className="h-4 w-4" />
                    <span>Resumo Nutricional</span>
                  </TabsTrigger>
                </TabsList>

                <div className="flex items-center space-x-2">
                  <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        <span>Adicionar Nutrição</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle>Adicionar Registro de Nutrição</DialogTitle>
                        <DialogDescription>
                          Cadastre um novo plano alimentar para {nutricao.selectedHorse.name}
                        </DialogDescription>
                      </DialogHeader>
                      
                      <NutricaoForm 
                        onSubmit={handleAddSubmit}
                        isSubmitting={nutricao.addNutricaoMutation.isPending}
                        horse_id={nutricao.selectedHorseId || 0}
                      />
                      
                      <DialogFooter>
                        <Button 
                          type="button" 
                          variant="outline" 
                          onClick={() => setIsAddDialogOpen(false)}
                        >
                          Cancelar
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              {/* Aba de histórico */}
              <TabsContent value="historico" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-xl">Plano Nutricional de {nutricao.selectedHorse.name}</CardTitle>
                    <CardDescription>
                      Gerencie a alimentação e suplementação do animal
                    </CardDescription>
                    
                    <div className="flex flex-col sm:flex-row gap-2 mt-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Buscar por nome, tipo ou fornecedor..."
                          className="pl-8"
                          value={nutricao.searchTerm}
                          onChange={(e) => nutricao.setSearchTerm(e.target.value)}
                        />
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="flex gap-1 items-center w-full sm:w-auto">
                            <Filter className="h-4 w-4" />
                            <span>Filtrar</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem onClick={() => nutricao.setFilterType(null)}>
                            Todos os tipos
                          </DropdownMenuItem>
                          {nutricao.tiposAlimentacao.map((tipo) => (
                            <DropdownMenuItem key={tipo} onClick={() => nutricao.setFilterType(tipo)}>
                              {tipo}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {nutricao.nutricoesQuery.isLoading ? (
                      <div className="flex justify-center items-center h-48">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                      </div>
                    ) : nutricao.nutricoesFiltradas.length === 0 ? (
                      <div className="text-center py-8">
                        <Utensils className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900">Nenhum registro de nutrição encontrado</h3>
                        <p className="text-gray-500 mt-2">
                          {nutricao.searchTerm || nutricao.filterType 
                            ? "Tente ajustar os filtros de busca" 
                            : "Adicione um plano nutricional para este animal"}
                        </p>
                        <Button 
                          className="mt-4" 
                          onClick={() => {
                            nutricao.setSearchTerm("");
                            nutricao.setFilterType(null);
                            setIsAddDialogOpen(true);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar Nutrição
                        </Button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {nutricao.nutricoesFiltradas.map((item) => (
                          <NutricaoCard 
                            key={item.id}
                            nutricao={item} 
                            onEdit={() => handleEdit(item.id)} 
                            onDelete={handleDelete}
                            calculateMonthlyCost={nutricao.calculateMonthlyCost}
                          />
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Aba de resumo nutricional */}
              <TabsContent value="resumo" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Resumo Nutricional de {nutricao.selectedHorse.name}</CardTitle>
                    <CardDescription>
                      Visão geral da dieta e recomendações nutricionais
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {nutricao.nutricoesQuery.isLoading ? (
                      <div className="flex justify-center items-center h-48">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                      </div>
                    ) : nutricao.nutricoesFiltradas.length === 0 ? (
                      <div className="text-center py-8">
                        <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900">Sem dados nutricionais</h3>
                        <p className="text-gray-500 mt-2">
                          Adicione registros de nutrição para visualizar o resumo
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {/* Resumo de custos */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Custo Nutricional Mensal</h3>
                          <EstatisticasNutricao 
                            nutricoes={nutricao.nutricoesFiltradas} 
                            calculateMonthlyCost={nutricao.calculateMonthlyCost}
                          />
                        </div>
                        
                        {/* Distribuição por tipo de alimento */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Distribuição por Tipo de Alimento</h3>
                          <TipoAlimentacaoDistribuicao 
                            nutricoes={nutricao.nutricoesFiltradas}
                            calculateMonthlyCost={nutricao.calculateMonthlyCost}
                          />
                        </div>
                        
                        {/* Recomendações */}
                        <div>
                          <h3 className="text-lg font-medium mb-4">Recomendações e Observações</h3>
                          <Card>
                            <CardContent className="pt-6">
                              <div className="space-y-4">
                                {nutricao.nutricoesFiltradas.filter(n => n.recomendacao || n.observacoes).length === 0 ? (
                                  <p className="text-gray-500 text-center py-4">
                                    Sem recomendações ou observações registradas
                                  </p>
                                ) : (
                                  nutricao.nutricoesFiltradas.filter(n => n.recomendacao || n.observacoes).map(n => (
                                    <div key={n.id} className="border-b pb-4 last:border-0">
                                      <h4 className="font-medium">{n.nomeAlimento} ({n.tipoAlimentacao})</h4>
                                      
                                      {n.recomendacao && (
                                        <div className="mt-2">
                                          <h5 className="text-sm font-medium text-gray-500">Recomendação:</h5>
                                          <p className="text-sm mt-1">{n.recomendacao}</p>
                                        </div>
                                      )}
                                      
                                      {n.observacoes && (
                                        <div className="mt-2">
                                          <h5 className="text-sm font-medium text-gray-500">Observações:</h5>
                                          <p className="text-sm mt-1">{n.observacoes}</p>
                                        </div>
                                      )}
                                    </div>
                                  ))
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-64">
                <Utensils className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Selecione um animal</h3>
                <p className="text-gray-500 mt-2">
                  Escolha um animal no painel lateral para gerenciar a nutrição
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Dialog para editar nutrição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Registro de Nutrição</DialogTitle>
            <DialogDescription>
              Atualize as informações do plano alimentar para {nutricao.selectedHorse?.name}
            </DialogDescription>
          </DialogHeader>
          
          <NutricaoForm 
            onSubmit={handleEditSubmit}
            defaultValues={getEditDefaultValues()}
            isSubmitting={nutricao.updateNutricaoMutation.isPending}
            horse_id={nutricao.selectedHorseId || 0}
          />
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                setIsEditDialogOpen(false);
                setSelectedNutricaoId(null);
              }}
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NutricaoPageRefactored;