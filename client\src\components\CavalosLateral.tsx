import { useState, useEffect } from 'react';
import { Link, useLocation, useRoute } from 'wouter';
import { <PERSON><PERSON><PERSON> } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { ChevronRight, ChevronUp, ChevronDown, Search, Plus, X, ChevronLeft } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useIsMobile, useScreenSize } from '@/hooks/use-mobile';

/**
 * Componente CavalosLateral
 * 
 * Mostra a lista de cavalos do usuário em um painel lateral
 * que pode ser expandido/contraído, com suporte responsivo para mobile
 */
export function CavalosLateral() {
  const [cavalos, setCavalos] = useState<Cavalo[]>([]);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [collapsed, setCollapsed] = useState(true);
  const [filtro, setFiltro] = useState('');
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { isTablet } = useScreenSize();
  const [, navigate] = useLocation(); // Hook do wouter para navegação

  // Ajusta o tamanho e posição para mobile com melhor responsividade
  const positionClass = isMobile 
    ? "fixed bottom-0 left-0 right-0 z-40" 
    : "fixed right-2 top-20 z-30";
  
  const dimensionClass = isMobile 
    ? "w-full h-[65vh] sm:h-[70vh] rounded-t-xl rounded-b-none" 
    : "w-64 h-[calc(100vh-12rem)] rounded-lg";

  const toggleButtonClass = isMobile
    ? "fixed bottom-16 right-4 z-40 h-12 w-12 sm:h-14 sm:w-14 rounded-full shadow-lg btn-mobile"
    : "fixed right-0 top-80 z-30 h-32 w-6 rounded-l-md rounded-r-none shadow-md hover:w-8";

  const toggleButtonContent = isMobile
    ? <Plus className="h-5 w-5 sm:h-6 sm:w-6" />
    : <ChevronRight className="h-4 w-4 group-hover:scale-125 transition-transform" />;

  // Buscar usuário do localStorage
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error("Erro ao carregar usuário:", error);
    }
  }, []);
  
  // Buscar lista de cavalos
  useEffect(() => {
    if (user) {
      const fetchCavalos = async () => {
        try {
          setLoading(true);
          const data = await apiRequest<Cavalo[]>('/api/cavalos', 'GET');
          // Ensure data is always an array
          setCavalos(Array.isArray(data) ? data : []);
        } catch (error) {
          console.error("Erro ao buscar cavalos:", error);
          setCavalos([]); // Set empty array on error
          toast({
            title: "Erro",
            description: "Não foi possível carregar a lista de cavalos",
            variant: "destructive"
          });
        } finally {
          setLoading(false);
        }
      };
      
      fetchCavalos();
    }
  }, [user, toast]);
  
  // Filtrar cavalos pelo nome ou raça - ensure cavalos is always an array
  const cavalosFiltrados = (Array.isArray(cavalos) ? cavalos : []).filter(cavalo => 
    cavalo.name.toLowerCase().includes(filtro.toLowerCase()) || 
    (cavalo.breed && cavalo.breed.toLowerCase().includes(filtro.toLowerCase()))
  );

  if (!expanded) {
    return (
      <div className={isMobile ? "fixed bottom-16 right-4 z-40" : "fixed right-0 top-80 z-30"}>
        <Button 
          onClick={() => {
            setCollapsed(false);
            setExpanded(true);
          }}
          className={`bg-[#0A3364] hover:bg-[#134282] text-white transition-all group ${toggleButtonClass}`}
          variant="default"
        >
          {toggleButtonContent}
        </Button>
      </div>
    );
  }

  // Overlay para mobile
  const MobileOverlay = () => isMobile && (
    <div
      className="fixed inset-0 bg-black/30 z-30"
      onClick={() => {
        setCollapsed(true);
        setTimeout(() => setExpanded(false), 300);
      }}
    />
  );

  return (
    <>
      {MobileOverlay()}
      <Card className={`${positionClass} ${dimensionClass} shadow-xl border-blue-200 overflow-hidden transition-all duration-300 ${collapsed ? 'opacity-0 transform translate-x-full' : 'opacity-100 transform translate-x-0'}`}>
        <CardHeader className="bg-[#0A3364] text-white py-2 px-3 sm:px-4 flex flex-row items-center justify-between space-y-0 card-mobile">
          <CardTitle className={`${isMobile ? 'text-sm sm:text-base' : 'text-sm'} font-medium flex items-center`}>
            {isMobile ? <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 mr-2" /> : <ChevronUp className="h-4 w-4 mr-2" />}
            Meus Animais
          </CardTitle>
          <Button
            variant="ghost"
            size={isMobile ? "sm" : "icon"}
            className={`${isMobile ? 'h-7 w-7 sm:h-8 sm:w-8' : 'h-5 w-5'} text-white hover:bg-[#134282] hover:text-white btn-mobile`}
            onClick={() => {
              setCollapsed(true);
              setTimeout(() => setExpanded(false), 300);
            }}
          >
            <X className={isMobile ? "h-4 w-4 sm:h-5 sm:w-5" : "h-4 w-4"} />
          </Button>
        </CardHeader>
        <CardContent className="p-0 overflow-hidden flex flex-col h-full">
          <div className="p-2 sm:p-3 border-b bg-blue-50">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-500" />
              <Input
                type="text"
                placeholder="Buscar animal por nome ou raça..."
                className="pl-8 py-1 text-sm border-blue-200 focus:border-blue-400 focus:ring-blue-400 transition-colors"
                value={filtro}
                onChange={(e) => setFiltro(e.target.value)}
                autoFocus={!isMobile}
              />
            </div>
            {filtro && (
              <div className="mt-1 text-xs text-blue-600 flex items-center">
                <span>Filtrando: {filtro}</span>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-4 w-4 p-0 ml-1 text-blue-600 hover:text-blue-800" 
                  onClick={() => setFiltro('')}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-500">Carregando...</span>
              </div>
            ) : cavalosFiltrados.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {filtro.length > 0 ? (
                  <span>Nenhum animal encontrado com este termo</span>
                ) : (
                  <span>Nenhum animal cadastrado</span>
                )}
              </div>
            ) : (
              <ul className="divide-y">
                {cavalosFiltrados.map(cavalo => (
                  <li key={cavalo.id}>
                    <a 
                      href={`/cavalo/${cavalo.id}`}
                      onClick={(e) => {
                        e.preventDefault();
                        // Fecha o painel lateral primeiro
                        setCollapsed(true);
                        // Navega após um pequeno delay
                        setTimeout(() => {
                          navigate(`/cavalo/${cavalo.id}`);
                          setExpanded(false);
                        }, 100);
                      }}
                      className={`block ${isMobile ? 'p-4' : 'p-2'} hover:bg-blue-50 cursor-pointer transition-colors border-l-2 border-transparent hover:border-blue-400`}
                    >
                      <div className={`${isMobile ? 'text-base' : 'text-sm'} font-medium text-gray-900 flex items-center`}>
                        <ChevronRight className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'} mr-1 text-blue-500`} />
                        {cavalo.name}
                      </div>
                      <div className="flex items-center mt-1 flex-wrap gap-1">
                        <Badge variant="outline" className={`${isMobile ? 'text-sm' : 'text-xs'} bg-gray-50 mr-1`}>
                          {cavalo.breed}
                        </Badge>
                        {cavalo.sexo && (
                          <Badge variant="outline" className={`${isMobile ? 'text-sm' : 'text-xs'} ${cavalo.sexo.toLowerCase().includes('fêmea') || cavalo.sexo.toLowerCase().includes('femea') ? 'bg-pink-50 text-pink-800' : 'bg-blue-50 text-blue-800'}`}>
                            {cavalo.sexo}
                          </Badge>
                        )}
                      </div>
                    </a>
                  </li>
                ))}
              </ul>
            )}
          </div>
          

        </CardContent>
      </Card>
    </>
  );
}

export default CavalosLateral;