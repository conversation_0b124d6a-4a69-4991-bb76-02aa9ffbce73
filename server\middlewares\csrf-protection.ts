import { doubleCsrf } from 'csrf-csrf';
import { Request, Response, NextFunction } from 'express';
import { createError } from '../error-handler';

// Configuração para proteção CSRF
const csrfProtectionConfig = {
  getSecret: () => process.env.CSRF_SECRET || 'default-csrf-secret-equigestor',
  cookieOptions: {
    httpOnly: true,
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' as const : 'lax' as const,
    secure: process.env.NODE_ENV === 'production',
    path: '/',
  },
  size: 64, // Tamanho do token em bytes
};

// Criar a instância de proteção CSRF
const { doubleCsrfProtection, generateToken } = doubleCsrf(csrfProtectionConfig);

/**
 * Middleware para injetar o token CSRF na resposta
 */
export const csrfInjection = (req: Request, res: Response, next: NextFunction) => {
  // Apenas para roteamento a partir do servidor
  if (req.path === '/' || req.path.startsWith('/api/csrf-token')) {
    const token = generateToken(res, req);
    // Adicionamos o token ao objeto de resposta para usá-lo nos templates
    res.locals = res.locals || {};
    res.locals.csrfToken = token;
  }
  next();
};

/**
 * Middleware para fornecer o token CSRF via API
 */
export const getCsrfToken = (req: Request, res: Response) => {
  const token = generateToken(res, req);
  res.json({ csrfToken: token });
};

/**
 * Middleware customizado para tratamento de erros CSRF
 */
export const csrfErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err && err.code === 'EBADCSRFTOKEN') {
    return next(createError.authorization(
      'Token CSRF inválido. Possível ataque de Cross-Site Request Forgery.',
      { 
        path: req.path, 
        method: req.method,
        origin: req.headers.origin || 'unknown'
      }
    ));
  }
  
  // Passar outros erros para o próximo middleware
  next(err);
};

export { doubleCsrfProtection as csrfProtection };