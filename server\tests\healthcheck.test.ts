/**
 * Testes para o sistema de healthcheck
 */
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import express from 'express';
import { healthcheck } from '../health/healthcheck';
import { requestLogger } from '../middlewares/logging';

describe('Healthcheck', () => {
  let app: express.Application;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use(requestLogger);
    app.get('/healthz', healthcheck);
  });

  afterAll(() => {
    // Cleanup if needed
  });

  it('should return health status', async () => {
    const response = await request(app)
      .get('/healthz')
      .expect('Content-Type', /json/);

    expect(response.body).toHaveProperty('status');
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('uptime');
    expect(response.body).toHaveProperty('services');
    expect(response.body).toHaveProperty('metrics');

    // Verificar estrutura dos serviços
    expect(response.body.services).toHaveProperty('database');
    expect(response.body.services).toHaveProperty('firebase');
    expect(response.body.services).toHaveProperty('openai');

    // Verificar estrutura das métricas
    expect(response.body.metrics).toHaveProperty('memory');
    expect(response.body.metrics).toHaveProperty('process');
  });

  it('should return valid status values', async () => {
    const response = await request(app)
      .get('/healthz');

    const validStatuses = ['healthy', 'unhealthy', 'degraded'];
    expect(validStatuses).toContain(response.body.status);

    // Verificar status dos serviços
    Object.values(response.body.services).forEach((service: any) => {
      expect(['healthy', 'unhealthy']).toContain(service.status);
      expect(service).toHaveProperty('lastCheck');
    });
  });

  it('should include response times for healthy services', async () => {
    const response = await request(app)
      .get('/healthz');

    Object.values(response.body.services).forEach((service: any) => {
      if (service.status === 'healthy') {
        expect(service).toHaveProperty('responseTime');
        expect(typeof service.responseTime).toBe('number');
        expect(service.responseTime).toBeGreaterThan(0);
      }
    });
  });

  it('should include error messages for unhealthy services', async () => {
    const response = await request(app)
      .get('/healthz');

    Object.values(response.body.services).forEach((service: any) => {
      if (service.status === 'unhealthy') {
        expect(service).toHaveProperty('error');
        expect(typeof service.error).toBe('string');
      }
    });
  });

  it('should return appropriate HTTP status codes', async () => {
    const response = await request(app)
      .get('/healthz');

    if (response.body.status === 'healthy') {
      expect(response.status).toBe(200);
    } else if (response.body.status === 'degraded') {
      expect(response.status).toBe(207);
    } else if (response.body.status === 'unhealthy') {
      expect(response.status).toBe(503);
    }
  });

  it('should include system metrics', async () => {
    const response = await request(app)
      .get('/healthz');

    const { memory, process } = response.body.metrics;

    // Verificar métricas de memória
    expect(memory).toHaveProperty('used');
    expect(memory).toHaveProperty('total');
    expect(memory).toHaveProperty('percentage');
    expect(typeof memory.used).toBe('number');
    expect(typeof memory.total).toBe('number');
    expect(typeof memory.percentage).toBe('number');
    expect(memory.percentage).toBeGreaterThanOrEqual(0);
    expect(memory.percentage).toBeLessThanOrEqual(100);

    // Verificar métricas do processo
    expect(process).toHaveProperty('pid');
    expect(process).toHaveProperty('uptime');
    expect(process).toHaveProperty('version');
    expect(typeof process.pid).toBe('number');
    expect(typeof process.uptime).toBe('number');
    expect(typeof process.version).toBe('string');
  });
});