# Final CamelCase Audit Report

## Database Schema Issues Found

### Table: genealogia
- File: shared/schema.ts
- Issues:
  - `avoPaternoId` → `avo_paterno_id`
  - `avoPaternoNome` → `avo_paterno_nome`
  - `avoMaternoId` → `avo_materno_id`
  - `avoMaternoNome` → `avo_materno_nome`
  - `bisavoPaternoPai` → `bisavo_paterno_pai`
  - `bisavoMaternaPaterno` → `bisavo_materna_paterno`

### Table: manejos
- File: shared/schema.ts
- Issues:
  - `horaInicio` → `hora_inicio`
  - `horaFim` → `hora_fim`
  - `updatedAt` → `updated_at`

### Table: cavalos
- File: shared/schema.ts
- Issues:
  - `isExternal` → `is_external`

### Enum Issues
- File: shared/schema.ts
- Issues:
  - `horseSexoEnum` values need snake_case
  - `horseStatusEnum` values need snake_case

### Service Files with SQL Issues
- File: server/abccc-import-service.ts
- Issues: Mixed camelCase/snake_case in SQL queries
- File: server/genealogy-sync-service.ts
- Issues: Field name mismatches

### Utility Functions
- File: server/abccc-import-service.ts
- Function: `normalizarSexo()` returns Title Case instead of snake_case

## Current Error Context
SQL syntax error "syntax error at or near 'where'" in ABCCC import due to schema misalignment.