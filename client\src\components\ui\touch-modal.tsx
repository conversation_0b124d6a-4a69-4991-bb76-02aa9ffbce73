import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

/**
 * TouchModal
 * 
 * Componente que adiciona suporte a gestos touch para modais em dispositivos móveis.
 * Permite fechar o modal deslizando para baixo.
 */

interface TouchModalProps extends React.HTMLAttributes<HTMLDivElement> {
  onClose?: () => void;
  threshold?: number; // Limiar para considerar o gesto como "fechar"
  children: React.ReactNode;
}

const TouchModal = React.forwardRef<HTMLDivElement, TouchModalProps>(
  ({ className, onClose, threshold = 100, children, ...props }, ref) => {
    const isMobile = useIsMobile();
    const [startY, setStartY] = React.useState<number | null>(null);
    const [currentY, setCurrentY] = React.useState<number | null>(null);
    const [isDragging, setIsDragging] = React.useState(false);
    
    // Só aplicar gestos touch em dispositivos móveis
    if (!isMobile) {
      return (
        <div ref={ref} className={className} {...props}>
          {children}
        </div>
      );
    }
    
    const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
      setStartY(e.touches[0].clientY);
    };
    
    const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
      if (startY === null) return;
      
      const currentTouchY = e.touches[0].clientY;
      const deltaY = currentTouchY - startY;
      
      // Só permitir arrastar para baixo (deltaY positivo)
      if (deltaY > 0) {
        setCurrentY(deltaY);
        setIsDragging(true);
      }
    };
    
    const handleTouchEnd = () => {
      if (currentY !== null && currentY > threshold && onClose) {
        onClose();
      }
      
      // Resetar estado
      setStartY(null);
      setCurrentY(null);
      setIsDragging(false);
    };
    
    // Calcular transformação com base no arrasto
    const transform = isDragging && currentY !== null 
      ? `translateY(${currentY}px)` 
      : 'translateY(0)';
    
    // Calcular opacidade com base no arrasto
    const opacity = isDragging && currentY !== null 
      ? Math.max(1 - currentY / (threshold * 2), 0.5) 
      : 1;
    
    return (
      <div
        ref={ref}
        className={cn(
          "touch-modal-container",
          isDragging && "touch-modal-dragging",
          className
        )}
        style={{
          transform,
          opacity,
          transition: isDragging ? 'none' : 'transform 0.2s ease, opacity 0.2s ease',
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        {...props}
      >
        {/* Indicador visual de arrasto */}
        <div className="touch-modal-drag-indicator w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4" />
        
        {children}
      </div>
    );
  }
);

TouchModal.displayName = "TouchModal";

export { TouchModal };

/**
 * Função utilitária para implementar recursivamente o suporte a gestos touch
 * 
 * Esta função implementa o padrão de recursão para processar uma árvore de elementos
 * e adicionar suporte a gestos touch em modais.
 */
export function processModalTouchGestures(
  element: React.ReactNode,
  onClose?: () => void
): React.ReactNode {
  // Caso base: se o elemento for nulo, indefinido ou um tipo primitivo
  if (!element || typeof element !== 'object') {
    return element;
  }
  
  // Se for um elemento React
  if (React.isValidElement(element)) {
    // Verificar se é um modal ou diálogo
    const isModal = 
      element.type === 'div' && 
      element.props.className && 
      typeof element.props.className === 'string' &&
      (
        element.props.className.includes('dialog') || 
        element.props.className.includes('modal')
      );
    
    // Se for um modal, envolver com TouchModal
    if (isModal) {
      return (
        <TouchModal onClose={onClose}>
          {React.cloneElement(element, {
            ...element.props,
            children: processModalTouchGestures(element.props.children, onClose)
          })}
        </TouchModal>
      );
    }
    
    // Se tiver filhos, processar recursivamente
    if (element.props.children) {
      return React.cloneElement(element, {
        ...element.props,
        children: processModalTouchGestures(element.props.children, onClose)
      });
    }
    
    // Se não tiver filhos, retornar o elemento como está
    return element;
  }
  
  // Se for um array, processar cada elemento
  if (Array.isArray(element)) {
    return element.map(child => processModalTouchGestures(child, onClose));
  }
  
  // Para outros tipos de objetos, retornar como está
  return element;
}
