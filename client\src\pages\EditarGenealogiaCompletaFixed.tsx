import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';

interface Cavalo {
  id: number;
  name: string;
  breed: string;
  sexo: string;
  cor?: string;
  pelagem_id?: number;
  status: string;
  user_id: number;
  is_external: boolean;
  pai_id?: number;
  mae_id?: number;
}

interface GenealogiaDados {
  genealogia: {
    id: number;
    horse_id: number;
    pai?: string;
    mae?: string;
    avo_paterno_id?: number;
    avo_paterno?: string;
    avo_paterna_id?: number;
    avo_paterna?: string;
    avo_materno_id?: number;
    avo_materno?: string;
    avo_materna_id?: number;
    avo_materna?: string;
    bisavo_paterno_paterno?: string;
    bisavo_paterna_paterno?: string;
    bisavo_materno_paterno?: string;
    bisavo_materna_paterno?: string;
    bisavo_paterno_materno?: string;
    bisavo_paterna_materno?: string;
    bisavo_materno_materno?: string;
    bisavo_materna_materno?: string;
  };
  cavaloId: number;
}

interface SelecionadoItem {
  tipo: 'sistema' | 'externo' | 'nenhum';
  id?: number;
  nome?: string;
}

export default function EditarGenealogiaCompletaFixed({ cavaloId }: { cavaloId: string }) {
  if (!cavaloId) {
    const params = useParams<{ cavaloId: string }>();
    cavaloId = params.cavaloId;
  }
  const [, navigate] = useLocation();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [genealogiaDados, setGenealogiaDados] = useState<GenealogiaDados | null>(null);
  const [cavalosDisponiveis, setCavalosDisponiveis] = useState<Cavalo[]>([]);
  
  // Estados para cada campo
  const [paiSelecionado, setPaiSelecionado] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  const [maeSelecionada, setMaeSelecionada] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  const [avoPaternoSelecionado, setAvoPaternoSelecionado] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  const [avoMaternoSelecionado, setAvoMaternoSelecionado] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  const [avoPatenaSelecionada, setAvoPatenaSelecionada] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  const [avoMaternaSelecionada, setAvoMaternaSelecionada] = useState<SelecionadoItem>({ tipo: 'nenhum' });
  
  // Bisavós
  const [bisavoPaternoPaterno, setBisavoPaternoPaterno] = useState('');
  const [bisavoPaternaPaterno, setBisavoPaternaPaterno] = useState('');
  const [bisavoMaternoPaterno, setBisavoMaternoPaterno] = useState('');
  const [bisavoMaternaPaterno, setBisavoMaternaPaterno] = useState('');
  const [bisavoPaternoMaterno, setBisavoPaternoMaterno] = useState('');
  const [bisavoPaternaMaterno, setBisavoPaternaMaterno] = useState('');
  const [bisavoMaternoMaterno, setBisavoMaternoMaterno] = useState('');
  const [bisavoMaternaMaterno, setBisavoMaternaMaterno] = useState('');

  useEffect(() => {
    carregarDados();
  }, [cavaloId]);

  const carregarDados = async () => {
    try {
      setLoading(true);
      
      // Carregar dados em paralelo
      const [cavalos, cavalo, genealogia] = await Promise.all([
        apiRequest<Cavalo[]>('/api/cavalos-genealogia', 'GET'),
        apiRequest<any>(`/api/cavalos/${cavaloId}`, 'GET'),
        apiRequest<GenealogiaDados>(`/api/cavalos/${cavaloId}/genealogia`, 'GET')
      ]);

      console.log('🔥 Dados carregados:', { cavalos, cavalo, genealogia });
      
      setCavalosDisponiveis(cavalos);
      setGenealogiaDados(genealogia);
      
      // Preencher campos imediatamente
      preencherCampos(cavalo, genealogia.genealogia, cavalos);
      
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados da genealogia",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const preencherCampos = (cavalo: any, gen: any, cavalos: Cavalo[]) => {
    console.log('🔧 Preenchendo campos:', { cavalo, gen, cavalos });
    
    // Resetar tudo primeiro
    setPaiSelecionado({ tipo: 'nenhum' });
    setMaeSelecionada({ tipo: 'nenhum' });
    setAvoPaternoSelecionado({ tipo: 'nenhum' });
    setAvoMaternoSelecionado({ tipo: 'nenhum' });
    setAvoPatenaSelecionada({ tipo: 'nenhum' });
    setAvoMaternaSelecionada({ tipo: 'nenhum' });
    
    // Pai
    if (cavalo.pai_id) {
      const pai = cavalos.find(c => c.id === cavalo.pai_id);
      if (pai) {
        console.log('✅ Pai encontrado:', pai);
        setPaiSelecionado({ tipo: 'sistema', id: pai.id, nome: pai.name });
      }
    } else if (gen.pai) {
      console.log('✅ Pai externo:', gen.pai);
      setPaiSelecionado({ tipo: 'externo', nome: gen.pai });
    }
    
    // Mãe
    if (cavalo.mae_id) {
      const mae = cavalos.find(c => c.id === cavalo.mae_id);
      if (mae) {
        console.log('✅ Mãe encontrada:', mae);
        setMaeSelecionada({ tipo: 'sistema', id: mae.id, nome: mae.name });
      }
    } else if (gen.mae) {
      console.log('✅ Mãe externa:', gen.mae);
      setMaeSelecionada({ tipo: 'externo', nome: gen.mae });
    }
    
    // Avô Paterno
    if (gen.avo_paterno_id) {
      const avo = cavalos.find(c => c.id === gen.avo_paterno_id);
      if (avo) {
        console.log('✅ Avô paterno encontrado:', avo);
        setAvoPaternoSelecionado({ tipo: 'sistema', id: avo.id, nome: avo.name });
      }
    } else if (gen.avo_paterno) {
      console.log('✅ Avô paterno externo:', gen.avo_paterno);
      setAvoPaternoSelecionado({ tipo: 'externo', nome: gen.avo_paterno });
    }
    
    // Avô Materno
    if (gen.avo_materno_id) {
      const avo = cavalos.find(c => c.id === gen.avo_materno_id);
      if (avo) {
        console.log('✅ Avô materno encontrado:', avo);
        setAvoMaternoSelecionado({ tipo: 'sistema', id: avo.id, nome: avo.name });
      }
    } else if (gen.avo_materno) {
      console.log('✅ Avô materno externo:', gen.avo_materno);
      setAvoMaternoSelecionado({ tipo: 'externo', nome: gen.avo_materno });
    }
    
    // Avó Paterna
    if (gen.avo_paterna_id) {
      const avo = cavalos.find(c => c.id === gen.avo_paterna_id);
      if (avo) {
        console.log('✅ Avó paterna encontrada:', avo);
        setAvoPatenaSelecionada({ tipo: 'sistema', id: avo.id, nome: avo.name });
      }
    } else if (gen.avo_paterna) {
      console.log('✅ Avó paterna externa:', gen.avo_paterna);
      setAvoPatenaSelecionada({ tipo: 'externo', nome: gen.avo_paterna });
    }
    
    // Avó Materna
    if (gen.avo_materna_id) {
      const avo = cavalos.find(c => c.id === gen.avo_materna_id);
      if (avo) {
        console.log('✅ Avó materna encontrada:', avo);
        setAvoMaternaSelecionada({ tipo: 'sistema', id: avo.id, nome: avo.name });
      }
    } else if (gen.avo_materna) {
      console.log('✅ Avó materna externa:', gen.avo_materna);
      setAvoMaternaSelecionada({ tipo: 'externo', nome: gen.avo_materna });
    }
    
    // Bisavós
    setBisavoPaternoPaterno(gen.bisavo_paterno_paterno || '');
    setBisavoPaternaPaterno(gen.bisavo_paterna_paterno || '');
    setBisavoMaternoPaterno(gen.bisavo_materno_paterno || '');
    setBisavoMaternaPaterno(gen.bisavo_materna_paterno || '');
    setBisavoPaternoMaterno(gen.bisavo_paterno_materno || '');
    setBisavoPaternaMaterno(gen.bisavo_paterna_materno || '');
    setBisavoMaternoMaterno(gen.bisavo_materno_materno || '');
    setBisavoMaternaMaterno(gen.bisavo_materna_materno || '');
    
    console.log('🎉 Campos preenchidos com sucesso!');
  };

  const SelectorAnimal = ({ 
    titulo, 
    selecionado, 
    setSelecionado 
  }: { 
    titulo: string;
    selecionado: SelecionadoItem;
    setSelecionado: (item: SelecionadoItem) => void;
  }) => {
    return (
      <div className="space-y-3">
        <Label className="text-base font-semibold">{titulo}</Label>
        
        <Select value={selecionado.tipo} onValueChange={(tipo) => {
          if (tipo === 'nenhum') {
            setSelecionado({ tipo: 'nenhum' });
          } else {
            setSelecionado({ ...selecionado, tipo });
          }
        }}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione o tipo" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="nenhum">Não informado</SelectItem>
            <SelectItem value="sistema">Cavalo do Sistema</SelectItem>
            <SelectItem value="externo">Nome Externo</SelectItem>
          </SelectContent>
        </Select>
        
        {selecionado.tipo === 'sistema' && (
          <Select value={selecionado.id?.toString() || ''} onValueChange={(value) => {
            const cavalo = cavalosDisponiveis.find(c => c.id === parseInt(value));
            if (cavalo) {
              setSelecionado({ tipo: 'sistema', id: cavalo.id, nome: cavalo.name });
            }
          }}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione o cavalo" />
            </SelectTrigger>
            <SelectContent>
              {cavalosDisponiveis.map(cavalo => (
                <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                  {cavalo.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        {selecionado.tipo === 'externo' && (
          <Input
            placeholder="Digite o nome do cavalo"
            value={selecionado.nome || ''}
            onChange={(e) => setSelecionado({ tipo: 'externo', nome: e.target.value })}
          />
        )}
        
        {selecionado.tipo !== 'nenhum' && (
          <div className="flex items-center gap-2">
            <Badge variant={selecionado.tipo === 'sistema' ? 'default' : 'secondary'}>
              {selecionado.tipo === 'sistema' ? 'Sistema' : 'Externo'}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {selecionado.nome || 'Não informado'}
            </span>
          </div>
        )}
      </div>
    );
  };

  const handleSalvar = async () => {
    // Implementar salvamento aqui
    console.log('Salvando dados...');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(`/cavalos/${cavaloId}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <h1 className="text-2xl font-bold">Editar Genealogia Completa</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Pais */}
        <Card>
          <CardHeader>
            <CardTitle>Pais</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <SelectorAnimal
              titulo="Pai"
              selecionado={paiSelecionado}
              setSelecionado={setPaiSelecionado}
            />
            <Separator />
            <SelectorAnimal
              titulo="Mãe"
              selecionado={maeSelecionada}
              setSelecionado={setMaeSelecionada}
            />
          </CardContent>
        </Card>

        {/* Avós */}
        <Card>
          <CardHeader>
            <CardTitle>Avós</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <SelectorAnimal
              titulo="Avô Paterno"
              selecionado={avoPaternoSelecionado}
              setSelecionado={setAvoPaternoSelecionado}
            />
            <SelectorAnimal
              titulo="Avó Paterna"
              selecionado={avoPatenaSelecionada}
              setSelecionado={setAvoPatenaSelecionada}
            />
            <Separator />
            <SelectorAnimal
              titulo="Avô Materno"
              selecionado={avoMaternoSelecionado}
              setSelecionado={setAvoMaternoSelecionado}
            />
            <SelectorAnimal
              titulo="Avó Materna"
              selecionado={avoMaternaSelecionada}
              setSelecionado={setAvoMaternaSelecionada}
            />
          </CardContent>
        </Card>

        {/* Bisavós Paternos */}
        <Card>
          <CardHeader>
            <CardTitle>Bisavós Paternos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Bisavô Paterno Paterno</Label>
              <Input
                value={bisavoPaternoPaterno}
                onChange={(e) => setBisavoPaternoPaterno(e.target.value)}
                placeholder="Nome do bisavô paterno paterno"
              />
            </div>
            <div>
              <Label>Bisavó Paterna Paterna</Label>
              <Input
                value={bisavoPaternaPaterno}
                onChange={(e) => setBisavoPaternaPaterno(e.target.value)}
                placeholder="Nome da bisavó paterna paterna"
              />
            </div>
            <div>
              <Label>Bisavô Materno Paterno</Label>
              <Input
                value={bisavoMaternoPaterno}
                onChange={(e) => setBisavoMaternoPaterno(e.target.value)}
                placeholder="Nome do bisavô materno paterno"
              />
            </div>
            <div>
              <Label>Bisavó Materna Paterna</Label>
              <Input
                value={bisavoMaternaPaterno}
                onChange={(e) => setBisavoMaternaPaterno(e.target.value)}
                placeholder="Nome da bisavó materna paterna"
              />
            </div>
          </CardContent>
        </Card>

        {/* Bisavós Maternos */}
        <Card>
          <CardHeader>
            <CardTitle>Bisavós Maternos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Bisavô Paterno Materno</Label>
              <Input
                value={bisavoPaternoMaterno}
                onChange={(e) => setBisavoPaternoMaterno(e.target.value)}
                placeholder="Nome do bisavô paterno materno"
              />
            </div>
            <div>
              <Label>Bisavó Paterna Materna</Label>
              <Input
                value={bisavoPaternaMaterno}
                onChange={(e) => setBisavoPaternaMaterno(e.target.value)}
                placeholder="Nome da bisavó paterna materna"
              />
            </div>
            <div>
              <Label>Bisavô Materno Materno</Label>
              <Input
                value={bisavoMaternoMaterno}
                onChange={(e) => setBisavoMaternoMaterno(e.target.value)}
                placeholder="Nome do bisavô materno materno"
              />
            </div>
            <div>
              <Label>Bisavó Materna Materna</Label>
              <Input
                value={bisavoMaternaMaterno}
                onChange={(e) => setBisavoMaternaMaterno(e.target.value)}
                placeholder="Nome da bisavó materna materna"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6 flex justify-end">
        <Button onClick={handleSalvar} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Salvando...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Salvar Genealogia
            </>
          )}
        </Button>
      </div>
    </div>
  );
}