-- Migração para adicionar a coluna inspetor na tabela cavalos
-- Autor: EquiGestor Team
-- Data: 15/05/2025

-- Verificar se a coluna já existe para evitar erros
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'cavalos' 
        AND column_name = 'inspetor'
    ) THEN
        -- Adicionar a coluna inspetor
        ALTER TABLE cavalos ADD COLUMN inspetor TEXT;
        
        -- Log da migração
        RAISE NOTICE 'Coluna inspetor adicionada com sucesso à tabela cavalos';
    ELSE
        RAISE NOTICE 'Coluna inspetor já existe na tabela cavalos, nenhuma ação necessária';
    END IF;
END
$$;