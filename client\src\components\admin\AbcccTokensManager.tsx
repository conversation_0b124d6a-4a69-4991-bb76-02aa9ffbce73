import React, { useState } from 'react';
import {
  useAbcccTokens,
  useAbcccTokenStats,
  useAddAbcccToken,
  useInvalidateAbcccToken,
  useGenerateAbcccToken,
  useGenerateAutomaticTokens
} from '@/hooks/use-abccc-token';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, RefreshCw, Plus, Shield, CheckCircle, XCircle, Info, BarChart2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { AbcccToken, TokenStats } from '@shared/schema-tokens';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function AbcccTokensManager() {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [generateDialogOpen, setGenerateDialogOpen] = useState(false);
  const [batchGenerateDialogOpen, setBatchGenerateDialogOpen] = useState(false);
  const [newToken, setNewToken] = useState('');
  const [tokenOrigem, setTokenOrigem] = useState('manual');
  const [tokenObservacoes, setTokenObservacoes] = useState('');
  const [batchQuantidade, setBatchQuantidade] = useState(5);
  
  // Toast hook
  const { toast } = useToast();
  
  // Queries e mutations
  const { data: tokens = [], isLoading: isLoadingTokens, isError: isErrorTokens } = useAbcccTokens();
  const { data: stats = {} as TokenStats, isLoading: isLoadingStats } = useAbcccTokenStats();
  const addToken = useAddAbcccToken();
  const invalidateToken = useInvalidateAbcccToken();
  const generateToken = useGenerateAbcccToken();
  const generateBatchTokens = useGenerateAutomaticTokens();
  
  const handleAddToken = async () => {
    if (!newToken.trim()) {
      toast({
        title: "Token inválido",
        description: "Por favor, insira um token válido.",
        variant: "destructive",
      });
      return;
    }
    
    await addToken.mutateAsync({
      token: newToken.trim(),
      origem: tokenOrigem,
      observacoes: tokenObservacoes
    });
    
    setNewToken('');
    setTokenOrigem('manual');
    setTokenObservacoes('');
    setAddDialogOpen(false);
  };
  
  const handleGenerateToken = async () => {
    await generateToken.mutateAsync(tokenOrigem);
    setTokenOrigem('manual');
    setGenerateDialogOpen(false);
  };
  
  const handleGenerateBatchTokens = async () => {
    if (batchQuantidade < 1 || batchQuantidade > 50) {
      toast({
        title: "Quantidade inválida",
        description: "Por favor, insira um valor entre 1 e 50.",
        variant: "destructive",
      });
      return;
    }
    
    await generateBatchTokens.mutateAsync(batchQuantidade);
    setBatchQuantidade(5);
    setBatchGenerateDialogOpen(false);
  };
  
  const handleInvalidateToken = async (id: string) => {
    if (window.confirm("Tem certeza que deseja invalidar este token?")) {
      await invalidateToken.mutateAsync(id);
    }
  };

  // Filtrar tokens válidos e inválidos
  const validTokens = tokens.filter(token => token.valido);
  const invalidTokens = tokens.filter(token => !token.valido);

  return (
    <div className="space-y-6">
      {/* Cards de estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total de Tokens</CardTitle>
            <CardDescription>Tokens cadastrados no sistema</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-3xl font-bold">{stats.total_tokens || 0}</span>
              <Shield className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Tokens Válidos</CardTitle>
            <CardDescription>Tokens ativos para uso</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-3xl font-bold">{stats.tokens_validos || 0}</span>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Tokens Inválidos</CardTitle>
            <CardDescription>Tokens desativados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <span className="text-3xl font-bold">{stats.tokens_invalidos || 0}</span>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Estatísticas de uso */}
      {stats.top_tokens && stats.top_tokens.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Taxa de Sucesso por Token</CardTitle>
            <CardDescription>
              Percentual de sucesso nas requisições por token (top {stats.top_tokens.length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.top_tokens && stats.top_tokens.reduce((acc, token) => {
                if (token.sucessos + token.falhas > 0) {
                  acc.push(token);
                }
                return acc;
              }, [] as (typeof stats.top_tokens)[0][]).map((token) => (
                <div key={token.id} className="space-y-2">
                  <div className="flex justify-between items-center text-sm">
                    <span className="font-medium truncate" title={token.token}>
                      {token.token.substring(0, 8)}...
                    </span>
                    <span className="text-muted-foreground">
                      {token.sucessos}/{token.sucessos + token.falhas} ({Math.round(token.taxa_sucesso * 100)}%)
                    </span>
                  </div>
                  <Progress
                    value={token.taxa_sucesso * 100}
                    className={
                      token.taxa_sucesso > 0.7
                        ? "bg-green-200"
                        : token.taxa_sucesso > 0.3
                        ? "bg-yellow-200"
                        : "bg-red-200"
                    }
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Ações */}
      <div className="flex flex-wrap gap-2">
        <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Token
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Adicionar Novo Token</DialogTitle>
              <DialogDescription>
                Insira um token de acesso válido para o site da ABCCC.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="token">Token</Label>
                <Input
                  id="token"
                  placeholder="Insira o token de acesso"
                  value={newToken}
                  onChange={(e) => setNewToken(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="origem">Origem</Label>
                <Input
                  id="origem"
                  placeholder="Origem do token (ex: manual, captura)"
                  value={tokenOrigem}
                  onChange={(e) => setTokenOrigem(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea
                  id="observacoes"
                  placeholder="Observações adicionais sobre este token"
                  value={tokenObservacoes}
                  onChange={(e) => setTokenObservacoes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleAddToken} disabled={addToken.isPending}>
                {addToken.isPending ? "Adicionando..." : "Adicionar Token"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Dialog open={generateDialogOpen} onOpenChange={setGenerateDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <RefreshCw className="h-4 w-4 mr-2" />
              Gerar Token
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Gerar Token Automaticamente</DialogTitle>
              <DialogDescription>
                O sistema irá gerar um novo token de acesso automaticamente baseado em padrões da ABCCC.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="origem-auto">Origem</Label>
                <Input
                  id="origem-auto"
                  placeholder="Origem do token (ex: gerado, algoritmo)"
                  value={tokenOrigem}
                  onChange={(e) => setTokenOrigem(e.target.value)}
                />
              </div>
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Informação</AlertTitle>
                <AlertDescription>
                  Tokens gerados automaticamente são baseados em análise de padrões do site da ABCCC.
                </AlertDescription>
              </Alert>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setGenerateDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleGenerateToken} disabled={generateToken.isPending}>
                {generateToken.isPending ? "Gerando..." : "Gerar Token"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Dialog open={batchGenerateDialogOpen} onOpenChange={setBatchGenerateDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <BarChart2 className="h-4 w-4 mr-2" />
              Gerar Múltiplos Tokens
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Gerar Múltiplos Tokens</DialogTitle>
              <DialogDescription>
                Gere vários tokens automaticamente de uma vez.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="quantidade">Quantidade de tokens (1-50)</Label>
                <Input
                  id="quantidade"
                  type="number"
                  min="1"
                  max="50"
                  value={batchQuantidade}
                  onChange={(e) => setBatchQuantidade(parseInt(e.target.value) || 5)}
                />
              </div>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Atenção</AlertTitle>
                <AlertDescription>
                  A geração de múltiplos tokens pode levar alguns segundos. Os tokens gerados serão adicionados ao banco de dados.
                </AlertDescription>
              </Alert>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setBatchGenerateDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleGenerateBatchTokens} disabled={generateBatchTokens.isPending}>
                {generateBatchTokens.isPending ? "Gerando..." : "Gerar Tokens"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      {/* Listagem de tokens */}
      {isErrorTokens && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>
            Ocorreu um erro ao carregar os tokens. Tente novamente mais tarde.
          </AlertDescription>
        </Alert>
      )}
      
      {isLoadingTokens ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <Tabs defaultValue="valid" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="valid">
              Tokens Válidos ({validTokens.length})
            </TabsTrigger>
            <TabsTrigger value="invalid">
              Tokens Inválidos ({invalidTokens.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="valid">
            <Card>
              <CardHeader>
                <CardTitle>Tokens Válidos</CardTitle>
                <CardDescription>Lista de tokens ativos que podem ser usados para acessar o site da ABCCC</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Token</TableHead>
                      <TableHead>Origem</TableHead>
                      <TableHead>Data Criação</TableHead>
                      <TableHead>Último Uso</TableHead>
                      <TableHead>Sucessos</TableHead>
                      <TableHead>Falhas</TableHead>
                      <TableHead>Taxa</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {validTokens.map((token) => (
                      <TableRow key={token.id}>
                        <TableCell className="font-mono">
                          {token.token.substring(0, 8)}...
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{token.origem}</Badge>
                        </TableCell>
                        <TableCell>
                          {token.data_criacao 
                            ? format(new Date(token.data_criacao), 'dd/MM/yyyy HH:mm', { locale: ptBR })
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {token.ultimo_uso 
                            ? format(new Date(token.ultimo_uso), 'dd/MM/yyyy HH:mm', { locale: ptBR })
                            : 'Nunca usado'}
                        </TableCell>
                        <TableCell>{token.sucessos}</TableCell>
                        <TableCell>{token.falhas}</TableCell>
                        <TableCell>
                          {token.sucessos + token.falhas > 0
                            ? `${Math.round((token.sucessos / (token.sucessos + token.falhas)) * 100)}%`
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleInvalidateToken(token.id)}
                            disabled={invalidateToken.isPending}
                          >
                            Invalidar
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                    {validTokens.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-4">
                          Nenhum token válido encontrado
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="invalid">
            <Card>
              <CardHeader>
                <CardTitle>Tokens Inválidos</CardTitle>
                <CardDescription>Lista de tokens que foram invalidados e não podem mais ser usados</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Token</TableHead>
                      <TableHead>Origem</TableHead>
                      <TableHead>Data Criação</TableHead>
                      <TableHead>Último Uso</TableHead>
                      <TableHead>Sucessos</TableHead>
                      <TableHead>Falhas</TableHead>
                      <TableHead>Taxa</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invalidTokens.map((token) => (
                      <TableRow key={token.id}>
                        <TableCell className="font-mono">
                          {token.token.substring(0, 8)}...
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{token.origem}</Badge>
                        </TableCell>
                        <TableCell>
                          {token.data_criacao 
                            ? format(new Date(token.data_criacao), 'dd/MM/yyyy HH:mm', { locale: ptBR })
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {token.ultimo_uso 
                            ? format(new Date(token.ultimo_uso), 'dd/MM/yyyy HH:mm', { locale: ptBR })
                            : 'Nunca usado'}
                        </TableCell>
                        <TableCell>{token.sucessos}</TableCell>
                        <TableCell>{token.falhas}</TableCell>
                        <TableCell>
                          {token.sucessos + token.falhas > 0
                            ? `${Math.round((token.sucessos / (token.sucessos + token.falhas)) * 100)}%`
                            : 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))}
                    {invalidTokens.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          Nenhum token inválido encontrado
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}