import { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/hooks/use-toast';
import { insertCavaloSchema, updateCavaloSchema } from '@shared/insert-schemas';
import { useValidation } from '@/lib/validation-engine';
// Removed performance optimizer import
import type { Cavalo } from '@shared/schema';

// Tipos exportados baseados nos schemas
export type CavaloFormData = z.infer<typeof insertCavaloSchema>;
export type CavaloUpdateData = z.infer<typeof updateCavaloSchema>;

// Tipos para configurações do hook
export interface CavaloQueryOptions {
  enabledByDefault?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

export interface CavaloFilters {
  searchTerm?: string;
  raca?: string;
  sexo?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CavaloStatistics {
  total: number;
  active: number;
  inactive: number;
  byBreed: Record<string, number>;
  bySex: Record<string, number>;
  averageAge: number;
}

/**
 * Hook unificado para gestão completa de cavalos
 * Combina operações CRUD, cache inteligente e gestão de estado
 */
export function useCavaloUnified(id?: string | number | null, options: CavaloQueryOptions = {}) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { validateCavalo, validateField } = useValidation();
  // Removed performance optimizer usage
  
  // Estados para filtros usando recursão para otimização
  const [filters, setFilters] = useState<CavaloFilters>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Query para buscar todos os cavalos do usuário
  const cavalosQuery = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    queryFn: () => apiRequest('/api/cavalos', 'GET'),
    enabled: !!user?.id && (options.enabledByDefault !== false),
    staleTime: options.staleTime || 5 * 60 * 1000,
    gcTime: options.cacheTime || 10 * 60 * 1000,
  });

  // Query para buscar cavalo específico
  const cavaloQuery = useQuery({
    queryKey: ['/api/cavalos', id],
    queryFn: () => apiRequest(`/api/cavalos/${id}`, 'GET'),
    enabled: !!id && !!user?.id,
    staleTime: options.staleTime || 60 * 1000,
    gcTime: options.cacheTime || 5 * 60 * 1000,
  });

  // Query para buscar cavalos para dropdown (incluindo externos)
  const cavalosDropdownQuery = useQuery({
    queryKey: ['/api/cavalos-genealogia', user?.id],
    queryFn: () => apiRequest('/api/cavalos-genealogia', 'GET'),
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
  });

  // Mutation para criar cavalo
  const createCavaloMutation = useMutation({
    mutationFn: (data: CavaloFormData) => apiRequest('/api/cavalos', 'POST', data),
    onSuccess: (newCavalo) => {
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      toast({
        title: 'Sucesso!',
        description: 'Cavalo cadastrado com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao cadastrar cavalo.',
        variant: 'destructive',
      });
    },
  });

  // Mutation para atualizar cavalo
  const updateCavaloMutation = useMutation({
    mutationFn: ({ id, data }: { id: string | number; data: CavaloUpdateData }) =>
      apiRequest(`/api/cavalos/${id}`, 'PUT', data),
    onSuccess: (updatedCavalo) => {
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos', id] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      toast({
        title: 'Sucesso!',
        description: 'Cavalo atualizado com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao atualizar cavalo.',
        variant: 'destructive',
      });
    },
  });

  // Mutation para deletar cavalo
  const deleteCavaloMutation = useMutation({
    mutationFn: (id: string | number) => apiRequest(`/api/cavalos/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
      toast({
        title: 'Sucesso!',
        description: 'Cavalo removido com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro',
        description: error.message || 'Erro ao remover cavalo.',
        variant: 'destructive',
      });
    },
  });

  // Funções helper usando recursão para melhor performance
  const getCavaloById = useCallback(
    (searchId: string | number): Cavalo | undefined => {
      return cavalosQuery.data?.find((cavalo: Cavalo) => 
        cavalo.id === Number(searchId)
      );
    },
    [cavalosQuery.data]
  );

  const getCavalosByFilter = useCallback(
    (filterFn: (cavalo: Cavalo) => boolean): Cavalo[] => {
      return cavalosQuery.data?.filter((cavalo: Cavalo) => 
        filterFn(cavalo)
      ) || [];
    },
    [cavalosQuery.data]
  );

  // Estatísticas calculadas com memoização otimizada
  const statistics = optimizedMemo(
    (): CavaloStatistics => {
      const cavalos = cavalosQuery.data || [];
      
      return {
        total: cavalos.length,
        active: cavalos.filter((c: Cavalo) => c.status === 'ativo').length,
        inactive: cavalos.filter((c: Cavalo) => c.status === 'inativo').length,
        byBreed: cavalos.reduce((acc: Record<string, number>, cavalo: Cavalo) => {
          const breed = cavalo.breed || 'Não informado';
          acc[breed] = (acc[breed] || 0) + 1;
          return acc;
        }, {}),
        bySex: cavalos.reduce((acc: Record<string, number>, cavalo: Cavalo) => {
          const sex = cavalo.sexo || 'Não informado';
          acc[sex] = (acc[sex] || 0) + 1;
          return acc;
        }, {}),
        averageAge: cavalos.length > 0 
          ? cavalos.reduce((sum, cavalo) => {
              const age = cavalo.birth_date 
                ? new Date().getFullYear() - new Date(cavalo.birth_date).getFullYear()
                : 0;
              return sum + age;
            }, 0) / cavalos.length
          : 0,
      };
    },
    [cavalosQuery.data]
  );

  // Filtros aplicados com recursão para otimização
  const filteredCavalos = useMemo(() => {
    const cavalos = cavalosQuery.data || [];
    
    return applyFiltersRecursively(cavalos, filters, Object.keys(filters));
  }, [cavalosQuery.data, filters]);

  // Função recursiva para aplicar filtros
  const applyFiltersRecursively = useCallback(
    (cavalos: Cavalo[], appliedFilters: CavaloFilters, filterKeys: string[]): Cavalo[] => {
      if (filterKeys.length === 0) {
        return cavalos;
      }

      const [currentKey, ...remainingKeys] = filterKeys;
      const filterValue = appliedFilters[currentKey as keyof CavaloFilters];

      if (!filterValue) {
        return applyFiltersRecursively(cavalos, appliedFilters, remainingKeys);
      }

      let filteredData = cavalos;

      switch (currentKey) {
        case 'searchTerm':
          filteredData = cavalos.filter(cavalo =>
            cavalo.nome?.toLowerCase().includes(filterValue.toLowerCase()) ||
            cavalo.numero_registro?.toLowerCase().includes(filterValue.toLowerCase())
          );
          break;
        case 'raca':
          filteredData = cavalos.filter(cavalo => cavalo.breed === filterValue);
          break;
        case 'sexo':
          filteredData = cavalos.filter(cavalo => cavalo.sexo === filterValue);
          break;
        case 'status':
          filteredData = cavalos.filter(cavalo => cavalo.status === filterValue);
          break;
        default:
          filteredData = cavalos;
      }

      return applyFiltersRecursively(filteredData, appliedFilters, remainingKeys);
    },
    []
  );

  // Validação simplificada
  const validateCavaloData = useCallback(
    (data: CavaloFormData, isUpdate: boolean = false) => {
      const result = validateCavalo(data, isUpdate);
      setValidationErrors(result.formatted);
      return result;
    },
    [validateCavalo]
  );

  // Validação de campo individual
  const validateSingleField = useCallback(
    (fieldName: string, value: any, isUpdate: boolean = false) => {
      const result = validateField(fieldName, value, isUpdate);
      
      if (result.error) {
        setValidationErrors(prev => ({
          ...prev,
          [fieldName]: result.error!
        }));
      } else {
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      }
      
      return result;
    },
    [validateField]
  );

  // Funções de filtro otimizadas
  const updateFilters = useCallback(
    (newFilters: Partial<CavaloFilters>) => {
      setFilters(prev => ({ ...prev, ...newFilters }));
    },
    []
  );

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const resetValidationErrors = useCallback(() => {
    setValidationErrors({});
  }, []);

  // Retorno do hook com todas as funcionalidades
  return {
    // Dados
    cavalos: filteredCavalos,
    allCavalos: cavalosQuery.data || [],
    cavalo: cavaloQuery.data,
    cavalosDropdown: cavalosDropdownQuery.data || [],
    statistics,
    
    // Estados
    isLoading: cavalosQuery.isLoading || cavaloQuery.isLoading,
    isError: cavalosQuery.isError || cavaloQuery.isError,
    error: cavalosQuery.error || cavaloQuery.error,
    validationErrors,
    
    // Filtros
    filters,
    updateFilters,
    clearFilters,
    
    // Operações CRUD
    createCavalo: createCavaloMutation.mutate,
    updateCavalo: updateCavaloMutation.mutate,
    deleteCavalo: deleteCavaloMutation.mutate,
    
    // Estados das mutações
    isCreating: createCavaloMutation.isPending,
    isUpdating: updateCavaloMutation.isPending,
    isDeleting: deleteCavaloMutation.isPending,
    
    // Funções helper
    getCavaloById,
    getCavalosByFilter,
    
    // Validação
    validateCavaloData,
    validateSingleField,
    resetValidationErrors,
    
    // Atualização de cache
    invalidateQueries: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos'] });
      queryClient.invalidateQueries({ queryKey: ['/api/cavalos-genealogia'] });
    },
  };
}

/**
 * Hook específico para formulários de cavalo
 * Inclui validação e gestão de estado específica para forms
 */
export function useCavaloForm(cavaloId?: string | number | null) {
  const {
    cavalo,
    createCavalo,
    updateCavalo,
    isCreating,
    isUpdating,
    validateCavaloData,
    validateSingleField,
    validationErrors,
    resetValidationErrors,
  } = useCavaloUnified(cavaloId);

  const [formData, setFormData] = useState<Partial<CavaloFormData>>({});
  const [isDirty, setIsDirty] = useState(false);

  // Atualizar dados do formulário quando cavalo for carregado
  useMemo(() => {
    if (cavalo && cavaloId) {
      setFormData(cavalo);
      setIsDirty(false);
    }
  }, [cavalo, cavaloId]);

  const updateFormData = useCallback((updates: Partial<CavaloFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setIsDirty(true);
  }, []);

  const validateForm = useCallback(() => {
    return validateCavaloData(formData as CavaloFormData, !!cavaloId);
  }, [formData, cavaloId, validateCavaloData]);

  const submitForm = (data: CavaloFormData) => {
    const validation = validateForm();
    
    if (!validation.isValid) {
      toast({
        title: 'Erro de validação',
        description: 'Verifique os campos destacados.',
        variant: 'destructive',
      });
      return;
    }

    if (cavaloId) {
      updateCavalo({ id: cavaloId, data: data as CavaloUpdateData });
    } else {
      createCavalo(data);
    }
  };

  const resetForm = useCallback(() => {
    setFormData({});
    setIsDirty(false);
    resetValidationErrors();
  }, [resetValidationErrors]);

  return {
    // Dados do formulário
    formData,
    updateFormData,
    isDirty,
    
    // Operações
    submitForm,
    resetForm,
    
    // Validação
    validateForm,
    validateSingleField,
    validationErrors,
    
    // Estados
    isLoading: isCreating || isUpdating,
    isEditing: !!cavaloId,
  };
}