/**
 * Sistema de Notificações Inteligentes
 * Monitora eventos importantes e exibe alertas relevantes sem interferir na lógica existente
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bell, AlertTriangle, Clock, Heart, Calendar, Zap, X } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { format, addDays, isWithinInterval, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface NotificationItem {
  id: string;
  type: 'warning' | 'info' | 'success' | 'urgent';
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface NotificationSystemProps {
  className?: string;
}

export function NotificationSystem({ className = '' }: NotificationSystemProps) {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [dismissed, setDismissed] = useState<Set<string>>(new Set());

  // Buscar dados para análise de notificações
  const { data: cavalos } = useQuery({
    queryKey: ['/api/cavalos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000, // 5 minutos
  });

  const { data: manejos } = useQuery({
    queryKey: ['/api/manejos', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  const { data: veterinario } = useQuery({
    queryKey: ['/api/veterinario', user?.id],
    enabled: !!user?.id,
    staleTime: 300000,
  });

  // Gerar notificações inteligentes baseadas nos dados
  useEffect(() => {
    if (!cavalos || !user?.id) return;

    const newNotifications: NotificationItem[] = [];
    const today = new Date();
    const nextWeek = addDays(today, 7);

    // 1. Análise de Cavalos
    if (Array.isArray(cavalos)) {
      // Cavalos sem registro há muito tempo
      const cavalosSemAtividade = cavalos.filter(cavalo => {
        if (!cavalo.created_at) return false;
        const criadoEm = parseISO(cavalo.created_at);
        const diasSemAtualizacao = Math.floor((today.getTime() - criadoEm.getTime()) / (1000 * 60 * 60 * 24));
        return diasSemAtualizacao > 30 && cavalo.status === 'ativo';
      });

      if (cavalosSemAtividade.length > 0) {
        newNotifications.push({
          id: 'cavalos-sem-atividade',
          type: 'warning',
          title: 'Cavalos precisam de atenção',
          message: `${cavalosSemAtividade.length} cavalo(s) sem registros há mais de 30 dias`,
          actionUrl: '/cavalos',
          actionText: 'Ver Cavalos',
          timestamp: today,
          priority: 'medium'
        });
      }

      // Cavalos jovens (menos de 3 anos) - precisam de cuidados especiais
      const cavalosJovens = cavalos.filter(cavalo => {
        if (!cavalo.birth_date) return false;
        const nascimento = parseISO(cavalo.birth_date);
        const idadeAnos = (today.getTime() - nascimento.getTime()) / (1000 * 60 * 60 * 24 * 365);
        return idadeAnos < 3 && idadeAnos > 0;
      });

      if (cavalosJovens.length > 0) {
        newNotifications.push({
          id: 'cavalos-jovens',
          type: 'info',
          title: 'Acompanhamento de potros',
          message: `${cavalosJovens.length} cavalo(s) jovem(ns) precisam de monitoramento especial`,
          actionUrl: '/cavalos',
          actionText: 'Monitorar',
          timestamp: today,
          priority: 'medium'
        });
      }
    }

    // 2. Análise de Manejos
    if (Array.isArray(manejos)) {
      // Manejos atrasados
      const manejosAtrasados = manejos.filter(manejo => {
        if (!manejo.data_execucao) return false;
        const dataExecucao = parseISO(manejo.data_execucao);
        return dataExecucao < today && manejo.status !== 'concluido';
      });

      if (manejosAtrasados.length > 0) {
        newNotifications.push({
          id: 'manejos-atrasados',
          type: 'urgent',
          title: 'Manejos em atraso',
          message: `${manejosAtrasados.length} manejo(s) precisam ser realizados`,
          actionUrl: '/manejos',
          actionText: 'Ver Manejos',
          timestamp: today,
          priority: 'high'
        });
      }

      // Manejos próximos (próximos 7 dias)
      const manejosProximos = manejos.filter(manejo => {
        if (!manejo.data_execucao) return false;
        const dataExecucao = parseISO(manejo.data_execucao);
        return isWithinInterval(dataExecucao, { start: today, end: nextWeek });
      });

      if (manejosProximos.length > 0) {
        newNotifications.push({
          id: 'manejos-proximos',
          type: 'info',
          title: 'Manejos programados',
          message: `${manejosProximos.length} manejo(s) programados para esta semana`,
          actionUrl: '/manejos',
          actionText: 'Planejar',
          timestamp: today,
          priority: 'low'
        });
      }
    }

    // 3. Análise Veterinária
    if (Array.isArray(veterinario)) {
      // Procedimentos com consulta de retorno próxima
      const consultasProximas = veterinario.filter(proc => {
        if (!proc.proxima_consulta) return false;
        const proximaConsulta = parseISO(proc.proxima_consulta);
        return isWithinInterval(proximaConsulta, { start: today, end: nextWeek });
      });

      if (consultasProximas.length > 0) {
        newNotifications.push({
          id: 'consultas-proximas',
          type: 'warning',
          title: 'Consultas veterinárias',
          message: `${consultasProximas.length} consulta(s) veterinária(s) programadas`,
          actionUrl: '/veterinario/registros',
          actionText: 'Ver Consultas',
          timestamp: today,
          priority: 'high'
        });
      }
    }

    // Filtrar notificações já dispensadas
    const notificacoesFiltradas = newNotifications.filter(n => !dismissed.has(n.id));
    setNotifications(notificacoesFiltradas);

  }, [cavalos, manejos, veterinario, user?.id, dismissed]);

  const handleDismiss = (notificationId: string) => {
    setDismissed(prev => new Set([...prev, notificationId]));
  };

  const getIcon = (type: NotificationItem['type']) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'info': return <Bell className="h-4 w-4 text-blue-500" />;
      case 'success': return <Heart className="h-4 w-4 text-green-500" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getBadgeVariant = (priority: NotificationItem['priority']) => {
    switch (priority) {
      case 'critical': return 'destructive' as const;
      case 'high': return 'destructive' as const;
      case 'medium': return 'secondary' as const;
      case 'low': return 'outline' as const;
      default: return 'secondary' as const;
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Bell className="h-5 w-5" />
            Notificações Inteligentes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className="flex items-start gap-3 p-3 rounded-lg border bg-card/50 hover:bg-card/80 transition-colors"
            >
              <div className="flex-shrink-0 mt-0.5">
                {getIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm text-foreground">
                      {notification.title}
                    </h4>
                    <p className="text-sm text-muted-foreground mt-0.5">
                      {notification.message}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Badge variant={getBadgeVariant(notification.priority)} className="text-xs">
                      {notification.priority}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDismiss(notification.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                {notification.actionUrl && notification.actionText && (
                  <div className="mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                      className="h-7 text-xs"
                    >
                      <a href={notification.actionUrl}>
                        {notification.actionText}
                      </a>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}

export default NotificationSystem;