import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Componentes genéticos
import MorfologiaForm from "@/components/genetics/MorfologiaForm";
import { EnhancedMorfologiaViewer } from "@/components/genetics/EnhancedMorfologiaViewer";

export default function MorfologiaPageEnhanced() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAvaliacaoSuccess = () => {
    setIsModalOpen(false);
    // Aqui poderia invalidar queries para recarregar dados
  };

  return (
    <div className="space-y-6">
      {/* Modal para adicionar nova avaliação */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Nova Avaliação Morfológica</DialogTitle>
            <DialogDescription>
              Preencha os dados da avaliação morfológica
            </DialogDescription>
          </DialogHeader>
          
          <MorfologiaForm 
            horse_id={null} // Será selecionado no formulário
            horseName="" 
            onSuccess={handleAvaliacaoSuccess}
            onCancel={() => setIsModalOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Componente principal aprimorado */}
      <EnhancedMorfologiaViewer onAddNew={() => setIsModalOpen(true)} />
    </div>
  );
}