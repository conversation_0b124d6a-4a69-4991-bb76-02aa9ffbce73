/**
 * Componente Select com capacidades avançadas de depuração
 * Envolve o componente Select original adicionando validação e logs
 */

import React, { useEffect } from 'react';
import {
  Select as OriginalSelect,
  SelectContent as OriginalSelectContent,
  SelectItem as OriginalSelectItem,
  SelectTrigger as OriginalSelectTrigger,
  SelectValue as OriginalSelectValue,
  SelectGroup as OriginalSelectGroup,
  SelectLabel as OriginalSelectLabel,
  SelectSeparator as OriginalSelectSeparator,
} from '@/components/ui/select';
import { logger } from '@/lib/logger';
import { DebugWrapper, withDebug } from '@/components/ui/debug-wrapper';

// Interface para o componente Select com depuração
interface DebugSelectProps extends React.ComponentPropsWithoutRef<typeof OriginalSelect> {
  debugId?: string;
}

// Interface para o componente SelectItem com depuração
interface DebugSelectItemProps extends React.ComponentPropsWithoutRef<typeof OriginalSelectItem> {
  debugId?: string;
}

/**
 * Verifica se um valor é válido para SelectItem
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for válido, falso caso contrário
 */
/**
 * Verifica se um valor é válido para SelectItem
 * Um valor válido deve ser uma string não vazia
 * @param value Valor a ser verificado
 * @returns Verdadeiro se o valor for válido, falso caso contrário
 */
const isValidSelectItemValue = (value: any): boolean => {
  // Garantir segurança com verificações detalhadas
  try {
    // Primeira verificação: deve ser uma string (e não um objeto, número, etc)
    if (typeof value !== 'string') {
      return false;
    }
    
    // Segunda verificação: não pode ser uma string vazia ou apenas espaços
    if (value.trim() === '') {
      return false;
    }
    
    // Terceira verificação: não pode ser 'undefined' ou 'null' como texto
    if (value === 'undefined' || value === 'null') {
      return false;
    }
    
    // Passou em todas as verificações
    return true;
  } catch (error) {
    // Em caso de qualquer erro, o valor é inválido
    logger.error('ui.select', 'Erro ao validar SelectItem value', { value, error });
    return false;
  }
};

/**
 * Corrige ou valida um valor de SelectItem para evitar erros de runtime
 * @param value Valor original
 * @param debugId ID para identificação em logs
 * @returns Valor corrigido ou validado
 */
const getSafeSelectItemValue = (value: any, debugId: string = 'unknown'): string => {
  // Verificação intensiva para valores válidos
  try {
    // Se o valor for válido, retorná-lo
    if (isValidSelectItemValue(value)) {
      return value;
    }
    
    // Log do valor inválido
    logger.warn('ui.select', `Valor inválido detectado em SelectItem: "${value}"`, {
      debugId,
      originalValue: value,
      valueType: typeof value,
      isNull: value === null,
      isUndefined: value === undefined,
      isEmpty: value === '',
      isObject: typeof value === 'object',
      stack: new Error().stack
    });
    
    // Gerar ID aleatório único para substituir valores inválidos
    const randomId = `item_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    
    // Tratamento detalhado para diferentes tipos de problemas
    if (value === undefined) {
      return `${randomId}_undefined`;
    }
    
    if (value === null) {
      return `${randomId}_null`;
    }
    
    if (value === '') {
      return `${randomId}_empty`;
    }
    
    if (typeof value === 'object') {
      try {
        // Tentar extrair alguma informação útil do objeto
        const objInfo = JSON.stringify(value).substring(0, 20);
        return `${randomId}_obj_${objInfo}`;
      } catch {
        return `${randomId}_object`;
      }
    }
    
    // Para outros tipos de valores, converter para string segura
    return `${randomId}_${String(value).replace(/\s+/g, '_').substring(0, 20)}`;
  } catch (error) {
    // Último recurso em caso de falhas
    logger.error('ui.select', 'Erro crítico ao processar SelectItem value', { error });
    return `emergency_value_${Date.now()}`;
  }
};

/**
 * Componente SelectItem com depuração que valida o valor
 * Implementa validação rigorosa de valores para evitar erros de runtime
 */
export const DebugSelectItem = React.forwardRef<
  React.ElementRef<typeof OriginalSelectItem>,
  DebugSelectItemProps
>(({ debugId = 'unnamed', value, children, ...props }, ref) => {
  // Extra inicialização de segurança
  const componentId = React.useId();
  const effectiveDebugId = debugId || `selectitem-${componentId}`;
  
  // Validar valor no momento de renderização com máxima segurança
  const safeValue = React.useMemo(() => {
    try {
      // Verificar se o valor é válido e sanitizá-lo se necessário
      return getSafeSelectItemValue(value, effectiveDebugId);
    } catch (error) {
      // Fallback extra de segurança caso getSafeSelectItemValue falhe
      logger.error('ui.select', 'Erro crítico ao sanitizar SelectItem value', { error, value, debugId: effectiveDebugId });
      return `safety_value_${Date.now()}`;
    }
  }, [value, effectiveDebugId]);
  
  // Log detalhado se o valor foi corrigido
  useEffect(() => {
    if (safeValue !== value) {
      logger.debug('ui.select', `SelectItem valor corrigido: "${value}" -> "${safeValue}"`, {
        debugId: effectiveDebugId,
        component: 'SelectItem',
        originalValue: value,
        correctedValue: safeValue,
        valueType: typeof value,
        props: { ...props },
        stack: new Error().stack
      });
    }
  }, [safeValue, value, effectiveDebugId, props]);

  // Verificação do conteúdo da children para evitar outros problemas
  const safeChildren = React.useMemo(() => {
    if (children === undefined || children === null) {
      return safeValue; // Usar o valor como conteúdo se não houver children
    }
    return children;
  }, [children, safeValue]);

  // Renderizar com máxima segurança
  return (
    <DebugWrapper componentName="SelectItem" module="ui.select" logProps={true}>
      <OriginalSelectItem 
        ref={ref} 
        value={safeValue} 
        {...props}
        // Forçar o id para debugging
        id={`select-item-${safeValue}`}
      >
        {safeChildren}
      </OriginalSelectItem>
    </DebugWrapper>
  );
});

DebugSelectItem.displayName = 'DebugSelectItem';

/**
 * Componente Select com depuração
 */
export const DebugSelect: React.FC<DebugSelectProps> = ({ 
  debugId = 'unnamed', 
  children, 
  ...props 
}) => {
  // Log de renderização
  useEffect(() => {
    logger.debug('ui.select', `Select renderizado: ${debugId}`, {
      props,
    });
  }, [debugId, props]);

  return (
    <DebugWrapper componentName="Select" module="ui.select" logProps={true}>
      <OriginalSelect {...props}>
        {children}
      </OriginalSelect>
    </DebugWrapper>
  );
};

DebugSelect.displayName = 'DebugSelect';

// Re-exportar todos os outros componentes do Select
export const SelectContent = withDebug(OriginalSelectContent, { module: 'ui.select', componentName: 'SelectContent' });
export const SelectTrigger = withDebug(OriginalSelectTrigger, { module: 'ui.select', componentName: 'SelectTrigger' });
export const SelectValue = withDebug(OriginalSelectValue, { module: 'ui.select', componentName: 'SelectValue' });
export const SelectGroup = withDebug(OriginalSelectGroup, { module: 'ui.select', componentName: 'SelectGroup' });
export const SelectLabel = withDebug(OriginalSelectLabel, { module: 'ui.select', componentName: 'SelectLabel' });
export const SelectSeparator = withDebug(OriginalSelectSeparator, { module: 'ui.select', componentName: 'SelectSeparator' });

// Re-exportar os componentes com depuração como padrão
export {
  DebugSelect as Select,
  DebugSelectItem as SelectItem,
};