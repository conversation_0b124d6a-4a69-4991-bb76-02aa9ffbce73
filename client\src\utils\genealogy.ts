/**
 * Desfaz recursivamente JSON aninhado em cavaloNome até restar a string.
 * Limita a 5 iterações para evitar loops infinitos em dados corrompidos.
 */
export const extractCleanName = (raw: string | null | undefined): string => {
  if (!raw) return "";
  
  // Se já é uma string simples (não JSON), retorna diretamente
  if (typeof raw === "string" && !raw.trim().startsWith("{")) {
    return raw;
  }
  
  let current: unknown = raw;
  let depth = 0;

  while (typeof current === "string" && current.trim().startsWith("{") && depth < 5) {
    try {
      const obj = JSON.parse(current);
      // Verifica diferentes propriedades possíveis no objeto
      current = obj?.cavaloNome ?? obj?.name ?? obj?.nome ?? "";
      depth += 1;
    } catch {
      break;
    }
  }

  return typeof current === "string" ? current : "";
};