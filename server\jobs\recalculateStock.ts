/**
 * Job de Recálculo de Estoque - EquiGestor AI
 * Executa diariamente às 00:05 para consumir estoque e gerar alertas
 */

import cron from 'node-cron';
import { stockService } from '../services/stock.service';
import { nutritionService } from '../services/nutrition.service';
import { db } from '../db';
import { users } from '@shared/schema';

export class StockRecalculationJob {
  private isRunning = false;

  /**
   * Inicia o job de recálculo de estoque
   */
  start(): void {
    // Executar todos os dias às 00:05
    cron.schedule('5 0 * * *', async () => {
      if (this.isRunning) {
        console.log('Job de recálculo de estoque já está executando...');
        return;
      }

      this.isRunning = true;
      console.log('Iniciando job de recálculo de estoque...', new Date().toISOString());

      try {
        await this.executeStockRecalculation();
        console.log('Job de recálculo de estoque concluído com sucesso');
      } catch (error) {
        console.error('Erro no job de recálculo de estoque:', error);
      } finally {
        this.isRunning = false;
      }
    }, {
      timezone: 'America/Sao_Paulo'
    });

    console.log('Job de recálculo de estoque agendado para executar diariamente às 00:05');
  }

  /**
   * Executa o recálculo de estoque para todos os usuários
   */
  private async executeStockRecalculation(): Promise<void> {
    try {
      // Buscar todos os usuários ativos
      const activeUsers = await db.select({ id: users.id }).from(users);
      
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      for (const user of activeUsers) {
        try {
          console.log(`Processando usuário ${user.id}...`);

          // 1. Consumir estoque baseado nos planos do dia anterior
          await stockService.consumeStockFromPlans(user.id, yesterdayStr);

          // 2. Calcular dias restantes e gerar alertas
          await stockService.checkAndCreateAlerts(user.id);

          console.log(`Usuário ${user.id} processado com sucesso`);
        } catch (userError) {
          console.error(`Erro ao processar usuário ${user.id}:`, userError);
          // Continuar com os próximos usuários mesmo se um falhar
        }
      }
    } catch (error) {
      console.error('Erro ao executar recálculo de estoque:', error);
      throw error;
    }
  }

  /**
   * Executa o job manualmente (para testes)
   */
  async executeManually(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Job já está executando');
    }

    this.isRunning = true;
    try {
      await this.executeStockRecalculation();
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Gera planos de alimentação para o dia atual (se não existirem)
   */
  async generateTodayPlansForAllUsers(): Promise<void> {
    try {
      const activeUsers = await db.select({ id: users.id }).from(users);
      const today = new Date().toISOString().split('T')[0];

      for (const user of activeUsers) {
        try {
          // Verificar se já existem planos para hoje
          const existingPlans = await nutritionService.getTodayPlans(user.id, today);
          
          if (existingPlans.length === 0) {
            console.log(`Gerando planos para usuário ${user.id}...`);
            await nutritionService.generateDailyPlans(user.id, today);
          }
        } catch (userError) {
          console.error(`Erro ao gerar planos para usuário ${user.id}:`, userError);
        }
      }
    } catch (error) {
      console.error('Erro ao gerar planos diários:', error);
    }
  }
}

// Instância global do job
export const stockRecalculationJob = new StockRecalculationJob();

// Auto-inicializar o job quando o módulo for importado
if (process.env.NODE_ENV !== 'test') {
  stockRecalculationJob.start();
}