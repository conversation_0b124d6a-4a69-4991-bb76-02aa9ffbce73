import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { Cavalo } from '@shared/schema';

// Schema de validação para o formulário de procedimento veterinário
const procedimentoFormSchema = z.object({
  cavalo_id: z.number({ required_error: "Selecione um animal" }),
  tipo_procedimento: z.string().min(1, { message: "Selecione o tipo de procedimento" }),
  data_procedimento: z.string().min(1, { message: "Selecione a data" }),
  veterinario_responsavel: z.string().min(1, { message: "Informe o nome do veterinário" }),
  diagnostico: z.string().optional(),
  tratamento: z.string().optional(),
  medicamentos: z.string().optional(),
  observacoes: z.string().optional(),
  custo: z.coerce.number().optional(),
  proxima_consulta: z.string().optional(),
  status: z.string().default("realizado"),
  user_id: z.number(),
});

export type ProcedimentoFormValues = z.infer<typeof procedimentoFormSchema>;

interface ProcedimentoVetFormProps {
  onSubmit: (values: ProcedimentoFormValues) => void;
  defaultValues?: Partial<ProcedimentoFormValues>;
  isSubmitting: boolean;
  cavalos: Cavalo[];
  user_id: number;
  procedimentoTipo?: 'vacinacao' | 'vermifugacao' | 'exame' | 'outro';
}

/**
 * Componente de formulário para procedimentos veterinários
 * 
 * Este componente é usado para criar e editar procedimentos veterinários,
 * incluindo vacinações, vermifugações, exames e outros tipos de procedimentos.
 * Os campos exibidos são adaptados ao tipo de procedimento selecionado.
 */
export const ProcedimentoVetForm: React.FC<ProcedimentoVetFormProps> = ({
  onSubmit,
  defaultValues,
  isSubmitting,
  cavalos,
  user_id,
  procedimentoTipo = 'outro',
}) => {
  // Definir valores iniciais com base no tipo de procedimento
  const getTipoInicial = () => {
    switch (procedimentoTipo) {
      case 'vacinacao': return 'Vacinação';
      case 'vermifugacao': return 'Vermifugação';
      case 'exame': return 'Exame clínico';
      default: return '';
    }
  };

  // Formulário com validação Zod
  const form = useForm<ProcedimentoFormValues>({
    resolver: zodResolver(procedimentoFormSchema),
    defaultValues: {
      cavalo_id: 0,
      tipo_procedimento: getTipoInicial(),
      data_procedimento: format(new Date(), 'yyyy-MM-dd'),
      veterinario_responsavel: '',
      diagnostico: '',
      tratamento: '',
      medicamentos: '',
      observacoes: '',
      custo: 0,
      proxima_consulta: '',
      status: 'realizado',
      user_id,
      ...defaultValues
    },
  });

  // Tipos de procedimentos disponíveis
  const tiposProcedimento = [
    // Vacinações
    { value: 'Vacinação', label: 'Vacinação' },
    { value: 'Reforço de vacina', label: 'Reforço de vacina' },
    
    // Vermifugações
    { value: 'Vermifugação', label: 'Vermifugação' },
    { value: 'Controle parasitário', label: 'Controle parasitário' },
    
    // Exames e consultas
    { value: 'Exame clínico', label: 'Exame clínico' },
    { value: 'Consulta veterinária', label: 'Consulta veterinária' },
    { value: 'Exame laboratorial', label: 'Exame laboratorial' },
    { value: 'Raio-X', label: 'Raio-X' },
    { value: 'Ultrassom', label: 'Ultrassom' },
    { value: 'Endoscopia', label: 'Endoscopia' },
    
    // Outros procedimentos
    { value: 'Cirurgia', label: 'Cirurgia' },
    { value: 'Tratamento dentário', label: 'Tratamento dentário' },
    { value: 'Casqueamento', label: 'Casqueamento' },
    { value: 'Fisioterapia', label: 'Fisioterapia' },
    { value: 'Acupuntura', label: 'Acupuntura' },
    { value: 'Outro', label: 'Outro procedimento' },
  ];

  // Verificar se o tipo atual é uma vacinação
  const tipoProcedimento = form.watch('tipo_procedimento');
  const isVacinacao = tipoProcedimento?.toLowerCase().includes('vacina') || false;
  
  // Verificar se o tipo atual é uma vermifugação
  const isVermifugacao = tipoProcedimento?.toLowerCase().includes('vermifug') || false;
  
  // Verificar se o tipo atual é um exame
  const isExame = tipoProcedimento?.toLowerCase().includes('exame') || false;

  // Handler para o envio do formulário
  const handleSubmit = (values: ProcedimentoFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <input 
          type="hidden" 
          {...form.register("user_id", { valueAsNumber: true })}
          value={user_id}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="cavalo_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Animal</FormLabel>
                <Select 
                  onValueChange={(value) => field.onChange(parseInt(value))} 
                  defaultValue={field.value?.toString() || ""}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um animal" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {cavalos.map((cavalo) => (
                      <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                        {cavalo.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="tipo_procedimento"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de Procedimento</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {tiposProcedimento.map((tipo) => (
                      <SelectItem key={tipo.value} value={tipo.value}>
                        {tipo.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="data_procedimento"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data do Procedimento</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="veterinario_responsavel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Veterinário</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="custo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Custo (R$)</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="proxima_consulta"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Próxima Consulta (opcional)</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="diagnostico"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Diagnóstico</FormLabel>
              <FormControl>
                <Textarea {...field} rows={3} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="medicamentos"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Medicamentos (opcional)</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormDescription>
                Medicamentos utilizados no procedimento
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="tratamento"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tratamento (opcional)</FormLabel>
              <FormControl>
                <Textarea {...field} rows={2} />
              </FormControl>
              <FormDescription>
                Descrição do tratamento realizado
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="observacoes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Observações Adicionais (opcional)</FormLabel>
              <FormControl>
                <Textarea {...field} rows={3} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end space-x-2">
          <Button 
            type="submit" 
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </form>
    </Form>
  );
};

// Comentário explicativo das correções aplicadas:
// - Corrigido schema para usar campos snake_case do banco (cavalo_id, tipo_procedimento, data_procedimento, veterinario_responsavel)
// - Removidos campos que não existem na tabela veterinario (crmv, dosagem, resultado, recomendacoes, dataProximoProcedimento)
// - Mantidos apenas campos válidos: cavalo_id, tipo_procedimento, data_procedimento, veterinario_responsavel, diagnostico, tratamento, medicamentos, observacoes, custo, proxima_consulta, status, user_id
// - Implementada validação Zod correta alinhada com o schema PostgreSQL