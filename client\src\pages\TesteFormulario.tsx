import React from "react";
import { useLocation } from "wouter";
import { CavaloFormRefatorado } from "../components/cavalo/CavaloFormRefatorado";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function TesteFormulario() {
  const [, navigate] = useLocation();

  const handleSuccess = (cavalo: any) => {
    console.log("Cavalo cadastrado com sucesso:", cavalo);
    navigate("/cavalos");
  };

  const handleCancel = () => {
    navigate("/cavalos");
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/cavalos")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Voltar
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Formulário Refatorado - Teste</h1>
          <p className="text-gray-600">
            Teste do novo formulário de cadastro de cavalos
          </p>
        </div>
      </div>

      <CavaloFormRefatorado
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}