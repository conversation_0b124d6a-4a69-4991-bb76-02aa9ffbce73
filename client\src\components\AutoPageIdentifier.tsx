import { useLocation } from 'wouter';
import { PageIdentifier } from './PageIdentifier';
import { PAGE_IDS, PAGE_NAMES } from '@/utils/page-ids';

/**
 * Sistema automático de identificação de páginas baseado na rota atual
 * Detecta automaticamente a página e aplica o ID correspondente
 */
export function AutoPageIdentifier() {
  const [location] = useLocation();
  
  // Mapeamento de rotas para IDs de página
  const routeToPageId = {
    '/': PAGE_IDS.DASHBOARD,
    '/login': PAGE_IDS.LOGIN,
    '/signup': PAGE_IDS.SIGNUP,
    
    // Cavalos
    '/cavalos': PAGE_IDS.CAVALOS_LIST,
    '/cavalo/cadastro': PAGE_IDS.CAVALO_FORM,
    '/cavalo/editar': PAGE_IDS.CAVALO_EDIT,
    '/cavalo/editar-simples': PAGE_IDS.CAVALO_EDIT_SIMPLE,
    '/cavalo/fotos': PAGE_IDS.CAVALO_PHOTOS,
    '/cavalo/upload': PAGE_IDS.CAVALO_UPLOAD,
    '/cavalos/cadastro-simples': PAGE_IDS.CADASTRO_SIMPLES,
    '/cavalos/biometria': PAGE_IDS.BIOMETRIA,
    '/cavalos/saida-retorno': PAGE_IDS.SAIDA_RETORNO,
    
    // Genética
    '/genetica': PAGE_IDS.GENETICS_MAIN,
    '/genetica/genealogia': PAGE_IDS.GENEALOGIA,
    '/genetica/genealogia/editar': PAGE_IDS.GENEALOGIA_EDIT,
    '/genetica/genealogia/editar-completa': PAGE_IDS.GENEALOGIA_EDIT_COMPLETE,
    '/genetica/morfologia': PAGE_IDS.MORFOLOGIA,
    '/genetica/desempenho': PAGE_IDS.DESEMPENHO,
    '/genetica/sugestoes-cruzamento': PAGE_IDS.SUGESTOES_CRUZAMENTO,
    '/genetica/heranca': PAGE_IDS.HERANCA_GENETICA,
    '/genetica/importacao-abccc': PAGE_IDS.IMPORTACAO_ABCCC,
    
    // Manejos
    '/manejos': PAGE_IDS.MANEJOS_MAIN,
    '/manejos/tipo': PAGE_IDS.MANEJOS_TIPO,
    
    // Veterinário
    '/veterinario': PAGE_IDS.VETERINARIO_MAIN,
    '/veterinario/registros': PAGE_IDS.VETERINARIO_REGISTROS,
    '/veterinario/vacinacoes': PAGE_IDS.VETERINARIO_VACINACOES,
    '/veterinario/vermifugacoes': PAGE_IDS.VETERINARIO_VERMIFUGACOES,
    '/veterinario/procedimentos': PAGE_IDS.PROCEDIMENTOS_VET,
    
    // Reprodução
    '/reproducao': PAGE_IDS.REPRODUCAO_MAIN,
    '/reproducao/estatisticas': PAGE_IDS.REPRODUCAO_ESTATISTICAS,
    '/reproducao/coleta-semen': PAGE_IDS.REPRODUCAO_COLETA_SEMEN,
    '/reproducao/detalhes': PAGE_IDS.REPRODUCAO_DETALHES,
    
    // Nutrição
    '/nutricao': PAGE_IDS.NUTRICAO_MAIN,
    '/nutricao/logistica': PAGE_IDS.NUTRICAO_LOGISTICA,
    
    // Financeiro
    '/financeiro': PAGE_IDS.FINANCEIRO_MAIN,
    '/financeiro/lancamentos': PAGE_IDS.FINANCEIRO_LANCAMENTOS,
    '/financeiro/centros-custo': PAGE_IDS.FINANCEIRO_CENTROS_CUSTO,
    '/financeiro/contas': PAGE_IDS.FINANCEIRO_CONTAS,
    '/financeiro/demonstrativos': PAGE_IDS.FINANCEIRO_DEMONSTRATIVOS,
    
    // Administrativo
    '/admin': PAGE_IDS.ADMIN_MAIN,
    '/admin/abccc-tokens': PAGE_IDS.ADMIN_ABCCC_TOKENS,
    '/admin/usuarios': PAGE_IDS.USUARIOS,
    '/admin/configuracoes': PAGE_IDS.CONFIGURACOES,
    
    // Outras páginas
    '/estatisticas': PAGE_IDS.ESTATISTICAS,
    '/alertas': PAGE_IDS.ALERTAS,
    '/agenda': PAGE_IDS.AGENDA,
    '/eventos': PAGE_IDS.EVENTOS,
    '/documentos': PAGE_IDS.DOCUMENTOS,
    '/arquivos': PAGE_IDS.ARQUIVOS,
    '/assistente': PAGE_IDS.ASSISTENTE,
    '/insumos': PAGE_IDS.INSUMOS,
    '/medicamentos': PAGE_IDS.MEDICAMENTOS,
    '/movimentacoes': PAGE_IDS.MOVIMENTACOES,
    '/relatorios': PAGE_IDS.RELATORIOS,
    '/usuarios': PAGE_IDS.USUARIOS,
    '/configuracoes': PAGE_IDS.CONFIGURACOES,
    '/cavalos/nutricao': PAGE_IDS.NUTRICAO_MAIN,
    '/nutricao-logistica': PAGE_IDS.NUTRICAO_LOGISTICA,
    '/procedimentos-vet': PAGE_IDS.PROCEDIMENTOS_VET,
    '/dashboard/agenda': PAGE_IDS.AGENDA,
    '/dashboard/estatisticas': PAGE_IDS.ESTATISTICAS
  };
  
  // Detecta rotas dinâmicas (com parâmetros)
  const detectDynamicRoute = (path: string): string | null => {
    // Cavalos com ID
    if (path.match(/^\/cavalo\/\d+$/)) return PAGE_IDS.CAVALO_DETAILS;
    if (path.match(/^\/cavalo\/\d+\/editar$/)) return PAGE_IDS.CAVALO_EDIT;
    if (path.match(/^\/cavalo\/\d+\/editar-simples$/)) return PAGE_IDS.CAVALO_EDIT_SIMPLE;
    if (path.match(/^\/cavalo\/\d+\/fotos$/)) return PAGE_IDS.CAVALO_PHOTOS;
    if (path.match(/^\/cavalo\/\d+\/upload$/)) return PAGE_IDS.CAVALO_UPLOAD;
    if (path.match(/^\/cavalo\/\d+\/genealogia\/editar$/)) return PAGE_IDS.GENEALOGIA_EDIT;
    
    // Genética com ID
    if (path.match(/^\/genetica\/genealogia\/\d+$/)) return PAGE_IDS.GENEALOGIA_EDIT;
    if (path.match(/^\/genetica\/morfologia\/\d+$/)) return PAGE_IDS.MORFOLOGIA;
    if (path.match(/^\/genetica\/desempenho\/\d+$/)) return PAGE_IDS.DESEMPENHO;
    
    // Manejos com tipo
    if (path.match(/^\/manejos\/[^\/]+$/)) return PAGE_IDS.MANEJOS_TIPO;
    
    // Reprodução com ID
    if (path.match(/^\/reproducao\/\d+$/)) return PAGE_IDS.REPRODUCAO_DETALHES;
    
    // Procedimentos veterinários com ID
    if (path.match(/^\/procedimentos-vet\/\d+$/)) return PAGE_IDS.PROCEDIMENTOS_VET;
    
    // Relatórios com tipo
    if (path.match(/^\/relatorios\/[^\/]+$/)) return PAGE_IDS.RELATORIOS;
    
    return null;
  };
  
  // Determina o ID da página atual
  const getCurrentPageId = (): string => {
    // Tenta rota exata primeiro
    const exactMatch = routeToPageId[location as keyof typeof routeToPageId];
    if (exactMatch) return exactMatch;
    
    // Tenta rotas dinâmicas
    const dynamicMatch = detectDynamicRoute(location);
    if (dynamicMatch) return dynamicMatch;
    
    // Página não encontrada
    return PAGE_IDS.NOT_FOUND;
  };
  
  const currentPageId = getCurrentPageId();
  const currentPageName = PAGE_NAMES[currentPageId as keyof typeof PAGE_NAMES] || 'Página Desconhecida';
  
  return <PageIdentifier pageId={currentPageId} pageName={currentPageName} />;
}