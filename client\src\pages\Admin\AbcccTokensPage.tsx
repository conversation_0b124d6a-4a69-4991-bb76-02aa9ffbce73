import React from 'react';
import { useTitle } from '@/hooks/use-title';
import { Container } from '@/components/ui/container';
import AbcccTokensManager from '@/components/admin/AbcccTokensManager';

export default function AbcccTokensPage() {
  useTitle('EquiGestor | Gerenciamento de Tokens ABCCC');
  
  return (
    <Container className="py-6">
      <h1 className="text-2xl font-bold mb-4">Gerenciamento de Tokens ABCCC</h1>
      <p className="text-muted-foreground mb-6">
        Gerencie os tokens de acesso ao sistema de busca do site da ABCCC. 
        Esta ferramenta permite adicionar, gerar e monitorar tokens para otimizar 
        as buscas de cavalos pelo registro.
      </p>
      
      <AbcccTokensManager />
    </Container>
  );
}