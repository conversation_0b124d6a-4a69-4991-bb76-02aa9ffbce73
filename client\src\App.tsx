import { ReactNode } from "react";
import { Switch, Route, useLocation } from "wouter";

// Configuração de funcionalidades temporariamente desabilitadas
const FEATURES_CONFIG = {
  agenda: false, // Temporariamente desabilitada
  eventos: false, // Temporariamente desabilitada
};

// Firebase initialization (even though we're using JWT auth)
import "./lib/firebase";

// Components
import Layout from "./components/Layout";
import ProtectedRoute from "./components/ProtectedRoute";
import { AutoPageIdentifier } from "./components/AutoPageIdentifier";
import { ErrorBoundary } from "./components/ErrorBoundary";

// Pages
import Home from "./pages/Home";
import Dashboard from "./pages/Dashboard";
import HorseDetails from "./pages/HorseDetails";
import CavalosPageRefactored from "./pages/CavalosPageRefactored";
import CavaloFormPage from "./pages/CavaloFormPage";
import CadastroSimples from "./pages/CadastroSimples";
import HorsePhotosPage from "./pages/HorsePhotosPage";
import Manejos from "./pages/Manejos";
import Upload from "./pages/Upload";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import AgendaPage from "./pages/AgendaPage";
import EstatisticasPage from "./pages/EstatisticasPage";
import AlertasPage from "./pages/AlertasPage";
import ProcedimentosVeterinarios from "./pages/ProcedimentosVeterinarios";
import ReproducaoPageSimple from "./pages/ReproducaoPageSimple";
import EstatisticasReproducao from "./pages/EstatisticasReproducao";
import BiometriaPage from "./pages/BiometriaPage";
import SaidaRetornoPage from "./pages/SaidaRetornoPage";
import NutricaoPage from "./pages/NutricaoPage";
import NutricaoPageModern from "./pages/NutricaoPageModern";
import NutricaoInteligente from "./pages/NutricaoInteligente";
import EditHorseSimple from "./pages/EditHorseSimple";
import EditarGenealogia from "./pages/EditarGenealogia";
import EditarGenealogiaCompleta from "./pages/EditarGenealogiaCompleta";
import EditarGenealogiaCompletaFixed from "./pages/EditarGenealogiaCompletaFixed";
import EditarGenealogiaArvore from "./pages/EditarGenealogiaArvore";
import NutricaoPageRefactored from "./pages/NutricaoPageRefactored";
import NutricaoLogistica from "./pages/NutricaoLogistica";
import ImportacaoPdfABCCC from "./pages/ImportacaoPdfABCCC";
import AbcccTokensPage from "./pages/Admin/AbcccTokensPage";
import AdminPage from "./pages/Admin/AdminPage";
import AdminUsersPage from "./pages/admin/AdminUsersPage";
import { ConfiguracoesPage } from "./pages/Configuracoes/ConfiguracoesCorrigida";
import AdminSimples from "./pages/AdminSimples";
import NotFound from "./pages/not-found";

// Páginas do módulo Veterinário
import VeterinarioPage from "./pages/Veterinario/VeterinarioPage";
import RegistrosPage from "./pages/Veterinario/RegistrosPage";
import RegistrosPageResponsivo from "./pages/Veterinario/RegistrosPageResponsivo";
import VacinacoesPage from "./pages/Veterinario/VacinacoesPage";
import VacinacoesPageRefactored from "./pages/Veterinario/VacinacoesPageRefactored";
import VermifugacoesPage from "./pages/Veterinario/VermifugacoesPage";
import VermifugacoesPageRefactored from "./pages/Veterinario/VermifugacoesPageRefactored";

// Página de coleta de sêmen para reprodução
import ColetaSemenPage from "./pages/Reproducao/ColetaSemenPage";

// Módulo Financeiro - Versão Refatorada
import FinanceiroPageRefactored from "./pages/Financeiro/FinanceiroPageRefactored";
import LancamentosPage from "./pages/Financeiro/LancamentosPage";
import CentrosCustoPage from "./pages/Financeiro/CentrosCustoPage";
import ContasPage from "./pages/Financeiro/ContasPage";
import DemonstrativosPage from "./pages/Financeiro/DemonstrativosPage";

// Novos módulos implementados
import UsuariosPage from "./pages/Usuarios/UsuariosPage";
import InsumosPage from "./pages/Insumos/InsumosPage";
import MedicamentosPage from "./pages/Medicamentos/MedicamentosPage";
import MovimentacoesPage from "./pages/Movimentacoes/MovimentacoesPage";
import RelatoriosPage from "./pages/Relatorios/RelatoriosPage";
import DocumentosPage from "./pages/Documentos/DocumentosPage";

import EventosPage from "./pages/Eventos/EventosPage";

// User Management Pages
import UserRegistration from "./pages/UserManagement/UserRegistration";
import UserProfile from "./pages/UserManagement/UserProfile";
import AdminPanel from "./pages/UserManagement/AdminPanel";

// Assistente IA
import { AssistantButton } from "./components/assistente/AssistantButton";

// Módulo de Genética
import GeneticsPage from "./pages/Genetics/GeneticsPage";
import TraitInheritancePage from "./pages/Genetics/TraitInheritancePage.jsx";
import MorfologiaPageModern from "./pages/Genetics/MorfologiaPageModern";

// Página do Assistente Virtual
import AssistentePage from './pages/Assistente/AssistentePage';

// Importar o LayoutWrapper do componente Layout
import { LayoutWrapper } from '@/components/Layout';

// Componentes de detalhes de reprodução
import { DetalhesReproducaoPartos } from './components/DetalhesReproducaoPartos';
import { DetalhesReproducaoNascimentos } from './components/DetalhesReproducaoNascimentos';
import { DetalhesReproducaoCobricoes } from './components/DetalhesReproducaoCobricoes';

// Assistente virtual
import SimpleChatBot from './components/assistente/SimpleChatBot';



/**
 * The main App component
 * 
 * This is the root component that wraps all other components.
 */
function App() {
  const [location] = useLocation();
  
  // Verificar se já existe um usuário autenticado no localStorage
  const checkUserLogged = () => {
    try {
      const userJson = localStorage.getItem('user');
      return !!userJson; // Retorna true se houver usuário, false caso contrário
    } catch (e) {
      console.error("Erro ao verificar autenticação:", e);
      return false;
    }
  };
  
  // Check if we're on a public route (login or signup)
  const isPublicRoute = location === "/login" || location === "/signup";
  
  return (
    <div className="min-h-screen">
      {/* Sistema automático de identificação de páginas */}
      <AutoPageIdentifier />
      
      {/* Assistente Virtual é exibido a partir do Layout, não precisa estar aqui */}
      
      <Switch>
        {/* Rotas públicas (sem Layout) */}
        <Route path="/login">
          <Login />
        </Route>
        <Route path="/signup">
          <Signup />
        </Route>
        
        {/* Página inicial - Tela de Login */}
        <Route path="/">
          <Home />
        </Route>
        
        {/* Dashboard - rota alternativa */}
        <Route path="/dashboard">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Dashboard">
              <Dashboard />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rotas de Cavalos */}
        <Route path="/cavalos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Cavalos">
              <CavalosPageRefactored />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/cavalo/cadastro">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Cadastro de Cavalo" showBackButton backUrl="/cavalos">
              <CavaloFormPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/cavalo/:id">
          {(params: any) => (
            <ProtectedRoute>
              <LayoutWrapper pageTitle="Detalhes do Cavalo" showBackButton backUrl="/cavalos">
                <HorseDetails id={params?.id || ''} />
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
        <Route path="/cavalo/:id/editar">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper pageTitle="Editar Cavalo" showBackButton backUrl="/cavalos">
                <EditHorseSimple />
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>

        <Route path="/cavalo/:id/genealogia/editar">
          {(params: any) => (
            <ProtectedRoute>
              <EditarGenealogiaArvore cavaloId={params?.id || ''} />
            </ProtectedRoute>
          )}
        </Route>
        
        <Route path="/cavalo/:id/fotos">
          {(params: any) => (
            <ProtectedRoute>
              <HorsePhotosPage id={params?.id || ''} />
            </ProtectedRoute>
          )}
        </Route>
        
        <Route path="/cavalo/:id/upload">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper pageTitle="Upload de Arquivo" showBackButton>
                <Upload />
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
        {/* Rotas de Manejos */}
        <Route path="/manejos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Manejos">
              <Manejos />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/manejos/:tipo">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper 
                pageTitle={`Manejos - ${params?.tipo?.charAt(0).toUpperCase() + params?.tipo?.slice(1) || 'Geral'}`} 
                showBackButton 
                backUrl="/manejos"
              >
                <Manejos tipoManejo={params?.tipo || ''} />
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
        {/* Página de biometria */}
        <Route path="/cavalos/biometria">
          <ProtectedRoute>
            <BiometriaPage />
          </ProtectedRoute>
        </Route>
        
        {/* Página de Saída e Retorno */}
        <Route path="/cavalos/saida-retorno">
          <ProtectedRoute>
            <SaidaRetornoPage />
          </ProtectedRoute>
        </Route>

        {/* Página de Nutrição */}
        <Route path="/nutricao">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Nutrição">
              <NutricaoPageModern />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/cavalos/nutricao">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Nutrição">
              <NutricaoPageModern />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>

        {/* Nova Página de Logística de Nutrição */}
        <Route path="/nutricao-logistica">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Logística de Nutrição">
              <NutricaoLogistica />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>

        {/* Sistema de Nutrição Inteligente */}
        <Route path="/nutricao-inteligente">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Nutrição Inteligente">
              <NutricaoInteligente />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>

        {/* Página de Administração */}
        <Route path="/admin">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Administração">
              <AdminSimples />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/reproducao">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Controle Reprodutivo">
              <ReproducaoPageSimple />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/reproducao/estatisticas">
          <ProtectedRoute>
            <LayoutWrapper 
              pageTitle="Estatísticas de Reprodução" 
              showBackButton 
              backUrl="/reproducao"
            >
              <EstatisticasReproducao />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/reproducao/:id">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper 
                pageTitle="Detalhes da Reprodução" 
                showBackButton 
                backUrl="/reproducao"
              >
                {params.id === "partos" ? (
                  <DetalhesReproducaoPartos />
                ) : params.id === "nascimentos" ? (
                  <DetalhesReproducaoNascimentos />
                ) : params.id === "cobricoes" ? (
                  <DetalhesReproducaoCobricoes />
                ) : (
                  <div className="text-center py-16">
                    <h2 className="text-2xl font-bold">Detalhes do Registro Reprodutivo ID: {params.id}</h2>
                    <p className="mt-2">Visualizando detalhes do registro específico</p>
                  </div>
                )}
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
{FEATURES_CONFIG.agenda && (
        <Route path="/agenda">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Agenda">
              <AgendaPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        )}
        
        {FEATURES_CONFIG.agenda && (
        <Route path="/dashboard/agenda">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Agenda">
              <AgendaPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        )}
        
        <Route path="/estatisticas">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Estatísticas">
              <EstatisticasPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/dashboard/estatisticas">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Estatísticas">
              <EstatisticasPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/alertas">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Alertas e Notificações">
              <AlertasPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/procedimentos-vet">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Procedimentos Veterinários">
              <ProcedimentosVeterinarios />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/procedimentos-vet/:id">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper 
                pageTitle="Detalhes do Procedimento" 
                showBackButton 
                backUrl="/procedimentos-vet"
              >
                <div className="text-center py-16">
                  <h2 className="text-2xl font-bold">Detalhes do Procedimento ID: {params.id}</h2>
                  <p className="mt-2">Visualização detalhada em desenvolvimento</p>
                </div>
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
        {/* Outras rotas em desenvolvimento mencionadas no menu */}
        <Route path="/usuarios">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Usuários">
              <UsuariosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/admin/abccc-tokens">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Gerenciamento de Tokens ABCCC">
              <AbcccTokensPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/insumos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Insumos">
              <InsumosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/medicamentos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Medicamentos">
              <MedicamentosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/movimentacoes">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Movimentações">
              <MovimentacoesPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
{FEATURES_CONFIG.eventos && (
        <Route path="/eventos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Eventos">
              <EventosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        )}
        
        {/* Rotas do Módulo Financeiro */}
        <Route path="/financeiro">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Financeiro">
              <FinanceiroPageRefactored />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/financeiro/lancamentos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Lançamentos Financeiros" showBackButton backUrl="/financeiro">
              <LancamentosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/financeiro/centros-custos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Centros de Custos" showBackButton backUrl="/financeiro">
              <CentrosCustoPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/financeiro/contas">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Contas Financeiras" showBackButton backUrl="/financeiro">
              <ContasPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/financeiro/demonstrativos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Demonstrativos Financeiros" showBackButton backUrl="/financeiro">
              <DemonstrativosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/relatorios">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Relatórios">
              <RelatoriosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/relatorios/:tipo">
          {params => (
            <ProtectedRoute>
              <LayoutWrapper
                pageTitle={`Relatório - ${params.tipo.charAt(0).toUpperCase() + params.tipo.slice(1)}`}
                showBackButton
                backUrl="/relatorios"
              >
                <RelatoriosPage />
              </LayoutWrapper>
            </ProtectedRoute>
          )}
        </Route>
        
        <Route path="/documentos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Documentos">
              <DocumentosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/arquivos">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Arquivos">
              <DocumentosPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/configuracoes">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Configurações">
              <ConfiguracoesPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rotas do módulo Veterinário */}
        <Route path="/veterinario">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Veterinário">
              <VeterinarioPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/veterinario/registros">
          <ProtectedRoute>
            <RegistrosPageResponsivo />
          </ProtectedRoute>
        </Route>
        
        <Route path="/veterinario/vacinacoes">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Vacinações" showBackButton backUrl="/veterinario">
              <VacinacoesPageRefactored />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/veterinario/vermifugacoes">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Vermifugações" showBackButton backUrl="/veterinario">
              <VermifugacoesPageRefactored />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rotas adicionais de reprodução */}
        <Route path="/reproducao/coleta-semen">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Controle de Coleta de Sêmen" showBackButton backUrl="/reproducao">
              <ColetaSemenPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Módulo de Genética */}
        <Route path="/genetica">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Genética">
              <GeneticsPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/genetica/heranca">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Simulador de Herança Genética" showBackButton backUrl="/genetica">
              <TraitInheritancePage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/genetica/importar-abccc">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Importação de Certificados ABCCC" showBackButton backUrl="/genetica">
              <ImportacaoPdfABCCC />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        <Route path="/genetics/morfologia">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Avaliação Morfológica" showBackButton backUrl="/genetica">
              <MorfologiaPageModern />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rota do Assistente Virtual */}
        <Route path="/assistente">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Assistente Virtual">
              <AssistentePage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rota para gerenciamento de tokens ABCCC */}
        <Route path="/admin/abccc-tokens">
          <ProtectedRoute>
            <LayoutWrapper pageTitle="Gerenciamento de Tokens ABCCC">
              <AbcccTokensPage />
            </LayoutWrapper>
          </ProtectedRoute>
        </Route>
        
        {/* Rota para painel de controle de usuários (admin only) */}
        <Route path="/admin/users">
          <ProtectedRoute>
            <AdminUsersPage />
          </ProtectedRoute>
        </Route>
        
        {/* Pagina 404 */}
        <Route>
          <NotFound />
        </Route>
      </Switch>
    </div>
  );
}

export default App;
