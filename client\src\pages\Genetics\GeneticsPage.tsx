import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocation, Link } from "wouter";
import { Play, Dna, FileUp, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";

// Contexto e componentes
import { GeneticsProvider, useGeneticsContext } from "@/contexts/GeneticsContext";
import HorseSelector from "@/components/genetics/HorseSelector";

// Importando páginas do módulo
import MorfologiaPageModern from "./MorfologiaPageModern";
import DesempenhoPageModern from "./DesempenhoPageModern";
import GenealogiaPageSimple from "./GenealogiaPageSimple";
import SugestoesCruzamentoPage from "./SugestoesCruzamentoPage";

// Importando configuração MVP
import { isModuleVisible } from "@/../../shared/mvp-config";

function GeneticsPageContent() {
  const [activeTab, setActiveTab] = useState("morfologia");
  const [location, navigate] = useLocation();
  const { selectedHorseId, setSelectedHorseId } = useGeneticsContext();
  
  // Detectar a aba pela query string da URL
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const tab = searchParams.get('tab');
    
    if (tab === 'morfologia' || tab === 'genealogia' || 
        tab === 'desempenho' || tab === 'sugestoes') {
      setActiveTab(tab);
    }
  }, [location]);
  
  // Atualizar a URL quando a aba ativa mudar
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    navigate(`/genetica?tab=${tab}`, { replace: true });
  };

  return (
    <div className="container mx-auto py-6">
      <Card className="mb-8">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-3xl font-bold text-primary">Genética e Melhoramento</CardTitle>
              <CardDescription>
                Módulo avançado para avaliação morfológica, genealogia, desempenho e
                sugestões de cruzamento.
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Link to="/genetica/importar-abccc">
                <Button variant="outline" className="gap-2">
                  <FileUp className="h-4 w-4" />
                  <span className="hidden md:inline">Importar</span> ABCCC
                </Button>
              </Link>
              {/* Simulador de Herança Genética - Oculto na versão MVP */}
              {isModuleVisible.simuladorHeranca() && (
                <Link to="/genetica/heranca">
                  <Button variant="outline" className="gap-2">
                    <Dna className="h-4 w-4" />
                    <span className="hidden md:inline">Simulador de</span> Herança Genética
                    <Play className="h-4 w-4 ml-1" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              O módulo de genética permite análises morfológicas detalhadas,
              cálculo de consanguinidade, histórico de desempenho e sugestões
              inteligentes de cruzamento para melhoramento genético.
            </p>
            
            {/* Seletor moderno de cavalos */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Cavalo Selecionado</label>
              <HorseSelector
                selectedHorseId={selectedHorseId}
                onHorseSelect={setSelectedHorseId}
                placeholder="Selecione um cavalo para análise genética..."
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className={`grid mb-8 ${
          isModuleVisible.desempenho() && isModuleVisible.sugestoesCruzamento() 
            ? "grid-cols-4" 
            : isModuleVisible.desempenho() || isModuleVisible.sugestoesCruzamento()
              ? "grid-cols-3"
              : "grid-cols-2"
        }`}>
          <TabsTrigger value="morfologia">Morfologia</TabsTrigger>
          <TabsTrigger value="genealogia">Genealogia</TabsTrigger>
          {/* Desempenho - Oculto na versão MVP */}
          {isModuleVisible.desempenho() && (
            <TabsTrigger value="desempenho">Desempenho</TabsTrigger>
          )}
          {/* Sugestões de Cruzamento - Oculto na versão MVP */}
          {isModuleVisible.sugestoesCruzamento() && (
            <TabsTrigger value="sugestoes">Sugestões de Cruzamento</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="morfologia">
          <MorfologiaPageModern />
        </TabsContent>

        <TabsContent value="genealogia">
          <GenealogiaPageSimple />
        </TabsContent>

        {/* Desempenho - Conteúdo oculto na versão MVP */}
        {isModuleVisible.desempenho() && (
          <TabsContent value="desempenho">
            <DesempenhoPageModern />
          </TabsContent>
        )}

        {/* Sugestões de Cruzamento - Conteúdo oculto na versão MVP */}
        {isModuleVisible.sugestoesCruzamento() && (
          <TabsContent value="sugestoes">
            <SugestoesCruzamentoPage />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

export default function GeneticsPage() {
  return (
    <GeneticsProvider>
      <GeneticsPageContent />
    </GeneticsProvider>
  );
}