/**
 * Configuração dos testes
 */
import { beforeAll } from 'vitest';

// Configurar variáveis de ambiente para testes
beforeAll(() => {
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error';
  
  // Mock das variáveis de ambiente necessárias para os testes
  if (!process.env.DATABASE_URL) {
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
  }
  
  if (!process.env.FIREBASE_PROJECT_ID) {
    process.env.FIREBASE_PROJECT_ID = 'test-project';
    process.env.FIREBASE_PRIVATE_KEY = 'test-key';
    process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
  }
  
  if (!process.env.OPENAI_API_KEY) {
    process.env.OPENAI_API_KEY = 'test-api-key';
  }
});