/**
 * Sistema de Log e Depuração Inteligente para EquiGestor AI
 * Centraliza logging, error tracking e debugging para identificar Error 500
 */

import { createLogger, format, transports } from 'winston';
import path from 'path';
import fs from 'fs';

// Criar diretório de logs se não existir
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Formato customizado para logs estruturados
const customFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format.errors({ stack: true }),
  format.json(),
  format.printf(({ timestamp, level, message, stack, ...meta }) => {
    const logObject = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...(stack && { stack }),
      ...(Object.keys(meta).length > 0 && { metadata: meta })
    };
    return JSON.stringify(logObject, null, 2);
  })
);

// Logger principal
export const logger = createLogger({
  level: process.env.LOG_LEVEL || 'debug',
  format: customFormat,
  transports: [
    // Log de console para desenvolvimento
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.timestamp({ format: 'HH:mm:ss' }),
        format.printf(({ timestamp, level, message, stack }) => {
          return `[${timestamp}] ${level}: ${message}${stack ? '\n' + stack : ''}`;
        })
      )
    }),
    
    // Log de arquivo para erros
    new transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Log combinado para debugging
    new transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Log específico para Error 500
    new transports.File({
      filename: path.join(logDir, 'error-500.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  ]
});

// Interface para contexto de requisição
interface RequestContext {
  method: string;
  url: string;
  user_id?: number;
  userAgent?: string;
  ip?: string;
  requestId: string;
  timestamp: string;
}

// Interface para detalhes de erro
interface ErrorDetails {
  name: string;
  message: string;
  stack?: string;
  code?: string | number;
  statusCode?: number;
  sql?: string;
  parameters?: any[];
}

// Classe para tracking de Error 500
class Error500Tracker {
  private static errors: Map<string, { count: number; lastOccurrence: Date; details: ErrorDetails[] }> = new Map();
  
  static track(error: ErrorDetails, context: RequestContext): void {
    const key = `${context.method}:${context.url}:${error.name}`;
    const existing = this.errors.get(key);
    
    if (existing) {
      existing.count++;
      existing.lastOccurrence = new Date();
      existing.details.push(error);
      
      // Manter apenas os últimos 5 detalhes
      if (existing.details.length > 5) {
        existing.details = existing.details.slice(-5);
      }
    } else {
      this.errors.set(key, {
        count: 1,
        lastOccurrence: new Date(),
        details: [error]
      });
    }
    
    // Log específico para Error 500
    logger.error('ERROR 500 DETECTED', {
      errorKey: key,
      count: this.errors.get(key)?.count,
      context,
      error
    });
  }
  
  static getReport(): any {
    const report = Array.from(this.errors.entries()).map(([key, data]) => ({
      endpoint: key,
      occurrences: data.count,
      lastSeen: data.lastOccurrence.toISOString(),
      recentErrors: data.details.slice(-3)
    }));
    
    return {
      totalUniqueErrors: this.errors.size,
      totalOccurrences: Array.from(this.errors.values()).reduce((sum, data) => sum + data.count, 0),
      errors: report.sort((a, b) => b.occurrences - a.occurrences)
    };
  }
}

// Middleware de logging para Express
export function requestLogger(req: any, res: any, next: any): void {
  const requestId = Math.random().toString(36).substring(2, 15);
  const startTime = Date.now();
  
  const context: RequestContext = {
    method: req.method,
    url: req.originalUrl || req.url,
    user_id: req.headers['user-id'] ? parseInt(req.headers['user-id'] as string) : undefined,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    requestId,
    timestamp: new Date().toISOString()
  };
  
  req.context = context;
  
  // Log da requisição
  logger.info('REQUEST RECEIVED', {
    ...context,
    body: req.method !== 'GET' ? req.body : undefined
  });
  
  // Interceptar resposta
  const originalSend = res.send;
  res.send = function(data: any) {
    const duration = Date.now() - startTime;
    const statusCode = res.statusCode;
    
    const logData = {
      ...context,
      statusCode,
      duration: `${duration}ms`,
      responseSize: data ? Buffer.byteLength(data, 'utf8') : 0
    };
    
    if (statusCode >= 500) {
      logger.error('RESPONSE ERROR 500', {
        ...logData,
        responseData: typeof data === 'string' ? data.substring(0, 1000) : data
      });
    } else if (statusCode >= 400) {
      logger.warn('RESPONSE ERROR 4XX', logData);
    } else {
      logger.info('RESPONSE SUCCESS', logData);
    }
    
    return originalSend.call(this, data);
  };
  
  next();
}

// Middleware de tratamento de erros
export function errorHandler(error: any, req: any, res: any, next: any): void {
  const context: RequestContext = req.context || {
    method: req.method,
    url: req.originalUrl || req.url,
    requestId: 'unknown',
    timestamp: new Date().toISOString()
  };
  
  const errorDetails: ErrorDetails = {
    name: error.name || 'UnknownError',
    message: error.message || 'Erro desconhecido',
    stack: error.stack,
    code: error.code,
    statusCode: error.statusCode || 500,
    sql: error.sql,
    parameters: error.parameters
  };
  
  // Track Error 500
  if (errorDetails.statusCode >= 500) {
    Error500Tracker.track(errorDetails, context);
  }
  
  // Log detalhado do erro
  logger.error('UNHANDLED ERROR', {
    context,
    error: errorDetails,
    requestBody: req.body,
    requestHeaders: req.headers
  });
  
  // Resposta ao cliente
  const isProduction = process.env.NODE_ENV === 'production';
  
  res.status(errorDetails.statusCode || 500).json({
    success: false,
    error: isProduction ? 'Erro interno do servidor' : errorDetails.message,
    requestId: context.requestId,
    timestamp: context.timestamp,
    ...(isProduction ? {} : {
      stack: errorDetails.stack,
      details: errorDetails
    })
  });
}

// Função para criar logger específico por módulo
export function getModuleLogger(moduleName: string) {
  return {
    info: (message: string, meta?: any) => logger.info(`[${moduleName}] ${message}`, meta),
    warn: (message: string, meta?: any) => logger.warn(`[${moduleName}] ${message}`, meta),
    error: (message: string, meta?: any) => logger.error(`[${moduleName}] ${message}`, meta),
    debug: (message: string, meta?: any) => logger.debug(`[${moduleName}] ${message}`, meta)
  };
}

// Função para análise de SQL queries
export function logDatabaseQuery(query: string, parameters?: any[], duration?: number): void {
  logger.debug('DATABASE QUERY', {
    query: query.replace(/\s+/g, ' ').trim(),
    parameters,
    duration: duration ? `${duration}ms` : undefined
  });
}

// Função para logging de operações críticas
export function logCriticalOperation(operation: string, data: any, user_id?: number): void {
  logger.warn('CRITICAL OPERATION', {
    operation,
    user_id,
    data,
    timestamp: new Date().toISOString()
  });
}

// Endpoint para relatório de Error 500
export function getError500Report(): any {
  return Error500Tracker.getReport();
}

// Função para limpar logs antigos
export function cleanOldLogs(): void {
  const now = Date.now();
  const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 dias
  
  try {
    const files = fs.readdirSync(logDir);
    files.forEach(file => {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        logger.info('OLD LOG FILE DELETED', { file });
      }
    });
  } catch (error) {
    logger.error('ERROR CLEANING OLD LOGS', { error });
  }
}

// Inicializar limpeza automática de logs
setInterval(cleanOldLogs, 24 * 60 * 60 * 1000); // Diário

export default logger;