/**
 * Hook para controle de acesso baseado em níveis de usuário
 */

import { useState, useEffect } from 'react';

export type AccessLevel = 'admin' | 'user' | 'readonly';

interface User {
  id: number;
  username: string;
  email: string;
  accessLevel: AccessLevel;
  isActive: boolean;
}

export function useAccessControl() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUser = () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setUser({
            id: userData.id,
            username: userData.username,
            email: userData.email || `${userData.username}@equigestor.com`,
            accessLevel: userData.accessLevel || 'user',
            isActive: userData.isActive !== false
          });
        }
      } catch (error) {
        console.error('Erro ao carregar dados do usuário:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Funções de verificação de permissão
  const isAdmin = () => user?.accessLevel === 'admin';
  const isUser = () => user?.accessLevel === 'user';
  const isReadOnly = () => user?.accessLevel === 'readonly';
  
  // Verifica se pode acessar área administrativa
  const canAccessAdmin = () => isAdmin();
  
  // Verifica se pode criar/editar dados
  const canEdit = () => isAdmin() || isUser();
  
  // Verifica se pode apenas visualizar
  const canOnlyView = () => isReadOnly();

  return {
    user,
    isLoading,
    isAdmin,
    isUser,
    isReadOnly,
    canAccessAdmin,
    canEdit,
    canOnlyView
  };
}