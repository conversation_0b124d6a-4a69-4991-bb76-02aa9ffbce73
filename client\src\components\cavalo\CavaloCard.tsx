import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Pencil, 
  Trash2, 
  Upload, 
  Ruler, 
  Clock, 
  Calendar,
  MoreHorizontal
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import HorseAvatar from '@/components/horse/HorseAvatar';

interface CavaloProps {
  cavalo: any; // Objeto do cavalo
  formatarData: (data: string | null) => string;
  calcularIdade: (data: string | null) => string;
  onView: (id: number) => void;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onUpload?: (id: number) => void;
  onBiometria?: (id: number) => void;
  onHistorico?: (id: number) => void;
  isInactive?: boolean;
  className?: string;
}

/**
 * Componente de card para exibição de informações do cavalo
 * 
 * Este componente oferece uma visualização compacta das informações
 * principais de um cavalo com opções de ações como visualizar, editar e excluir.
 */
export function CavaloCard({
  cavalo,
  formatarData,
  calcularIdade,
  onView,
  onEdit,
  onDelete,
  onUpload,
  onBiometria,
  onHistorico,
  isInactive = false,
  className = "",
}: CavaloProps) {
  const getStatusColor = (status: string | null) => {
    if (!status) return "secondary";
    
    switch (status.toLowerCase()) {
      case 'ativo':
        return 'default';
      case 'vendido':
        return 'secondary';
      case 'inativo':
        return 'destructive';
      default:
        return 'secondary';
    }
  };
  
  const getSexoIcon = (sexo: string | null) => {
    if (!sexo) return null;
    
    switch (sexo.toLowerCase()) {
      case 'macho':
      case 'garanhão':
        return '♂️';
      case 'fêmea':
      case 'égua':
        return '♀️';
      case 'castrado':
        return '⚲';
      default:
        return null;
    }
  };
  
  return (
    <Card className={`overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary ${className}`}>
      <CardHeader className="pb-3 bg-gradient-to-r from-primary/5 to-transparent">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <HorseAvatar
                horse_id={cavalo.id}
                horseName={cavalo.name}
                size="md"
                className="ring-2 ring-primary/20"
              />
              <div>
                <CardTitle className="text-xl font-bold text-primary">{cavalo.nome}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs font-medium">
                    ID: {cavalo.id}
                  </Badge>
                  {cavalo.numero_registro && (
                    <Badge variant="secondary" className="text-xs">
                      Reg: {cavalo.numero_registro}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <Badge
            variant={getStatusColor(cavalo.status) as any}
            className="capitalize font-semibold"
          >
            {cavalo.status || "Não informado"}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pb-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Nascimento:</span>
              <span className="text-muted-foreground">{formatarData(cavalo.birth_date)}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-green-500" />
              <span className="font-medium">Idade:</span>
              <span className="text-muted-foreground">{calcularIdade(cavalo.birth_date)}</span>
            </div>
          </div>
          
          {cavalo.breed && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <span className="w-4 h-4 text-purple-500">🏆</span>
                <span className="font-medium">Raça:</span>
                <span className="text-muted-foreground">{cavalo.breed}</span>
              </div>
            </div>
          )}
          
          {cavalo.sexo && (
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <span className="w-4 h-4">{getSexoIcon(cavalo.sexo) || '⚲'}</span>
                <span className="font-medium">Sexo:</span>
                <span className="text-muted-foreground">{cavalo.sexo}</span>
              </div>
            </div>
          )}
          
          {(cavalo.altura || cavalo.peso) && (
            <div className="pt-2 border-t border-gray-100">
              {cavalo.altura && (
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <Ruler className="h-4 w-4 text-orange-500" />
                    <span className="font-medium">Altura:</span>
                    <span className="text-muted-foreground">{cavalo.altura} m</span>
                  </div>
                </div>
              )}
              
              {cavalo.peso && (
                <div className="flex items-center justify-between text-sm mt-2">
                  <div className="flex items-center gap-2">
                    <span className="w-4 h-4 text-red-500">⚖️</span>
                    <span className="font-medium">Peso:</span>
                    <span className="text-muted-foreground">{cavalo.peso} kg</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        {cavalo.observacoes && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border-l-4 border-l-blue-400">
            <h4 className="text-sm font-medium text-gray-700 mb-1">Observações</h4>
            <p className="text-sm text-gray-600 line-clamp-3">
              {cavalo.observacoes}
            </p>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-0 pb-4">
        <div className="flex justify-between w-full gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onView(cavalo.id)}
            className="flex-1"
          >
            <Eye className="mr-2 h-4 w-4" />
            Ver Detalhes
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="px-3">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ações</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={() => onView(cavalo.id)}>
                <Eye className="mr-2 h-4 w-4" />
                <span>Ver Detalhes</span>
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onEdit(cavalo.id)}>
                <Pencil className="mr-2 h-4 w-4" />
                <span>Editar</span>
              </DropdownMenuItem>
              
              {onUpload && (
                <DropdownMenuItem onClick={() => onUpload(cavalo.id)}>
                  <Upload className="mr-2 h-4 w-4" />
                  <span>Upload de Arquivos</span>
                </DropdownMenuItem>
              )}
              
              {onBiometria && (
                <DropdownMenuItem onClick={() => onBiometria(cavalo.id)}>
                  <Ruler className="mr-2 h-4 w-4" />
                  <span>Biometria</span>
                </DropdownMenuItem>
              )}
              
              {onHistorico && (
                <DropdownMenuItem onClick={() => onHistorico(cavalo.id)}>
                  <Clock className="mr-2 h-4 w-4" />
                  <span>Histórico</span>
                </DropdownMenuItem>
              )}
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={() => onDelete(cavalo.id)}
                className="text-orange-600 focus:text-orange-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                <span>Remover do plantel</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  );
}