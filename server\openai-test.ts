// Teste básico da API OpenAI
import { openai } from './openai-wrapper.js';
import dotenv from 'dotenv';

// Carrega variáveis de ambiente
dotenv.config();

async function testOpenAI() {
  try {
    console.log('Iniciando teste da API OpenAI...');
    
    // Teste simples de completions
    const completion = await openai.chat.completions.create({
      model: "gpt-4o", // o modelo mais recente da OpenAI é "gpt-4o" que foi lançado em 13 de maio de 2024
      messages: [
        { role: "system", content: "Você é um especialista em cavalos Crioulos." },
        { role: "user", content: "Quais são as características distintas do cavalo Crioulo brasileiro?" }
      ],
      max_tokens: 150
    });

    console.log('Resposta da API:');
    console.log(completion.choices[0].message.content);
    console.log('Teste concluído com sucesso!');
    
    return {
      success: true,
      response: completion.choices[0].message.content
    };
  } catch (error) {
    console.error('Erro ao testar a API OpenAI:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Executa o teste automaticamente em modo módulo ES
console.log('Executando teste diretamente...');
testOpenAI()
  .then(result => {
    console.log('Resultado do teste:', result);
    if (!result.success) {
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Erro fatal:', err);
    process.exit(1);
  });

export { testOpenAI };