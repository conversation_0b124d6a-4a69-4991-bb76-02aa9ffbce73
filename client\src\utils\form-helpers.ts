/**
 * Utilitários para formulários - EditHorseSimple refatoração
 */

// Converte enum para snake_case (remove acento, lowercase, troca espaço por _)
export function toSnakeEnum(value: string): string {
  return value
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[()]/g, ''); // Remove parênteses
}

// Converte snake_case para Title Case para display
export function fromSnakeEnum(value: string): string {
  return value
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Converte data para formato ISO (YYYY-MM-DD)
export function isoDate(date: string | Date | null): string {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString().split('T')[0];
}

// Converte data ISO para formato brasileiro (DD/MM/YYYY)
export function brDate(isoDate: string): string {
  if (!isoDate) return '';
  const [year, month, day] = isoDate.split('-');
  return `${day}/${month}/${year}`;
}

// Mapeia sexo display para snake_case
export const sexoMapping = {
  'Macho': 'macho',
  'Fêmea': 'femea', 
  'Macho (Castrado)': 'macho_castrado',
  'Garanhão': 'garanhao',
  'Égua': 'egua'
} as const;

// Mapeia sexo snake_case para display
export const sexoDisplayMapping = {
  'macho': 'Macho',
  'femea': 'Fêmea',
  'macho_castrado': 'Macho (Castrado)',
  'garanhao': 'Garanhão',
  'egua': 'Égua'
} as const;

export type SexoDisplay = keyof typeof sexoMapping;
export type SexoSnake = typeof sexoMapping[SexoDisplay];