// Interfaces para o módulo de genética

import { C<PERSON><PERSON> } from "./cavalo";

// Interfaces para morfologia
export interface AvaliacaoMorfologica {
  id: number;
  horse_id: number;
  user_id: number;
  dataMedicao: string;
  avaliador: string;
  
  // Pontuações de 1 a 10 para diferentes características
  pontuacaoCabeca: number;
  pontuacaoPescoco: number;
  pontuacaoEspadua: number;
  pontuacaoDorso: number;
  pontuacaoGarupa: number;
  pontuacaoMembrosDianteiros: number;
  pontuacaoMembrosPosteriores: number;
  pontuacaoAndamento: number;
  
  // Pontuação total (soma das pontuações ou pontuação geral)
  pontuacaoTotal: number;
  
  // Observações do avaliador
  observacoes?: string;
  
  // Dados para gráfico radar
  pontosFortes: string[];
  pontosFracos: string[];
}

// Interface para informações de imagens dos animais na árvore genealógica
export interface ImagemAncestral {
  cavaloId: number;
  imageUrl: string;
  descricao?: string;
}

// Interfaces para genealogia
export interface Genealogia {
  cavalo: Cavalo;
  pai?: Cavalo;
  mae?: Cavalo;
  avoPaterno?: Cavalo;
  avoPaterna?: Cavalo;
  avoMaterno?: Cavalo;
  avoMaterna?: Cavalo;
  
  // Bisavós paternos (array [0-3] para pai, [4-7] para mãe)
  bisavosPai?: Cavalo[];
  bisavosMae?: Cavalo[];
  
  // Dados de consanguinidade
  consanguinidade: number | null; // Porcentagem (ex: 18.75)
  bisavosConhecidos: number; // Total de bisavós conhecidos (máximo 8)
  arvoreGenealogica?: string; // URL da imagem ou estrutura para renderizar árvore
  observacoes?: string; // Observações sobre a genealogia
  
  // Imagens dos animais na árvore genealógica
  imagensAncestral?: ImagemAncestral[]; // Lista de imagens associadas aos ancestrais
}

// Interfaces para desempenho
export interface RegistroDesempenho {
  id: number;
  horse_id: number;
  user_id: number;
  dataEvento: string;
  data?: string; // Campo alternativo para compatibilidade
  tipoEvento: string; // Competição, Prova, Treinamento, etc.
  nomeEvento: string;
  categoria?: string;
  posicao?: number; // Posição final (1º, 2º, etc.)
  tempoOuPontuacao?: string; // Tempo ou pontuação conforme o evento
  observacoes?: string;
  desempenhoDetalhes?: string; // Detalhes específicos sobre o desempenho
  imagensUrl?: string[]; // URLs de imagens do evento
}

// Interfaces para sugestões de cruzamento
export interface SugestaoCruzamento {
  id: number;
  horseIdBase: number | null; // ID do cavalo base para a análise
  horseIdSugerido: number; // ID do cavalo sugerido para cruzamento
  pontuacaoCompatibilidade: number; // Porcentagem de compatibilidade (0-100)
  consanguinidade: number | null; // Porcentagem (ex: 12.5)
  pontosFortes: string[]; // Pontos fortes compatíveis
  recomendacoes: string; // Descrição das recomendações
  
  // Para compatibilidade com a interface anterior
  cavalo?: Cavalo; // Cavalo sugerido para cruzamento
  compatibilidade?: number; // Porcentagem de compatibilidade (0-100)
  pontosFortesCompativeis?: string[];
  probabilidadeCaracteristicas?: {
    [caracteristica: string]: number; // Probabilidade (0-100) de passar certas características
  };
  descricaoCompatibilidade?: string;
  objetivosCruzamento?: string[]; // Para quais objetivos este cruzamento é indicado
}

// Interface para respostas da API
export interface RespostaGenetica<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}