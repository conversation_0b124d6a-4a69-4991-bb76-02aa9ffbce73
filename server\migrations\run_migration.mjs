import pkg from 'pg';
const { Pool } = pkg;
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

// Configuração da conexão com o banco de dados
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

// Obter o diretório atual
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  const client = await pool.connect();
  try {
    // Ler o arquivo SQL
    const sqlFile = path.join(__dirname, 'add_indice_cefalico.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    console.log('Executando migração...');
    await client.query(sql);
    console.log('Migração concluída com sucesso!');
  } catch (err) {
    console.error('Erro ao executar migração:', err);
  } finally {
    client.release();
    await pool.end();
  }
}

runMigration();
