import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import { useModalButtonSize } from "@/lib/modal-utils";

/**
 * ModalForm
 * 
 * Componente de formulário otimizado para uso em modais, com responsividade
 * aprimorada para dispositivos móveis.
 */

interface ModalFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
  showCancelButton?: boolean;
  children: React.ReactNode;
  footerClassName?: string;
}

const ModalForm = React.forwardRef<HTMLFormElement, ModalFormProps>(
  (
    {
      className,
      onSubmit,
      onCancel,
      isSubmitting = false,
      submitLabel = "Salvar",
      cancelLabel = "Cancelar",
      showCancelButton = true,
      children,
      footerClassName,
      ...props
    },
    ref
  ) => {
    const isMobile = useIsMobile();
    const buttonSize = useModalButtonSize();
    
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      onSubmit(e);
    };

    return (
      <form
        ref={ref}
        onSubmit={handleSubmit}
        className={cn("space-y-4", className)}
        {...props}
      >
        <div className={cn(
          isMobile ? "space-y-4" : "space-y-3",
          "modal-form-content"
        )}>
          {children}
        </div>

        <div className={cn(
          isMobile 
            ? "flex flex-col space-y-2 pt-4" 
            : "flex flex-row-reverse justify-start space-x-2 space-x-reverse pt-2",
          "modal-form-footer",
          footerClassName
        )}>
          <Button
            type="submit"
            disabled={isSubmitting}
            className={cn(buttonSize.className)}
          >
            {isSubmitting ? "Salvando..." : submitLabel}
          </Button>
          
          {showCancelButton && onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className={cn(buttonSize.className)}
              disabled={isSubmitting}
            >
              {cancelLabel}
            </Button>
          )}
        </div>
      </form>
    );
  }
);

ModalForm.displayName = "ModalForm";

export { ModalForm };

/**
 * ModalFormSection
 * 
 * Componente para agrupar seções de formulário em modais
 */
interface ModalFormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  description?: string;
  children: React.ReactNode;
}

const ModalFormSection = React.forwardRef<HTMLDivElement, ModalFormSectionProps>(
  ({ className, title, description, children, ...props }, ref) => {
    const isMobile = useIsMobile();
    
    return (
      <div
        ref={ref}
        className={cn(
          "modal-form-section",
          isMobile ? "py-3" : "py-2",
          className
        )}
        {...props}
      >
        {title && (
          <h4 className={cn(
            "text-sm font-medium mb-2",
            isMobile && "text-base"
          )}>
            {title}
          </h4>
        )}
        
        {description && (
          <p className="text-sm text-muted-foreground mb-3">{description}</p>
        )}
        
        <div className={cn(
          "modal-form-section-content",
          isMobile ? "space-y-4" : "space-y-3"
        )}>
          {children}
        </div>
      </div>
    );
  }
);

ModalFormSection.displayName = "ModalFormSection";

export { ModalFormSection };

/**
 * ModalFormRow
 * 
 * Componente para criar linhas de formulário responsivas em modais
 */
interface ModalFormRowProps extends React.HTMLAttributes<HTMLDivElement> {
  columns?: 1 | 2 | 3 | 4;
  children: React.ReactNode;
}

const ModalFormRow = React.forwardRef<HTMLDivElement, ModalFormRowProps>(
  ({ className, columns = 2, children, ...props }, ref) => {
    const isMobile = useIsMobile();
    
    // Em dispositivos móveis, sempre usar uma coluna
    const effectiveColumns = isMobile ? 1 : columns;
    
    return (
      <div
        ref={ref}
        className={cn(
          "modal-form-row grid gap-4",
          {
            "grid-cols-1": effectiveColumns === 1,
            "grid-cols-2": effectiveColumns === 2,
            "grid-cols-3": effectiveColumns === 3,
            "grid-cols-4": effectiveColumns === 4,
          },
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ModalFormRow.displayName = "ModalFormRow";

export { ModalFormRow };
