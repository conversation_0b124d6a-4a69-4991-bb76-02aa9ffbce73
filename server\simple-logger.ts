/**
 * Simple logging system for EquiGestor AI - No external dependencies
 */

import fs from 'fs';
import path from 'path';

// Create logs directory if it doesn't exist
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context?: any;
}

class SimpleLogger {
  private logFile: string;
  private errorFile: string;

  constructor() {
    this.logFile = path.join(logDir, 'app-simple.log');
    this.errorFile = path.join(logDir, 'error-simple.log');
  }

  private writeLog(entry: LogEntry, toErrorFile = false): void {
    const logLine = `[${entry.timestamp}] ${entry.level.toUpperCase()}: ${entry.message}${
      entry.context ? ' | ' + JSON.stringify(entry.context) : ''
    }\n`;

    try {
      fs.appendFileSync(toErrorFile ? this.errorFile : this.logFile, logLine);
    } catch (err) {
      console.error('Failed to write to log file:', err);
    }
  }

  info(message: string, context?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'info',
      message,
      context
    };
    
    console.log(`[INFO] ${message}`, context || '');
    this.writeLog(entry);
  }

  error(message: string, context?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'error',
      message,
      context
    };
    
    console.error(`[ERROR] ${message}`, context || '');
    this.writeLog(entry, true);
    this.writeLog(entry);
  }

  warn(message: string, context?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'warn',
      message,
      context
    };
    
    console.warn(`[WARN] ${message}`, context || '');
    this.writeLog(entry);
  }

  debug(message: string, context?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'debug',
      message,
      context
    };
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DEBUG] ${message}`, context || '');
      this.writeLog(entry);
    }
  }
}

// Export simple logger instance
export const logger = new SimpleLogger();

// Simple request logging middleware
export function requestLogger(req: any, res: any, next: any): void {
  const start = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);
  
  req.requestId = requestId;
  
  logger.info(`Request started: ${req.method} ${req.url}`, {
    requestId,
    user_id: req.headers['user-id'],
    userAgent: req.headers['user-agent']?.substring(0, 100)
  });

  const originalSend = res.send;
  res.send = function(data: any) {
    const duration = Date.now() - start;
    const statusCode = res.statusCode;
    
    if (statusCode >= 500) {
      logger.error(`Request completed with error: ${req.method} ${req.url}`, {
        requestId,
        statusCode,
        duration: `${duration}ms`,
        user_id: req.headers['user-id']
      });
    } else {
      logger.info(`Request completed: ${req.method} ${req.url}`, {
        requestId,
        statusCode,
        duration: `${duration}ms`
      });
    }
    
    return originalSend.call(this, data);
  };

  next();
}

// Simple error handler
export function errorHandler(error: any, req: any, res: any, next: any): void {
  const requestId = req.requestId || 'unknown';
  
  logger.error('Unhandled error occurred', {
    requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    request: {
      method: req.method,
      url: req.url,
      user_id: req.headers['user-id']
    }
  });

  if (!res.headersSent) {
    res.status(500).json({
      error: 'Internal Server Error',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}

export default logger;