import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Server, 
  Database, 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock,
  Users,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface SystemStatus {
  status: string;
  timestamp: string;
  version: string;
  database: string;
  statistics: {
    totalCavalos: number;
    totalUsuarios: number;
    atividadeHoje: {
      manejos: number;
      veterinario: number;
      nutricao: number;
      reproducao: number;
    };
  };
  modules: Array<{
    module: string;
    status: 'online' | 'error';
  }>;
  mvpStatus: string;
  health: 'good' | 'warning' | 'error';
}

export function SystemStatusCard() {
  const { data: systemStatus, isLoading, error } = useQuery<SystemStatus>({
    queryKey: ['system-status'],
    queryFn: async () => {
      const response = await fetch('/api/system-status', {
        headers: {
          'user-id': '1', // Admin user ID
        },
      });
      if (!response.ok) {
        throw new Error('Erro ao buscar status do sistema');
      }
      return response.json();
    },
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !systemStatus) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            Status do Sistema - Erro
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-600">
            Não foi possível carregar o status do sistema.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'operational':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'operational':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <Card className="border-blue-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-600">
          <Server className="h-5 w-5" />
          Status do Sistema (Admin)
          <Badge variant="outline" className={getStatusColor(systemStatus.status)}>
            {getStatusIcon(systemStatus.status)}
            <span className="ml-1 capitalize">{systemStatus.status}</span>
          </Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Última atualização: {format(new Date(systemStatus.timestamp), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Estatísticas gerais */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 mx-auto mb-2">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <p className="text-2xl font-bold">{systemStatus.statistics.totalCavalos}</p>
            <p className="text-xs text-muted-foreground">Total Cavalos</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 mx-auto mb-2">
              <Users className="h-5 w-5 text-green-600" />
            </div>
            <p className="text-2xl font-bold">{systemStatus.statistics.totalUsuarios}</p>
            <p className="text-xs text-muted-foreground">Usuários Ativos</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 mx-auto mb-2">
              <Database className="h-5 w-5 text-purple-600" />
            </div>
            <p className="text-2xl font-bold capitalize">{systemStatus.database}</p>
            <p className="text-xs text-muted-foreground">Database</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-orange-100 mx-auto mb-2">
              <Zap className="h-5 w-5 text-orange-600" />
            </div>
            <p className="text-2xl font-bold">{systemStatus.mvpStatus}</p>
            <p className="text-xs text-muted-foreground">MVP Status</p>
          </div>
        </div>

        {/* Atividade hoje */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Atividade Hoje
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-3 rounded-lg bg-gray-50">
              <p className="text-lg font-semibold">{systemStatus.statistics.atividadeHoje.manejos}</p>
              <p className="text-xs text-muted-foreground">Manejos</p>
            </div>
            <div className="text-center p-3 rounded-lg bg-gray-50">
              <p className="text-lg font-semibold">{systemStatus.statistics.atividadeHoje.veterinario}</p>
              <p className="text-xs text-muted-foreground">Veterinário</p>
            </div>
            <div className="text-center p-3 rounded-lg bg-gray-50">
              <p className="text-lg font-semibold">{systemStatus.statistics.atividadeHoje.nutricao}</p>
              <p className="text-xs text-muted-foreground">Nutrição</p>
            </div>
            <div className="text-center p-3 rounded-lg bg-gray-50">
              <p className="text-lg font-semibold">{systemStatus.statistics.atividadeHoje.reproducao}</p>
              <p className="text-xs text-muted-foreground">Reprodução</p>
            </div>
          </div>
        </div>

        {/* Status dos módulos */}
        <div>
          <h4 className="text-sm font-medium mb-3">Status dos Módulos</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {systemStatus.modules.map((module) => (
              <div key={module.module} className="flex items-center justify-between p-3 rounded-lg border">
                <span className="text-sm font-medium">{module.module}</span>
                <Badge variant="outline" className={getStatusColor(module.status)}>
                  {getStatusIcon(module.status)}
                  <span className="ml-1 capitalize">{module.status}</span>
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Informações adicionais */}
        <div className="pt-4 border-t text-center">
          <p className="text-xs text-muted-foreground">
            Versão {systemStatus.version} • Sistema {systemStatus.health === 'good' ? 'Saudável' : 'Com Problemas'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}