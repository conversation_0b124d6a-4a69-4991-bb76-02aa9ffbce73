import * as React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

/**
 * Utilitário para calcular o tamanho máximo de um modal com base no dispositivo
 *
 * @param desktopMaxWidth Largura máxima em desktop (ex: '600px', '50%', etc)
 * @param mobileMaxHeight Altura máxima em mobile (ex: '90vh', '500px', etc)
 * @returns Um objeto com as classes CSS apropriadas
 */
export function useModalDimensions(desktopMaxWidth: string = '600px', mobileMaxHeight: string = '90vh') {
  const isMobile = useIsMobile();

  if (isMobile) {
    return {
      className: 'w-full h-full',
      style: {
        maxHeight: mobileMaxHeight,
        overflowY: 'auto',
        overscrollBehavior: 'contain'
      }
    };
  }

  return {
    className: `max-w-[${desktopMaxWidth}]`,
    style: {}
  };
}

/**
 * Utilitário para prevenir scroll do body quando um modal está aberto
 *
 * @param isOpen Estado que indica se o modal está aberto
 */
export function usePreventBodyScroll(isOpen: boolean) {
  React.useEffect(() => {
    if (isOpen) {
      // Salvar a posição atual do scroll
      const scrollY = window.scrollY;

      // Adicionar classe para prevenir scroll
      document.body.classList.add('overflow-hidden');
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
    } else {
      // Restaurar scroll
      const scrollY = document.body.style.top;
      document.body.classList.remove('overflow-hidden');
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }

    return () => {
      // Cleanup
      document.body.classList.remove('overflow-hidden');
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isOpen]);
}

/**
 * Utilitário para ajustar o tamanho do botão com base no dispositivo
 *
 * @returns Um objeto com as classes CSS apropriadas para botões em modais
 */
export function useModalButtonSize() {
  const isMobile = useIsMobile();

  return {
    className: isMobile ? 'h-12 text-base w-full mb-1' : '',
  };
}
