import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CalendarIcon, Stethoscope, Syringe, FileText, DollarSign } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON>avalo } from '@shared/schema';

// Schema moderno focado na experiência veterinária real
const procedimentoSchema = z.object({
  cavalo_id: z.number({ required_error: "Selecione um animal" }),
  tipo_procedimento: z.string().min(1, { message: "Selecione o tipo de procedimento" }),
  data_procedimento: z.string().min(1, { message: "Selecione a data" }),
  veterinario_responsavel: z.string().min(1, { message: "Informe o veterinário responsável" }),
  diagnostico: z.string().optional(),
  tratamento: z.string().optional(),
  medicamentos: z.string().optional(),
  observacoes: z.string().optional(),
  custo: z.coerce.number().min(0).optional(),
  proxima_consulta: z.string().optional(),
  status: z.string().default("concluido"),
  user_id: z.number(),
});

type ProcedimentoFormData = z.infer<typeof procedimentoSchema>;

interface Props {
  onSubmit: (data: ProcedimentoFormData) => void;
  defaultValues?: Partial<ProcedimentoFormData>;
  isSubmitting: boolean;
  cavalos: Cavalo[];
  user_id: number;
  onCancel?: () => void;
}

// Tipos de procedimentos organizados por categoria
const PROCEDIMENTO_CATEGORIES = {
  preventivos: {
    label: "Procedimentos Preventivos",
    icon: Syringe,
    color: "bg-green-50 border-green-200",
    items: [
      { value: "Vacinação", label: "Vacinação" },
      { value: "Vermifugação", label: "Vermifugação" },
      { value: "Controle de ectoparasitas", label: "Controle de Ectoparasitas" },
    ]
  },
  clinicos: {
    label: "Procedimentos Clínicos",
    icon: Stethoscope,
    color: "bg-blue-50 border-blue-200",
    items: [
      { value: "Exame clínico", label: "Exame Clínico Geral" },
      { value: "Consulta de rotina", label: "Consulta de Rotina" },
      { value: "Acompanhamento", label: "Acompanhamento" },
    ]
  },
  diagnosticos: {
    label: "Diagnósticos",
    icon: FileText,
    color: "bg-purple-50 border-purple-200",
    items: [
      { value: "Exame laboratorial", label: "Exame Laboratorial" },
      { value: "Radiografia", label: "Radiografia" },
      { value: "Ultrassonografia", label: "Ultrassonografia" },
      { value: "Endoscopia", label: "Endoscopia" },
    ]
  },
  terapeuticos: {
    label: "Procedimentos Terapêuticos",
    icon: DollarSign,
    color: "bg-orange-50 border-orange-200",
    items: [
      { value: "Tratamento medicamentoso", label: "Tratamento Medicamentoso" },
      { value: "Fisioterapia", label: "Fisioterapia" },
      { value: "Acupuntura", label: "Acupuntura" },
      { value: "Cirurgia", label: "Cirurgia" },
      { value: "Tratamento dentário", label: "Tratamento Dentário" },
      { value: "Casqueamento", label: "Casqueamento" },
    ]
  }
};

export const ProcedimentoVetFormModern: React.FC<Props> = ({
  onSubmit,
  defaultValues,
  isSubmitting,
  cavalos,
  user_id,
  onCancel
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  
  const form = useForm<ProcedimentoFormData>({
    resolver: zodResolver(procedimentoSchema),
    defaultValues: {
      cavalo_id: 0,
      tipo_procedimento: '',
      data_procedimento: format(new Date(), 'yyyy-MM-dd'),
      veterinario_responsavel: '',
      diagnostico: '',
      tratamento: '',
      medicamentos: '',
      observacoes: '',
      custo: 0,
      proxima_consulta: '',
      status: 'concluido',
      user_id,
      ...defaultValues
    },
  });

  const watchedTipo = form.watch('tipo_procedimento');
  
  // Detectar categoria baseada no tipo selecionado
  React.useEffect(() => {
    const categoria = Object.entries(PROCEDIMENTO_CATEGORIES).find(([_, cat]) =>
      cat.items.some(item => item.value === watchedTipo)
    );
    setSelectedCategory(categoria ? categoria[0] : '');
  }, [watchedTipo]);

  const handleSubmit = (data: ProcedimentoFormData) => {
    // Limpar campos vazios antes de enviar
    const cleanData = { ...data };
    if (!cleanData.proxima_consulta) delete cleanData.proxima_consulta;
    if (!cleanData.custo) delete cleanData.custo;
    
    onSubmit(cleanData);
  };

  const selectedCavalo = cavalos.find(c => c.id === form.watch('cavalo_id'));

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-2 sm:p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[95vh] overflow-hidden">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <Stethoscope className="h-5 w-5 text-blue-600" />
            Novo Procedimento Veterinário
          </CardTitle>
          {selectedCavalo && (
            <Badge variant="outline" className="w-fit">
              {selectedCavalo.name} - {selectedCavalo.breed}
            </Badge>
          )}
        </CardHeader>
        
        <CardContent className="overflow-y-auto max-h-[calc(95vh-120px)]">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Seleção de Animal */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="cavalo_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Animal *</FormLabel>
                      <Select 
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        defaultValue={field.value?.toString() || ""}
                      >
                        <FormControl>
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Selecione um animal" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {cavalos.map((cavalo) => (
                            <SelectItem key={cavalo.id} value={cavalo.id.toString()}>
                              <div className="flex flex-col">
                                <span className="font-medium">{cavalo.name}</span>
                                <span className="text-xs text-muted-foreground">{cavalo.breed}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="data_procedimento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Data do Procedimento *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} className="h-10" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Tipo de Procedimento por Categoria */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="tipo_procedimento"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Tipo de Procedimento *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Selecione o tipo de procedimento" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="max-h-80">
                          {Object.entries(PROCEDIMENTO_CATEGORIES).map(([key, category]) => (
                            <div key={key}>
                              <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
                                {category.label}
                              </div>
                              {category.items.map((item) => (
                                <SelectItem key={item.value} value={item.value}>
                                  {item.label}
                                </SelectItem>
                              ))}
                            </div>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Informações do Veterinário */}
              <div className="space-y-4">
                <h3 className="text-base font-semibold flex items-center gap-2">
                  <Stethoscope className="h-4 w-4" />
                  Informações do Veterinário
                </h3>
                
                <FormField
                  control={form.control}
                  name="veterinario_responsavel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Veterinário Responsável *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nome do veterinário" className="h-10" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Detalhes Clínicos */}
              <div className="space-y-4">
                <h3 className="text-base font-semibold flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Detalhes Clínicos
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="diagnostico"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Diagnóstico</FormLabel>
                        <FormControl>
                          <Textarea {...field} rows={3} placeholder="Diagnóstico clínico..." />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tratamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Tratamento</FormLabel>
                        <FormControl>
                          <Textarea {...field} rows={3} placeholder="Descrição do tratamento..." />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="medicamentos"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Medicamentos</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Medicamentos utilizados..." className="h-10" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Informações Adicionais */}
              <div className="space-y-4">
                <h3 className="text-base font-semibold flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Informações Adicionais
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="custo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Custo (R$)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01" 
                            {...field} 
                            placeholder="0,00" 
                            className="h-10" 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="proxima_consulta"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Próxima Consulta</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} className="h-10" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="observacoes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Observações</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} placeholder="Observações adicionais..." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="w-full sm:w-auto h-10 px-6"
                >
                  {isSubmitting ? 'Salvando...' : 'Salvar Procedimento'}
                </Button>
                
                {onCancel && (
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={onCancel}
                    className="w-full sm:w-auto h-10 px-6"
                  >
                    Cancelar
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

// Comentário explicativo das melhorias implementadas:
// - Interface responsiva com modal fullscreen em mobile e card em desktop
// - Organização inteligente por categorias de procedimentos veterinários
// - Formulário estruturado em seções lógicas com separadores visuais
// - Validação robusta com limpeza de campos vazios antes do envio
// - Design moderno seguindo padrões de UX do mercado atual
// - Ícones contextuais para melhor identificação visual
// - Tratamento adequado de datas opcionais evitando erro de string vazia