import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AlertCircle, HelpCircle, AlertTriangle, CheckCircle, AlertOctagon } from 'lucide-react';

interface ConsanguinityIndicatorProps {
  value: number; // Valor entre 0 e 1 (0% a 100%)
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showInfo?: boolean;
  className?: string;
}

/**
 * Componente que exibe um indicador visual do coeficiente de consanguinidade
 * Escala de cores:
 * - Verde: 0-6.25% (Baixo risco genético)
 * - Amarelo: 6.25%-12.5% (Médio risco - equivalente a primos de primeiro grau)
 * - Laranja: 12.5%-25% (Alto risco - equivalente a tio-sobrinha)
 * - Vermelho: >25% (Muito alto risco - equivalente a irmãos ou pais-filhos)
 */
const ConsanguinityIndicator: React.FC<ConsanguinityIndicatorProps> = ({
  value,
  size = 'md',
  showLabel = true,
  showInfo = true,
  className = '',
}) => {
  // Garantir que o coeficiente esteja entre 0 e 1
  const safeValue = Math.max(0, Math.min(1, value));
  const percentage = Math.round(safeValue * 100);
  
  // Determinar categoria de risco
  const getRiskCategory = () => {
    if (safeValue <= 0.0625) return { level: 'Baixo', color: 'bg-green-500', icon: CheckCircle };
    if (safeValue <= 0.125) return { level: 'Médio', color: 'bg-yellow-500', icon: AlertCircle };
    if (safeValue <= 0.25) return { level: 'Alto', color: 'bg-orange-500', icon: AlertTriangle };
    return { level: 'Muito Alto', color: 'bg-red-600', icon: AlertOctagon };
  };
  
  const riskCategory = getRiskCategory();
  const Icon = riskCategory.icon;
  
  // Determinar tamanho com base na propriedade
  const getSize = () => {
    switch (size) {
      case 'sm': return 'h-1 mt-1';
      case 'lg': return 'h-3 mt-3';
      default: return 'h-2 mt-2'; // md
    }
  };

  // Determinar tamanho da fonte
  const getFontSize = () => {
    switch (size) {
      case 'sm': return 'text-xs';
      case 'lg': return 'text-lg';
      default: return 'text-sm'; // md
    }
  };
  
  // Exemplo de relacionamento equivalente para ser usado no tooltip
  const getEquivalentRelationship = () => {
    if (safeValue <= 0.0625) return 'Primos de segundo grau';
    if (safeValue <= 0.125) return 'Primos de primeiro grau';
    if (safeValue <= 0.25) return 'Tio-sobrinha ou avô-neta';
    return 'Irmãos ou pais-filhos';
  };
  
  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center justify-between mb-1">
        {showLabel && (
          <div className={`flex items-center gap-1 ${getFontSize()}`}>
            <Icon 
              className={`h-4 w-4 ${
                value <= 0.0625 ? 'text-green-500' : 
                value <= 0.125 ? 'text-yellow-500' : 
                value <= 0.25 ? 'text-orange-500' : 
                'text-red-600'
              }`} 
            />
            <span>Coeficiente de Consanguinidade: <strong>{percentage}%</strong></span>
            <span className="font-medium ml-1">
              ({riskCategory.level})
            </span>
          </div>
        )}
        
        {showInfo && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p className="text-sm">
                  O coeficiente de consanguinidade indica a probabilidade de um animal herdar genes 
                  idênticos de ambos os pais. Valores elevados aumentam o risco de problemas genéticos.
                </p>
                <ul className="text-xs mt-2">
                  <li>• <strong>Baixo</strong>: &lt;6.25% - Risco mínimo</li>
                  <li>• <strong>Médio</strong>: 6.25-12.5% - Equivalente a {getEquivalentRelationship()}</li>
                  <li>• <strong>Alto</strong>: 12.5-25% - Maior probabilidade de problemas</li>
                  <li>• <strong>Muito Alto</strong>: &gt;25% - Risco máximo</li>
                </ul>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      
      <div className={`${getSize()} rounded-sm w-full bg-secondary overflow-hidden`}>
        <div 
          className={`h-full ${riskCategory.color} transition-all`} 
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    </div>
  );
};

export default ConsanguinityIndicator;