import { pgTable, text, serial, integer, boolean, timestamp, real, date } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";
import { 
  horseStatusEnum, 
  horseSexoEnum,
  nomeSchema,
  observacoesSchema,
  numeroRegistroSchema,
  pesoSchema,
  alturaSchema,
  currencySchema,
  optionalDateSchema,
  statusSchema,
  sexoSchema,
  UserContext,
  AuditFields 
} from './core';

// ============================================================================
// DATABASE TABLES
// ============================================================================

export const horses = pgTable("horses", {
  id: serial("id").primaryKey(),
  user_id: integer("user_id").notNull(),
  
  // Basic horse information
  name: text("name").notNull(),
  numeroRegistro: text("numero_registro"),
  microchip: text("microchip"),
  birthDate: date("birthDate"),
  sexo: text("sexo"),
  cor: text("cor"),
  pelagem: text("pelagem"),
  
  // Physical measurements
  peso: real("peso"), // kg
  altura: real("altura"), // cm
  
  // Genealogy (foreign keys to same table)
  paiId: integer("pai_id"),
  maeId: integer("mae_id"),
  linhagemPaterna: text("linhagem_paterna"),
  linhagemMaterna: text("linhagem_materna"),
  
  // Status and tracking
  status: text("status").default('Ativo'),
  dataEntrada: date("data_entrada"),
  dataSaida: date("data_saida"),
  
  // Location and breeding information
  localNascimento: text("local_nascimento"),
  criador: text("criador"),
  proprietarioAtual: text("proprietario_atual"),
  raca: text("raca"),
  
  // Documentation
  certificadoRegistro: text("certificado_registro"),
  
  // Financial information
  valorEstimado: real("valor_estimado"),
  
  // Purpose and additional info
  finalidade: text("finalidade"),
  observacoes: text("observacoes"),
  
  // File attachments
  arquivoIds: text("arquivo_ids").array(),
  
  // Audit fields
  created_at: timestamp("created_at"),
  updatedAt: timestamp("updated_at"),
  
  // Grandparents genealogy
  avoPaternoId: integer("avo_paterno_id"),
  avoPaternoNome: text("avo_paterno_nome"),
  avoMaternaId: integer("avo_materna_id"),
  avoMaternaNome: text("avo_materna_nome"),
});

// ============================================================================
// RELATIONS
// ============================================================================

export const horsesRelations = relations(horses, ({ one, many }) => ({
  // Self-referencing relationships for genealogy
  pai: one(horses, {
    fields: [horses.paiId],
    references: [horses.id],
    relationName: "pai"
  }),
  mae: one(horses, {
    fields: [horses.maeId],
    references: [horses.id],
    relationName: "mae"
  }),
  
  // Children relationships
  filhosPorPai: many(horses, {
    relationName: "pai"
  }),
  filhosPorMae: many(horses, {
    relationName: "mae"
  }),
}));

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

// Core horse validation schema
export const horseSchema = z.object({
  id: z.number().int().positive().optional(),
  user_id: z.number().int().positive(),
  
  // Required fields
  name: nomeSchema,
  
  // Registration and identification
  numeroRegistro: z.string().optional().nullable(),
  microchip: z.string().optional().nullable(),
  
  // Basic information
  birthDate: optionalDateSchema,
  sexo: z.string().optional().nullable(),
  cor: z.string().max(30, 'Cor deve ter no máximo 30 caracteres').optional().nullable(),
  pelagem: z.string().max(30, 'Pelagem deve ter no máximo 30 caracteres').optional().nullable(),
  
  // Physical measurements
  peso: pesoSchema.optional().nullable(),
  altura: alturaSchema.optional().nullable(),
  
  // Genealogy
  paiId: z.number().int().positive().optional().nullable(),
  maeId: z.number().int().positive().optional().nullable(),
  linhagemPaterna: z.string().optional().nullable(),
  linhagemMaterna: z.string().optional().nullable(),
  
  // Status tracking
  status: z.string().default('Ativo'),
  dataEntrada: optionalDateSchema,
  dataSaida: optionalDateSchema,
  
  // Location and breeding
  localNascimento: z.string().optional().nullable(),
  criador: z.string().max(100, 'Criador deve ter no máximo 100 caracteres').optional().nullable(),
  proprietarioAtual: z.string().max(100, 'Proprietário atual deve ter no máximo 100 caracteres').optional().nullable(),
  raca: z.string().max(50, 'Raça deve ter no máximo 50 caracteres').optional().nullable(),
  
  // Documentation
  certificadoRegistro: z.string().optional().nullable(),
  
  // Financial
  valorEstimado: currencySchema.optional().nullable(),
  
  // Purpose and additional
  finalidade: z.string().optional().nullable(),
  observacoes: observacoesSchema,
  
  // File attachments
  arquivoIds: z.array(z.string()).optional().nullable(),
  
  // Grandparents genealogy
  avoPaternoId: z.number().int().positive().optional().nullable(),
  avoPaternoNome: z.string().max(100, 'Nome do avô paterno deve ter no máximo 100 caracteres').optional().nullable(),
  avoMaternaId: z.number().int().positive().optional().nullable(),
  avoMaternaNome: z.string().max(100, 'Nome da avó materna deve ter no máximo 100 caracteres').optional().nullable(),
});

// Form validation schema (more permissive for UI)
export const horseFormSchema = z.object({
  // Only name is required in forms
  nome: nomeSchema, // Maps to 'name' in database
  
  // Registration and identification
  numeroRegistro: z.string().optional().nullable(),
  microchip: z.string().optional().nullable(),
  
  // Basic information
  birthDate: z.string().optional().nullable(), // String in forms, converted to Date
  sexo: z.string().optional().nullable(),
  cor: z.string().optional().nullable(),
  pelagem: z.string().optional().nullable(),
  
  // Physical measurements
  peso: z.coerce.number().optional().nullable(),
  altura: z.coerce.number().optional().nullable(),
  
  // Genealogy
  paiNome: z.string().optional().nullable(),
  maeNome: z.string().optional().nullable(),
  linhagemPaterna: z.string().optional().nullable(),
  linhagemMaterna: z.string().optional().nullable(),
  
  // Status tracking
  status: z.string().optional().nullable(),
  dataEntrada: z.string().optional().nullable(),
  dataSaida: z.string().optional().nullable(),
  
  // Location and breeding
  localNascimento: z.string().optional().nullable(),
  criador: z.string().optional().nullable(),
  proprietarioAtual: z.string().optional().nullable(),
  raca: z.string().optional().nullable(),
  
  // Documentation
  certificadoRegistro: z.string().optional().nullable(),
  
  // Financial
  valorEstimado: z.coerce.number().optional().nullable(),
  
  // Purpose and additional
  finalidade: z.string().optional().nullable(),
  observacoes: z.string().optional().nullable(),
  
  // Grandparents genealogy
  avoPaternoNome: z.string().optional().nullable(),
  avoMaternaNome: z.string().optional().nullable(),
}).refine((data) => {
  // Business rule: data saída deve ser posterior à data entrada
  if (data.dataEntrada && data.dataSaida) {
    const entrada = new Date(data.dataEntrada);
    const saida = new Date(data.dataSaida);
    return saida >= entrada;
  }
  return true;
}, {
  message: "Data de saída deve ser posterior à data de entrada",
  path: ["dataSaida"]
});

// Insert schema generated from table
export const insertHorseSchema = createInsertSchema(horses).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

// Update schema (all fields optional except ID)
export const updateHorseSchema = insertHorseSchema.partial().extend({
  id: z.number().int().positive(),
});

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type Horse = typeof horses.$inferSelect;
export type NewHorse = typeof horses.$inferInsert;
export type HorseFormData = z.infer<typeof horseFormSchema>;
export type HorseInsert = z.infer<typeof insertHorseSchema>;
export type HorseUpdate = z.infer<typeof updateHorseSchema>;

// ============================================================================
// BUSINESS LOGIC & CONVERTERS
// ============================================================================

/**
 * Converts form data to database insert format
 * Handles field mapping and data transformation
 */
export function convertFormToInsert(
  formData: HorseFormData, 
  userContext: UserContext
): HorseInsert {
  return {
    user_id: userContext.user_id,
    name: formData.nome || "Cavalo sem nome",
    numeroRegistro: formData.numeroRegistro || null,
    microchip: formData.microchip || null,
    birthDate: formData.birthDate || null,
    sexo: formData.sexo || null,
    cor: formData.cor || null,
    pelagem: formData.pelagem || null,
    peso: formData.peso || null,
    altura: formData.altura || null,
    paiId: null, // Will be resolved separately via genealogy lookup
    maeId: null, // Will be resolved separately via genealogy lookup
    linhagemPaterna: formData.linhagemPaterna || null,
    linhagemMaterna: formData.linhagemMaterna || null,
    status: formData.status || 'Ativo',
    dataEntrada: formData.dataEntrada || null,
    dataSaida: formData.dataSaida || null,
    localNascimento: formData.localNascimento || null,
    criador: formData.criador || null,
    proprietarioAtual: formData.proprietarioAtual || null,
    raca: formData.raca || null,
    certificadoRegistro: formData.certificadoRegistro || null,
    valorEstimado: formData.valorEstimado || null,
    finalidade: formData.finalidade || null,
    observacoes: formData.observacoes || null,
    arquivoIds: null, // Will be set separately when files are uploaded
    avoPaternoId: null,
    avoPaternoNome: formData.avoPaternoNome || null,
    avoMaternaId: null,
    avoMaternaNome: formData.avoMaternaNome || null,
  };
}

/**
 * Validates business rules for horse registration
 */
export function validateHorseBusinessRules(horse: HorseInsert): string[] {
  const errors: string[] = [];
  
  // Age validation
  if (horse.birth_date) {
    const birthDate = new Date(horse.birth_date);
    const today = new Date();
    const ageInYears = (today.getTime() - birthDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    
    if (ageInYears > 50) {
      errors.push("Idade do cavalo parece irreal (mais de 50 anos)");
    }
    
    if (birthDate > today) {
      errors.push("Data de nascimento não pode ser futura");
    }
  }
  
  // Weight and height correlation
  if (horse.peso && horse.altura) {
    const peso = horse.peso;
    const altura = horse.altura;
    
    // Rough validation: very light horses for their height
    if (peso < (altura * 0.5)) {
      errors.push("Peso parece muito baixo para a altura informada");
    }
    
    // Very heavy horses for their height
    if (peso > (altura * 4)) {
      errors.push("Peso parece muito alto para a altura informada");
    }
  }
  
  return errors;
}