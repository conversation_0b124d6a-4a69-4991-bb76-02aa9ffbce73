import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

/**
 * Tipos de erros customizados para melhor gerenciamento
 */
export enum ErrorTypes {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',
  DATABASE = 'DATABASE_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT = 'RATE_LIMIT_ERROR',
  INTERNAL = 'INTERNAL_SERVER_ERROR',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC_ERROR',
  SECURITY = 'SECURITY_ERROR',
}

/**
 * Classe de erro customizada para padronizar os erros da aplicação
 */
export class AppError extends Error {
  statusCode: number;
  type: ErrorTypes;
  details?: any;
  operationId?: string;

  constructor(
    message: string,
    type: ErrorTypes = ErrorTypes.INTERNAL,
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.operationId = generateOperationId();

    // Para capturar o stack trace no Node.js
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Gerar um ID único para cada operação para rastreamento
 */
function generateOperationId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
}

/**
 * Middleware de tratamento de erros centralizado
 */
export function errorHandler(
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) {
  const isProduction = process.env.NODE_ENV === 'production';
  
  console.error('Erro detectado:', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    user_id: req.body?.user_id,
    error: {
      name: err.name,
      message: err.message,
      stack: isProduction ? undefined : err.stack,
      ...(err instanceof AppError 
        ? { 
            type: err.type, 
            details: err.details,
            operationId: err.operationId
          } 
        : {})
    }
  });

  // Tratar erros customizados da aplicação
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      error: {
        type: err.type,
        message: err.message,
        ...(err.details ? { details: err.details } : {}),
        operationId: err.operationId,
      }
    });
  }

  // Tratar erros de validação do Zod
  if (err instanceof ZodError) {
    return res.status(400).json({
      error: {
        type: ErrorTypes.VALIDATION,
        message: 'Erro de validação nos dados enviados',
        details: err.format(),
        operationId: generateOperationId(),
      }
    });
  }

  // Tratar erros do banco de dados
  if (err.name === 'DatabaseError' || err.name === 'SequelizeError' || err.name === 'PostgresError') {
    return res.status(400).json({
      error: {
        type: ErrorTypes.DATABASE,
        message: 'Erro na operação do banco de dados',
        details: isProduction ? undefined : {
          code: (err as any).code || err.name,
          detail: (err as any).detail || err.message,
        },
        operationId: generateOperationId(),
      }
    });
  }

  // Erro genérico
  return res.status(500).json({
    error: {
      type: ErrorTypes.INTERNAL,
      message: isProduction 
        ? 'Ocorreu um erro interno no servidor' 
        : err.message || 'Erro interno no servidor',
      operationId: generateOperationId(),
    }
  });
}

/**
 * Funções auxiliares para criar erros customizados
 */
export const createError = {
  validation: (message: string, details?: any) => 
    new AppError(message, ErrorTypes.VALIDATION, 400, details),
  
  authentication: (message: string = 'Não autenticado', details?: any) => 
    new AppError(message, ErrorTypes.AUTHENTICATION, 401, details),
  
  authorization: (message: string = 'Não autorizado', details?: any) => 
    new AppError(message, ErrorTypes.AUTHORIZATION, 403, details),
  
  notFound: (resource: string, id?: string | number) => 
    new AppError(
      `${resource} não encontrado${id ? ` (id: ${id})` : ''}`, 
      ErrorTypes.RESOURCE_NOT_FOUND, 
      404,
      { resource, id }
    ),
  
  duplicate: (resource: string, field: string, value: any) => 
    new AppError(
      `${resource} com ${field} '${value}' já existe`, 
      ErrorTypes.DUPLICATE_RESOURCE, 
      409,
      { resource, field, value }
    ),
  
  database: (message: string, details?: any) => 
    new AppError(message, ErrorTypes.DATABASE, 400, details),
  
  externalService: (service: string, message: string, details?: any) => 
    new AppError(
      `Erro no serviço externo ${service}: ${message}`, 
      ErrorTypes.EXTERNAL_SERVICE, 
      502,
      { service, ...details }
    ),
  
  rateLimit: (message: string = 'Muitas requisições. Tente novamente mais tarde.') => 
    new AppError(message, ErrorTypes.RATE_LIMIT, 429),
  
  businessLogic: (message: string, details?: any) => 
    new AppError(message, ErrorTypes.BUSINESS_LOGIC, 422, details),
    
  security: (message: string, details?: any) => 
    new AppError(message, ErrorTypes.SECURITY, 403, details),
    
  internal: (message: string, details?: any) => 
    new AppError(message, ErrorTypes.INTERNAL, 500, details)
};