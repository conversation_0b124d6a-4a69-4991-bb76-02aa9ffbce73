/**
 * Testes de integração para as rotas de debug
 */
import express from 'express';
import request from 'supertest';
import debugRouter from '../../routes/debug';
import { traceMiddleware, TRACE_ID_KEY } from '../../middleware/tracing';

describe('Rotas de Debug - Integração', () => {
  let app: express.Express;

  beforeEach(() => {
    // Configura uma aplicação Express para testes
    app = express();
    
    // Adiciona o middleware de tracing
    app.use(traceMiddleware);
    
    // Registra as rotas de debug
    app.use('/api/debug', debugRouter);
    
    // Middleware de tratamento de erros
    app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
      res.status(500).json({ error: err.message });
    });
  });

  test('GET /api/debug/trace deve retornar um traceId', async () => {
    const response = await request(app)
      .get('/api/debug/trace')
      .set('user-id', '123');
    
    // Verifica se a resposta contém o traceId
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.traceId).toBeDefined();
    expect(typeof response.body.traceId).toBe('string');
    
    // Verifica se o header contém o traceId
    expect(response.headers[TRACE_ID_KEY.toLowerCase()]).toBeDefined();
    expect(response.headers[TRACE_ID_KEY.toLowerCase()]).toBe(response.body.traceId);
  });

  test('GET /api/debug/log-levels deve gerar logs de diferentes níveis', async () => {
    const response = await request(app)
      .get('/api/debug/log-levels')
      .set('user-id', '123');
    
    // Verifica se a resposta foi bem-sucedida
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Logs gerados com sucesso');
    expect(response.body.traceId).toBeDefined();
  });

  test('GET /api/debug/sentry-test deve capturar um erro', async () => {
    const response = await request(app)
      .get('/api/debug/sentry-test')
      .set('user-id', '123');
    
    // Verifica se a resposta contém o erro
    expect(response.status).toBe(500);
    expect(response.body.error).toBe('Erro de teste para o Sentry');
  });

  test('GET /api/debug/sentry-capture deve capturar um erro manualmente', async () => {
    const response = await request(app)
      .get('/api/debug/sentry-capture')
      .set('user-id', '123');
    
    // Verifica se a resposta foi bem-sucedida
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Erro capturado e enviado para o Sentry');
    expect(response.body.traceId).toBeDefined();
  });

  test('GET /api/debug/performance deve executar um teste de desempenho', async () => {
    const response = await request(app)
      .get('/api/debug/performance')
      .query({ iterations: 10 }) // Usar um valor baixo para o teste
      .set('user-id', '123');
    
    // Verifica se a resposta foi bem-sucedida
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.iterations).toBe(10);
    expect(response.body.duration).toBeDefined();
    expect(typeof response.body.duration).toBe('number');
    expect(response.body.result).toBeDefined();
    expect(response.body.traceId).toBeDefined();
  });
});
