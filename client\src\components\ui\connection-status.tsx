import React, { useState, useEffect } from 'react';
import { AlertCircle, WifiOff, Wifi } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

/**
 * Componente que monitora o status da conexão e exibe alertas quando necessário
 */
export function ConnectionStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showReconnected, setShowReconnected] = useState(false);
  const [apiAvailable, setApiAvailable] = useState(true);
  const [checkingApi, setCheckingApi] = useState(false);

  // Monitorar status de conexão do navegador
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowReconnected(true);
      // Esconder mensagem de reconexão após 5 segundos
      setTimeout(() => setShowReconnected(false), 5000);
      // Verificar API quando reconectar
      checkApiStatus();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setApiAvailable(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Verificar API no carregamento inicial
    checkApiStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Função para verificar se a API está disponível
  const checkApiStatus = async () => {
    if (!navigator.onLine) return;

    setCheckingApi(true);
    try {
      // Fazer uma requisição simples para verificar se a API está respondendo
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/health', {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      clearTimeout(timeoutId);
      setApiAvailable(response.ok);
    } catch (error) {
      console.error('Erro ao verificar status da API:', error);
      setApiAvailable(false);
    } finally {
      setCheckingApi(false);
    }
  };

  // Se estiver online e a API estiver disponível, não mostrar nada
  if (isOnline && apiAvailable && !showReconnected) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md">
      {!isOnline && (
        <Alert variant="destructive" className="mb-2">
          <WifiOff className="h-4 w-4" />
          <AlertTitle>Sem conexão</AlertTitle>
          <AlertDescription>
            Você está offline. Verifique sua conexão com a internet.
          </AlertDescription>
        </Alert>
      )}

      {isOnline && !apiAvailable && (
        <Alert variant="destructive" className="mb-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Servidor indisponível</AlertTitle>
          <AlertDescription className="flex flex-col gap-2">
            <span>O servidor não está respondendo. Tente novamente mais tarde.</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={checkApiStatus}
              disabled={checkingApi}
            >
              {checkingApi ? 'Verificando...' : 'Verificar novamente'}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {isOnline && apiAvailable && showReconnected && (
        <Alert className="bg-green-50 border-green-200 mb-2">
          <Wifi className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Conexão restaurada</AlertTitle>
          <AlertDescription className="text-green-700">
            Sua conexão com a internet foi restaurada.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

export default ConnectionStatus;
