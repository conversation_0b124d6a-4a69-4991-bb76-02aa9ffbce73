/**
 * Rotas para gerenciamento dos serviços ABCCC
 * 
 * Este módulo contém as rotas relacionadas ao scraping de dados da ABCCC,
 * incluindo tokens, configurações e estatísticas de uso.
 */
import express from 'express';
import { db } from '../db';
import { abcccTokens } from '../../shared/schema-tokens';
import { eq, desc, sql, inArray } from 'drizzle-orm';
import { getModuleLogger } from '../logger';
import { obterTokensDisponiveis, registrarUsoToken, gerarNovoToken, gerarTokensAutomaticamente } from '../abccc-config';
import rateLimit from 'express-rate-limit';
import { validate, authValidation, abcccValidation } from '../validation';

// Logger para as rotas
const routesLogger = getModuleLogger('abccc-routes');

// Criar router
const router = express.Router();

// Rate limiting para API de scraping
const scrapingLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // Limite de 100 requisições por IP em 15 minutos
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Limite de requisições excedido. Tente novamente mais tarde.'
});

// Middleware para verificar se é admin
const requireAdmin = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Verificar se o usuário tem permissão admin
  const isAdmin = req.headers['admin-access'] === process.env.ADMIN_ACCESS_KEY;
  
  if (!isAdmin) {
    return res.status(403).json({ error: 'Acesso negado. Permissão de administrador necessária.' });
  }
  
  next();
};

// Rota para obter tokens
router.get('/tokens', requireAdmin, async (req, res) => {
  try {
    const tokens = await db.select()
      .from(abcccTokens)
      .orderBy(desc(abcccTokens.ultimo_uso))
      .limit(100);
      
    res.json(tokens);
  } catch (error) {
    routesLogger.error(`Erro ao listar tokens: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao listar tokens' });
  }
});

// Rota para adicionar token
router.post('/tokens', requireAdmin, validate(abcccValidation.token), async (req, res) => {
  try {
    const { token, origem, observacoes } = req.body;
    
    if (!token) {
      return res.status(400).json({ error: 'Token é obrigatório' });
    }
    
    // Verificar se o token já existe
    const tokenExistente = await db.select({ id: abcccTokens.id })
      .from(abcccTokens)
      .where(eq(abcccTokens.token, token))
      .limit(1);
      
    if (tokenExistente.length > 0) {
      return res.status(400).json({ error: 'Token já existe no sistema' });
    }
    
    // Inserir novo token
    const resultado = await db.insert(abcccTokens)
      .values({
        token,
        valido: true,
        origem: origem || 'manual',
        observacoes
      })
      .returning();
      
    res.status(201).json(resultado[0]);
  } catch (error) {
    routesLogger.error(`Erro ao adicionar token: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao adicionar token' });
  }
});

// Rota para invalidar token
router.patch('/tokens/:id/invalidar', requireAdmin, validate(authValidation.idParam, 'params'), async (req, res) => {
  try {
    const id = req.params.id as unknown as number;
    
    // Atualizar token
    const resultado = await db.update(abcccTokens)
      .set({ 
        valido: false,
        observacoes: `Invalidado em ${new Date().toISOString()}`
      })
      .where(eq(abcccTokens.id, id))
      .returning();
      
    if (resultado.length === 0) {
      return res.status(404).json({ error: 'Token não encontrado' });
    }
    
    res.json(resultado[0]);
  } catch (error) {
    routesLogger.error(`Erro ao invalidar token: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao invalidar token' });
  }
});

// Rota para gerar novo token individual
router.post('/tokens/gerar', requireAdmin, async (req, res) => {
  try {
    const novoToken = gerarNovoToken();
    const origem = req.body.origem || 'gerado';
    
    // Inserir no banco
    const resultado = await db.insert(abcccTokens)
      .values({
        token: novoToken,
        valido: true,
        origem,
        observacoes: `Gerado manualmente em ${new Date().toISOString()}`
      })
      .returning();
      
    res.status(201).json(resultado[0]);
  } catch (error) {
    routesLogger.error(`Erro ao gerar token: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao gerar token' });
  }
});

// Rota para geração automática de múltiplos tokens
router.post('/tokens/gerar-automaticos', requireAdmin, async (req, res) => {
  try {
    const quantidade = req.body.quantidade || 5;
    
    // Limitar quantidade para evitar sobrecarga
    const quantidadeReal = Math.min(quantidade, 10);
    
    // Usar a função de geração automática
    const tokensGerados = await gerarTokensAutomaticamente(quantidadeReal);
    
    // Buscar os tokens gerados no banco para retornar os dados completos
    if (tokensGerados.length > 0) {
      const tokensCompletos = await db.select()
        .from(abcccTokens)
        .where(inArray(abcccTokens.token, tokensGerados));
        
      res.status(201).json({ 
        quantidade_solicitada: quantidade,
        quantidade_gerada: tokensGerados.length,
        tokens: tokensCompletos
      });
    } else {
      res.status(200).json({ 
        quantidade_solicitada: quantidade,
        quantidade_gerada: 0,
        tokens: [],
        mensagem: 'Não foi possível gerar novos tokens'
      });
    }
  } catch (error) {
    routesLogger.error(`Erro ao gerar tokens automaticamente: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao gerar tokens automaticamente' });
  }
});

// Rota para estatísticas de uso
router.get('/tokens/stats', requireAdmin, async (req, res) => {
  try {
    // Total de tokens
    const [totalTokensResult] = await db.select({ 
      count: sql`count(*)` 
    }).from(abcccTokens);
    
    // Tokens válidos
    const [tokensValidosResult] = await db.select({ 
      count: sql`count(*)` 
    })
    .from(abcccTokens)
    .where(eq(abcccTokens.valido, true));
    
    // Top tokens por sucesso
    const topTokens = await db.select({
      id: abcccTokens.id,
      token: abcccTokens.token,
      sucessos: abcccTokens.sucessos,
      falhas: abcccTokens.falhas,
      taxa_sucesso: sql`CASE WHEN ${abcccTokens.sucessos} + ${abcccTokens.falhas} > 0 
                  THEN ROUND((${abcccTokens.sucessos}::float / (${abcccTokens.sucessos} + ${abcccTokens.falhas})::float) * 100, 2)
                  ELSE 0 END`
    })
    .from(abcccTokens)
    .where(eq(abcccTokens.valido, true))
    .orderBy(desc(sql`${abcccTokens.sucessos}::float / GREATEST((${abcccTokens.sucessos} + ${abcccTokens.falhas})::float, 1)`))
    .limit(10);
    
    const totalTokens = Number(totalTokensResult?.count || 0);
    const tokensValidos = Number(tokensValidosResult?.count || 0);
    
    res.json({
      total_tokens: totalTokens,
      tokens_validos: tokensValidos,
      tokens_invalidos: totalTokens - tokensValidos,
      top_tokens: topTokens
    });
  } catch (error) {
    routesLogger.error(`Erro ao obter estatísticas: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao obter estatísticas' });
  }
});

// Rota para obter cavalo ABCCC com limitação de taxa
router.get('/cavalo/:registro', scrapingLimiter, async (req, res) => {
  try {
    const registro = req.params.registro.trim();
    
    // Implementar a lógica para obter dados do cavalo usando abccc-scraper-service
    
    res.json({ message: 'Obtenção de dados do cavalo pelo registro', registro });
  } catch (error) {
    routesLogger.error(`Erro ao obter cavalo: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({ error: 'Erro interno ao obter cavalo', message: error instanceof Error ? error.message : String(error) });
  }
});

export default router;