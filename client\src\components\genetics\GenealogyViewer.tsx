import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Loader2, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import GenealogyTree from './GenealogyTree';
import AnimatedGenealogyTree from './AnimatedGenealogyTree';
// Usando uma interface genérica para genealogia para maior compatibilidade
interface GenealogiaDados {
  cavalo?: any;
  consanguinidade?: number | null;
  bisavosConhecidos?: number;
  observacoes?: string;
  [key: string]: any;
}
import { GenealogyNode } from './GenealogyTree';

interface GenealogyViewerProps {
  isLoading: boolean;
  genealogia: GenealogiaDados | undefined;
  rootNode: GenealogyNode | null;
  onNodeClick: (id: number | null) => void;
  onEditClick: () => void;
}

/**
 * Componente para visualização de árvore genealógica com múltiplas visualizações
 */
const GenealogyViewer: React.FC<GenealogyViewerProps> = ({
  isLoading,
  genealogia,
  rootNode,
  onNodeClick,
  onEditClick,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando genealogia...</span>
      </div>
    );
  }

  if (!genealogia || !rootNode) {
    return (
      <div className="border rounded-md p-6 bg-muted/20 flex flex-col items-center justify-center">
        <PieChart className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-1">Árvore Genealógica Vazia</h3>
        <p className="text-sm text-muted-foreground text-center max-w-md mb-4">
          Registrar a genealogia de seus animais ajuda no controle de cruzamentos e 
          evita problemas de consanguinidade.
        </p>
        <Button
          onClick={onEditClick}
        >
          <Edit className="mr-2 h-4 w-4" />
          Cadastrar Genealogia
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-sm rounded-md p-4">
      <div>
        {/* Abas para alternar entre visualizações */}
        <Tabs defaultValue="tradicional" className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <TabsList>
              <TabsTrigger value="tradicional">Tradicional</TabsTrigger>
              <TabsTrigger value="animada">Interativa</TabsTrigger>
            </TabsList>
            {/* Dica de uso para a visualização animada */}
            <div className="text-xs text-muted-foreground p-1 bg-muted/10 rounded-sm">
              <span>Dica: Use o mouse para navegar e zoom</span>
            </div>
          </div>
          
          <TabsContent value="tradicional" className="mt-0">
            <GenealogyTree 
              node={rootNode} 
              onNodeClick={onNodeClick}
              maxGenerations={3}
            />
          </TabsContent>
          
          <TabsContent value="animada" className="mt-0">
            <AnimatedGenealogyTree 
              rootNode={rootNode}
              onNodeClick={onNodeClick}
              maxGenerations={4}
              className="h-[500px]"
            />
          </TabsContent>
        </Tabs>
        
        {genealogia.bisavosConhecidos !== undefined && genealogia.bisavosConhecidos !== null ? (
          <div className="text-xs text-muted-foreground mt-4">
            Completude da árvore: {genealogia.bisavosConhecidos} de 8 bisavós conhecidos (
            {((genealogia.bisavosConhecidos / 8) * 100).toFixed(0)}%)
          </div>
        ) : (
          <div className="text-xs text-muted-foreground mt-4">
            Não há informações sobre bisavós cadastradas.
          </div>
        )}
      </div>
    </div>
  );
};

export default GenealogyViewer;