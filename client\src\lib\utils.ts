import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formata uma data no formato ISO para o formato brasileiro (DD/MM/YYYY)
 */
export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Data inválida';
    
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return 'Data inválida';
  }
}

/**
 * Calcula a idade em anos a partir de uma data de nascimento
 */
export function calculateAge(birth_dateString: string | null | undefined): number | null {
  if (!birth_dateString) return null;
  
  try {
    const birth_date = new Date(birth_dateString);
    if (isNaN(birth_date.getTime())) return null;
    
    const today = new Date();
    let age = today.getFullYear() - birth_date.getFullYear();
    const monthDiff = today.getMonth() - birth_date.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth_date.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    return null;
  }
}

/**
 * Trunca um texto para um determinado número de caracteres
 */
export function truncateText(text: string | undefined | null, maxLength: number): string {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
}

/**
 * Formata um valor numérico para exibição com unidade
 */
export function formatMeasurement(value: number | undefined | null, unit: string): string {
  if (value === null || value === undefined) return 'N/A';
  return `${value} ${unit}`;
}

/**
 * Gera uma cor baseada em um valor de 0 a 100
 */
export function getColorByValue(value: number): string {
  if (value >= 80) return 'text-green-600 dark:text-green-400';
  if (value >= 60) return 'text-emerald-600 dark:text-emerald-400';
  if (value >= 40) return 'text-yellow-600 dark:text-yellow-400';
  if (value >= 20) return 'text-orange-600 dark:text-orange-400';
  return 'text-red-600 dark:text-red-400';
}