import { useState, useEffect } from 'react';
import { LayoutWrapper } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { SmartAssistantAI } from '@/components/assistente/SmartAssistantAI';
import { Bo<PERSON>, Sparkles, FileQuestion, Wand2 } from 'lucide-react';

/**
 * Página principal do Assistente Virtual
 * 
 * Esta página contém a interface do assistente virtual integrado
 * e informações de uso
 */
export default function AssistentePage() {
  return (
    <LayoutWrapper pageTitle="Assistente Virtual">
      <div className="container mx-auto p-4">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Coluna principal com o assistente */}
          <div className="lg:w-3/5">
            <Card className="shadow-lg border-blue-100">
              <CardHeader className="pb-0">
                <div className="flex items-center gap-2 mb-2">
                  <Bot className="h-6 w-6 text-primary" />
                  <CardTitle className="text-2xl font-bold text-primary">
                    Assistente Virtual
                  </CardTitle>
                </div>
                <CardDescription className="text-base">
                  Seu assistente inteligente para gestão equina
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="h-[600px]">
                  <SmartAssistantAI isExpanded={true} />
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Coluna lateral com informações */}
          <div className="lg:w-2/5">
            <div className="space-y-6">
              <Card className="shadow-md border-blue-100">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-amber-500" />
                    <CardTitle className="text-lg">O que posso fazer?</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-primary">•</span>
                      <span>Responder perguntas sobre seus cavalos e suas informações</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-primary">•</span>
                      <span>Agendar eventos e compromissos</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-primary">•</span>
                      <span>Buscar informações de manejo e cuidados</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-primary">•</span>
                      <span>Encontrar dados reprodutivos e genéticos</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-primary">•</span>
                      <span>Auxiliar na análise de saúde e nutrição</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="shadow-md border-blue-100">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Wand2 className="h-5 w-5 text-purple-500" />
                    <CardTitle className="text-lg">Comandos úteis</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="p-2 bg-gray-50 rounded">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-primary">"Agende uma consulta para o cavalo Pegasus amanhã às 10h"</span>
                    </li>
                    <li className="p-2 bg-gray-50 rounded">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-primary">"Quais são as próximas vacinas pendentes?"</span>
                    </li>
                    <li className="p-2 bg-gray-50 rounded">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-primary">"Mostre o histórico de peso do cavalo Trovão"</span>
                    </li>
                    <li className="p-2 bg-gray-50 rounded">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded text-primary">"Quando foi a última vermifugação da égua Luna?"</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card className="shadow-md border-blue-100">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <FileQuestion className="h-5 w-5 text-green-500" />
                    <CardTitle className="text-lg">Dicas de uso</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 text-sm">
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-green-500">1.</span>
                      <span>Seja específico ao mencionar nomes de cavalos ou datas para obter respostas mais precisas.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-green-500">2.</span>
                      <span>O assistente pode ajudar a encontrar informações mesmo se você não lembrar o nome exato do cavalo.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="font-semibold mr-2 text-green-500">3.</span>
                      <span>Pergunte sobre relatórios e estatísticas para obter análises detalhadas.</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </LayoutWrapper>
  );
}